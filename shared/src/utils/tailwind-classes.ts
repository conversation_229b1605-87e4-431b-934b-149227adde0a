/**
 * Centralized Tailwind CSS classes for consistent styling across all platforms
 * These classes ensure consistent design patterns and make it easy to update styles globally
 */

/**
 * Background color classes
 */
export const BG_CLASSES = {
  // Primary backgrounds
  primary: 'bg-primary-500',
  'primary-light': 'bg-primary-50',
  'primary-dark': 'bg-primary-900',
  'primary-hover': 'hover:bg-primary-600',
  
  // Status backgrounds
  success: 'bg-success-500',
  'success-light': 'bg-success-50',
  'success-hover': 'hover:bg-success-600',
  
  warning: 'bg-warning-500',
  'warning-light': 'bg-warning-50',
  'warning-hover': 'hover:bg-warning-600',
  
  error: 'bg-error-500',
  'error-light': 'bg-error-50',
  'error-hover': 'hover:bg-error-600',
  
  info: 'bg-info-500',
  'info-light': 'bg-info-50',
  'info-hover': 'hover:bg-info-600',
  
  // Neutral backgrounds
  white: 'bg-white dark:bg-gray-900',
  muted: 'bg-gray-50 dark:bg-gray-800',
  card: 'bg-white dark:bg-gray-800',
  'card-hover': 'hover:bg-gray-50 dark:hover:bg-gray-700',
} as const;

/**
 * Text color classes
 */
export const TEXT_CLASSES = {
  // Primary text
  primary: 'text-gray-900 dark:text-gray-100',
  secondary: 'text-gray-600 dark:text-gray-400',
  muted: 'text-gray-500 dark:text-gray-500',
  
  // Status text
  success: 'text-success-600 dark:text-success-400',
  warning: 'text-warning-600 dark:text-warning-400',
  error: 'text-error-600 dark:text-error-400',
  info: 'text-info-600 dark:text-info-400',
  
  // Brand text
  brand: 'text-primary-600 dark:text-primary-400',
  
  // Interactive text
  link: 'text-primary-600 hover:text-primary-700 dark:text-primary-400 dark:hover:text-primary-300',
} as const;

/**
 * Border color classes
 */
export const BORDER_CLASSES = {
  default: 'border-gray-200 dark:border-gray-700',
  muted: 'border-gray-100 dark:border-gray-800',
  primary: 'border-primary-500',
  success: 'border-success-500',
  warning: 'border-warning-500',
  error: 'border-error-500',
  info: 'border-info-500',
} as const;

/**
 * Button classes for consistent button styling
 */
export const BUTTON_CLASSES = {
  // Primary buttons
  primary: `${BG_CLASSES.primary} ${BG_CLASSES['primary-hover']} text-white border-transparent`,
  'primary-outline': `border-2 ${BORDER_CLASSES.primary} ${TEXT_CLASSES.brand} hover:${BG_CLASSES.primary} hover:text-white`,
  
  // Secondary buttons
  secondary: `${BG_CLASSES.muted} hover:bg-gray-100 dark:hover:bg-gray-700 ${TEXT_CLASSES.primary} ${BORDER_CLASSES.default}`,
  
  // Status buttons
  success: `${BG_CLASSES.success} ${BG_CLASSES['success-hover']} text-white border-transparent`,
  warning: `${BG_CLASSES.warning} ${BG_CLASSES['warning-hover']} text-white border-transparent`,
  error: `${BG_CLASSES.error} ${BG_CLASSES['error-hover']} text-white border-transparent`,
  
  // Ghost buttons
  ghost: `hover:${BG_CLASSES.muted} ${TEXT_CLASSES.primary}`,
  
  // Base button styles
  base: 'inline-flex items-center justify-center rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:opacity-50 disabled:pointer-events-none ring-offset-background',
} as const;

/**
 * Card classes for consistent card styling
 */
export const CARD_CLASSES = {
  base: `${BG_CLASSES.card} ${BORDER_CLASSES.default} rounded-lg shadow-sm`,
  hover: `${CARD_CLASSES.base} ${BG_CLASSES['card-hover']} transition-colors`,
  elevated: `${BG_CLASSES.card} ${BORDER_CLASSES.default} rounded-lg shadow-md`,
  interactive: `${CARD_CLASSES.base} ${BG_CLASSES['card-hover']} cursor-pointer transition-all hover:shadow-md`,
} as const;

/**
 * Input classes for consistent form styling
 */
export const INPUT_CLASSES = {
  base: `flex h-10 w-full rounded-md border ${BORDER_CLASSES.default} bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:${TEXT_CLASSES.muted} focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50`,
  error: `${INPUT_CLASSES.base} ${BORDER_CLASSES.error} focus-visible:ring-error-500`,
  success: `${INPUT_CLASSES.base} ${BORDER_CLASSES.success} focus-visible:ring-success-500`,
} as const;

/**
 * Badge classes for status indicators
 */
export const BADGE_CLASSES = {
  // Status badges
  success: `${BG_CLASSES['success-light']} ${TEXT_CLASSES.success} ${BORDER_CLASSES.success}`,
  warning: `${BG_CLASSES['warning-light']} ${TEXT_CLASSES.warning} ${BORDER_CLASSES.warning}`,
  error: `${BG_CLASSES['error-light']} ${TEXT_CLASSES.error} ${BORDER_CLASSES.error}`,
  info: `${BG_CLASSES['info-light']} ${TEXT_CLASSES.info} ${BORDER_CLASSES.info}`,
  
  // Neutral badges
  default: `${BG_CLASSES.muted} ${TEXT_CLASSES.secondary} ${BORDER_CLASSES.muted}`,
  
  // Base badge styles
  base: 'inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors',
} as const;

/**
 * Job-specific styling classes
 */
export const JOB_CLASSES = {
  // Job status badges
  status: {
    draft: `${BADGE_CLASSES.base} ${BADGE_CLASSES.default}`,
    active: `${BADGE_CLASSES.base} ${BADGE_CLASSES.info}`,
    completed: `${BADGE_CLASSES.base} ${BADGE_CLASSES.success}`,
    cancelled: `${BADGE_CLASSES.base} ${BADGE_CLASSES.error}`,
  },
  
  // Job type indicators
  type: {
    emergency: `${BADGE_CLASSES.base} ${BADGE_CLASSES.error}`,
    featured: `${BADGE_CLASSES.base} ${BADGE_CLASSES.warning}`,
    regular: `${BADGE_CLASSES.base} ${BADGE_CLASSES.info}`,
  },
  
  // Trust score indicators
  trustScore: {
    excellent: TEXT_CLASSES.success, // 80-100
    good: 'text-success-500',        // 60-79
    fair: TEXT_CLASSES.warning,      // 40-59
    poor: TEXT_CLASSES.error,        // 0-39
  },
} as const;

/**
 * Application status classes
 */
export const APPLICATION_CLASSES = {
  status: {
    pending: `${BADGE_CLASSES.base} ${BADGE_CLASSES.warning}`,
    accepted: `${BADGE_CLASSES.base} ${BADGE_CLASSES.success}`,
    rejected: `${BADGE_CLASSES.base} ${BADGE_CLASSES.error}`,
    completed: `${BADGE_CLASSES.base} ${BADGE_CLASSES.info}`,
  },
} as const;

/**
 * Payment status classes
 */
export const PAYMENT_CLASSES = {
  status: {
    pending: `${BADGE_CLASSES.base} ${BADGE_CLASSES.warning}`,
    processing: `${BADGE_CLASSES.base} ${BADGE_CLASSES.info}`,
    completed: `${BADGE_CLASSES.base} ${BADGE_CLASSES.success}`,
    failed: `${BADGE_CLASSES.base} ${BADGE_CLASSES.error}`,
    cancelled: `${BADGE_CLASSES.base} ${BADGE_CLASSES.default}`,
  },
} as const;

/**
 * Layout classes for consistent spacing and structure
 */
export const LAYOUT_CLASSES = {
  container: 'container mx-auto px-4 sm:px-6 lg:px-8',
  section: 'py-8 sm:py-12 lg:py-16',
  grid: {
    cols1: 'grid grid-cols-1 gap-6',
    cols2: 'grid grid-cols-1 md:grid-cols-2 gap-6',
    cols3: 'grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6',
    cols4: 'grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6',
  },
  flex: {
    center: 'flex items-center justify-center',
    between: 'flex items-center justify-between',
    start: 'flex items-center justify-start',
    end: 'flex items-center justify-end',
  },
} as const;

/**
 * Animation classes for consistent transitions
 */
export const ANIMATION_CLASSES = {
  fadeIn: 'animate-in fade-in-0 duration-200',
  slideIn: 'animate-in slide-in-from-bottom-4 duration-300',
  scaleIn: 'animate-in zoom-in-95 duration-200',
  transition: 'transition-all duration-200 ease-in-out',
} as const;

/**
 * Utility function to combine classes safely
 */
export function cn(...classes: (string | undefined | null | false)[]): string {
  return classes.filter(Boolean).join(' ');
}

/**
 * Helper function to get trust score color class
 */
export function getTrustScoreColorClass(score: number): string {
  if (score >= 80) return JOB_CLASSES.trustScore.excellent;
  if (score >= 60) return JOB_CLASSES.trustScore.good;
  if (score >= 40) return JOB_CLASSES.trustScore.fair;
  return JOB_CLASSES.trustScore.poor;
}

/**
 * Helper function to get job status badge class
 */
export function getJobStatusBadgeClass(status: string): string {
  return JOB_CLASSES.status[status as keyof typeof JOB_CLASSES.status] || JOB_CLASSES.status.draft;
}

/**
 * Helper function to get application status badge class
 */
export function getApplicationStatusBadgeClass(status: string): string {
  return APPLICATION_CLASSES.status[status as keyof typeof APPLICATION_CLASSES.status] || APPLICATION_CLASSES.status.pending;
}

/**
 * Helper function to get payment status badge class
 */
export function getPaymentStatusBadgeClass(status: string): string {
  return PAYMENT_CLASSES.status[status as keyof typeof PAYMENT_CLASSES.status] || PAYMENT_CLASSES.status.pending;
}
