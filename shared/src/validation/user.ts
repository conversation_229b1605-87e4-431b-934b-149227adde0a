import { z } from "zod"

export const updateProfileSchema = z.object({
  fullName: z.string().min(2).max(100).optional(),
  email: z.string().email().optional(),
  phone: z.string().min(10).max(15).optional(),
  address: z.string().optional(),
  city: z.string().optional(),
  state: z.string().optional(),
  country: z.string().optional(),
  postalCode: z.string().optional(),
  bio: z.string().optional(),
  companyName: z.string().optional(),
  companyRegistrationNumber: z.string().optional(),
  companyTaxId: z.string().optional(),
  companyWebsite: z.string().url().optional(),
  companySize: z.string().optional(),
  companyIndustry: z.string().optional(),
  deviceToken: z.string().optional(),
})

export const updateKycSchema = z.object({
  aadharNumber: z.string().min(12).max(12).optional(),
  panNumber: z.string().min(10).max(10).optional(),
  gstNumber: z.string().optional(),
  companyRegistrationNumber: z.string().optional(),
})

export const updateTrustScoreSchema = z.object({
  userId: z.string().uuid(),
  score: z.number().min(0).max(100),
  reason: z.string(),
})

export type UpdateProfileDto = z.infer<typeof updateProfileSchema>
export type UpdateKycDto = z.infer<typeof updateKycSchema>
export type UpdateTrustScoreDto = z.infer<typeof updateTrustScoreSchema>
