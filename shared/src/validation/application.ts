import { z } from "zod"

export const createApplicationSchema = z.object({
  jobId: z.string().uuid(),
  coverLetter: z.string().optional(),
  expectedPay: z.number().positive().optional(),
  availabilityStart: z.string().or(z.date()).optional(),
  availabilityEnd: z.string().or(z.date()).optional(),
})

export const updateApplicationSchema = z.object({
  status: z.enum(["pending", "accepted", "rejected", "cancelled", "completed", "no-show"]),
  reason: z.string().optional(),
})

export type CreateApplicationDto = z.infer<typeof createApplicationSchema>
export type UpdateApplicationDto = z.infer<typeof updateApplicationSchema>
