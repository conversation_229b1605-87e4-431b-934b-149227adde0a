import { z } from "zod"
import { PayoutStatus } from "../types/payout"

export const createPayoutSchema = z.object({
  jobId: z.string().uuid(),
  workerId: z.string().uuid(),
  grossPay: z.number().positive(),
  commission: z.number().min(0),
  netPay: z.number().positive(),
  paymentMethod: z.string().optional(),
})

export const updatePayoutSchema = z.object({
  status: z.nativeEnum(PayoutStatus).optional(),
  paymentMethod: z.string().optional(),
  transactionId: z.string().optional(),
  paymentDate: z.date().optional(),
})

export type CreatePayoutDto = z.infer<typeof createPayoutSchema>
export type UpdatePayoutDto = z.infer<typeof updatePayoutSchema>
