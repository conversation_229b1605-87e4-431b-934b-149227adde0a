import { z } from "zod"

export const createJobSchema = z.object({
  title: z.string().min(5).max(100),
  description: z.string().min(20),
  locationAddress: z.string(),
  locationCity: z.string(),
  locationState: z.string().optional(),
  locationCountry: z.string(),
  locationPostalCode: z.string().optional(),
  latitude: z.number().optional(),
  longitude: z.number().optional(),
  startDate: z.string().or(z.date()),
  endDate: z.string().or(z.date()),
  hourlyRate: z.number().positive(),
  estimatedHours: z.number().positive(),
  requiredTrustScore: z.number().min(0).max(100).default(0),
  requiredWorkers: z.number().positive().default(1),
  equipmentRequired: z.string().optional(),
  skillsRequired: z.string().optional(),
  isEmergency: z.boolean().default(false),
})

export const createJobTemplateSchema = z.object({
  title: z.string().min(5).max(100),
  description: z.string().min(20),
  hourlyRate: z.number().positive().optional(),
  estimatedHours: z.number().positive().optional(),
  requiredTrustScore: z.number().min(0).max(100).optional(),
  equipmentRequired: z.string().optional(),
  skillsRequired: z.string().optional(),
})

export const updateJobSchema = createJobSchema.partial().extend({
  status: z.enum(["open", "in-progress", "completed", "cancelled"]).optional(),
})

export type CreateJobDto = z.infer<typeof createJobSchema>
export type CreateJobTemplateDto = z.infer<typeof createJobTemplateSchema>
export type UpdateJobDto = z.infer<typeof updateJobSchema>
