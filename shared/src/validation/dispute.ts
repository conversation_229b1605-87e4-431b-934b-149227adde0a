import { z } from "zod"
import { DisputeStatus } from "../types/dispute"

export const createDisputeSchema = z.object({
  jobId: z.string().uuid(),
  againstId: z.string().uuid(),
  title: z.string().min(5).max(100),
  description: z.string().min(10),
})

export const updateDisputeSchema = z.object({
  status: z.nativeEnum(DisputeStatus).optional(),
  resolution: z.string().optional(),
})

export type CreateDisputeDto = z.infer<typeof createDisputeSchema>
export type UpdateDisputeDto = z.infer<typeof updateDisputeSchema>
