import { z } from "zod"

export const loginSchema = z
  .object({
    email: z.string().email().optional(),
    phone: z.string().min(10).max(15).optional(),
    password: z.string().min(6),
  })
  .refine((data) => data.email || data.phone, {
    message: "Either email or phone is required",
    path: ["email", "phone"],
  })

export const registerSchema = z
  .object({
    fullName: z.string().min(2).max(100),
    email: z.string().email().optional(),
    phone: z.string().min(10).max(15).optional(),
    password: z.string().min(6),
    role: z.enum(["worker", "company"]),
    companyName: z.string().min(2).max(100).optional(),
  })
  .refine((data) => data.email || data.phone, {
    message: "Either email or phone is required",
    path: ["email", "phone"],
  })
  .refine((data) => data.role !== "company" || data.companyName, {
    message: "Company name is required for company accounts",
    path: ["companyName"],
  })

export const otpRequestSchema = z.object({
  phone: z.string().min(10).max(15),
})

export const otpVerifySchema = z.object({
  phone: z.string().min(10).max(15),
  code: z.string().length(6),
})

export type LoginDto = z.infer<typeof loginSchema>
export type RegisterDto = z.infer<typeof registerSchema>
export type OtpRequestDto = z.infer<typeof otpRequestSchema>
export type OtpVerifyDto = z.infer<typeof otpVerifySchema>
