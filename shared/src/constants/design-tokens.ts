/**
 * Centralized Design Tokens for the Job Platform
 * These tokens ensure consistent colors across all platforms (mobile, company portal, admin portal)
 */

export const COLORS = {
  // Primary brand colors
  primary: {
    50: '#f0f9ff',
    100: '#e0f2fe',
    200: '#bae6fd',
    300: '#7dd3fc',
    400: '#38bdf8',
    500: '#0ea5e9', // Main primary color
    600: '#0284c7',
    700: '#0369a1',
    800: '#075985',
    900: '#0c4a6e',
    950: '#082f49',
  },

  // Secondary colors
  secondary: {
    50: '#f8fafc',
    100: '#f1f5f9',
    200: '#e2e8f0',
    300: '#cbd5e1',
    400: '#94a3b8',
    500: '#64748b',
    600: '#475569',
    700: '#334155',
    800: '#1e293b',
    900: '#0f172a',
    950: '#020617',
  },

  // Success colors
  success: {
    50: '#f0fdf4',
    100: '#dcfce7',
    200: '#bbf7d0',
    300: '#86efac',
    400: '#4ade80',
    500: '#22c55e', // Main success color
    600: '#16a34a',
    700: '#15803d',
    800: '#166534',
    900: '#14532d',
    950: '#052e16',
  },

  // Warning colors
  warning: {
    50: '#fffbeb',
    100: '#fef3c7',
    200: '#fde68a',
    300: '#fcd34d',
    400: '#fbbf24',
    500: '#f59e0b', // Main warning color
    600: '#d97706',
    700: '#b45309',
    800: '#92400e',
    900: '#78350f',
    950: '#451a03',
  },

  // Error/Destructive colors
  error: {
    50: '#fef2f2',
    100: '#fee2e2',
    200: '#fecaca',
    300: '#fca5a5',
    400: '#f87171',
    500: '#ef4444', // Main error color
    600: '#dc2626',
    700: '#b91c1c',
    800: '#991b1b',
    900: '#7f1d1d',
    950: '#450a0a',
  },

  // Info colors
  info: {
    50: '#f0f9ff',
    100: '#e0f2fe',
    200: '#bae6fd',
    300: '#7dd3fc',
    400: '#38bdf8',
    500: '#0ea5e9', // Same as primary for consistency
    600: '#0284c7',
    700: '#0369a1',
    800: '#075985',
    900: '#0c4a6e',
    950: '#082f49',
  },

  // Neutral/Gray colors
  gray: {
    50: '#f9fafb',
    100: '#f3f4f6',
    200: '#e5e7eb',
    300: '#d1d5db',
    400: '#9ca3af',
    500: '#6b7280',
    600: '#4b5563',
    700: '#374151',
    800: '#1f2937',
    900: '#111827',
    950: '#030712',
  },

  // Background colors
  background: {
    light: '#ffffff',
    dark: '#0f172a',
    muted: {
      light: '#f8fafc',
      dark: '#1e293b',
    },
  },

  // Text colors
  text: {
    primary: {
      light: '#111827',
      dark: '#f9fafb',
    },
    secondary: {
      light: '#6b7280',
      dark: '#9ca3af',
    },
    muted: {
      light: '#9ca3af',
      dark: '#6b7280',
    },
  },

  // Border colors
  border: {
    light: '#e5e7eb',
    dark: '#374151',
    muted: {
      light: '#f3f4f6',
      dark: '#1f2937',
    },
  },

  // Card colors
  card: {
    light: '#ffffff',
    dark: '#1e293b',
    hover: {
      light: '#f9fafb',
      dark: '#334155',
    },
  },

  // Input colors
  input: {
    background: {
      light: '#ffffff',
      dark: '#1e293b',
    },
    border: {
      light: '#d1d5db',
      dark: '#475569',
    },
    focus: {
      light: '#0ea5e9',
      dark: '#38bdf8',
    },
  },
} as const;

/**
 * Semantic color mappings for easier usage
 */
export const SEMANTIC_COLORS = {
  // Status colors
  status: {
    success: COLORS.success[500],
    warning: COLORS.warning[500],
    error: COLORS.error[500],
    info: COLORS.info[500],
  },

  // Job-specific colors
  job: {
    emergency: COLORS.error[500],
    featured: COLORS.warning[500],
    regular: COLORS.primary[500],
  },

  // Trust score colors
  trustScore: {
    excellent: COLORS.success[500], // 80-100
    good: COLORS.success[400],      // 60-79
    fair: COLORS.warning[500],      // 40-59
    poor: COLORS.error[500],        // 0-39
  },

  // Application status colors
  application: {
    pending: COLORS.warning[500],
    accepted: COLORS.success[500],
    rejected: COLORS.error[500],
    completed: COLORS.primary[500],
  },

  // Payment status colors
  payment: {
    pending: COLORS.warning[500],
    processing: COLORS.info[500],
    completed: COLORS.success[500],
    failed: COLORS.error[500],
  },
} as const;

/**
 * CSS Custom Properties (CSS Variables) for web platforms
 */
export const CSS_VARIABLES = {
  // Primary colors
  '--color-primary-50': COLORS.primary[50],
  '--color-primary-100': COLORS.primary[100],
  '--color-primary-200': COLORS.primary[200],
  '--color-primary-300': COLORS.primary[300],
  '--color-primary-400': COLORS.primary[400],
  '--color-primary-500': COLORS.primary[500],
  '--color-primary-600': COLORS.primary[600],
  '--color-primary-700': COLORS.primary[700],
  '--color-primary-800': COLORS.primary[800],
  '--color-primary-900': COLORS.primary[900],
  '--color-primary-950': COLORS.primary[950],

  // Success colors
  '--color-success-50': COLORS.success[50],
  '--color-success-500': COLORS.success[500],
  '--color-success-600': COLORS.success[600],

  // Warning colors
  '--color-warning-50': COLORS.warning[50],
  '--color-warning-500': COLORS.warning[500],
  '--color-warning-600': COLORS.warning[600],

  // Error colors
  '--color-error-50': COLORS.error[50],
  '--color-error-500': COLORS.error[500],
  '--color-error-600': COLORS.error[600],

  // Gray colors
  '--color-gray-50': COLORS.gray[50],
  '--color-gray-100': COLORS.gray[100],
  '--color-gray-200': COLORS.gray[200],
  '--color-gray-300': COLORS.gray[300],
  '--color-gray-400': COLORS.gray[400],
  '--color-gray-500': COLORS.gray[500],
  '--color-gray-600': COLORS.gray[600],
  '--color-gray-700': COLORS.gray[700],
  '--color-gray-800': COLORS.gray[800],
  '--color-gray-900': COLORS.gray[900],
  '--color-gray-950': COLORS.gray[950],
} as const;

/**
 * Tailwind CSS class mappings for consistent usage
 */
export const TAILWIND_CLASSES = {
  // Background colors
  bg: {
    primary: 'bg-primary-500',
    'primary-light': 'bg-primary-50',
    'primary-dark': 'bg-primary-900',
    success: 'bg-success-500',
    warning: 'bg-warning-500',
    error: 'bg-error-500',
    muted: 'bg-gray-100 dark:bg-gray-800',
    card: 'bg-white dark:bg-gray-800',
  },

  // Text colors
  text: {
    primary: 'text-gray-900 dark:text-gray-100',
    secondary: 'text-gray-600 dark:text-gray-400',
    muted: 'text-gray-500 dark:text-gray-500',
    success: 'text-success-600',
    warning: 'text-warning-600',
    error: 'text-error-600',
  },

  // Border colors
  border: {
    default: 'border-gray-200 dark:border-gray-700',
    muted: 'border-gray-100 dark:border-gray-800',
    primary: 'border-primary-500',
    success: 'border-success-500',
    warning: 'border-warning-500',
    error: 'border-error-500',
  },
} as const;
