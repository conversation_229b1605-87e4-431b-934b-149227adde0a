export enum JobStatus {
  OPEN = "open",
  IN_PROGRESS = "in-progress",
  COMPLETED = "completed",
  CANCELLED = "cancelled",
}

export interface JobLocation {
  address: string;
  city: string;
  state: string;
  country: string;
  postalCode: string;
  coordinates: {
    latitude: number;
    longitude: number;
  };
}

export interface JobSalary {
  min: number;
  max: number;
  currency: string;
}

export interface Job {
  id: string;
  title: string;
  description: string;
  companyId: string;
  company?: import("./company").Company;
  userId?: string;
  location: string;
  city?: string;
  state?: string;
  country?: string;
  postalCode?: string;
  latitude?: number;
  longitude?: number;
  startDateTime: string;
  endDateTime: string;
  paymentAmount: number;
  duration: number;
  trustScoreRequired: number;
  requiredWorkers: number;
  hiredWorkers: number;
  requiresLaptop: boolean;
  requiresSmartphone: boolean;
  skillsRequired?: string;
  requirements?: string[];
  responsibilities?: string[];
  benefits?: string[];
  isEmergencyJob: boolean;
  isFeatured?: boolean;
  status: JobStatus;
  createdAt: string;
  updatedAt: string;
  distance?: number;
  applicationsCount?: number;
}
