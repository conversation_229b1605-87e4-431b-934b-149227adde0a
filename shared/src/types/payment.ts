export enum PaymentGateway {
  STRIPE = "stripe",
  RAZORPAY = "razorpay",
}

export enum PaymentMethod {
  STRIPE = "stripe",
  RAZORPAY = "razorpay",
  UPI = "upi",
  BANK_TRANSFER = "bank_transfer",
  WALLET = "wallet",
  CARD = "card",
}

export enum PaymentStatus {
  PENDING = "pending",
  PROCESSING = "processing",
  COMPLETED = "completed",
  FAILED = "failed",
  CANCELLED = "cancelled",
  REFUNDED = "refunded",
  DISPUTED = "disputed",
}

export enum PaymentType {
  ESCROW_FUNDING = "escrow_funding",
  WORKER_PAYOUT = "worker_payout",
  REFUND = "refund",
  DISPUTE_RESOLUTION = "dispute_resolution",
}

export interface PaymentMetadata {
  jobId?: string;
  applicationId?: string;
  workerId?: string;
  companyId?: string;
  escrowId?: string;
  payoutId?: string;
  disputeId?: string;
  description?: string;
  type?: PaymentType;
  [key: string]: unknown;
}

// Base payment parameters interface
export interface BasePaymentParams {
  amount: number;
  currency: string;
  description?: string;
  metadata?: PaymentMetadata;
}

// Stripe-specific payment parameters
export interface StripePaymentParams extends BasePaymentParams {
  paymentMethodId?: string;
  customerId?: string;
  confirmationMethod?: "automatic" | "manual";
  captureMethod?: "automatic" | "manual";
  setupFutureUsage?: "on_session" | "off_session";
}

// Razorpay-specific payment parameters
export interface RazorpayPaymentParams extends BasePaymentParams {
  customerEmail?: string;
  customerPhone?: string;
  customerName?: string;
  paymentId?: string;
  orderId?: string;
  receipt?: string;
  notes?: Record<string, string>;
}

// UPI payment parameters (can work with both gateways)
export interface UpiPaymentParams extends BasePaymentParams {
  vpa: string;
  reference?: string;
}

// Generic payment result interface
export interface PaymentResult {
  success: boolean;
  transactionId?: string;
  orderId?: string;
  status: PaymentStatus;
  message?: string;
  gateway: PaymentGateway;
  paymentMethod: PaymentMethod;
  gatewayResponse?: Record<string, unknown>;
  clientSecret?: string; // For Stripe
  redirectUrl?: string; // For 3D Secure or other redirects
}

// Payment gateway configuration
export interface PaymentGatewayConfig {
  gateway: PaymentGateway;
  enabled: boolean;
  supportedCurrencies: string[];
  supportedMethods: PaymentMethod[];
  defaultCurrency: string;
  webhookUrl?: string;
}

// Refund parameters
export interface RefundParams {
  transactionId: string;
  amount?: number; // Partial refund if specified
  reason?: string;
  metadata?: PaymentMetadata;
}

// Refund result
export interface RefundResult {
  success: boolean;
  refundId: string;
  amount: number;
  status: "pending" | "succeeded" | "failed";
  gateway: PaymentGateway;
  gatewayResponse?: Record<string, unknown>;
}

// Webhook event types
export enum WebhookEventType {
  PAYMENT_SUCCEEDED = "payment.succeeded",
  PAYMENT_FAILED = "payment.failed",
  PAYMENT_CAPTURED = "payment.captured",
  PAYMENT_REFUNDED = "payment.refunded",
  ORDER_PAID = "order.paid",
  REFUND_CREATED = "refund.created",
  REFUND_PROCESSED = "refund.processed",
  DISPUTE_CREATED = "dispute.created",
  DISPUTE_UPDATED = "dispute.updated",
}

// Generic webhook payload
export interface WebhookPayload {
  id: string;
  type: WebhookEventType;
  gateway: PaymentGateway;
  data: Record<string, unknown>;
  created: number;
  livemode: boolean;
}

// Gateway-specific response types
export interface RazorpayOrderResponse {
  id: string;
  entity: "order";
  amount: number;
  amount_paid: number;
  amount_due: number;
  currency: string;
  receipt: string;
  status: "created" | "attempted" | "paid";
  attempts: number;
  notes: Record<string, string>;
  created_at: number;
}

export interface RazorpayPaymentResponse {
  id: string;
  entity: "payment";
  amount: number;
  currency: string;
  status: "created" | "authorized" | "captured" | "refunded" | "failed";
  order_id: string;
  method: "card" | "netbanking" | "wallet" | "emi" | "upi";
  amount_refunded: number;
  captured: boolean;
  description?: string;
  email: string;
  contact: string;
  notes: Record<string, string>;
  created_at: number;
}

export interface StripePaymentIntentResponse {
  id: string;
  object: "payment_intent";
  amount: number;
  currency: string;
  status:
    | "requires_payment_method"
    | "requires_confirmation"
    | "requires_action"
    | "processing"
    | "requires_capture"
    | "canceled"
    | "succeeded";
  client_secret: string;
  payment_method?: string;
  customer?: string;
  description?: string;
  metadata: Record<string, string>;
  created: number;
}

export interface StripeRefundResponse {
  id: string;
  object: "refund";
  amount: number;
  currency: string;
  payment_intent: string;
  status: "pending" | "succeeded" | "failed" | "canceled";
  reason?: "duplicate" | "fraudulent" | "requested_by_customer";
  metadata: Record<string, string>;
  created: number;
}

// Webhook payload interfaces
export interface RazorpayWebhookPayload {
  entity: string;
  account_id: string;
  event: string;
  contains: string[];
  payload: {
    payment?: {
      entity: RazorpayPaymentResponse;
    };
    order?: {
      entity: RazorpayOrderResponse;
    };
    refund?: {
      entity: {
        id: string;
        entity: "refund";
        amount: number;
        currency: string;
        payment_id: string;
        notes: Record<string, string>;
        created_at: number;
        status: "pending" | "processed" | "failed";
      };
    };
  };
  created_at: number;
}

export interface StripeWebhookPayload {
  id: string;
  object: "event";
  type: string;
  data: {
    object:
      | StripePaymentIntentResponse
      | StripeRefundResponse
      | Record<string, unknown>;
  };
  created: number;
  livemode: boolean;
  pending_webhooks: number;
  request?: {
    id: string;
    idempotency_key?: string;
  };
}

// Escrow types
export enum EscrowStatus {
  PENDING = "pending",
  HELD = "held",
  RELEASED = "released",
  REFUNDED = "refunded",
  DISPUTED = "disputed",
}

export interface EscrowAccount {
  id: string;
  jobId: string;
  companyId: string;
  amount: number;
  currency: string;
  status: EscrowStatus;
  gateway: PaymentGateway;
  transactionId?: string;
  paymentIntentId?: string;
  createdAt: string;
  updatedAt: string;
  releasedAt?: string;
  refundedAt?: string;
  workerId?: string;
  metadata?: PaymentMetadata;
}

// Payment intent for tracking
export interface PaymentIntent {
  id: string;
  amount: number;
  currency: string;
  status: PaymentStatus;
  gateway: PaymentGateway;
  paymentMethod: PaymentMethod;
  metadata?: PaymentMetadata;
  createdAt: string;
  updatedAt: string;
}
