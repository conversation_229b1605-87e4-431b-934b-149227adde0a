import { Job } from "./job";
import { User } from "./user";

export enum ApplicationStatus {
  PENDING = "pending",
  SHORTLISTED = "shortlisted",
  ACCEPTED = "accepted",
  REJECTED = "rejected",
  HIRED = "hired",
  WITHDRAWN = "withdrawn",
  CANCELLED = "cancelled",
  COMPLETED = "completed",
  NO_SHOW = "no_show",
}

export interface Application {
  id: string;
  jobId: string;
  job?: Job;
  workerId: string;
  worker?: User;
  status: ApplicationStatus;
  coverLetter?: string;
  expectedSalary?: number;
  availability?: string;
  attachments?: string[];
  cancellationReason?: string;
  rejectionReason?: string;
  isEmergencyJob?: boolean;
  createdAt: string;
  updatedAt: string;
}
