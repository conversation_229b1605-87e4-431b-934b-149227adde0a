// Enums for the new generic chat system
export enum ChatType {
  WORKER_COMPANY = "worker_company",
  SUPPORT = "support",
  GROUP = "group",
  ADMIN_USER = "admin_user",
  GENERAL = "general",
}

export enum ParticipantRole {
  WORKER = "worker",
  COMPANY = "company",
  ADMIN = "admin",
  SUPPORT = "support",
  MODERATOR = "moderator",
  MEMBER = "member",
}

export enum MessageType {
  TEXT = "text",
  IMAGE = "image",
  FILE = "file",
  SYSTEM = "system",
  NOTIFICATION = "notification",
}

export enum MessageStatus {
  SENT = "sent",
  DELIVERED = "delivered",
  READ = "read",
  FAILED = "failed",
}

// Generic chat participant interface
export interface ChatParticipant {
  id: string;
  chatId: string;
  userId: string;
  user?: import("./user").User;
  role: ParticipantRole;
  unreadCount: number;
  isActive: boolean;
  joinedAt: string;
  leftAt?: string;
  lastReadAt?: string;
  createdAt: string;
  updatedAt: string;
}

// New generic chat interface
export interface GenericChat {
  id: string;
  type: ChatType;
  title?: string;
  description?: string;
  contextId?: string;
  contextType?: string;
  metadata?: Record<string, any>;
  isActive: boolean;
  createdBy: string;
  lastMessageAt: string;
  participants: ChatParticipant[];
  messages?: ChatMessage[];
  createdAt: string;
  updatedAt: string;

  // Computed properties for backward compatibility
  workerId?: string;
  companyId?: string;
  worker?: import("./user").User;
  company?: import("./company").Company;
  jobId?: string;
  job?: import("./job").Job;
  unreadWorkerCount?: number;
  unreadCompanyCount?: number;
}

// Legacy chat interface (for backward compatibility)
export interface Chat {
  id: string;
  workerId: string;
  worker?: import("./user").User;
  companyId: string;
  company?: import("./company").Company;
  companyUserId?: string;
  jobId?: string;
  job?: import("./job").Job;
  createdAt: string;
  updatedAt: string;
}

// Enhanced chat message interface
export interface ChatMessage {
  id: string;
  chatId: string;
  genericChatId?: string;
  messageType: MessageType;
  message: string;
  fileUrl?: string;
  fileName?: string;
  fileSize?: number;
  fileMimeType?: string;
  senderId: string;
  sender?: import("./user").User;
  senderType?: "worker" | "company"; // For backward compatibility
  status: MessageStatus;
  isRead: boolean;
  readAt?: string;
  metadata?: Record<string, any>;
  replyToId?: string;
  replyTo?: ChatMessage;
  isEdited: boolean;
  editedAt?: string;
  createdAt: string;
  updatedAt: string;
}
