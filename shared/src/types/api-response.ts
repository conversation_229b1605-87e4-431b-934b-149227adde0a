/**
 * Base API response format
 * All API responses follow this structure
 */
export interface ApiResponse<T> {
  success: boolean;
  data: T;
  message?: string;
  error?: string;
  meta?: Record<string, unknown>;
}

/**
 * Pagination metadata
 */
export interface PaginationMeta {
  total: number;
  page: number;
  limit: number;
  totalPages: number;
  hasNextPage: boolean;
  hasPreviousPage: boolean;
}

/**
 * Paginated API response format
 */
export interface PaginatedResponse<T> extends Omit<ApiResponse<T[]>, "meta"> {
  meta: PaginationMeta;
}

/**
 * Error response format
 */
export interface ApiErrorResponse {
  success: false;
  error: string;
  message: string;
  path?: string;
  timestamp?: string;
  statusCode?: number;
}

/**
 * Common query parameters for paginated endpoints
 */
export interface PaginationParams {
  page?: number;
  limit?: number;
  sortBy?: string;
  sortOrder?: "ASC" | "DESC" | "asc" | "desc";
}
