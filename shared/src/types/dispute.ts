export enum DisputeStatus {
  OPEN = "open",
  UNDER_REVIEW = "under-review",
  RESOLVED = "resolved",
  REJECTED = "rejected",
  CLOSED = "closed",
}

export interface Dispute {
  id: string;
  jobId: string;
  job?: import("./job").Job;
  raisedById: string;
  raisedBy?: import("./user").User;
  againstId: string;
  againstUser?: import("./user").User;
  title: string;
  description: string;
  status: DisputeStatus;
  resolution?: string;
  resolvedById?: string;
  resolvedBy?: import("./user").User;
  resolvedAt?: string;
  createdAt: string;
  updatedAt: string;
}
