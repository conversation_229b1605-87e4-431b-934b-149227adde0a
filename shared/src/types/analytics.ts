export interface DashboardStats {
  totalUsers: number;
  totalJobs: number;
  totalApplications: number;
  totalRevenue: number;
  userGrowth: number;
  jobGrowth: number;
  applicationGrowth: number;
  revenueGrowth: number;
  chartData?: ChartDataPoint[];
  recentActivities?: ActivitySummary[];
}

export interface ChartDataPoint {
  date: string;
  users: number;
  jobs: number;
  applications: number;
  revenue: number;
}

export interface ActivitySummary {
  id: string;
  type: 'user_registration' | 'job_posted' | 'application_submitted' | 'payment_completed';
  description: string;
  timestamp: string;
  metadata?: Record<string, unknown>;
}

export interface UserAnalytics {
  totalWorkers: number;
  totalCompanies: number;
  activeWorkers: number;
  activeCompanies: number;
  averageTrustScore: number;
  kycVerificationRate: number;
  topPerformingWorkers: WorkerPerformance[];
  userGrowthTrend: GrowthTrendPoint[];
}

export interface WorkerPerformance {
  userId: string;
  fullName: string;
  completedJobs: number;
  totalEarnings: number;
  averageRating: number;
  trustScore: number;
}

export interface JobAnalytics {
  totalJobsPosted: number;
  completedJobs: number;
  cancelledJobs: number;
  averageJobValue: number;
  averageCompletionTime: number;
  jobCompletionRate: number;
  emergencyJobsPercentage: number;
  jobsByCategory: CategoryStats[];
  jobsByLocation: LocationStats[];
}

export interface CategoryStats {
  category: string;
  count: number;
  averageValue: number;
  completionRate: number;
}

export interface LocationStats {
  city: string;
  state: string;
  jobCount: number;
  averageValue: number;
}

export interface RevenueAnalytics {
  totalRevenue: number;
  totalCommissions: number;
  averageJobValue: number;
  revenueGrowth: number;
  monthlyRevenue: MonthlyRevenue[];
  revenueByPaymentMethod: PaymentMethodRevenue[];
}

export interface MonthlyRevenue {
  month: string;
  year: number;
  revenue: number;
  commissions: number;
  jobCount: number;
}

export interface PaymentMethodRevenue {
  method: string;
  revenue: number;
  transactionCount: number;
  averageValue: number;
}

export interface GrowthTrendPoint {
  date: string;
  value: number;
  change: number;
}

export interface CompanyAnalytics {
  totalJobsPosted: number;
  completedJobs: number;
  totalSpent: number;
  averageJobValue: number;
  workerRatings: number;
  repeatWorkerRate: number;
  jobCompletionRate: number;
  averageTimeToHire: number;
}

export interface PlatformMetrics {
  dailyActiveUsers: number;
  monthlyActiveUsers: number;
  jobPostingRate: number;
  applicationRate: number;
  completionRate: number;
  disputeRate: number;
  averageResponseTime: number;
  platformHealth: 'excellent' | 'good' | 'fair' | 'poor';
}
