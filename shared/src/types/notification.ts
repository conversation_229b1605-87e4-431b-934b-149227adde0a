export enum NotificationType {
  ALERT = "alert",
  INFO = "info",
  JOB = "job",
  APPLICATION = "application",
  PAYOUT = "payout",
  DISPUTE = "dispute",
  DOCUMENT = "document",
  CHAT = "chat",
}

export interface NotificationMetadata {
  jobId?: string;
  applicationId?: string;
  payoutId?: string;
  disputeId?: string;
  documentId?: string;
  chatId?: string;
  messageId?: string;
  amount?: number;
  currency?: string;
  [key: string]: unknown;
}

export interface Notification {
  id: string;
  userId: string;
  title: string;
  message: string;
  type: NotificationType;
  isRead: boolean;
  link?: string;
  metadata?: NotificationMetadata;
  createdAt: string;
}
