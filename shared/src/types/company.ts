export interface Company {
  id: string;
  name: string;
  registrationNumber?: string;
  taxId?: string;
  website?: string;
  size?: string;
  industry?: string;
  description?: string;
  logo?: string;
  address?: string;
  city?: string;
  state?: string;
  country?: string;
  postalCode?: string;
  isKycVerified: boolean;
  isActive: boolean;
  isBanned: boolean;
  userId: string;
  user?: import("./user").User;
  createdAt: string;
  updatedAt: string;
}

export interface CompanyProfile extends Company {
  postedJobs?: number;
  completedJobs?: number;
  averageRating?: number;
  reviewsCount?: number;
}
