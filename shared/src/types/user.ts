export enum UserRole {
  WORKER = "worker",
  COMPANY = "company",
  ADMIN = "admin",
  SUPER_ADMIN = "super_admin",
}

export interface User {
  id: string;
  email?: string;
  phone?: string;
  name: string;
  role: UserRole;
  trustScore: number;
  profilePic?: string;
  address?: string;
  city?: string;
  state?: string;
  country?: string;
  postalCode?: string;
  bio?: string;
  isVerified: boolean;
  isKycVerified: boolean;
  isActive: boolean;
  isBanned: boolean;
  companyName?: string;
  companyRegistrationNumber?: string;
  companyTaxId?: string;
  companyWebsite?: string;
  companySize?: string;
  companyIndustry?: string;
  createdAt: string;
  updatedAt: string;
}

export interface WorkerProfile extends User {
  role: UserRole.WORKER;
  badges?: import("./badge").Badge[];
  completedJobs?: number;
  totalEarnings?: number;
  averageRating?: number;
}

export interface CompanyUserProfile extends User {
  role: UserRole.COMPANY;
  postedJobs?: number;
  completedJobs?: number;
  averageRating?: number;
}

export interface AdminProfile extends User {
  role: UserRole.ADMIN;
}
