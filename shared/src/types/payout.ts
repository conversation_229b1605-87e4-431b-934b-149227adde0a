export enum PayoutStatus {
  PENDING = "pending",
  PROCESSING = "processing",
  COMPLETED = "completed",
  FAILED = "failed",
}

export interface Payout {
  id: string;
  jobId: string;
  job?: import("./job").Job;
  workerId: string;
  worker?: import("./user").User;
  grossPay: number;
  commission: number;
  netPay: number;
  status: PayoutStatus;
  paymentMethod?: string;
  transactionId?: string;
  paymentDate?: string;
  createdAt: string;
  updatedAt: string;
}
