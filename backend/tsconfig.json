{"compilerOptions": {"module": "commonjs", "declaration": true, "removeComments": true, "emitDecoratorMetadata": true, "experimentalDecorators": true, "allowSyntheticDefaultImports": true, "target": "ES2021", "sourceMap": true, "outDir": "./dist", "baseUrl": "./", "incremental": true, "skipLibCheck": true, "strict": true, "strictNullChecks": true, "noImplicitAny": true, "strictBindCallApply": true, "strictFunctionTypes": true, "strictPropertyInitialization": true, "noImplicitReturns": true, "noImplicitThis": true, "noUnusedLocals": true, "noUnusedParameters": true, "exactOptionalPropertyTypes": true, "forceConsistentCasingInFileNames": true, "noFallthroughCasesInSwitch": true, "paths": {"@shared/*": ["../shared/src/*"]}}, "include": ["src/**/*"]}