import { Test, TestingModule } from '@nestjs/testing';
import { INestApplication, ValidationPipe } from '@nestjs/common';
import * as request from 'supertest';
import { AppModule } from '../src/app.module';
import { getRepositoryToken } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { User } from '../src/users/entities/user.entity';
import { Company } from '../src/companies/entities/company.entity';
import { UserRole } from '../src/common/enums/user-role.enum';
import { JwtService } from '@nestjs/jwt';

describe('CompaniesController (e2e)', () => {
  let app: INestApplication;
  let userRepository: Repository<User>;
  let companyRepository: Repository<Company>;
  let jwtService: JwtService;
  
  let adminToken: string;
  let companyToken: string;
  let workerToken: string;
  let adminUser: User;
  let companyUser: User;
  let workerUser: User;
  let testCompany: Company;

  beforeAll(async () => {
    const moduleFixture: TestingModule = await Test.createTestingModule({
      imports: [AppModule],
    }).compile();

    app = moduleFixture.createNestApplication();
    app.useGlobalPipes(new ValidationPipe());
    await app.init();

    userRepository = moduleFixture.get<Repository<User>>(getRepositoryToken(User));
    companyRepository = moduleFixture.get<Repository<Company>>(getRepositoryToken(Company));
    jwtService = moduleFixture.get<JwtService>(JwtService);

    // Create test users
    adminUser = await userRepository.save({
      email: '<EMAIL>',
      fullName: 'Admin User',
      password: 'hashedPassword',
      role: UserRole.ADMIN,
    });

    companyUser = await userRepository.save({
      email: '<EMAIL>',
      fullName: 'Company User',
      password: 'hashedPassword',
      role: UserRole.COMPANY,
    });

    workerUser = await userRepository.save({
      email: '<EMAIL>',
      fullName: 'Worker User',
      password: 'hashedPassword',
      role: UserRole.WORKER,
    });

    // Create test company
    testCompany = await companyRepository.save({
      name: 'Test Company',
      industry: 'Technology',
      userId: companyUser.id,
    });

    // Generate JWT tokens
    adminToken = jwtService.sign({ 
      sub: adminUser.id,
      email: adminUser.email,
      role: adminUser.role,
    });

    companyToken = jwtService.sign({ 
      sub: companyUser.id,
      email: companyUser.email,
      role: companyUser.role,
    });

    workerToken = jwtService.sign({ 
      sub: workerUser.id,
      email: workerUser.email,
      role: workerUser.role,
    });
  });

  afterAll(async () => {
    // Clean up
    await companyRepository.delete({});
    await userRepository.delete({});
    await app.close();
  });

  describe('/companies (GET)', () => {
    it('should return 403 for non-admin users', () => {
      return request(app.getHttpServer())
        .get('/companies')
        .set('Authorization', `Bearer ${companyToken}`)
        .expect(403);
    });

    it('should return companies for admin users', () => {
      return request(app.getHttpServer())
        .get('/companies')
        .set('Authorization', `Bearer ${adminToken}`)
        .expect(200)
        .expect((res) => {
          expect(res.body.data).toBeDefined();
          expect(res.body.meta).toBeDefined();
          expect(Array.isArray(res.body.data)).toBe(true);
        });
    });
  });

  describe('/companies/profile (GET)', () => {
    it('should return 403 for non-company users', () => {
      return request(app.getHttpServer())
        .get('/companies/profile')
        .set('Authorization', `Bearer ${workerToken}`)
        .expect(403);
    });

    it('should return company profile for company users', () => {
      return request(app.getHttpServer())
        .get('/companies/profile')
        .set('Authorization', `Bearer ${companyToken}`)
        .expect(200)
        .expect((res) => {
          expect(res.body.id).toBeDefined();
          expect(res.body.name).toBe('Test Company');
          expect(res.body.userId).toBe(companyUser.id);
        });
    });
  });

  describe('/companies/:id (GET)', () => {
    it('should return 401 for unauthenticated users', () => {
      return request(app.getHttpServer())
        .get(`/companies/${testCompany.id}`)
        .expect(401);
    });

    it('should return company by id for authenticated users', () => {
      return request(app.getHttpServer())
        .get(`/companies/${testCompany.id}`)
        .set('Authorization', `Bearer ${companyToken}`)
        .expect(200)
        .expect((res) => {
          expect(res.body.id).toBe(testCompany.id);
          expect(res.body.name).toBe(testCompany.name);
        });
    });
  });

  describe('/companies/:id (PATCH)', () => {
    it('should return 403 for non-company/non-admin users', () => {
      return request(app.getHttpServer())
        .patch(`/companies/${testCompany.id}`)
        .set('Authorization', `Bearer ${workerToken}`)
        .send({ name: 'Updated Company' })
        .expect(403);
    });

    it('should update company for company owner', () => {
      return request(app.getHttpServer())
        .patch(`/companies/${testCompany.id}`)
        .set('Authorization', `Bearer ${companyToken}`)
        .send({ name: 'Updated Company Name' })
        .expect(200)
        .expect((res) => {
          expect(res.body.name).toBe('Updated Company Name');
        });
    });
  });

  describe('/companies/kyc (PATCH)', () => {
    it('should return 403 for non-company users', () => {
      return request(app.getHttpServer())
        .patch('/companies/kyc')
        .set('Authorization', `Bearer ${workerToken}`)
        .send({
          registrationNumber: 'REG123',
          taxId: 'TAX123',
        })
        .expect(403);
    });

    it('should update KYC information for company users', () => {
      return request(app.getHttpServer())
        .patch('/companies/kyc')
        .set('Authorization', `Bearer ${companyToken}`)
        .send({
          registrationNumber: 'REG123',
          taxId: 'TAX123',
        })
        .expect(200)
        .expect((res) => {
          expect(res.body.registrationNumber).toBe('REG123');
          expect(res.body.taxId).toBe('TAX123');
        });
    });
  });
});
