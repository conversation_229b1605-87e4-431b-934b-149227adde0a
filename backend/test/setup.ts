import { ConfigService } from '@nestjs/config';

// Mock environment variables for testing
process.env.NODE_ENV = 'test';
process.env.DATABASE_URL = 'postgresql://test:test@localhost:5432/test_db';
process.env.JWT_SECRET = 'test_jwt_secret';
process.env.JWT_EXPIRES_IN = '1h';

// Mock ConfigService for tests
jest.mock('@nestjs/config', () => ({
  ConfigService: jest.fn().mockImplementation(() => ({
    get: jest.fn((key: string, defaultValue?: any) => {
      const config: Record<string, any> = {
        NODE_ENV: 'test',
        DATABASE_URL: 'postgresql://test:test@localhost:5432/test_db',
        JWT_SECRET: 'test_jwt_secret',
        JWT_EXPIRES_IN: '1h',
        PORT: 3000,
        CORS_ORIGIN: 'http://localhost:3000',
        THROTTLE_TTL: 60,
        THROTTLE_LIMIT: 100,
      };
      return config[key] ?? defaultValue;
    }),
  })),
}));

// Mock external services
jest.mock('@sentry/node', () => ({
  init: jest.fn(),
  captureException: jest.fn(),
  captureMessage: jest.fn(),
  setUser: jest.fn(),
  addBreadcrumb: jest.fn(),
  startTransaction: jest.fn(),
  withScope: jest.fn((callback) => callback({ setTag: jest.fn() })),
}));

// Mock TypeORM
jest.mock('typeorm', () => ({
  Entity: () => (target: any) => target,
  PrimaryGeneratedColumn: () => (target: any, propertyKey: string) => {},
  Column: () => (target: any, propertyKey: string) => {},
  CreateDateColumn: () => (target: any, propertyKey: string) => {},
  UpdateDateColumn: () => (target: any, propertyKey: string) => {},
  ManyToOne: () => (target: any, propertyKey: string) => {},
  OneToMany: () => (target: any, propertyKey: string) => {},
  JoinColumn: () => (target: any, propertyKey: string) => {},
  Repository: class MockRepository {
    find = jest.fn();
    findOne = jest.fn();
    findOneBy = jest.fn();
    save = jest.fn();
    update = jest.fn();
    delete = jest.fn();
    create = jest.fn();
    remove = jest.fn();
    count = jest.fn();
    createQueryBuilder = jest.fn(() => ({
      where: jest.fn().mockReturnThis(),
      andWhere: jest.fn().mockReturnThis(),
      orderBy: jest.fn().mockReturnThis(),
      skip: jest.fn().mockReturnThis(),
      take: jest.fn().mockReturnThis(),
      getMany: jest.fn(),
      getOne: jest.fn(),
      getManyAndCount: jest.fn(),
    }));
  },
  DataSource: jest.fn(),
}));

// Global test timeout
jest.setTimeout(30000);

// Suppress console logs during tests unless explicitly needed
const originalConsoleLog = console.log;
const originalConsoleError = console.error;
const originalConsoleWarn = console.warn;

beforeAll(() => {
  console.log = jest.fn();
  console.error = jest.fn();
  console.warn = jest.fn();
});

afterAll(() => {
  console.log = originalConsoleLog;
  console.error = originalConsoleError;
  console.warn = originalConsoleWarn;
});

// Clean up after each test
afterEach(() => {
  jest.clearAllMocks();
});
