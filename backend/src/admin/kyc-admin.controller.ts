import {
  Controller,
  Get,
  Post,
  Patch,
  Body,
  Param,
  Query,
  UseGuards,
  Request,
  ParseIntPipe,
  ParseEnumPipe,
  ParseUUIDPipe,
} from "@nestjs/common";
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiBearerAuth,
  ApiParam,
  ApiQuery,
  ApiBody,
} from "@nestjs/swagger";
import { KycAdminService, BulkVerificationDto } from "./kyc-admin.service";
import { JwtAuthGuard } from "../auth/guards/jwt-auth.guard";
import { RolesGuard } from "../auth/guards/roles.guard";
import { Roles } from "../auth/decorators/roles.decorator";
import { UserRole } from "@shared/types/user";
import { VerificationStatus } from "../common/enums/verification-status.enum";
import { DocumentType } from "../common/enums/document-type.enum";
import { RequestWithUser } from "../auth/interfaces/request.interface";

@ApiTags("admin/kyc")
@Controller("admin/kyc")
@UseGuards(JwtAuthGuard, RolesGuard)
@Roles(UserRole.ADMIN)
@ApiBearerAuth()
export class KycAdminController {
  constructor(private readonly kycAdminService: KycAdminService) {}

  @Get("dashboard")
  @ApiOperation({
    summary: "Get KYC dashboard statistics",
    description: "Get comprehensive statistics for KYC verification dashboard",
  })
  @ApiResponse({
    status: 200,
    description: "KYC dashboard statistics",
    schema: {
      type: "object",
      properties: {
        totalPendingDocuments: { type: "number", example: 45 },
        totalVerifiedDocuments: { type: "number", example: 1250 },
        totalRejectedDocuments: { type: "number", example: 23 },
        pendingWorkerKyc: { type: "number", example: 32 },
        pendingCompanyKyc: { type: "number", example: 8 },
        verifiedWorkers: { type: "number", example: 890 },
        verifiedCompanies: { type: "number", example: 156 },
        recentSubmissions: { type: "number", example: 12 },
        averageVerificationTime: { type: "number", example: 2.5 },
      },
    },
  })
  async getDashboardStats() {
    return await this.kycAdminService.getDashboardStats();
  }

  @Get("pending")
  @ApiOperation({
    summary: "Get pending KYC documents for review",
    description: "Get paginated list of documents pending verification",
  })
  @ApiQuery({ name: "page", required: false, type: Number, example: 1 })
  @ApiQuery({ name: "limit", required: false, type: Number, example: 20 })
  @ApiQuery({
    name: "priority",
    required: false,
    enum: ["high", "medium", "low"],
    description: "Filter by priority level",
  })
  @ApiQuery({
    name: "documentType",
    required: false,
    enum: DocumentType,
    description: "Filter by document type",
  })
  @ApiResponse({
    status: 200,
    description: "List of pending KYC documents",
    schema: {
      type: "object",
      properties: {
        items: {
          type: "array",
          items: {
            type: "object",
            properties: {
              id: { type: "string" },
              userId: { type: "string" },
              userType: { type: "string", enum: ["worker", "company"] },
              userName: { type: "string" },
              userEmail: { type: "string" },
              documentType: { type: "string" },
              documentUrl: { type: "string" },
              submittedAt: { type: "string", format: "date-time" },
              priority: { type: "string", enum: ["high", "medium", "low"] },
            },
          },
        },
        total: { type: "number" },
        page: { type: "number" },
        limit: { type: "number" },
      },
    },
  })
  async getPendingDocuments(
    @Query("page", new ParseIntPipe({ optional: true })) page: number = 1,
    @Query("limit", new ParseIntPipe({ optional: true })) limit: number = 20,
    @Query("priority") priority?: "high" | "medium" | "low",
    @Query("documentType", new ParseEnumPipe(DocumentType, { optional: true }))
    documentType?: DocumentType
  ) {
    return await this.kycAdminService.getPendingKycDocuments(
      page,
      limit,
      priority,
      documentType
    );
  }

  @Get("history")
  @ApiOperation({
    summary: "Get KYC verification history",
    description: "Get paginated history of verified/rejected documents",
  })
  @ApiQuery({ name: "page", required: false, type: Number, example: 1 })
  @ApiQuery({ name: "limit", required: false, type: Number, example: 20 })
  @ApiQuery({
    name: "status",
    required: false,
    enum: VerificationStatus,
    description: "Filter by verification status",
  })
  @ApiQuery({
    name: "startDate",
    required: false,
    type: String,
  })
  @ApiQuery({ name: "endDate", required: false, type: String })
  @ApiResponse({
    status: 200,
    description: "KYC verification history",
  })
  async getVerificationHistory(
    @Query("page", new ParseIntPipe({ optional: true })) page: number = 1,
    @Query("limit", new ParseIntPipe({ optional: true })) limit: number = 20,
    @Query("status", new ParseEnumPipe(VerificationStatus, { optional: true }))
    status?: VerificationStatus,
    @Query("startDate") startDate?: string,
    @Query("endDate") endDate?: string
  ) {
    const start = startDate ? new Date(startDate) : undefined;
    const end = endDate ? new Date(endDate) : undefined;

    return await this.kycAdminService.getVerificationHistory(
      page,
      limit,
      status,
      start,
      end
    );
  }

  @Get("document/:id")
  @ApiOperation({
    summary: "Get document details for review",
    description:
      "Get detailed information about a specific document for verification",
  })
  @ApiParam({ name: "id", description: "Document ID" })
  @ApiResponse({
    status: 200,
    description: "Document details",
  })
  @ApiResponse({
    status: 404,
    description: "Document not found",
  })
  async getDocumentForReview(@Param("id", ParseUUIDPipe) documentId: string) {
    return await this.kycAdminService.getDocumentForReview(documentId);
  }

  @Patch("document/:id/verify")
  @ApiOperation({
    summary: "Verify or reject a document",
    description: "Update the verification status of a document",
  })
  @ApiParam({ name: "id", description: "Document ID" })
  @ApiBody({
    schema: {
      type: "object",
      properties: {
        status: {
          type: "string",
          enum: Object.values(VerificationStatus),
          example: VerificationStatus.VERIFIED,
        },
        rejectionReason: {
          type: "string",
          example: "Document image is not clear",
        },
      },
      required: ["status"],
    },
  })
  @ApiResponse({
    status: 200,
    description: "Document verification status updated",
  })
  @ApiResponse({
    status: 400,
    description: "Invalid verification data",
  })
  @ApiResponse({
    status: 404,
    description: "Document not found",
  })
  async verifyDocument(
    @Request() req: RequestWithUser,
    @Param("id", ParseUUIDPipe) documentId: string,
    @Body() body: { status: VerificationStatus; rejectionReason?: string }
  ) {
    const { status, rejectionReason } = body;
    return await this.kycAdminService.verifyDocument(
      documentId,
      req.user.id,
      status,
      rejectionReason
    );
  }

  @Post("bulk-verify")
  @ApiOperation({
    summary: "Bulk verify multiple documents",
    description: "Verify or reject multiple documents at once",
  })
  @ApiBody({
    schema: {
      type: "object",
      properties: {
        documentIds: {
          type: "array",
          items: { type: "string" },
          example: ["doc1-uuid", "doc2-uuid", "doc3-uuid"],
        },
        status: {
          type: "string",
          enum: Object.values(VerificationStatus),
          example: VerificationStatus.VERIFIED,
        },
        rejectionReason: {
          type: "string",
          example: "Bulk rejection reason",
        },
      },
      required: ["documentIds", "status"],
    },
  })
  @ApiResponse({
    status: 200,
    description: "Bulk verification completed",
    schema: {
      type: "object",
      properties: {
        verified: { type: "number", example: 8 },
        failed: {
          type: "array",
          items: { type: "string" },
          example: ["failed-doc-uuid"],
        },
      },
    },
  })
  async bulkVerifyDocuments(
    @Request() req: RequestWithUser,
    @Body() bulkVerificationDto: BulkVerificationDto
  ) {
    return await this.kycAdminService.bulkVerifyDocuments(
      req.user.id,
      bulkVerificationDto
    );
  }

  @Get("user/:id/status")
  @ApiOperation({
    summary: "Get user KYC status",
    description: "Get detailed KYC status and documents for a specific user",
  })
  @ApiParam({ name: "id", description: "User ID" })
  @ApiResponse({
    status: 200,
    description: "User KYC status and documents",
    schema: {
      type: "object",
      properties: {
        user: { type: "object" },
        documents: { type: "array", items: { type: "object" } },
        kycComplete: { type: "boolean" },
        missingDocuments: {
          type: "array",
          items: { type: "string" },
        },
      },
    },
  })
  @ApiResponse({
    status: 404,
    description: "User not found",
  })
  async getUserKycStatus(@Param("id", ParseUUIDPipe) userId: string) {
    return await this.kycAdminService.getUserKycStatus(userId);
  }

  @Post("send-reminders")
  @ApiOperation({
    summary: "Send KYC completion reminders",
    description: "Send reminder notifications to users with incomplete KYC",
  })
  @ApiResponse({
    status: 200,
    description: "Reminders sent",
    schema: {
      type: "object",
      properties: {
        sent: { type: "number", example: 25 },
        failed: { type: "number", example: 2 },
      },
    },
  })
  async sendKycReminders() {
    return await this.kycAdminService.sendKycReminders();
  }
}
