import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { KycAdminController } from './kyc-admin.controller';
import { KycAdminService } from './kyc-admin.service';
import { Document } from '../users/entities/document.entity';
import { User } from '../users/entities/user.entity';
import { Company } from '../companies/entities/company.entity';
import { DocumentsModule } from '../documents/documents.module';
import { NotificationsModule } from '../notifications/notifications.module';
import { ActivityLogModule } from '../activity-log/activity-log.module';

@Module({
  imports: [
    TypeOrmModule.forFeature([Document, User, Company]),
    DocumentsModule,
    NotificationsModule,
    ActivityLogModule,
  ],
  controllers: [KycAdminController],
  providers: [KycAdminService],
  exports: [KycAdminService],
})
export class AdminModule {}
