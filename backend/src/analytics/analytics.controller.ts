import {
  <PERSON>,
  Get,
  Param,
  Query,
  UseGuards,
  Request,
  ForbiddenException,
  Inject,
} from "@nestjs/common";
import { AnalyticsService } from "./analytics.service";
import { JwtAuthGuard } from "../auth/guards/jwt-auth.guard";
import { RolesGuard } from "../auth/guards/roles.guard";
import { Roles } from "../auth/decorators/roles.decorator";
import { UserRole } from "@shared/types";
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiBearerAuth,
  ApiParam,
  ApiQuery,
} from "@nestjs/swagger";

@ApiTags("analytics")
@Controller("analytics")
@UseGuards(JwtAuthGuard)
@ApiBearerAuth()
export class AnalyticsController {
  constructor(
    @Inject(AnalyticsService)
    private readonly analyticsService: AnalyticsService
  ) {}

  @Get("admin-dashboard")
  @UseGuards(RolesGuard)
  @Roles(UserRole.ADMIN)
  @ApiOperation({ summary: "Get admin dashboard statistics (Admin only)" })
  @ApiResponse({
    status: 200,
    description: "Returns admin dashboard statistics",
  })
  async getAdminDashboardStats() {
    return this.analyticsService.getAdminDashboardStats();
  }

  @Get("company/:companyId")
  @UseGuards(RolesGuard)
  @Roles(UserRole.ADMIN, UserRole.COMPANY)
  @ApiOperation({ summary: "Get company performance analytics" })
  @ApiResponse({
    status: 200,
    description: "Returns company performance analytics",
  })
  @ApiResponse({
    status: 403,
    description: "Forbidden - Company can only access their own analytics",
  })
  @ApiParam({ name: "companyId", description: "ID of the company" })
  @ApiQuery({
    name: "timeRange",
    description: "Time range for analytics (7days, 30days, 90days, year)",
    required: false,
    enum: ["7days", "30days", "90days", "year"],
    example: "30days",
  })
  async getCompanyPerformanceAnalytics(
    @Param("companyId") companyId: string,
    @Query("timeRange") timeRange: string = "30days",
    @Request() req
  ) {
    // Check if user is company and requesting their own data
    if (
      req.user.role === UserRole.COMPANY &&
      req.user.companyId !== companyId
    ) {
      throw new ForbiddenException(
        "You can only view analytics for your own company"
      );
    }

    return this.analyticsService.getCompanyPerformanceAnalytics(
      companyId,
      timeRange
    );
  }

  @Get("user/:userId/activity")
  @UseGuards(RolesGuard)
  @Roles(UserRole.ADMIN)
  @ApiOperation({ summary: "Get user activity analytics (Admin only)" })
  @ApiResponse({ status: 200, description: "Returns user activity analytics" })
  @ApiParam({ name: "userId", description: "ID of the user" })
  @ApiQuery({
    name: "timeRange",
    description: "Time range for analytics (7days, 30days, 90days, year)",
    required: false,
    enum: ["7days", "30days", "90days", "year"],
    example: "30days",
  })
  async getUserActivityAnalytics(
    @Param("userId") userId: string,
    @Query("timeRange") timeRange: string = "30days"
  ) {
    // This is a placeholder for future implementation
    // The service method doesn't exist yet, but adding the endpoint for completeness
    return { message: "This endpoint is not implemented yet" };
  }
}
