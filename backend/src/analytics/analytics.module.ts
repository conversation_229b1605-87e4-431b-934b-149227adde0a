import { Modu<PERSON> } from "@nestjs/common";
import { TypeOrmModule } from "@nestjs/typeorm";
import { AnalyticsController } from "./analytics.controller";
import { AnalyticsService } from "./analytics.service";
import { Application } from "src/applications/entities/application.entity";
import { Job } from "src/jobs/entities/job.entity";
import { Payout } from "src/payouts/entities/payout.entity";
import { Rating } from "src/ratings/entities/rating.entity";
import { User } from "src/users/entities/user.entity";
import { Company } from "src/companies/entities/company.entity";
import { EscrowAccount } from "src/payments/entities/escrow.entity";

@Module({
  imports: [
    TypeOrmModule.forFeature([
      User,
      Job,
      Application,
      Rating,
      Payout,
      Company,
      EscrowAccount,
    ]),
  ],
  controllers: [AnalyticsController],
  providers: [AnalyticsService],
  exports: [AnalyticsService],
})
export class AnalyticsModule {}
