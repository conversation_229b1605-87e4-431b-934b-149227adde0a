import { ApiProperty, ApiPropertyOptional } from "@nestjs/swagger"
import { IsString, IsOptional, Length, IsUUID } from "class-validator"

export class CreateDisputeDto {
  @ApiProperty({ description: "User ID against whom the dispute is raised" })
  @IsUUID()
  againstId: string

  @ApiProperty({ description: "Job ID associated with this dispute" })
  @IsUUID()
  jobId: string

  @ApiProperty({ description: "Reason for dispute" })
  @IsString()
  @Length(3, 100)
  reason: string

  @ApiPropertyOptional({ description: "Detailed description of the dispute" })
  @IsOptional()
  @IsString()
  @Length(0, 2000)
  description?: string
}
