import { ApiPropertyOptional } from "@nestjs/swagger"
import { IsString, IsOptional, IsEnum, Length } from "class-validator"
import { DisputeStatus } from "@shared/types"

export class UpdateDisputeDto {
  @ApiPropertyOptional({ description: "Dispute status" })
  @IsOptional()
  @IsEnum(DisputeStatus)
  status?: DisputeStatus

  @ApiPropertyOptional({ description: "Resolution details" })
  @IsOptional()
  @IsString()
  @Length(0, 2000)
  resolution?: string
}
