import { <PERSON><PERSON><PERSON> } from "@nestjs/common";
import { TypeOrmModule } from "@nestjs/typeorm";
import { DisputesService } from "./disputes.service";
import { DisputesController } from "./disputes.controller";
import { UsersModule } from "../users/users.module";
import { JobsModule } from "../jobs/jobs.module";
import { NotificationsModule } from "../notifications/notifications.module";
import { Dispute } from "./entities/dispute.entity";

@Module({
  imports: [
    TypeOrmModule.forFeature([Dispute]),
    UsersModule,
    JobsModule,
    NotificationsModule,
  ],
  providers: [DisputesService],
  controllers: [DisputesController],
  exports: [DisputesService],
})
export class DisputesModule {}
