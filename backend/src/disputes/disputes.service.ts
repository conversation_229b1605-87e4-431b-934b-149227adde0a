import {
  Injectable,
  NotFoundException,
  BadRequestException,
  Inject,
} from "@nestjs/common";
import { InjectRepository } from "@nestjs/typeorm";
import type { Repository } from "typeorm";
import { UsersService } from "../users/users.service";
import { JobsService } from "../jobs/jobs.service";
import { NotificationsService } from "../notifications/notifications.service";
import type { CreateDisputeDto, UpdateDisputeDto } from "@shared/validation";
import { DisputeStatus, UserRole } from "@shared/types";
import { Dispute } from "./entities/dispute.entity";

@Injectable()
export class DisputesService {
  constructor(
    @InjectRepository(Dispute)
    private disputesRepository: Repository<Dispute>,
    @Inject(UsersService)
    private readonly usersService: UsersService,
    @Inject(JobsService)
    private readonly jobsService: JobsService,
    @Inject(NotificationsService)
    private readonly notificationsService: NotificationsService
  ) {}

  async create(
    userId: string,
    createDisputeDto: CreateDisputeDto
  ): Promise<Dispute> {
    // Verify job exists
    const job = await this.jobsService.findOne(createDisputeDto.jobId);

    // Verify against user exists
    const againstUser = await this.usersService.findOne(
      createDisputeDto.againstId
    );

    // Create dispute
    const dispute = this.disputesRepository.create({
      ...createDisputeDto,
      raisedById: userId,
    });

    const savedDispute = await this.disputesRepository.save(dispute);

    // Notify admin
    const admins = await this.usersService.findAdmins();
    for (const admin of admins) {
      await this.notificationsService.create({
        userId: admin.id,
        title: "New Dispute Created",
        message: `A new dispute has been raised for job: ${job.title}`,
        type: "dispute",
        metadata: { disputeId: savedDispute.id, jobId: job.id },
        link: `/disputes/${savedDispute.id}`,
      });
    }

    // Notify against user
    await this.notificationsService.create({
      userId: againstUser.id,
      title: "Dispute Raised Against You",
      message: `A dispute has been raised against you for job: ${job.title}`,
      type: "dispute",
      metadata: { disputeId: savedDispute.id, jobId: job.id },
      link: `/disputes/${savedDispute.id}`,
    });

    return savedDispute;
  }

  async findAll(filters?: {
    raisedById?: string;
    againstId?: string;
    jobId?: string;
    status?: DisputeStatus;
  }): Promise<Dispute[]> {
    const queryBuilder = this.disputesRepository
      .createQueryBuilder("dispute")
      .leftJoinAndSelect("dispute.raisedBy", "raisedBy")
      .leftJoinAndSelect("dispute.againstUser", "againstUser")
      .leftJoinAndSelect("dispute.job", "job")
      .leftJoinAndSelect("dispute.resolvedBy", "resolvedBy")
      .orderBy("dispute.createdAt", "DESC");

    if (filters) {
      if (filters.raisedById) {
        queryBuilder.andWhere("dispute.raisedById = :raisedById", {
          raisedById: filters.raisedById,
        });
      }

      if (filters.againstId) {
        queryBuilder.andWhere("dispute.againstId = :againstId", {
          againstId: filters.againstId,
        });
      }

      if (filters.jobId) {
        queryBuilder.andWhere("dispute.jobId = :jobId", {
          jobId: filters.jobId,
        });
      }

      if (filters.status) {
        queryBuilder.andWhere("dispute.status = :status", {
          status: filters.status,
        });
      }
    }

    return queryBuilder.getMany();
  }

  async findOne(id: string): Promise<Dispute> {
    const dispute = await this.disputesRepository.findOne({
      where: { id },
      relations: ["raisedBy", "againstUser", "job", "resolvedBy"],
    });

    if (!dispute) {
      throw new NotFoundException(`Dispute with ID ${id} not found`);
    }

    return dispute;
  }

  async update(
    id: string,
    userId: string,
    userRole: UserRole,
    updateDisputeDto: UpdateDisputeDto
  ): Promise<Dispute> {
    // Only admins can update disputes
    if (userRole !== UserRole.ADMIN) {
      throw new BadRequestException("Only admins can update disputes");
    }

    const dispute = await this.findOne(id);

    // Update dispute
    Object.assign(dispute, updateDisputeDto);

    // If status is being updated to resolved, set resolvedBy and resolvedAt
    if (updateDisputeDto.status === DisputeStatus.RESOLVED) {
      dispute.resolvedById = userId;
      dispute.resolvedAt = new Date();
    }

    const updatedDispute = await this.disputesRepository.save(dispute);

    // Send notifications
    if (updateDisputeDto.status) {
      // Notify raised by user
      await this.notificationsService.create({
        userId: dispute.raisedById,
        title: "Dispute Status Updated",
        message: `Your dispute for job: ${dispute.job.title} is now ${updateDisputeDto.status}`,
        type: "dispute",
        metadata: { disputeId: id, jobId: dispute.jobId },
        link: `/disputes/${id}`,
      });

      // Notify against user
      await this.notificationsService.create({
        userId: dispute.againstId,
        title: "Dispute Status Updated",
        message: `The dispute against you for job: ${dispute.job.title} is now ${updateDisputeDto.status}`,
        type: "dispute",
        metadata: { disputeId: id, jobId: dispute.jobId },
        link: `/disputes/${id}`,
      });
    }

    return updatedDispute;
  }
}
