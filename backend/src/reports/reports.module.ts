import { Module } from "@nestjs/common"
import { TypeOrmModule } from "@nestjs/typeorm"
import { ReportsService } from "./reports.service"
import { ReportsController } from "./reports.controller"
import { Report } from "./entities/report.entity"
import { AnalyticsModule } from "../analytics/analytics.module"
import { ActivityLogModule } from "../activity-log/activity-log.module"

@Module({
  imports: [TypeOrmModule.forFeature([Report]), AnalyticsModule, ActivityLogModule],
  controllers: [ReportsController],
  providers: [ReportsService],
  exports: [ReportsService],
})
export class ReportsModule {}
