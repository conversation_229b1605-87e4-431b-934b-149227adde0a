import {
  <PERSON>,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  UseGuards,
  Request,
  Inject,
} from "@nestjs/common";
import { ReportsService } from "./reports.service";
import { JwtAuthGuard } from "../auth/guards/jwt-auth.guard";
import { RolesGuard } from "../auth/guards/roles.guard";
import { Roles } from "../auth/decorators/roles.decorator";
import { UserRole } from "src/common/enums/user-role.enum";

@Controller("admin/reports")
@UseGuards(JwtAuthGuard, RolesGuard)
@Roles(UserRole.ADMIN)
export class ReportsController {
  constructor(
    @Inject(ReportsService)
    private readonly reportsService: ReportsService
  ) {}

  @Post()
  create(@Body() createReportDto: any, @Request() req) {
    return this.reportsService.create(createReportDto, req.user.userId);
  }

  @Get()
  findAll(@Request() req) {
    return this.reportsService.findAll(req.user.userId);
  }

  @Get(":id")
  findOne(@Param("id") id: string, @Request() req) {
    return this.reportsService.findOne(id, req.user.userId);
  }

  @Patch(":id")
  update(
    @Param("id") id: string,
    @Body() updateReportDto: any,
    @Request() req
  ) {
    return this.reportsService.update(id, updateReportDto, req.user.userId);
  }

  @Delete(":id")
  remove(@Param("id") id: string, @Request() req) {
    return this.reportsService.remove(id, req.user.userId);
  }

  @Post(":id/generate")
  generateReport(@Param("id") id: string, @Request() req) {
    return this.reportsService.generateReport(id, req.user.userId);
  }
}
