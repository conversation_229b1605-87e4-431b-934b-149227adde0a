import { Inject, Injectable, NotFoundException } from "@nestjs/common";
import { InjectRepository } from "@nestjs/typeorm";
import type { Repository } from "typeorm";
import { Report } from "./entities/report.entity";
import { AnalyticsService } from "../analytics/analytics.service";
import { ActivityLogService } from "../activity-log/activity-log.service";

@Injectable()
export class ReportsService {
  constructor(
    @InjectRepository(Report)
    private readonly reportsRepository: Repository<Report>,
    @Inject(AnalyticsService)
    private readonly analyticsService: AnalyticsService,
    @Inject(ActivityLogService)
    private readonly activityLogService: ActivityLogService
  ) {}

  async create(createReportDto: any, userId: string): Promise<Report> {
    const report = this.reportsRepository.create({
      ...createReportDto,
      createdBy: userId,
    });

    const savedReport = await this.reportsRepository.save(report);

    await this.activityLogService.logActivity({
      userId,
      action: "report_created",
      description: `Created report: ${savedReport.name}`,
      entityId: savedReport.id,
      entityType: "report",
    });

    return savedReport;
  }

  async findAll(userId: string): Promise<Report[]> {
    return this.reportsRepository.find({
      where: { createdBy: userId },
      order: { createdAt: "DESC" },
    });
  }

  async findOne(id: string, userId: string): Promise<Report> {
    const report = await this.reportsRepository.findOne({
      where: { id, createdBy: userId },
    });

    if (!report) {
      throw new NotFoundException(`Report with ID ${id} not found`);
    }

    return report;
  }

  async update(
    id: string,
    updateReportDto: any,
    userId: string
  ): Promise<Report> {
    const report = await this.findOne(id, userId);

    Object.assign(report, updateReportDto);

    const updatedReport = await this.reportsRepository.save(report);

    await this.activityLogService.logActivity({
      userId,
      action: "report_updated",
      description: `Updated report: ${updatedReport.name}`,
      entityId: updatedReport.id,
      entityType: "report",
    });

    return updatedReport;
  }

  async remove(id: string, userId: string): Promise<void> {
    const report = await this.findOne(id, userId);

    await this.reportsRepository.remove(report);

    await this.activityLogService.logActivity({
      userId,
      action: "report_deleted",
      description: `Deleted report: ${report.name}`,
      entityId: id,
      entityType: "report",
    });
  }

  async generateReport(
    id: string,
    userId: string
  ): Promise<{ downloadUrl: string }> {
    const report = await this.findOne(id, userId);

    // In a real application, this would generate a PDF or Excel file
    // For now, we'll just update the lastGenerated date and return a mock URL

    report.lastGenerated = new Date();
    report.downloadUrl = `/reports/download/${id}`;

    await this.reportsRepository.save(report);

    await this.activityLogService.logActivity({
      userId,
      action: "report_generated",
      description: `Generated report: ${report.name}`,
      entityId: report.id,
      entityType: "report",
    });

    return { downloadUrl: report.downloadUrl };
  }
}
