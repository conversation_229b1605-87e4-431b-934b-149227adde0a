import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  UseGuards,
  Query,
  Request,
  UseInterceptors,
  UploadedFile,
  BadRequestException,
  Inject,
  Put,
} from "@nestjs/common";
import { FileInterceptor } from "@nestjs/platform-express";
import {
  ApiBearerAuth,
  ApiBody,
  ApiConsumes,
  ApiOperation,
  ApiParam,
  ApiQuery,
  ApiResponse,
  ApiTags,
} from "@nestjs/swagger";
import { UsersService } from "./users.service";
import {
  TrustScoreService,
  TrustScoreLevel,
  PaginatedTrustScoreLogs,
  TrustScoreBreakdown,
  TrustScoreSuggestion,
} from "./trust-score.service";

import { CreateUserDto } from "./dto/create-user.dto";
import { UpdateUserDto } from "./dto/update-user.dto";
import { UpdateProfileDto } from "./dto/update-profile.dto";
import { UpdateKycDto } from "./dto/update-kyc.dto";
import { QueryUsersDto } from "./dto/query-users.dto";
import { VerifyDocumentDto } from "./dto/verify-document.dto";
import { JwtAuthGuard } from "../auth/guards/jwt-auth.guard";
import { RolesGuard } from "../auth/guards/roles.guard";
import { Roles } from "../auth/decorators/roles.decorator";
import { UserRole } from "../common/enums/user-role.enum";
import { DocumentType } from "../common/enums/document-type.enum";
import { ApiPaginatedResponse } from "../common/decorators/api-paginated-response.decorator";
import { User } from "./entities/user.entity";
import { TrustScoreLog } from "./entities/trust-score-log.entity";
import {
  CreateUserSwaggerDto,
  UpdateUserSwaggerDto,
  UpdateProfileSwaggerDto,
  UpdateKycSwaggerDto,
  UploadDocumentSwaggerDto,
  VerifyDocumentSwaggerDto,
  UserResponseSwaggerDto,
  DocumentResponseSwaggerDto,
} from "./dto/swagger.dto";

// Define the request type with user property
interface RequestWithUser {
  user: {
    id: string;
    [key: string]: any;
  };
}

@ApiTags("users")
@Controller({
  path: "users",
  version: "1",
})
export class UsersController {
  constructor(
    @Inject(UsersService)
    private readonly usersService: UsersService,
    @Inject(TrustScoreService)
    private readonly trustScoreService: TrustScoreService
  ) {}

  @Post()
  @UseGuards(JwtAuthGuard, RolesGuard)
  @Roles(UserRole.ADMIN)
  @ApiBearerAuth()
  @ApiOperation({ summary: "Create a new user (Admin only)" })
  @ApiBody({ type: CreateUserSwaggerDto })
  @ApiResponse({
    status: 201,
    description: "User created successfully",
    type: UserResponseSwaggerDto,
  })
  @ApiResponse({ status: 400, description: "Bad request" })
  @ApiResponse({ status: 401, description: "Unauthorized" })
  @ApiResponse({ status: 403, description: "Forbidden" })
  create(@Body() createUserDto: CreateUserDto) {
    return this.usersService.create(createUserDto);
  }

  @Get()
  @UseGuards(JwtAuthGuard, RolesGuard)
  @Roles(UserRole.ADMIN)
  @ApiBearerAuth()
  @ApiOperation({ summary: "Get all users (Admin only)" })
  @ApiPaginatedResponse(User)
  @ApiResponse({ status: 401, description: "Unauthorized" })
  @ApiResponse({ status: 403, description: "Forbidden" })
  findAll(@Query() queryUsersDto: QueryUsersDto) {
    return this.usersService.findAll(queryUsersDto);
  }

  @Get("profile")
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: "Get current user's profile" })
  @ApiResponse({
    status: 200,
    description: "User profile found",
    type: UserResponseSwaggerDto,
  })
  @ApiResponse({ status: 401, description: "Unauthorized" })
  @ApiResponse({ status: 404, description: "User not found" })
  getProfile(@Request() req: RequestWithUser) {
    return this.usersService.findOne(req.user.id);
  }

  @Get("skills")
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: "Get user skills" })
  @ApiResponse({
    status: 200,
    description: "Returns user skills",
    schema: {
      type: "array",
      items: { type: "string" },
    },
  })
  @ApiResponse({ status: 401, description: "Unauthorized" })
  async getUserSkills(@Request() req: RequestWithUser): Promise<string[]> {
    return this.usersService.getUserSkills(req.user.id);
  }

  @Put("skills")
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: "Update user skills" })
  @ApiBody({
    schema: {
      type: "object",
      properties: {
        skills: {
          type: "array",
          items: { type: "string" },
          example: ["Inventory Management", "Stock Auditing", "Data Entry"],
        },
      },
    },
  })
  @ApiResponse({
    status: 200,
    description: "Skills updated successfully",
    schema: {
      properties: {
        success: { type: "boolean", example: true },
      },
    },
  })
  @ApiResponse({ status: 400, description: "Bad request" })
  @ApiResponse({ status: 401, description: "Unauthorized" })
  async updateUserSkills(
    @Request() req: RequestWithUser,
    @Body() body: { skills: string[] }
  ): Promise<{ success: boolean }> {
    return this.usersService.updateUserSkills(req.user.id, body.skills);
  }

  @Get(":id")
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: "Get user by ID" })
  @ApiParam({ name: "id", description: "User ID" })
  @ApiResponse({
    status: 200,
    description: "User found",
    type: UserResponseSwaggerDto,
  })
  @ApiResponse({ status: 401, description: "Unauthorized" })
  @ApiResponse({ status: 404, description: "User not found" })
  findOne(@Param("id") id: string) {
    return this.usersService.findOne(id);
  }

  @Patch(":id")
  @UseGuards(JwtAuthGuard, RolesGuard)
  @Roles(UserRole.ADMIN)
  @ApiBearerAuth()
  @ApiOperation({ summary: "Update user (Admin only)" })
  @ApiParam({ name: "id", description: "User ID" })
  @ApiBody({ type: UpdateUserSwaggerDto })
  @ApiResponse({
    status: 200,
    description: "User updated successfully",
    type: UserResponseSwaggerDto,
  })
  @ApiResponse({ status: 400, description: "Bad request" })
  @ApiResponse({ status: 401, description: "Unauthorized" })
  @ApiResponse({ status: 403, description: "Forbidden" })
  @ApiResponse({ status: 404, description: "User not found" })
  update(@Param("id") id: string, @Body() updateUserDto: UpdateUserDto) {
    return this.usersService.update(id, updateUserDto);
  }

  @Delete(":id")
  @UseGuards(JwtAuthGuard, RolesGuard)
  @Roles(UserRole.ADMIN)
  @ApiBearerAuth()
  @ApiOperation({ summary: "Delete user (Admin only)" })
  @ApiParam({ name: "id", description: "User ID" })
  @ApiResponse({ status: 200, description: "User deleted successfully" })
  @ApiResponse({ status: 401, description: "Unauthorized" })
  @ApiResponse({ status: 403, description: "Forbidden" })
  @ApiResponse({ status: 404, description: "User not found" })
  remove(@Param("id") id: string) {
    return this.usersService.remove(id);
  }

  @Patch("profile")
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: "Update user profile" })
  @ApiBody({ type: UpdateProfileSwaggerDto })
  @ApiResponse({
    status: 200,
    description: "Profile updated successfully",
    type: UserResponseSwaggerDto,
  })
  @ApiResponse({ status: 400, description: "Bad request" })
  @ApiResponse({ status: 401, description: "Unauthorized" })
  updateProfile(
    @Request() req: RequestWithUser,
    @Body() updateProfileDto: UpdateProfileDto
  ): Promise<User> {
    return this.usersService.updateProfile(req.user.id, updateProfileDto);
  }

  @Patch("kyc")
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: "Update KYC information" })
  @ApiBody({ type: UpdateKycSwaggerDto })
  @ApiResponse({
    status: 200,
    description: "KYC information updated successfully",
    type: UserResponseSwaggerDto,
  })
  @ApiResponse({ status: 400, description: "Bad request" })
  @ApiResponse({ status: 401, description: "Unauthorized" })
  updateKyc(
    @Request() req: RequestWithUser,
    @Body() updateKycDto: UpdateKycDto
  ): Promise<User> {
    return this.usersService.updateKyc(req.user.id, updateKycDto);
  }

  @Post("upload-document")
  @UseGuards(JwtAuthGuard)
  @UseInterceptors(FileInterceptor("file"))
  @ApiBearerAuth()
  @ApiOperation({ summary: "Upload document" })
  @ApiConsumes("multipart/form-data")
  @ApiBody({ type: UploadDocumentSwaggerDto })
  @ApiResponse({
    status: 201,
    description: "Document uploaded successfully",
    type: DocumentResponseSwaggerDto,
  })
  @ApiResponse({ status: 400, description: "Bad request" })
  @ApiResponse({ status: 401, description: "Unauthorized" })
  uploadDocument(
    @Request() req: RequestWithUser,
    @UploadedFile() file: any,
    @Body("type") documentType: DocumentType,
    @Body("documentNumber") documentNumber?: string
  ): Promise<any> {
    if (!file) {
      throw new BadRequestException("File is required");
    }

    return this.usersService.processAndUploadDocument(
      req.user.id,
      documentType,
      file,
      documentNumber
    );
  }

  @Patch(":id/verify-document/:documentId")
  @UseGuards(JwtAuthGuard, RolesGuard)
  @Roles(UserRole.ADMIN)
  @ApiBearerAuth()
  @ApiOperation({ summary: "Verify or reject document (Admin only)" })
  @ApiParam({ name: "id", description: "User ID" })
  @ApiParam({ name: "documentId", description: "Document ID" })
  @ApiBody({ type: VerifyDocumentSwaggerDto })
  @ApiResponse({
    status: 200,
    description: "Document verification status updated",
    type: DocumentResponseSwaggerDto,
  })
  @ApiResponse({ status: 400, description: "Bad request" })
  @ApiResponse({ status: 401, description: "Unauthorized" })
  @ApiResponse({ status: 403, description: "Forbidden" })
  @ApiResponse({ status: 404, description: "Document not found" })
  verifyDocument(
    @Param("id") userId: string,
    @Param("documentId") documentId: string,
    @Body() verifyDocumentDto: VerifyDocumentDto
  ) {
    return this.usersService.verifyDocument(
      userId,
      documentId,
      "system", //TODO: need to fix this
      verifyDocumentDto.status,
      verifyDocumentDto.rejectionReason
    );
  }

  @Get("trust-score")
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: "Get current user's trust score" })
  @ApiResponse({
    status: 200,
    description: "Returns user's trust score",
    schema: {
      properties: {
        score: { type: "number", example: 85 },
        level: { type: "string", example: "Good" },
        nextLevel: { type: "string", example: "Excellent" },
        nextLevelThreshold: { type: "number", example: 90 },
      },
    },
  })
  @ApiResponse({ status: 401, description: "Unauthorized" })
  async getTrustScore(
    @Request() req: RequestWithUser
  ): Promise<{ score: number } & TrustScoreLevel> {
    return this.trustScoreService.getTrustScoreLevel(req.user.id);
  }

  @Get("trust-score/logs")
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: "Get trust score history logs" })
  @ApiResponse({
    status: 200,
    description: "Returns trust score logs",
    type: [TrustScoreLog],
  })
  @ApiResponse({ status: 401, description: "Unauthorized" })
  @ApiQuery({ name: "limit", required: false, type: Number, example: 10 })
  @ApiQuery({ name: "offset", required: false, type: Number, example: 0 })
  async getTrustScoreLogs(
    @Request() req: RequestWithUser,
    @Query("limit") limit: number = 10,
    @Query("offset") offset: number = 0
  ): Promise<PaginatedTrustScoreLogs> {
    return this.trustScoreService.getPaginatedTrustScoreLogs(
      req.user.id,
      limit,
      offset
    );
  }

  @Get("trust-score/breakdown")
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: "Get trust score breakdown by categories" })
  @ApiResponse({
    status: 200,
    description: "Returns trust score breakdown",
    schema: {
      properties: {
        overall: { type: "number", example: 85 },
        categories: {
          type: "object",
          properties: {
            jobCompletion: { type: "number", example: 90 },
            punctuality: { type: "number", example: 85 },
            communication: { type: "number", example: 80 },
            reliability: { type: "number", example: 85 },
          },
        },
      },
    },
  })
  @ApiResponse({ status: 401, description: "Unauthorized" })
  async getTrustScoreBreakdown(
    @Request() req: RequestWithUser
  ): Promise<TrustScoreBreakdown> {
    return this.trustScoreService.getTrustScoreBreakdown(req.user.id);
  }

  @Get("trust-score/suggestions")
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: "Get trust score improvement suggestions" })
  @ApiResponse({
    status: 200,
    description: "Returns trust score improvement suggestions",
    schema: {
      properties: {
        suggestions: {
          type: "array",
          items: {
            type: "object",
            properties: {
              id: { type: "string", example: "complete_profile" },
              title: { type: "string", example: "Complete Your Profile" },
              description: {
                type: "string",
                example:
                  "Add a profile picture and fill out all profile fields",
              },
              potentialPoints: { type: "number", example: 5 },
              completed: { type: "boolean", example: false },
            },
          },
        },
      },
    },
  })
  @ApiResponse({ status: 401, description: "Unauthorized" })
  async getTrustScoreImprovementSuggestions(
    @Request() req: RequestWithUser
  ): Promise<{ suggestions: TrustScoreSuggestion[] }> {
    return this.trustScoreService.getTrustScoreImprovementSuggestions(
      req.user.id
    );
  }

  @Get("stats")
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: "Get user statistics" })
  @ApiResponse({
    status: 200,
    description: "Returns user statistics",
    schema: {
      properties: {
        completedJobs: { type: "number", example: 15 },
        totalEarnings: { type: "number", example: 25000 },
        averageRating: { type: "number", example: 4.5 },
        totalRatings: { type: "number", example: 12 },
        activeApplications: { type: "number", example: 3 },
        savedJobs: { type: "number", example: 7 },
        jobsInProgress: { type: "number", example: 2 },
        trustScore: { type: "number", example: 85 },
        memberSince: { type: "string", example: "2024-01-15" },
      },
    },
  })
  @ApiResponse({ status: 401, description: "Unauthorized" })
  async getUserStats(@Request() req: RequestWithUser): Promise<{
    completedJobs: number;
    totalEarnings: number;
    averageRating: number;
    totalRatings: number;
    activeApplications: number;
    savedJobs: number;
    jobsInProgress: number;
    trustScore: number;
    memberSince: string;
  }> {
    return this.usersService.getUserStats(req.user.id);
  }
}
