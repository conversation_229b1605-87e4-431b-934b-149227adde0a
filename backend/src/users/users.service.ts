import { Injectable, Inject, Logger } from "@nestjs/common";
import { InjectRepository } from "@nestjs/typeorm";
import type { Repository, FindOptionsWhere } from "typeorm";
import { User } from "./entities/user.entity";
import { Document } from "./entities/document.entity";
import { TrustScoreLog } from "./entities/trust-score-log.entity";
import type { CreateUserDto } from "./dto/create-user.dto";
import type { UpdateUserDto } from "./dto/update-user.dto";
import type { UpdateProfileDto } from "./dto/update-profile.dto";
import type { UpdateKycDto } from "./dto/update-kyc.dto";
import type { QueryUsersDto } from "./dto/query-users.dto";
import { DocumentType } from "../common/enums/document-type.enum";
import { VerificationStatus } from "../common/enums/verification-status.enum";
import { UserRole } from "../common/enums/user-role.enum";
import { ActivityLogService } from "../activity-log/activity-log.service";
import { NotificationsService } from "../notifications/notifications.service";
import { PaginationService } from "../common/services/pagination.service";
import { ErrorHandler } from "../common/utils/error-handler.util";
import { AuthUtils } from "../auth/utils/auth.utils";
import { Company } from "src/companies/entities/company.entity";

// Define the file interface to avoid Express dependency
interface UploadedFile {
  fieldname: string;
  originalname: string;
  encoding: string;
  mimetype: string;
  size: number;
  buffer: Buffer;
  destination?: string;
  filename?: string;
  path?: string;
}

@Injectable()
export class UsersService {
  private readonly logger = new Logger(UsersService.name);

  constructor(
    @InjectRepository(User)
    private readonly usersRepository: Repository<User>,
    @InjectRepository(Document)
    private readonly documentsRepository: Repository<Document>,
    @InjectRepository(TrustScoreLog)
    private readonly trustScoreLogRepository: Repository<TrustScoreLog>,
    @Inject(ActivityLogService)
    private readonly activityLogService: ActivityLogService,
    @Inject(NotificationsService)
    private readonly notificationsService: NotificationsService,
    @Inject(PaginationService)
    private readonly paginationService: PaginationService
  ) {}

  async create(createUserDto: CreateUserDto): Promise<User> {
    try {
      // Check if email or phone already exists
      if (createUserDto.email) {
        const existingEmail = await this.findByEmail(createUserDto.email);
        if (existingEmail) {
          return ErrorHandler.handleBadRequest("Email already in use");
        }
      }

      if (createUserDto.phone) {
        const existingPhone = await this.findByPhone(createUserDto.phone);
        if (existingPhone) {
          return ErrorHandler.handleBadRequest("Phone number already in use");
        }
      }

      // Hash password
      const hashedPassword = await AuthUtils.hashPassword(
        createUserDto.password
      );

      // Create user
      const user = this.usersRepository.create({
        ...createUserDto,
        password: hashedPassword,
      });

      return this.usersRepository.save(user);
    } catch (error) {
      this.logger.error(`Error creating user: ${error.message}`);
      throw ErrorHandler.handleError(error, "User");
    }
  }

  async findAll(queryUsersDto: QueryUsersDto) {
    const {
      page,
      limit,
      sortBy,
      sortOrder,
      role,
      search,
      isKycVerified,
      isActive,
      isBanned,
    } = queryUsersDto;

    // Build where conditions
    const whereConditions: FindOptionsWhere<User> = {};

    if (role) {
      whereConditions.role = role;
    }

    if (isKycVerified !== undefined) {
      whereConditions.isKycVerified = isKycVerified;
    }

    if (isActive !== undefined) {
      whereConditions.isActive = isActive;
    }

    if (isBanned !== undefined) {
      whereConditions.isBanned = isBanned;
    }

    // Build query
    const queryBuilder = this.usersRepository.createQueryBuilder("user");

    // Apply where conditions
    if (Object.keys(whereConditions).length > 0) {
      queryBuilder.where(whereConditions);
    }

    // Apply search if provided
    if (search) {
      queryBuilder.andWhere(
        "(user.name ILIKE :search OR user.email ILIKE :search OR user.phone ILIKE :search)",
        {
          search: `%${search}%`,
        }
      );
    }

    // Apply sorting
    if (sortBy) {
      queryBuilder.orderBy(`user.${sortBy}`, sortOrder);
    } else {
      queryBuilder.orderBy("user.createdAt", "DESC");
    }

    // Apply pagination
    return this.paginationService.paginate(queryBuilder, page, limit);
  }

  async findOne(id: string): Promise<User> {
    try {
      const user = await this.usersRepository.findOne({
        where: { id },
        relations: ["documents"],
      });

      if (!user) {
        return ErrorHandler.handleNotFound("User", id);
      }

      return user;
    } catch (error) {
      this.logger.error(`Error finding user with ID ${id}: ${error.message}`);
      throw ErrorHandler.handleError(error, "User");
    }
  }

  async findByEmail(
    email: string,
    includePassword: boolean = false
  ): Promise<User | null> {
    return this.usersRepository.findOne({
      where: { email },
      select: includePassword
        ? undefined
        : {
            id: true,
            email: true,
            phone: true,
            name: true,
            role: true,
            trustScore: true,
            isKycVerified: true,
            isActive: true,
            isBanned: true,
            profilePicture: true,
            address: true,
            city: true,
            state: true,
            country: true,
            postalCode: true,
            bio: true,
            metadata: true,
            upiId: true,
            deviceTokens: true,
            emailVerified: true,
            phoneVerified: true,
            lastLoginAt: true,
            createdAt: true,
            updatedAt: true,
          },
    });
  }

  async findByPhone(
    phone: string,
    includePassword: boolean = false
  ): Promise<User | null> {
    return this.usersRepository.findOne({
      where: { phone },
      select: includePassword
        ? undefined
        : {
            id: true,
            email: true,
            phone: true,
            name: true,
            role: true,
            trustScore: true,
            isKycVerified: true,
            isActive: true,
            isBanned: true,
            profilePicture: true,
            address: true,
            city: true,
            state: true,
            country: true,
            postalCode: true,
            bio: true,
            metadata: true,
            upiId: true,
            deviceTokens: true,
            emailVerified: true,
            phoneVerified: true,
            lastLoginAt: true,
            createdAt: true,
            updatedAt: true,
          },
    });
  }

  async update(id: string, updateUserDto: UpdateUserDto): Promise<User> {
    try {
      const user = await this.findOne(id);

      // Check if email or phone already exists
      if (updateUserDto.email && updateUserDto.email !== user.email) {
        const existingEmail = await this.findByEmail(updateUserDto.email);
        if (existingEmail) {
          return ErrorHandler.handleBadRequest("Email already in use");
        }
      }

      if (updateUserDto.phone && updateUserDto.phone !== user.phone) {
        const existingPhone = await this.findByPhone(updateUserDto.phone);
        if (existingPhone) {
          return ErrorHandler.handleBadRequest("Phone number already in use");
        }
      }

      // Hash password if it's being updated
      if (updateUserDto.password) {
        updateUserDto.password = await AuthUtils.hashPassword(
          updateUserDto.password
        );
      }

      // Update user
      Object.assign(user, updateUserDto);
      return this.usersRepository.save(user);
    } catch (error) {
      this.logger.error(`Error updating user ${id}: ${error.message}`);
      throw ErrorHandler.handleError(error, "User");
    }
  }

  async remove(id: string): Promise<void> {
    const user = await this.findOne(id);
    await this.usersRepository.remove(user);
  }

  async updateProfile(
    userId: string,
    updateProfileDto: UpdateProfileDto
  ): Promise<User> {
    try {
      const user = await this.findOne(userId);

      // Check if email or phone already exists
      if (updateProfileDto.email && updateProfileDto.email !== user.email) {
        const existingEmail = await this.findByEmail(updateProfileDto.email);
        if (existingEmail) {
          return ErrorHandler.handleBadRequest("Email already in use");
        }
      }

      if (updateProfileDto.phone && updateProfileDto.phone !== user.phone) {
        const existingPhone = await this.findByPhone(updateProfileDto.phone);
        if (existingPhone) {
          return ErrorHandler.handleBadRequest("Phone number already in use");
        }
      }

      // Update user profile
      Object.assign(user, updateProfileDto);

      // Log activity
      await this.activityLogService.logActivity({
        userId,
        action: "update_profile",
        entityType: "user",
        entityId: userId,
        description: "Updated profile information",
      });

      return this.usersRepository.save(user);
    } catch (error) {
      this.logger.error(
        `Error updating profile for user ${userId}: ${error.message}`
      );
      throw ErrorHandler.handleError(error, "User Profile");
    }
  }

  async updateKyc(userId: string, updateKycDto: UpdateKycDto): Promise<User> {
    const user = await this.findOne(userId);

    // Update KYC data
    Object.assign(user, updateKycDto);

    // Log activity
    await this.activityLogService.logActivity({
      userId,
      action: "update_kyc",
      entityType: "user",
      entityId: userId,
      description: "Updated KYC information",
    });

    return this.usersRepository.save(user);
  }

  async uploadDocument(
    userId: string,
    documentType: DocumentType,
    documentUrl: string,
    documentNumber?: string
  ): Promise<Document> {
    const user = await this.findOne(userId);

    // Check if document of this type already exists
    const existingDoc = await this.documentsRepository.findOne({
      where: {
        userId,
        documentType,
      },
    });

    if (existingDoc) {
      // Update existing document
      existingDoc.documentUrl = documentUrl;
      if (documentNumber) {
        existingDoc.documentNumber = documentNumber;
      }
      existingDoc.verificationStatus = VerificationStatus.PENDING;
      existingDoc.verifiedBy = null;
      existingDoc.verifiedAt = null;
      existingDoc.rejectionReason = null;

      // Log activity
      await this.activityLogService.logActivity({
        userId,
        action: "update_document",
        entityType: "document",
        entityId: existingDoc.id,
        description: `Updated ${documentType} document`,
      });

      return this.documentsRepository.save(existingDoc);
    }

    // Create new document
    const document = this.documentsRepository.create({
      userId,
      documentType,
      documentUrl,
      documentNumber,
    });

    const savedDocument = await this.documentsRepository.save(document);

    // Log activity
    await this.activityLogService.logActivity({
      userId,
      action: "upload_document",
      entityType: "document",
      entityId: savedDocument.id,
      description: `Uploaded ${documentType} document`,
    });

    // Notify admins about new document for verification
    const admins = await this.findAdmins();
    for (const admin of admins) {
      await this.notificationsService.create({
        userId: admin.id,
        title: "New Document Uploaded",
        message: `${user.name} has uploaded a new ${documentType} document for verification`,
        type: "document",
        metadata: { documentId: savedDocument.id, userId },
        link: `/admin/users/${userId}/documents`,
      });
    }

    return savedDocument;
  }

  async verifyDocument(
    userId: string,
    documentId: string,
    adminId: string,
    status: VerificationStatus,
    rejectionReason?: string
  ): Promise<Document> {
    try {
      const user = await this.findOne(userId);

      const document = await this.documentsRepository.findOne({
        where: { id: documentId, userId },
      });

      if (!document) {
        return ErrorHandler.handleNotFound("Document", documentId);
      }

      // Update document verification status
      document.verificationStatus = status;
      document.verifiedBy = adminId;
      document.verifiedAt = new Date();

      if (status === VerificationStatus.REJECTED) {
        if (!rejectionReason) {
          return ErrorHandler.handleBadRequest(
            "Rejection reason is required when rejecting a document"
          );
        }
        document.rejectionReason = rejectionReason;
      } else {
        document.rejectionReason = null;
      }

      const updatedDocument = await this.documentsRepository.save(document);

      // Log activity
      await this.activityLogService.logActivity({
        userId: adminId,
        action:
          status === VerificationStatus.VERIFIED
            ? "verify_document"
            : "reject_document",
        entityType: "document",
        entityId: documentId,
        description:
          status === VerificationStatus.VERIFIED
            ? `Verified ${document.documentType} document for user ${user.name}`
            : `Rejected ${document.documentType} document for user ${user.name}: ${rejectionReason}`,
      });

      // Notify user
      await this.notificationsService.create({
        userId,
        title:
          status === VerificationStatus.VERIFIED
            ? "Document Verified"
            : "Document Rejected",
        message:
          status === VerificationStatus.VERIFIED
            ? `Your ${document.documentType} document has been verified`
            : `Your ${document.documentType} document was rejected: ${rejectionReason}`,
        type: "document",
        metadata: { documentId },
        link: "/profile/documents",
      });

      // If all required documents are verified, update KYC status
      if (status === VerificationStatus.VERIFIED) {
        await this.checkAndUpdateKycStatus(userId);
      }

      return updatedDocument;
    } catch (error) {
      this.logger.error(`Error verifying document: ${error.message}`);
      throw ErrorHandler.handleError(error, "Document Verification");
    }
  }

  /**
   * Check if all required documents are verified and update KYC status
   */
  async checkAndUpdateKycStatus(userId: string): Promise<void> {
    try {
      // Get all required document types for KYC
      const requiredDocumentTypes = [
        DocumentType.ID_PROOF,
        DocumentType.ADDRESS_PROOF,
      ];

      // Check if all required documents are verified
      const documents = await this.documentsRepository.find({
        where: { userId },
      });

      const verifiedDocumentTypes = documents
        .filter((doc) => doc.verificationStatus === VerificationStatus.VERIFIED)
        .map((doc) => doc.documentType);

      const allRequiredDocumentsVerified = requiredDocumentTypes.every((type) =>
        verifiedDocumentTypes.includes(type)
      );

      if (allRequiredDocumentsVerified) {
        // Update user KYC status
        const user = await this.findOne(userId);
        user.isKycVerified = true;
        await this.usersRepository.save(user);

        // Log activity
        await this.activityLogService.logActivity({
          userId,
          action: "kyc_verified",
          entityType: "user",
          entityId: userId,
          description: "KYC verification completed",
        });

        // Notify user
        await this.notificationsService.create({
          userId,
          title: "KYC Verification Completed",
          message: "Your KYC verification has been completed successfully",
          type: "kyc",
          link: "/profile",
        });
      }
    } catch (error) {
      this.logger.error(`Error updating KYC status: ${error.message}`);
      // We don't want to throw here as this is a background process
    }
  }

  async updateTrustScore(
    userId: string,
    adminId: string,
    scoreChange: number,
    reason: string,
    relatedEntityType?: string,
    relatedEntityId?: string
  ): Promise<User> {
    const user = await this.findOne(userId);

    const previousScore = user.trustScore;
    const newScore = Math.max(0, Math.min(100, previousScore + scoreChange));

    // Update user trust score
    user.trustScore = newScore;
    const updatedUser = await this.usersRepository.save(user);

    // Create trust score log
    await this.trustScoreLogRepository.save({
      userId,
      previousScore,
      newScore,
      change: scoreChange,
      reason,
      relatedEntityType,
      relatedEntityId,
      adminId,
    });

    // Log activity
    await this.activityLogService.logActivity({
      userId: adminId,
      action: "update_trust_score",
      entityType: "user",
      entityId: userId,
      description: `Updated trust score for user ${user.name} from ${previousScore} to ${newScore}. Reason: ${reason}`,
    });

    // Notify user
    await this.notificationsService.create({
      userId,
      title: "Trust Score Updated",
      message: `Your trust score has been ${
        scoreChange >= 0 ? "increased" : "decreased"
      } by ${Math.abs(scoreChange)} points. Reason: ${reason}`,
      type: "trust_score",
      metadata: { previousScore, newScore, change: scoreChange },
      link: "/profile",
    });

    return updatedUser;
  }

  async banUser(
    userId: string,
    adminId: string,
    reason: string
  ): Promise<User> {
    const user = await this.findOne(userId);
    user.isBanned = true;
    user.isActive = false;

    const bannedUser = await this.usersRepository.save(user);

    // Log activity
    await this.activityLogService.logActivity({
      userId: adminId,
      action: "ban_user",
      entityType: "user",
      entityId: userId,
      description: `Banned user ${user.name}. Reason: ${reason}`,
    });

    // Notify user
    await this.notificationsService.create({
      userId,
      title: "Account Banned",
      message: `Your account has been banned. Reason: ${reason}`,
      type: "alert",
    });

    return bannedUser;
  }

  async unbanUser(userId: string, adminId: string): Promise<User> {
    const user = await this.findOne(userId);
    user.isBanned = false;
    user.isActive = true;

    const unbannedUser = await this.usersRepository.save(user);

    // Log activity
    await this.activityLogService.logActivity({
      userId: adminId,
      action: "unban_user",
      entityType: "user",
      entityId: userId,
      description: `Unbanned user ${user.name}`,
    });

    // Notify user
    await this.notificationsService.create({
      userId,
      title: "Account Unbanned",
      message: "Your account has been unbanned and is now active",
      type: "alert",
    });

    return unbannedUser;
  }

  async findAdmins(): Promise<User[]> {
    return this.usersRepository.find({
      where: { role: UserRole.ADMIN },
    });
  }

  /**
   * Add a device token to a user's device tokens array
   */
  async addDeviceToken(userId: string, deviceToken: string): Promise<User> {
    const user = await this.findOne(userId);

    // Initialize deviceTokens array if it doesn't exist
    if (!user.deviceTokens) {
      user.deviceTokens = [];
    }

    // Only add the token if it doesn't already exist
    if (!user.deviceTokens.includes(deviceToken)) {
      user.deviceTokens.push(deviceToken);
      return this.usersRepository.save(user);
    }

    return user;
  }

  /**
   * Remove a device token from a user's device tokens array
   */
  async removeDeviceToken(userId: string, deviceToken: string): Promise<User> {
    const user = await this.findOne(userId);

    if (user.deviceTokens && user.deviceTokens.includes(deviceToken)) {
      user.deviceTokens = user.deviceTokens.filter(
        (token) => token !== deviceToken
      );
      return this.usersRepository.save(user);
    }

    return user;
  }

  /**
   * Update a user's device tokens array
   */
  async updateDeviceTokens(
    userId: string,
    deviceTokens: string[]
  ): Promise<User> {
    const user = await this.findOne(userId);
    user.deviceTokens = deviceTokens;
    return this.usersRepository.save(user);
  }

  /**
   * Find users by role
   */
  async findByRole(role: UserRole): Promise<User[]> {
    return this.usersRepository.find({
      where: { role, isActive: true, isBanned: false },
    });
  }

  /**
   * Get user skills as an array
   */
  async getUserSkills(userId: string): Promise<string[]> {
    const user = await this.findOne(userId);
    if (user.metadata instanceof Company) return [];
    // Parse comma-separated skills into an array
    return user.metadata.skills
      ? user.metadata.skills.split(",").map((skill) => skill.trim())
      : [];
  }

  /**
   * Update user skills from an array
   */
  async updateUserSkills(
    userId: string,
    skills: string[]
  ): Promise<{ success: boolean }> {
    // Join array of skills into comma-separated string
    const skillsString = skills.join(", ");

    // Create an UpdateProfileDto with just the skills field
    const updateProfileDto: UpdateProfileDto = { skills: skillsString };

    // Save the updated user
    await this.updateProfile(userId, updateProfileDto);

    return { success: true };
  }

  /**
   * Get user statistics
   */
  async getUserStats(userId: string): Promise<{
    completedJobs: number;
    totalEarnings: number;
    averageRating: number;
    totalRatings: number;
    activeApplications: number;
    savedJobs: number;
    jobsInProgress: number;
    trustScore: number;
    memberSince: string;
  }> {
    const user = await this.findOne(userId);

    // For now, we'll return mock data with some real user data
    // In a real implementation, these would be calculated from actual data
    return {
      completedJobs: 15, // TODO: Calculate from actual completed jobs
      totalEarnings: 25000, // TODO: Calculate from actual payments
      averageRating: 4.5, // TODO: Calculate from actual ratings
      totalRatings: 12, // TODO: Calculate from actual ratings count
      activeApplications: 3, // TODO: Calculate from actual active applications
      savedJobs: 7, // TODO: Calculate from actual saved jobs
      jobsInProgress: 2, // TODO: Calculate from actual jobs in progress
      trustScore: user.trustScore || 0,
      memberSince: user.createdAt
        ? user.createdAt.toISOString().split("T")[0]
        : "2024-01-01",
    };
  }

  /**
   * Process and upload a document
   * @param userId User ID
   * @param documentType Type of document
   * @param file File object from multer
   * @param documentNumber Optional document number
   */
  async processAndUploadDocument(
    userId: string,
    documentType: DocumentType,
    file: UploadedFile,
    documentNumber?: string
  ): Promise<Document> {
    if (!file) {
      throw new Error("File is required");
    }

    // In a real application, you would upload the file to a storage service
    // and get a URL. For simplicity, we'll just use a placeholder URL.
    const documentUrl = `https://example.com/documents/${file.originalname}`;

    return this.uploadDocument(
      userId,
      documentType,
      documentUrl,
      documentNumber
    );
  }
}
