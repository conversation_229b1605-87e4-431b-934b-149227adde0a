import { Inject, Injectable } from "@nestjs/common";
import { InjectRepository } from "@nestjs/typeorm";
import type { Repository } from "typeorm";
import { User } from "./entities/user.entity";
import { TrustScoreLog } from "./entities/trust-score-log.entity";
import { ActivityLogService } from "../activity-log/activity-log.service";
import { NotificationsService } from "../notifications/notifications.service";

export interface UpdateTrustScoreParams {
  userId: string;
  change: number;
  reason: string;
  adminId?: string;
  relatedEntityType?: string;
  relatedEntityId?: string;
}

export interface TrustScoreLevel {
  level: string;
  nextLevel: string;
  nextLevelThreshold: number;
}

export interface TrustScoreBreakdown {
  overall: number;
  categories: {
    jobCompletion: number;
    punctuality: number;
    communication: number;
    reliability: number;
  };
}

export interface TrustScoreSuggestion {
  id: string;
  title: string;
  description: string;
  potentialPoints: number;
  completed: boolean;
}

export interface PaginatedTrustScoreLogs {
  logs: TrustScoreLog[];
  total: number;
  limit: number;
  offset: number;
}

@Injectable()
export class TrustScoreService {
  constructor(
    @InjectRepository(User)
    private readonly usersRepository: Repository<User>,
    @InjectRepository(TrustScoreLog)
    private readonly trustScoreLogRepository: Repository<TrustScoreLog>,
    @Inject(ActivityLogService)
    private readonly activityLogService: ActivityLogService,
    @Inject(NotificationsService)
    private readonly notificationsService: NotificationsService
  ) {}

  async updateTrustScore({
    userId,
    change,
    reason,
    adminId,
    relatedEntityType,
    relatedEntityId,
  }: UpdateTrustScoreParams): Promise<User> {
    // Get user
    const user = await this.usersRepository.findOne({ where: { id: userId } });
    if (!user) {
      throw new Error(`User with ID ${userId} not found`);
    }

    // Store previous score
    const previousScore = user.trustScore;

    // Update trust score (ensure it stays within 0-100 range)
    user.trustScore = Math.max(0, Math.min(100, user.trustScore + change));
    const updatedUser = await this.usersRepository.save(user);

    // Log the change
    const trustScoreLog = this.trustScoreLogRepository.create({
      userId,
      previousScore,
      newScore: user.trustScore,
      change,
      reason,
      adminId,
      relatedEntityType,
      relatedEntityId,
    });
    await this.trustScoreLogRepository.save(trustScoreLog);

    // Log activity
    await this.activityLogService.logActivity({
      userId: adminId || userId,
      action: "update_trust_score",
      entityType: "user",
      entityId: userId,
      description: `Trust score updated from ${previousScore} to ${
        user.trustScore
      } (${change > 0 ? "+" : ""}${change}). Reason: ${reason}`,
      metadata: {
        previousScore,
        newScore: user.trustScore,
        change,
        reason,
        relatedEntityType,
        relatedEntityId,
      },
    });

    // Notify user
    await this.notificationsService.create({
      userId,
      title: "Trust Score Updated",
      message: `Your trust score has been ${
        change > 0 ? "increased" : "decreased"
      } by ${Math.abs(change)} points. New score: ${
        user.trustScore
      }. Reason: ${reason}`,
      type: "trust_score",
      metadata: {
        previousScore,
        newScore: user.trustScore,
        change,
      },
    });

    return updatedUser;
  }

  async getTrustScoreLogs(userId: string): Promise<TrustScoreLog[]> {
    return this.trustScoreLogRepository.find({
      where: { userId },
      order: { createdAt: "DESC" },
      relations: ["admin"],
    });
  }

  async getPaginatedTrustScoreLogs(
    userId: string,
    limit: number = 10,
    offset: number = 0
  ): Promise<PaginatedTrustScoreLogs> {
    const logs = await this.getTrustScoreLogs(userId);

    // Apply pagination manually
    const paginatedLogs = logs.slice(offset, offset + limit);

    return {
      logs: paginatedLogs,
      total: logs.length,
      limit,
      offset,
    };
  }

  async getRecentTrustScoreChanges(limit = 10): Promise<TrustScoreLog[]> {
    return this.trustScoreLogRepository.find({
      order: { createdAt: "DESC" },
      take: limit,
      relations: ["user", "admin"],
    });
  }

  async getTrustScoreLevel(
    userId: string
  ): Promise<{ score: number } & TrustScoreLevel> {
    const user = await this.usersRepository.findOne({ where: { id: userId } });
    if (!user) {
      throw new Error(`User with ID ${userId} not found`);
    }

    // Determine level based on score
    let level = "Poor";
    let nextLevel = "Below Average";
    let nextLevelThreshold = 20;

    if (user.trustScore >= 80) {
      level = "Excellent";
      nextLevel = "Maximum";
      nextLevelThreshold = 100;
    } else if (user.trustScore >= 60) {
      level = "Good";
      nextLevel = "Excellent";
      nextLevelThreshold = 80;
    } else if (user.trustScore >= 40) {
      level = "Average";
      nextLevel = "Good";
      nextLevelThreshold = 60;
    } else if (user.trustScore >= 20) {
      level = "Below Average";
      nextLevel = "Average";
      nextLevelThreshold = 40;
    }

    return {
      score: user.trustScore,
      level,
      nextLevel,
      nextLevelThreshold,
    };
  }

  async getTrustScoreBreakdown(userId: string): Promise<TrustScoreBreakdown> {
    const user = await this.usersRepository.findOne({ where: { id: userId } });
    if (!user) {
      throw new Error(`User with ID ${userId} not found`);
    }

    // This is a placeholder implementation
    // In a real application, you would calculate these scores based on user activity
    return {
      overall: user.trustScore,
      categories: {
        jobCompletion: Math.min(100, user.trustScore + 5),
        punctuality: Math.max(0, user.trustScore),
        communication: Math.max(0, user.trustScore - 5),
        reliability: Math.max(0, user.trustScore),
      },
    };
  }

  async getTrustScoreImprovementSuggestions(
    userId: string
  ): Promise<{ suggestions: TrustScoreSuggestion[] }> {
    const user = await this.usersRepository.findOne({ where: { id: userId } });
    if (!user) {
      throw new Error(`User with ID ${userId} not found`);
    }

    // Generate suggestions based on user's current state
    const suggestions: TrustScoreSuggestion[] = [];

    // Profile completeness suggestion
    if (!user.profilePicture) {
      suggestions.push({
        id: "add_profile_picture",
        title: "Add a Profile Picture",
        description:
          "Upload a clear profile picture to increase trust with employers",
        potentialPoints: 3,
        completed: false,
      });
    }

    // KYC verification suggestion
    if (!user.isKycVerified) {
      suggestions.push({
        id: "complete_kyc",
        title: "Complete KYC Verification",
        description:
          "Verify your identity to significantly boost your trust score",
        potentialPoints: 10,
        completed: false,
      });
    }

    // Generic suggestions
    suggestions.push({
      id: "complete_jobs",
      title: "Complete More Jobs",
      description: "Successfully complete jobs to increase your trust score",
      potentialPoints: 5,
      completed: false,
    });

    suggestions.push({
      id: "be_punctual",
      title: "Be Punctual",
      description: "Arrive on time for jobs to maintain a high trust score",
      potentialPoints: 3,
      completed: false,
    });

    return { suggestions };
  }
}
