import {
  Controller,
  Post,
  Body,
  Delete,
  UseGuards,
  Request,
  Inject,
  BadRequestException,
} from "@nestjs/common";
import { JwtAuthGuard } from "../auth/guards/jwt-auth.guard";
import { UsersService } from "./users.service";
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiBearerAuth,
} from "@nestjs/swagger";

@ApiTags("device-tokens")
@Controller("device-tokens")
@UseGuards(JwtAuthGuard)
@ApiBearerAuth()
export class DeviceTokenController {
  constructor(
    @Inject(UsersService)
    private readonly usersService: UsersService
  ) {}

  @Post("register")
  @ApiOperation({ summary: "Register a device token for push notifications" })
  @ApiResponse({ status: 201, description: "Device token registered successfully" })
  async registerDeviceToken(
    @Request() req,
    @Body() body: { token: string }
  ) {
    if (!body.token) {
      throw new BadRequestException("Device token is required");
    }

    const userId = req.user.id;
    await this.usersService.addDeviceToken(userId, body.token);

    return { success: true, message: "Device token registered successfully" };
  }

  @Delete("unregister")
  @ApiOperation({ summary: "Unregister a device token" })
  @ApiResponse({ status: 200, description: "Device token unregistered successfully" })
  async unregisterDeviceToken(
    @Request() req,
    @Body() body: { token: string }
  ) {
    if (!body.token) {
      throw new BadRequestException("Device token is required");
    }

    const userId = req.user.id;
    await this.usersService.removeDeviceToken(userId, body.token);

    return { success: true, message: "Device token unregistered successfully" };
  }
}
