import { ApiPropertyOptional } from "@nestjs/swagger";
import {
  IsOptional,
  IsString,
  Length,
  IsEmail,
  IsPhoneNumber,
  IsEnum,
  MinLength,
  Matches,
  IsBoolean,
} from "class-validator";
import { UserRole } from "../../common/enums/user-role.enum";

export class UpdateUserDto {
  @ApiPropertyOptional({
    description: "User's full name",
    example: "<PERSON> Doe",
  })
  @IsOptional()
  @IsString()
  @Length(2, 100)
  fullName?: string;

  @ApiPropertyOptional({
    description: "User's email address",
    example: "<EMAIL>",
  })
  @IsOptional()
  @IsEmail()
  email?: string;

  @ApiPropertyOptional({
    description: "User's phone number",
    example: "+1234567890",
  })
  @IsOptional()
  @IsPhoneNumber()
  phone?: string;

  @ApiPropertyOptional({
    description:
      "User's password (min 8 chars, must include uppercase, lowercase, and number/special char)",
    example: "NewPassword123!",
  })
  @IsOptional()
  @IsString()
  @MinLength(8)
  @Matches(/((?=.*\d)|(?=.*\W+))(?![.\n])(?=.*[A-Z])(?=.*[a-z]).*$/, {
    message: "Password is too weak",
  })
  password?: string;

  @ApiPropertyOptional({
    description: "User role",
    enum: UserRole,
    example: UserRole.WORKER,
  })
  @IsOptional()
  @IsEnum(UserRole)
  role?: UserRole;

  @ApiPropertyOptional({
    description: "KYC verification status",
    example: true,
  })
  @IsOptional()
  @IsBoolean()
  isKycVerified?: boolean;
}
