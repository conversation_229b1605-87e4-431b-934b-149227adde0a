import { ApiProperty, ApiPropertyOptional } from "@nestjs/swagger";
import {
  IsEmail,
  IsEnum,
  IsNotEmpty,
  IsOptional,
  IsPhoneNumber,
  IsString,
  Length,
  Matches,
  MinLength,
} from "class-validator";
import { UserRole } from "../../common/enums/user-role.enum";

export class CreateUserDto {
  @ApiProperty({
    description: "User's name",
    example: "<PERSON>",
  })
  @IsNotEmpty()
  @IsString()
  @Length(2, 100)
  name: string;

  @ApiPropertyOptional({
    description: "User's email address",
    example: "<EMAIL>",
  })
  @IsOptional()
  @IsEmail()
  email?: string;

  @ApiPropertyOptional({
    description: "User's phone number",
    example: "+1234567890",
  })
  @IsOptional()
  @IsPhoneNumber()
  phone?: string;

  @ApiProperty({
    description:
      "User's password (min 8 chars, must include uppercase, lowercase, and number/special char)",
    example: "Password123!",
  })
  @IsNotEmpty()
  @IsString()
  @MinLength(8)
  @Matches(/((?=.*\d)|(?=.*\W+))(?![.\n])(?=.*[A-Z])(?=.*[a-z]).*$/, {
    message: "Password is too weak",
  })
  password: string;

  @ApiProperty({
    description: "User role",
    enum: UserRole,
    default: UserRole.WORKER,
    example: UserRole.WORKER,
  })
  @IsEnum(UserRole)
  role: UserRole;
}
