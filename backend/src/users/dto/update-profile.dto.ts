import { ApiPropertyOptional } from "@nestjs/swagger"
import { IsString, IsOptional, IsEmail, IsDate, Length, IsISO31661Alpha2, IsPhoneNumber } from "class-validator"
import { Type } from "class-transformer"

export class UpdateProfileDto {
  @ApiPropertyOptional({
    description: "User's full name",
    example: "<PERSON> Doe",
  })
  @IsOptional()
  @IsString()
  @Length(2, 100)
  fullName?: string

  @ApiPropertyOptional({
    description: "User's email address",
    example: "<EMAIL>",
  })
  @IsOptional()
  @IsEmail()
  email?: string

  @ApiPropertyOptional({
    description: "User's phone number",
    example: "+1234567890",
  })
  @IsOptional()
  @IsPhoneNumber()
  phone?: string

  @ApiPropertyOptional({
    description: "User's date of birth",
    example: "1990-01-01",
  })
  @IsOptional()
  @IsDate()
  @Type(() => Date)
  dateOfBirth?: Date

  @ApiPropertyOptional({
    description: "User's street address",
    example: "123 Main St",
  })
  @IsOptional()
  @IsString()
  address?: string

  @ApiPropertyOptional({
    description: "User's city",
    example: "New York",
  })
  @IsOptional()
  @IsString()
  city?: string

  @ApiPropertyOptional({
    description: "User's state or province",
    example: "NY",
  })
  @IsOptional()
  @IsString()
  state?: string

  @ApiPropertyOptional({
    description: "User's postal code",
    example: "10001",
  })
  @IsOptional()
  @IsString()
  postalCode?: string

  @ApiPropertyOptional({
    description: "User's country (ISO 3166-1 alpha-2)",
    example: "US",
  })
  @IsOptional()
  @IsISO31661Alpha2()
  country?: string

  @ApiPropertyOptional({
    description: "User's bio or description",
    example: "Experienced inventory specialist with 5+ years in retail.",
  })
  @IsOptional()
  @IsString()
  @Length(0, 500)
  bio?: string

  @ApiPropertyOptional({
    description: "User's skills (comma separated)",
    example: "Inventory management, Stock auditing, Data entry",
  })
  @IsOptional()
  @IsString()
  skills?: string

  @ApiPropertyOptional({
    description: "User's education background",
    example: "Bachelor's in Business Administration",
  })
  @IsOptional()
  @IsString()
  education?: string

  @ApiPropertyOptional({
    description: "User's work experience",
    example: "5 years at ABC Retail as Inventory Specialist",
  })
  @IsOptional()
  @IsString()
  experience?: string

  @ApiPropertyOptional({
    description: "Languages spoken by user (comma separated)",
    example: "English, Spanish",
  })
  @IsOptional()
  @IsString()
  languages?: string
}
