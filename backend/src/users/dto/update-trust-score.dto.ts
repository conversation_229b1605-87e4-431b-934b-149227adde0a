import { ApiProperty, ApiPropertyOptional } from "@nestjs/swagger"
import { IsN<PERSON>ber, IsString, IsOptional, <PERSON>, Max, Length } from "class-validator"

export class UpdateTrustScoreDto {
  @ApiProperty({
    description: "Trust score change amount",
    minimum: -100,
    maximum: 100,
    example: 10,
  })
  @IsNumber()
  @Min(-100)
  @Max(100)
  score: number

  @ApiProperty({
    description: "Reason for trust score change",
    example: "Completed emergency job successfully",
  })
  @IsString()
  @Length(3, 200)
  reason: string

  @ApiPropertyOptional({
    description: "Related entity type (e.g., 'job', 'application')",
    example: "job",
  })
  @IsOptional()
  @IsString()
  relatedEntityType?: string

  @ApiPropertyOptional({
    description: "Related entity ID",
    example: "550e8400-e29b-41d4-a716-446655440000",
  })
  @IsOptional()
  @IsString()
  relatedEntityId?: string
}
