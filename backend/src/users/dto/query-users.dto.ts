import { ApiPropertyOptional } from "@nestjs/swagger"
import { IsEnum, IsOptional, IsString, IsBoolean } from "class-validator"
import { Transform } from "class-transformer"
import { UserRole } from "../../common/enums/user-role.enum"
import { PaginationDto } from "../../common/dto/pagination.dto"

export class QueryUsersDto extends PaginationDto {
  @ApiPropertyOptional({
    description: "Filter users by role",
    enum: UserRole,
    example: UserRole.WORKER,
  })
  @IsOptional()
  @IsEnum(UserRole)
  role?: UserRole

  @ApiPropertyOptional({
    description: "Search users by name, email, or phone",
    example: "john",
  })
  @IsOptional()
  @IsString()
  search?: string

  @ApiPropertyOptional({
    description: "Filter by KYC verification status",
    type: Boolean,
    example: true,
  })
  @IsOptional()
  @Transform(({ value }) => {
    if (value === "true") return true
    if (value === "false") return false
    return value
  })
  @IsBoolean()
  isKycVerified?: boolean

  @ApiPropertyOptional({
    description: "Filter by active status",
    type: Boolean,
    example: true,
  })
  @IsOptional()
  @Transform(({ value }) => {
    if (value === "true") return true
    if (value === "false") return false
    return value
  })
  @IsBoolean()
  isActive?: boolean

  @ApiPropertyOptional({
    description: "Filter by banned status",
    type: Boolean,
    example: false,
  })
  @IsOptional()
  @Transform(({ value }) => {
    if (value === "true") return true
    if (value === "false") return false
    return value
  })
  @IsBoolean()
  isBanned?: boolean
}
