import { ApiProperty, ApiPropertyOptional } from "@nestjs/swagger";
import { Type } from "class-transformer";
import { UserRole } from "../../common/enums/user-role.enum";
import { DocumentType } from "../../common/enums/document-type.enum";

export class CreateUserSwaggerDto {
  @ApiProperty({ example: "John Doe" })
  fullName: string;

  @ApiPropertyOptional({ example: "<EMAIL>" })
  email?: string;

  @ApiPropertyOptional({ example: "+**********" })
  phone?: string;

  @ApiProperty({ example: "Password123!" })
  password: string;

  @ApiProperty({ enum: Object.values(UserRole), example: UserRole.WORKER })
  role: UserRole;
}

export class UpdateUserSwaggerDto {
  @ApiPropertyOptional({ example: "John Doe" })
  fullName?: string;

  @ApiPropertyOptional({ example: "<EMAIL>" })
  email?: string;

  @ApiPropertyOptional({ example: "+**********" })
  phone?: string;

  @ApiPropertyOptional({ enum: Object.values(UserRole), example: UserRole.WORKER })
  role?: UserRole;
}

export class UpdateProfileSwaggerDto {
  @ApiPropertyOptional({ example: "John Doe" })
  fullName?: string;

  @ApiPropertyOptional({ example: "Experienced professional" })
  bio?: string;

  @ApiPropertyOptional({ example: "https://example.com/avatar.jpg" })
  avatar?: string;

  @ApiPropertyOptional({ 
    type: [String],
    example: ["JavaScript", "React", "Node.js"] 
  })
  skills?: string[];
}

export class UpdateKycSwaggerDto {
  @ApiProperty({ example: "John Michael Doe" })
  fullName: string;

  @ApiProperty({ example: "1990-01-01" })
  @Type(() => Date)
  dateOfBirth: Date;

  @ApiProperty({ example: "123 Main St, Apt 4B" })
  address: string;

  @ApiProperty({ example: "New York" })
  city: string;

  @ApiProperty({ example: "NY" })
  state: string;

  @ApiProperty({ example: "10001" })
  postalCode: string;

  @ApiProperty({ example: "US" })
  country: string;

  @ApiPropertyOptional({ 
    enum: ["passport", "national_id", "drivers_license"],
    example: "passport" 
  })
  idType?: string;

  @ApiPropertyOptional({ example: "AB123456" })
  idNumber?: string;
}

export class UploadDocumentSwaggerDto {
  @ApiProperty({ type: 'string', format: 'binary' })
  file: any;

  @ApiProperty({ 
    enum: Object.values(DocumentType),
    example: DocumentType.ID_PROOF
  })
  type: DocumentType;

  @ApiPropertyOptional({ example: "AB123456" })
  documentNumber?: string;
}

export class VerifyDocumentSwaggerDto {
  @ApiProperty({ 
    enum: ["approved", "rejected"],
    example: "approved" 
  })
  status: string;

  @ApiPropertyOptional({ example: "Document is not clear" })
  rejectionReason?: string;
}

export class UserResponseSwaggerDto {
  @ApiProperty({ example: "550e8400-e29b-41d4-a716-446655440000" })
  id: string;

  @ApiProperty({ example: "John Doe" })
  fullName: string;

  @ApiProperty({ example: "<EMAIL>" })
  email: string;

  @ApiProperty({ example: "+**********" })
  phone: string;

  @ApiProperty({ enum: Object.values(UserRole), example: UserRole.WORKER })
  role: UserRole;

  @ApiPropertyOptional({ example: "Experienced professional" })
  bio?: string;

  @ApiPropertyOptional({ example: "https://example.com/avatar.jpg" })
  avatar?: string;

  @ApiPropertyOptional({ 
    type: [String],
    example: ["JavaScript", "React", "Node.js"] 
  })
  skills?: string[];
}

export class DocumentResponseSwaggerDto {
  @ApiProperty({ example: "550e8400-e29b-41d4-a716-446655440000" })
  id: string;

  @ApiProperty({ enum: Object.values(DocumentType), example: DocumentType.ID_PROOF })
  type: DocumentType;

  @ApiProperty({ example: "https://example.com/documents/passport.jpg" })
  url: string;

  @ApiProperty({ example: "pending" })
  status: string;

  @ApiPropertyOptional({ example: "AB123456" })
  documentNumber?: string;
}

export class PaginatedUsersResponseSwaggerDto {
  @ApiProperty({ type: [UserResponseSwaggerDto] })
  items: UserResponseSwaggerDto[];

  @ApiProperty({ example: 10 })
  total: number;

  @ApiProperty({ example: 1 })
  page: number;

  @ApiProperty({ example: 10 })
  limit: number;
}