import {
  <PERSON><PERSON><PERSON>,
  PrimaryGenerated<PERSON><PERSON>umn,
  <PERSON>umn,
  CreateDateColumn,
  UpdateDateColumn,
  OneToOne,
  JoinColumn,
} from "typeorm";
import { Transform } from "class-transformer";
import { ApiProperty, ApiPropertyOptional } from "@nestjs/swagger";
import { User } from "./user.entity";

export class Worker {
  //   @ApiProperty({ description: "Unique identifier" })
  //   @PrimaryGeneratedColumn("uuid")
  //   id: string;

  @ApiProperty({
    description: "KYC verification status",
    default: false,
  })
  @Column({ default: false })
  isKycVerified: boolean;

  @ApiPropertyOptional({ description: "Date of birth" })
  @Column({ nullable: true })
  @Transform(({ value }) => value && new Date(value))
  dateOfBirth?: Date;

  @ApiPropertyOptional({ description: "Skills (comma separated)" })
  @Column({ nullable: true })
  skills?: string;

  @ApiPropertyOptional({ description: "Education background" })
  @Column({ nullable: true })
  education?: string;

  @ApiPropertyOptional({ description: "Work experience" })
  @Column({ nullable: true })
  experience?: string;

  @ApiPropertyOptional({ description: "Languages (comma separated)" })
  @Column({ nullable: true })
  languages?: string;

  //   @ApiProperty({ description: "Creation date" })
  //   @CreateDateColumn()
  //   @Transform(({ value }) => value && new Date(value))
  //   createdAt: Date;

  //   @ApiProperty({ description: "Last update date" })
  //   @UpdateDateColumn()
  //   @Transform(({ value }) => value && new Date(value))
  //   updatedAt: Date;
}
