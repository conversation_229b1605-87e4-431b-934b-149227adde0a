import {
  <PERSON><PERSON><PERSON>,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  OneToMany,
  Index,
} from "typeorm";
import { Exclude, Transform, Type } from "class-transformer";
import { ApiProperty, ApiPropertyOptional } from "@nestjs/swagger";
import { UserRole } from "../../common/enums/user-role.enum";
import { Document } from "./document.entity";
import { TrustScoreLog } from "./trust-score-log.entity";
import { Payout } from "src/payouts/entities/payout.entity";
import { Application } from "src/applications/entities/application.entity";
import { Dispute } from "src/disputes/entities/dispute.entity";
import { Rating } from "src/ratings/entities/rating.entity";
import { Favorite } from "src/favorites/entities/favorite.entity";
import { Worker } from "./worker.entity";
import { Company } from "src/companies/entities/company.entity";
import { Job } from "src/jobs/entities/job.entity";
import { ValidateNested } from "class-validator";

@Entity("users")
export class User {
  @ApiProperty({ description: "Unique identifier" })
  @PrimaryGeneratedColumn("uuid")
  id: string;

  @ApiProperty({ description: "Username" })
  @Column()
  @Index()
  username: string;

  @ApiPropertyOptional({ description: "Email address" })
  @Column({ nullable: true })
  @Index()
  email?: string;

  @ApiPropertyOptional({ description: "Phone number" }) //TODO:make it required
  @Column({ nullable: true })
  @Index()
  phone?: string;

  @ApiProperty({ description: "Name (can be a company or a user name)" })
  @Column()
  name: string;

  @Exclude()
  @Column({ select: false })
  password: string;

  @ApiProperty({
    description: "User role",
    enum: UserRole,
    default: UserRole.WORKER,
  })
  @Column({
    type: "enum",
    enum: UserRole,
    default: UserRole.WORKER,
  })
  role: UserRole;

  @ApiProperty({
    description: "Trust score (0-100)",
    default: 70,
  })
  @Column({ default: 70 })
  trustScore: number;

  @ApiProperty({
    description: "KYC verification status",
    default: false,
  })
  @Column({ default: false })
  isKycVerified: boolean;

  @ApiPropertyOptional({ description: "Company verification date" })
  @Column({ nullable: true })
  @Transform(({ value }) => value && new Date(value))
  kycVerificationDate?: Date;

  @ApiProperty({
    description: "Account active status",
    default: true,
  })
  @Column({ default: true })
  isActive: boolean;

  @ApiProperty({
    description: "Account banned status",
    default: false,
  })
  @Column({ default: false })
  isBanned: boolean;

  @ApiPropertyOptional({ description: "Profile picture URL" })
  @Column({ nullable: true })
  profilePicture?: string;

  @ApiPropertyOptional({ description: "Street address" })
  @Column({ nullable: true })
  address?: string;

  @ApiPropertyOptional({ description: "City" })
  @Column({ nullable: true })
  city?: string;

  @ApiPropertyOptional({ description: "State/Province" })
  @Column({ nullable: true })
  state?: string;

  @ApiPropertyOptional({ description: "Postal code" })
  @Column({ nullable: true })
  postalCode?: string;

  @ApiPropertyOptional({ description: "Country" })
  @Column({ nullable: true })
  country?: string;

  @ApiPropertyOptional({ description: "Bio" })
  @Column({ nullable: true })
  bio?: string;

  @ApiPropertyOptional({ description: "UPI ID for payments" })
  @Column({ nullable: true })
  upiId?: string;

  @ApiPropertyOptional({ description: "Device tokens for push notifications" })
  @Column({ type: "simple-array", nullable: true })
  deviceTokens?: string[];

  @ApiProperty({
    description: "Email verification status",
    default: false,
  })
  @Column({ default: false })
  emailVerified: boolean;

  @ApiProperty({
    description: "Phone verification status",
    default: false,
  })
  @Column({ default: false })
  phoneVerified: boolean;

  @ApiPropertyOptional({
    description:
      "Metadata this will hold any additional information about the worker/company",
  })
  @ValidateNested()
  @Type((options) => {
    const role = options?.object?.role;
    return role === UserRole.WORKER ? Worker : Company;
  })
  @Column({ type: "jsonb", nullable: true })
  metadata?: Worker | Company;

  @ApiPropertyOptional({ description: "Last login date" })
  @Column({ nullable: true })
  @Transform(({ value }) => value && new Date(value))
  lastLoginAt?: Date;

  @ApiProperty({ description: "Creation date" })
  @CreateDateColumn()
  @Transform(({ value }) => value && new Date(value))
  createdAt: Date;

  @ApiProperty({ description: "Last update date" })
  @UpdateDateColumn()
  @Transform(({ value }) => value && new Date(value))
  updatedAt: Date;

  // Relations
  @OneToMany(() => Document, (document) => document.user)
  documents: Document[];

  @OneToMany(() => Job, (job) => job.company)
  jobs: Job[];

  @OneToMany(() => TrustScoreLog, (log) => log.user)
  trustScoreLogs: TrustScoreLog[];

  @OneToMany(() => Payout, (payout) => payout.worker)
  payouts: Payout[];

  @OneToMany(() => Application, (application) => application.worker)
  applications: Application[];

  @OneToMany(() => Dispute, (dispute) => dispute.raisedBy)
  raisedDisputes: Dispute[];

  @OneToMany(() => Dispute, (dispute) => dispute.againstUser)
  receivedDisputes: Dispute[];

  @OneToMany(() => Rating, (dispute) => dispute.ratedBy)
  givenRatings: Rating[];

  @OneToMany(() => Rating, (dispute) => dispute.ratedUser)
  receivedRatings: Rating[];

  @OneToMany(() => Favorite, (dispute) => dispute.worker)
  favorites: Favorite[];
}
