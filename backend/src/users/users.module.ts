import { Module, forwardRef } from "@nestjs/common";
import { TypeOrmModule } from "@nestjs/typeorm";
import { UsersService } from "./users.service";
import { UsersController } from "./users.controller";
import { DeviceTokenController } from "./device-token.controller";
import { User } from "./entities/user.entity";
import { Document } from "./entities/document.entity";
import { TrustScoreLog } from "./entities/trust-score-log.entity";
import { ActivityLogModule } from "../activity-log/activity-log.module";
import { NotificationsModule } from "../notifications/notifications.module";
import { CommonModule } from "../common/common.module";
import { TrustScoreService } from "./trust-score.service";
import { JobsModule } from "../jobs/jobs.module";
import { PaymentsModule } from "../payments/payments.module";
import { RatingsModule } from "../ratings/ratings.module";

@Module({
  imports: [
    TypeOrmModule.forFeature([User, Document, TrustScoreLog]),
    ActivityLogModule,
    CommonModule,
    forwardRef(() => NotificationsModule),
    forwardRef(() => JobsModule),
    forwardRef(() => PaymentsModule),
    forwardRef(() => RatingsModule),
  ],
  controllers: [UsersController, DeviceTokenController],
  providers: [UsersService, TrustScoreService],
  exports: [UsersService, TrustScoreService],
})
export class UsersModule {}
