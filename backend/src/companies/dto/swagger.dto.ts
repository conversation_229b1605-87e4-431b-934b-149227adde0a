import { ApiProperty, ApiPropertyOptional } from "@nestjs/swagger";

export class CreateCompanySwaggerDto {
  @ApiProperty({ example: "Acme Corporation" })
  name: string;

  @ApiPropertyOptional({ example: "REG123456789" })
  registrationNumber?: string;

  @ApiPropertyOptional({ example: "TAX123456789" })
  taxId?: string;

  @ApiPropertyOptional({ example: "https://www.acmecorp.com" })
  website?: string;

  @ApiPropertyOptional({ example: "50-100" })
  size?: string;

  @ApiPropertyOptional({ example: "Technology" })
  industry?: string;

  @ApiPropertyOptional({ example: "Acme Corporation is a leading provider of technology solutions..." })
  description?: string;

  @ApiPropertyOptional({ example: "123 Business Ave" })
  address?: string;

  @ApiPropertyOptional({ example: "San Francisco" })
  city?: string;

  @ApiPropertyOptional({ example: "CA" })
  state?: string;

  @ApiPropertyOptional({ example: "94105" })
  postalCode?: string;

  @ApiPropertyOptional({ example: "US" })
  country?: string;
}

export class UpdateCompanySwaggerDto {
  @ApiPropertyOptional({ example: "Acme Corporation" })
  name?: string;

  @ApiPropertyOptional({ example: "REG123456789" })
  registrationNumber?: string;

  @ApiPropertyOptional({ example: "TAX123456789" })
  taxId?: string;

  @ApiPropertyOptional({ example: "https://www.acmecorp.com" })
  website?: string;

  @ApiPropertyOptional({ example: "50-100" })
  size?: string;

  @ApiPropertyOptional({ example: "Technology" })
  industry?: string;

  @ApiPropertyOptional({ example: "Acme Corporation is a leading provider of technology solutions..." })
  description?: string;
}

export class UpdateCompanyKycSwaggerDto {
  @ApiProperty({ example: "REG123456789" })
  registrationNumber: string;

  @ApiProperty({ example: "TAX123456789" })
  taxId: string;

  @ApiPropertyOptional({ example: "Company was established in 2010" })
  notes?: string;
}

export class CompanyResponseSwaggerDto {
  @ApiProperty({ example: "550e8400-e29b-41d4-a716-446655440000" })
  id: string;

  @ApiProperty({ example: "Acme Corporation" })
  name: string;

  @ApiPropertyOptional({ example: "REG123456789" })
  registrationNumber?: string;

  @ApiPropertyOptional({ example: "TAX123456789" })
  taxId?: string;

  @ApiPropertyOptional({ example: "https://www.acmecorp.com" })
  website?: string;

  @ApiPropertyOptional({ example: "50-100" })
  size?: string;

  @ApiPropertyOptional({ example: "Technology" })
  industry?: string;

  @ApiPropertyOptional({ example: "Acme Corporation is a leading provider of technology solutions..." })
  description?: string;

  @ApiPropertyOptional({ example: "https://example.com/logo.jpg" })
  logo?: string;

  @ApiProperty({ example: true })
  isKycVerified: boolean;

  @ApiProperty({ example: true })
  isActive: boolean;

  @ApiProperty({ example: false })
  isBanned: boolean;

  @ApiProperty({ example: "2023-01-01T00:00:00.000Z" })
  createdAt: Date;

  @ApiProperty({ example: "2023-01-01T00:00:00.000Z" })
  updatedAt: Date;
}
