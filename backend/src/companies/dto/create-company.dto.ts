import { ApiProperty, ApiPropertyOptional } from "@nestjs/swagger";
import {
  IsEmail,
  IsNotEmpty,
  IsOptional,
  IsPhoneNumber,
  IsString,
  IsUrl,
  Length,
} from "class-validator";

export class CreateCompanyDto {
  @ApiProperty({
    description: "Company name",
    example: "Acme Corporation",
  })
  @IsNotEmpty()
  @IsString()
  @Length(2, 100)
  name: string;

  @ApiPropertyOptional({
    description: "Company registration number",
    example: "REG123456789",
  })
  @IsOptional()
  @IsString()
  registrationNumber?: string;

  @ApiPropertyOptional({
    description: "Company tax ID",
    example: "TAX123456789",
  })
  @IsOptional()
  @IsString()
  taxId?: string;

  @ApiPropertyOptional({
    description: "Company website",
    example: "https://www.acmecorp.com",
  })
  @IsOptional()
  @IsUrl()
  website?: string;

  @ApiPropertyOptional({
    description: "Company size (number of employees)",
    example: "50-100",
  })
  @IsOptional()
  @IsString()
  size?: string;

  @ApiPropertyOptional({
    description: "Company industry",
    example: "Technology",
  })
  @IsOptional()
  @IsString()
  industry?: string;

  @ApiPropertyOptional({
    description: "Company description",
    example: "Acme Corporation is a leading provider of technology solutions...",
  })
  @IsOptional()
  @IsString()
  description?: string;

  @ApiPropertyOptional({
    description: "Street address",
    example: "123 Business Ave",
  })
  @IsOptional()
  @IsString()
  address?: string;

  @ApiPropertyOptional({
    description: "City",
    example: "San Francisco",
  })
  @IsOptional()
  @IsString()
  city?: string;

  @ApiPropertyOptional({
    description: "State/Province",
    example: "CA",
  })
  @IsOptional()
  @IsString()
  state?: string;

  @ApiPropertyOptional({
    description: "Postal code",
    example: "94105",
  })
  @IsOptional()
  @IsString()
  postalCode?: string;

  @ApiPropertyOptional({
    description: "Country",
    example: "US",
  })
  @IsOptional()
  @IsString()
  country?: string;
}
