import { ApiProperty, ApiPropertyOptional } from "@nestjs/swagger";
import { IsNotEmpty, IsOptional, IsString } from "class-validator";

export class UpdateCompanyKycDto {
  @ApiProperty({
    description: "Company registration number",
    example: "REG123456789",
  })
  @IsNotEmpty()
  @IsString()
  registrationNumber: string;

  @ApiProperty({
    description: "Company tax ID",
    example: "TAX123456789",
  })
  @IsNotEmpty()
  @IsString()
  taxId: string;

  @ApiPropertyOptional({
    description: "Additional notes",
    example: "Company was established in 2010",
  })
  @IsOptional()
  @IsString()
  notes?: string;
}
