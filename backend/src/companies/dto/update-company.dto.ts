import { PartialType } from "@nestjs/mapped-types";
import { ApiPropertyOptional } from "@nestjs/swagger";
import {
  IsOptional,
  IsString,
  IsUrl,
  Length,
} from "class-validator";
import { CreateCompanyDto } from "./create-company.dto";

export class UpdateCompanyDto extends PartialType(CreateCompanyDto) {
  @ApiPropertyOptional({
    description: "Company name",
    example: "Acme Corporation",
  })
  @IsOptional()
  @IsString()
  @Length(2, 100)
  name?: string;

  @ApiPropertyOptional({
    description: "Company registration number",
    example: "REG123456789",
  })
  @IsOptional()
  @IsString()
  registrationNumber?: string;

  @ApiPropertyOptional({
    description: "Company tax ID",
    example: "TAX123456789",
  })
  @IsOptional()
  @IsString()
  taxId?: string;

  @ApiPropertyOptional({
    description: "Company website",
    example: "https://www.acmecorp.com",
  })
  @IsOptional()
  @IsUrl()
  website?: string;

  @ApiPropertyOptional({
    description: "Company size (number of employees)",
    example: "50-100",
  })
  @IsOptional()
  @IsString()
  size?: string;

  @ApiPropertyOptional({
    description: "Company industry",
    example: "Technology",
  })
  @IsOptional()
  @IsString()
  industry?: string;

  @ApiPropertyOptional({
    description: "Company description",
    example: "Acme Corporation is a leading provider of technology solutions...",
  })
  @IsOptional()
  @IsString()
  description?: string;
}
