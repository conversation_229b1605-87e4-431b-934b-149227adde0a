import { Test, TestingModule } from '@nestjs/testing';
import { getRepositoryToken } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { CompaniesService } from './companies.service';
import { Company } from './entities/company.entity';
import { User } from '../users/entities/user.entity';
import { Document } from '../users/entities/document.entity';
import { ActivityLogService } from '../activity-log/activity-log.service';
import { NotificationsService } from '../notifications/notifications.service';
import { CreateCompanyDto } from './dto/create-company.dto';
import { UpdateCompanyDto } from './dto/update-company.dto';
import { UserRole } from '../common/enums/user-role.enum';
import { DocumentType } from '../common/enums/document-type.enum';
import { VerificationStatus } from '../common/enums/verification-status.enum';

//TODO: fix the test cases
const mockCompanyRepository = () => ({
  create: jest.fn(),
  save: jest.fn(),
  findOne: jest.fn(),
  find: jest.fn(),
  createQueryBuilder: jest.fn(() => ({
    leftJoinAndSelect: jest.fn().mockReturnThis(),
    where: jest.fn().mockReturnThis(),
    andWhere: jest.fn().mockReturnThis(),
    orderBy: jest.fn().mockReturnThis(),
    skip: jest.fn().mockReturnThis(),
    take: jest.fn().mockReturnThis(),
    getManyAndCount: jest.fn(),
  })),
  remove: jest.fn(),
});

const mockUserRepository = () => ({
  findOne: jest.fn(),
});

const mockDocumentRepository = () => ({
  findOne: jest.fn(),
  create: jest.fn(),
  save: jest.fn(),
  count: jest.fn(),
});

const mockActivityLogService = () => ({
  logActivity: jest.fn(),
});

const mockNotificationsService = () => ({
  create: jest.fn(),
});

describe('CompaniesService', () => {
  let service: CompaniesService;
  let companyRepository: Repository<Company>;
  let userRepository: Repository<User>;
  let documentRepository: Repository<Document>;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        CompaniesService,
        {
          provide: getRepositoryToken(Company),
          useFactory: mockCompanyRepository,
        },
        {
          provide: getRepositoryToken(User),
          useFactory: mockUserRepository,
        },
        {
          provide: getRepositoryToken(Document),
          useFactory: mockDocumentRepository,
        },
        {
          provide: ActivityLogService,
          useFactory: mockActivityLogService,
        },
        {
          provide: NotificationsService,
          useFactory: mockNotificationsService,
        },
      ],
    }).compile();

    service = module.get<CompaniesService>(CompaniesService);
    companyRepository = module.get<Repository<Company>>(getRepositoryToken(Company));
    userRepository = module.get<Repository<User>>(getRepositoryToken(User));
    documentRepository = module.get<Repository<Document>>(getRepositoryToken(Document));
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe('create', () => {
    it('should create a new company', async () => {
      const userId = 'test-user-id';
      const createCompanyDto: CreateCompanyDto = {
        name: 'Test Company',
        industry: 'Technology',
      };
      
      const user = { id: userId, role: UserRole.COMPANY };
      const savedCompany = { id: 'test-company-id', ...createCompanyDto, userId };
      
      jest.spyOn(userRepository, 'findOne').mockResolvedValue(user as User);
      jest.spyOn(companyRepository, 'findOne').mockResolvedValue(null);
      jest.spyOn(companyRepository, 'create').mockReturnValue(createCompanyDto as unknown as Company);
      jest.spyOn(companyRepository, 'save').mockResolvedValue(savedCompany as Company);
      
      const result = await service.create(userId, createCompanyDto);
      
      expect(userRepository.findOne).toHaveBeenCalledWith({ where: { id: userId } });
      expect(companyRepository.findOne).toHaveBeenCalledWith({ where: { userId } });
      expect(companyRepository.create).toHaveBeenCalledWith({
        ...createCompanyDto,
        userId,
      });
      expect(companyRepository.save).toHaveBeenCalled();
      expect(result).toEqual(savedCompany);
    });
  });

  describe('findAll', () => {
    it('should return paginated companies', async () => {
      const queryCompaniesDto = { page: 1, limit: 10 };
      const companies = [{ id: 'test-company-id', name: 'Test Company' }];
      const total = 1;
      
      const queryBuilder = companyRepository.createQueryBuilder();
      jest.spyOn(queryBuilder, 'getManyAndCount').mockResolvedValue([companies as Company[], total]);
      
      const result = await service.findAll(queryCompaniesDto);
      
      expect(queryBuilder.getManyAndCount).toHaveBeenCalled();
      expect(result).toEqual({
        data: companies,
        meta: {
          total,
          page: queryCompaniesDto.page,
          limit: queryCompaniesDto.limit,
          totalPages: Math.ceil(total / queryCompaniesDto.limit),
        },
      });
    });
  });

  describe('findOne', () => {
    it('should find a company by id', async () => {
      const companyId = 'test-company-id';
      const company = { id: companyId, name: 'Test Company' };
      
      jest.spyOn(companyRepository, 'findOne').mockResolvedValue(company as Company);
      
      const result = await service.findOne(companyId);
      
      expect(companyRepository.findOne).toHaveBeenCalledWith({
        where: { id: companyId },
        relations: ['user', 'documents'],
      });
      expect(result).toEqual(company);
    });
  });

  describe('update', () => {
    it('should update a company', async () => {
      const companyId = 'test-company-id';
      const updateCompanyDto: UpdateCompanyDto = { name: 'Updated Company' };
      const company = { id: companyId, name: 'Test Company' };
      const updatedCompany = { ...company, ...updateCompanyDto };
      
      jest.spyOn(service, 'findOne').mockResolvedValue(company as Company);
      jest.spyOn(companyRepository, 'save').mockResolvedValue(updatedCompany as Company);
      
      const result = await service.update(companyId, updateCompanyDto);
      
      expect(service.findOne).toHaveBeenCalledWith(companyId);
      expect(companyRepository.save).toHaveBeenCalledWith({
        ...company,
        ...updateCompanyDto,
      });
      expect(result).toEqual(updatedCompany);
    });
  });

  describe('uploadDocument', () => {
    it('should upload a new document for a company', async () => {
      const companyId = 'test-company-id';
      const documentType = DocumentType.COMPANY_REGISTRATION;
      const documentUrl = 'https://example.com/document.pdf';
      const documentNumber = 'DOC123';
      
      const company = { id: companyId, name: 'Test Company' };
      const document = {
        id: 'test-document-id',
        companyId,
        documentType,
        documentUrl,
        documentNumber,
        verificationStatus: VerificationStatus.PENDING,
      };
      
      jest.spyOn(service, 'findOne').mockResolvedValue(company as Company);
      jest.spyOn(documentRepository, 'findOne').mockResolvedValue(null);
      jest.spyOn(documentRepository, 'create').mockReturnValue(document as Document);
      jest.spyOn(documentRepository, 'save').mockResolvedValue(document as Document);
      
      const result = await service.uploadDocument(companyId, documentType, documentUrl, documentNumber);
      
      expect(service.findOne).toHaveBeenCalledWith(companyId);
      expect(documentRepository.findOne).toHaveBeenCalledWith({
        where: {
          companyId,
          documentType,
        },
      });
      expect(documentRepository.create).toHaveBeenCalledWith({
        companyId,
        documentType,
        documentUrl,
        documentNumber,
        verificationStatus: VerificationStatus.PENDING,
      });
      expect(documentRepository.save).toHaveBeenCalled();
      expect(result).toEqual(document);
    });
  });
});
