import { Modu<PERSON> } from "@nestjs/common";
import { TypeOrmModule } from "@nestjs/typeorm";
import { CompaniesService } from "./companies.service";
import { CompaniesController } from "./companies.controller";
import { Company } from "./entities/company.entity";
import { User } from "../users/entities/user.entity";
import { Document } from "../users/entities/document.entity";
import { ActivityLogModule } from "../activity-log/activity-log.module";
import { NotificationsModule } from "../notifications/notifications.module";

@Module({
  imports: [
    TypeOrmModule.forFeature([Company, User, Document]),
    ActivityLogModule,
    NotificationsModule,
  ],
  controllers: [CompaniesController],
  providers: [CompaniesService],
  exports: [CompaniesService],
})
export class CompaniesModule {}
