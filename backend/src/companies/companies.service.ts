import { Injectable, Logger, NotFoundException, Inject } from "@nestjs/common";
import { InjectRepository } from "@nestjs/typeorm";
import { Repository, In } from "typeorm";
import { Company } from "./entities/company.entity";
import { CreateCompanyDto } from "./dto/create-company.dto";
import { UpdateCompanyDto } from "./dto/update-company.dto";
import { QueryCompaniesDto } from "./dto/query-companies.dto";
import { UpdateCompanyKycDto } from "./dto/update-company-kyc.dto";
import { User } from "../users/entities/user.entity";
import { Document } from "../users/entities/document.entity";
import { DocumentType } from "../common/enums/document-type.enum";
import { VerificationStatus } from "../common/enums/verification-status.enum";
import { ActivityLogService } from "../activity-log/activity-log.service";
import { NotificationsService } from "../notifications/notifications.service";
import { Error<PERSON>and<PERSON> } from "../common/utils/error-handler.util";

@Injectable()
export class CompaniesService {
  private readonly logger = new Logger(CompaniesService.name);

  constructor(
    @InjectRepository(Company)
    private readonly companiesRepository: Repository<Company>,
    @InjectRepository(User)
    private readonly usersRepository: Repository<User>,
    @InjectRepository(Document)
    private readonly documentsRepository: Repository<Document>,
    @Inject(ActivityLogService)
    private readonly activityLogService: ActivityLogService,
    @Inject(NotificationsService)
    private readonly notificationsService: NotificationsService
  ) {}

  async create(
    userId: string,
    createCompanyDto: CreateCompanyDto
  ): Promise<Company> {
    try {
      // Check if user exists
      const user = await this.usersRepository.findOne({
        where: { id: userId },
      });
      if (!user) {
        return ErrorHandler.handleNotFound("User", userId);
      }

      // Check if company already exists for this user
      const existingCompany = await this.companiesRepository.findOne({
        // where: { userId },
      });
      if (existingCompany) {
        return ErrorHandler.handleBadRequest(
          "Company already exists for this user"
        );
      }

      // Create company
      const company = this.companiesRepository.create({
        ...createCompanyDto,
        // userId,
      });

      const savedCompany = await this.companiesRepository.save(company);

      // Log activity
      await this.activityLogService.logActivity({
        userId,
        action: "create_company",
        entityType: "company",
        entityId: savedCompany.id,
        description: `Created company with ID: ${savedCompany.id}`,
      });

      return savedCompany;
    } catch (error) {
      this.logger.error(
        `Error creating company for user ${userId}: ${
          error instanceof Error ? error.message : "Unknown error"
        }`
      );
      throw ErrorHandler.handleError(error, "Company");
    }
  }

  async findAll(queryCompaniesDto: QueryCompaniesDto) {
    const {
      page = 1,
      limit = 10,
      sortBy = "createdAt",
      sortOrder = "DESC",
      search,
      industry,
      isKycVerified,
      isActive,
    } = queryCompaniesDto;

    try {
      const queryBuilder =
        this.companiesRepository.createQueryBuilder("company");
      queryBuilder.leftJoinAndSelect("company.user", "user");

      // Apply filters
      if (search) {
        queryBuilder.andWhere(
          "company.name ILIKE :search OR company.industry ILIKE :search",
          { search: `%${search}%` }
        );
      }

      if (industry) {
        queryBuilder.andWhere("company.industry = :industry", { industry });
      }

      if (isKycVerified !== undefined) {
        queryBuilder.andWhere("company.isKycVerified = :isKycVerified", {
          isKycVerified,
        });
      }

      if (isActive !== undefined) {
        queryBuilder.andWhere("company.isActive = :isActive", { isActive });
      }

      // Apply sorting
      queryBuilder.orderBy(
        `company.${sortBy}`,
        sortOrder === "ASC" ? "ASC" : "DESC"
      );

      // Apply pagination
      queryBuilder.skip((page - 1) * limit);
      queryBuilder.take(limit);

      const [companies, total] = await queryBuilder.getManyAndCount();

      return {
        data: companies,
        meta: {
          total,
          page,
          limit,
          totalPages: Math.ceil(total / limit),
        },
      };
    } catch (error) {
      this.logger.error(
        `Error finding companies: ${
          error instanceof Error ? error.message : "Unknown error"
        }`
      );
      throw ErrorHandler.handleError(error, "Company");
    }
  }

  async findOne(id: string): Promise<Company> {
    try {
      const company = await this.companiesRepository.findOne({
        where: { id },
        relations: ["user", "documents"],
      });

      if (!company) {
        return ErrorHandler.handleNotFound("Company", id);
      }

      return company;
    } catch (error) {
      this.logger.error(
        `Error finding company with ID ${id}: ${
          error instanceof Error ? error.message : "Unknown error"
        }`
      );
      throw ErrorHandler.handleError(error, "Company");
    }
  }

  async findByUserId(userId: string): Promise<Company> {
    try {
      const company = await this.companiesRepository.findOne({
        // where: { userId },
        relations: ["user", "documents"],
      });

      if (!company) {
        return ErrorHandler.handleNotFound("Company for user", userId);
      }

      return company;
    } catch (error) {
      this.logger.error(
        `Error finding company for user ${userId}: ${
          error instanceof Error ? error.message : "Unknown error"
        }`
      );
      throw ErrorHandler.handleError(error, "Company");
    }
  }

  async update(
    id: string,
    updateCompanyDto: UpdateCompanyDto
  ): Promise<Company> {
    try {
      const company = await this.findOne(id);

      // Update company
      Object.assign(company, updateCompanyDto);
      return this.companiesRepository.save(company);
    } catch (error) {
      this.logger.error(
        `Error updating company ${id}: ${
          error instanceof Error ? error.message : "Unknown error"
        }`
      );
      throw ErrorHandler.handleError(error, "Company");
    }
  }

  /**
   * Update a company with permission check
   * @param id The company ID
   * @param updateCompanyDto The update data
   * @param userId The ID of the user making the request
   * @param userRole The role of the user making the request
   * @returns The updated company
   */
  async updateWithPermissionCheck(
    id: string,
    updateCompanyDto: UpdateCompanyDto,
    userId: string,
    userRole: string
  ): Promise<Company> {
    try {
      // If admin, allow updating any company
      // If company, only allow updating own company
      if (userRole !== "admin") {
        // Get company by user ID
        const company = await this.findByUserId(userId);
        if (company.id !== id) {
          return ErrorHandler.handleBadRequest(
            "You can only update your own company"
          );
        }
      }

      return this.update(id, updateCompanyDto);
    } catch (error) {
      this.logger.error(
        `Error updating company ${id}: ${
          error instanceof Error ? error.message : "Unknown error"
        }`
      );
      throw ErrorHandler.handleError(error, "Company");
    }
  }

  async updateKyc(
    id: string,
    updateCompanyKycDto: UpdateCompanyKycDto
  ): Promise<Company> {
    try {
      const company = await this.findOne(id);

      // Update KYC data
      Object.assign(company, {
        registrationNumber: updateCompanyKycDto.registrationNumber,
        taxId: updateCompanyKycDto.taxId,
      });

      // Log activity
      // await this.activityLogService.logActivity({
      //   userId: company.userId,
      //   action: "update_company_kyc",
      //   entityType: "company",
      //   entityId: id,
      //   description: "Updated company KYC information",
      // });

      return this.companiesRepository.save(company);
    } catch (error) {
      this.logger.error(
        `Error updating company KYC ${id}: ${
          error instanceof Error ? error.message : "Unknown error"
        }`
      );
      throw ErrorHandler.handleError(error, "Company");
    }
  }

  async remove(id: string): Promise<void> {
    const company = await this.findOne(id);
    await this.companiesRepository.remove(company);
  }

  async uploadDocument(
    companyId: string,
    documentType: DocumentType,
    documentUrl: string,
    documentNumber?: string
  ): Promise<Document> {
    const company = await this.findOne(companyId);

    // Check if document of this type already exists
    const existingDoc = await this.documentsRepository.findOne({
      where: {
        companyId,
        documentType,
      },
    });

    if (existingDoc) {
      // Update existing document
      existingDoc.documentUrl = documentUrl;
      if (documentNumber) {
        existingDoc.documentNumber = documentNumber;
      }
      existingDoc.verificationStatus = VerificationStatus.PENDING;

      return this.documentsRepository.save(existingDoc);
    }

    // Create new document
    const document = this.documentsRepository.create({
      companyId,
      documentType,
      documentUrl,
      documentNumber,
      verificationStatus: VerificationStatus.PENDING,
    });

    return this.documentsRepository.save(document);
  }

  /**
   * Process and upload a document for a company
   * @param userId The ID of the user making the request
   * @param file The uploaded file
   * @param documentType The type of document
   * @param documentNumber Optional document number
   * @returns The uploaded document
   */
  async processAndUploadDocument(
    userId: string,
    file: any,
    documentType: DocumentType,
    documentNumber?: string
  ): Promise<Document> {
    try {
      if (!file) {
        return ErrorHandler.handleBadRequest("File is required");
      }

      if (!Object.values(DocumentType).includes(documentType)) {
        return ErrorHandler.handleBadRequest("Invalid document type");
      }

      // Get company by user ID
      const company = await this.findByUserId(userId);

      // TODO: Upload file to storage service and get URL
      // For now, we'll use a placeholder URL
      const documentUrl = `https://example.com/documents/${file.originalname}`;

      return this.uploadDocument(
        company.id,
        documentType,
        documentUrl,
        documentNumber
      );
    } catch (error) {
      this.logger.error(
        `Error uploading document: ${
          error instanceof Error ? error.message : "Unknown error"
        }`
      );
      throw ErrorHandler.handleError(error, "Document");
    }
  }

  async verifyDocument(
    documentId: string,
    adminId: string,
    status: VerificationStatus,
    rejectionReason?: string
  ): Promise<Document> {
    const document = await this.documentsRepository.findOne({
      where: { id: documentId },
      relations: ["company"],
    });

    if (!document) {
      throw new NotFoundException(`Document with ID ${documentId} not found`);
    }

    document.verificationStatus = status;
    document.verifiedAt = new Date();
    document.verifiedBy = adminId;

    if (status === VerificationStatus.REJECTED && rejectionReason) {
      document.rejectionReason = rejectionReason;
    }

    const savedDocument = await this.documentsRepository.save(document);

    // If all required documents are verified, update company KYC status
    if (status === VerificationStatus.APPROVED) {
      await this.checkAndUpdateKycStatus(document.companyId);
    }

    // Log activity
    await this.activityLogService.logActivity({
      userId: adminId,
      action: "verify_company_document",
      entityType: "document",
      entityId: documentId,
      description: `Verified company document with status: ${status}`,
    });

    // Notify company
    // await this.notificationsService.create({
    //   userId: document.company.userId,
    //   title:
    //     status === VerificationStatus.APPROVED
    //       ? "Document Approved"
    //       : "Document Rejected",
    //   message:
    //     status === VerificationStatus.APPROVED
    //       ? `Your ${document.documentType} has been approved.`
    //       : `Your ${document.documentType} has been rejected. Reason: ${rejectionReason}`,
    //   type: "kyc",
    // });

    return savedDocument;
  }

  /**
   * Verify a document with validation
   * @param documentId The document ID
   * @param adminId The ID of the admin making the request
   * @param status The verification status
   * @param rejectionReason Optional rejection reason
   * @returns The verified document
   */
  async verifyDocumentWithValidation(
    documentId: string,
    adminId: string,
    status: VerificationStatus,
    rejectionReason?: string
  ): Promise<Document> {
    try {
      // Validate status
      if (!Object.values(VerificationStatus).includes(status)) {
        return ErrorHandler.handleBadRequest("Invalid verification status");
      }

      // Validate rejection reason
      if (status === VerificationStatus.REJECTED && !rejectionReason) {
        return ErrorHandler.handleBadRequest(
          "Rejection reason is required when rejecting a document"
        );
      }

      return this.verifyDocument(documentId, adminId, status, rejectionReason);
    } catch (error) {
      this.logger.error(
        `Error verifying document ${documentId}: ${
          error instanceof Error ? error.message : "Unknown error"
        }`
      );
      throw ErrorHandler.handleError(error, "Document");
    }
  }

  private async checkAndUpdateKycStatus(companyId: string): Promise<void> {
    const company = await this.findOne(companyId);

    // Check if all required documents are verified
    const requiredDocuments = [
      DocumentType.REGISTRATION_CERTIFICATE,
      DocumentType.TAX_CERTIFICATE,
    ];

    const verifiedDocuments = await this.documentsRepository.count({
      where: {
        companyId,
        documentType: In(requiredDocuments),
        verificationStatus: VerificationStatus.APPROVED,
      },
    });

    if (verifiedDocuments === requiredDocuments.length) {
      // company.isKycVerified = true;
      await this.companiesRepository.save(company);

      // Notify company
      // await this.notificationsService.create({
      //   userId: company.userId,
      //   title: "KYC Verification Completed",
      //   message:
      //     "Your company has been successfully verified. You can now post jobs.",
      //   type: "kyc",
      // });
    }
  }
}
