import {
  <PERSON><PERSON>ty,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  OneToMany,
  OneToOne,
  JoinColumn,
  Index,
} from "typeorm";

import { ApiProperty, ApiPropertyOptional } from "@nestjs/swagger";

export class Company {
  @ApiProperty({ description: "Unique identifier" })
  @PrimaryGeneratedColumn("uuid")
  id: string;

  @ApiPropertyOptional({ description: "Company registration number" })
  @Column({ nullable: true })
  registrationNumber?: string;

  @ApiPropertyOptional({ description: "Company tax ID" })
  @Column({ nullable: true })
  taxId?: string;

  @ApiPropertyOptional({ description: "Company website" })
  @Column({ nullable: true })
  website?: string;

  @ApiPropertyOptional({ description: "Company size" })
  @Column({ nullable: true })
  size?: string;

  @ApiPropertyOptional({ description: "Company industry" })
  @Column({ nullable: true })
  industry?: string;

  @ApiPropertyOptional({ description: "Company description" })
  @Column({ type: "text", nullable: true })
  description?: string;

  @ApiPropertyOptional({ description: "Year the company was founded" })
  @Column({ nullable: true })
  foundedYear?: number;

  @ApiPropertyOptional({ description: "Headquarters address" })
  @Column({ nullable: true, name: "headquartersAddress" })
  headquartersAddress?: string;

  @ApiPropertyOptional({ description: "Headquarters city" })
  @Column({ nullable: true, name: "headquartersCity" })
  headquartersCity?: string;

  @ApiPropertyOptional({ description: "Headquarters state/province" })
  @Column({ nullable: true, name: "headquartersState" })
  headquartersState?: string;

  @ApiPropertyOptional({ description: "Headquarters postal code" })
  @Column({ nullable: true, name: "headquartersPostalCode" })
  headquartersPostalCode?: string;

  @ApiPropertyOptional({ description: "Headquarters country" })
  @Column({ nullable: true, name: "headquartersCountry" })
  headquartersCountry?: string;
}
