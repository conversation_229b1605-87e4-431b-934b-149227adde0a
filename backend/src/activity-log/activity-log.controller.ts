import { Controller, Get, Inject, Query, UseGuards } from "@nestjs/common";
import { ActivityLogService } from "./activity-log.service";
import { JwtAuthGuard } from "../auth/guards/jwt-auth.guard";
import { RolesGuard } from "../auth/guards/roles.guard";
import { Roles } from "../auth/decorators/roles.decorator";
import { UserRole } from "@shared/types";
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiBearerAuth,
  ApiQuery,
} from "@nestjs/swagger";
import { ActivityLogResponseDto } from "./dto/activity-log.dto";

@ApiTags("activity-logs")
@Controller("activity-logs")
@UseGuards(JwtAuthGuard, RolesGuard)
@Roles(UserRole.ADMIN)
@ApiBearerAuth()
export class ActivityLogController {
  constructor(
    @Inject(ActivityLogService) private activityLogService: ActivityLogService
  ) {}

  @Get()
  @ApiOperation({
    summary: "Get all activity logs (Admin only)",
    description:
      "Retrieve activity logs with optional filtering and pagination. Only accessible by admins.",
  })
  @ApiQuery({
    name: "page",
    required: false,
    description: "Page number for pagination",
    type: Number,
    example: 1,
  })
  @ApiQuery({
    name: "limit",
    required: false,
    description: "Number of items per page",
    type: Number,
    example: 20,
  })
  @ApiQuery({
    name: "userId",
    required: false,
    description: "Filter by user ID",
    type: String,
    example: "550e8400-e29b-41d4-a716-446655440000",
  })
  @ApiQuery({
    name: "action",
    required: false,
    description: "Filter by action type",
    type: String,
    example: "login",
  })
  @ApiQuery({
    name: "entityType",
    required: false,
    description: "Filter by entity type",
    type: String,
    example: "job",
  })
  @ApiQuery({
    name: "fromDate",
    required: false,
    description: "Filter by start date (ISO format)",
    type: String,
    example: "2023-01-01T00:00:00.000Z",
  })
  @ApiQuery({
    name: "toDate",
    required: false,
    description: "Filter by end date (ISO format)",
    type: String,
    example: "2023-12-31T23:59:59.999Z",
  })
  @ApiQuery({
    name: "search",
    required: false,
    description: "Search term for activity description",
    type: String,
    example: "login",
  })
  @ApiResponse({
    status: 200,
    description: "Return activity logs with pagination",
    schema: {
      type: "object",
      properties: {
        items: {
          type: "array",
          items: { $ref: "#/components/schemas/ActivityLogResponseDto" },
        },
        meta: {
          type: "object",
          properties: {
            totalItems: { type: "number", example: 100 },
            itemsPerPage: { type: "number", example: 20 },
            totalPages: { type: "number", example: 5 },
            currentPage: { type: "number", example: 1 },
          },
        },
      },
    },
  })
  @ApiResponse({
    status: 401,
    description: "Unauthorized - invalid or missing authentication",
  })
  @ApiResponse({
    status: 403,
    description: "Forbidden - user does not have required role",
  })
  async findAll(
    @Query("page") page = 1,
    @Query("limit") limit = 20,
    @Query("userId") userId?: string,
    @Query("action") action?: string,
    @Query("entityType") entityType?: string,
    @Query("fromDate") fromDate?: Date,
    @Query("toDate") toDate?: Date,
    @Query("search") search?: string
  ) {
    return this.activityLogService.findAll(page, limit, {
      userId,
      action,
      entityType,
      fromDate,
      toDate,
      search,
    });
  }
}
