import { InjectRepository } from "@nestjs/typeorm";
import type { Repository } from "typeorm";
import { Injectable } from "@nestjs/common";
import { ActivityLog } from "./entities/activity-log.entity";

interface LogActivityParams {
  userId: string;
  action: string;
  entityType: string;
  entityId: string;
  description: string;
  metadata?: Record<string, any>;
}

interface FindAllParams {
  userId?: string;
  action?: string;
  entityType?: string;
  entityId?: string;
  fromDate?: Date;
  toDate?: Date;
  search?: string;
  page?: number;
  limit?: number;
}

@Injectable()
export class ActivityLogService {
  constructor(
    @InjectRepository(ActivityLog)
    private activityLogRepository: Repository<ActivityLog>
  ) {}

  async logActivity({
    userId,
    action,
    entityType,
    entityId,
    description,
    metadata,
  }: LogActivityParams): Promise<ActivityLog> {
    const activityLog = this.activityLogRepository.create({
      userId,
      action,
      entityType,
      entityId,
      description,
      metadata,
    });

    return this.activityLogRepository.save(activityLog);
  }

  async findAll(
    page = 1,
    limit = 20,
    filters?: Omit<FindAllParams, "page" | "limit">
  ): Promise<{
    data: ActivityLog[];
    total: number;
    page: number;
    limit: number;
  }> {
    const queryBuilder = this.activityLogRepository
      .createQueryBuilder("activityLog")
      .orderBy("activityLog.createdAt", "DESC");

    if (filters) {
      if (filters.userId) {
        queryBuilder.andWhere("activityLog.userId = :userId", {
          userId: filters.userId,
        });
      }

      if (filters.action) {
        queryBuilder.andWhere("activityLog.action = :action", {
          action: filters.action,
        });
      }

      if (filters.entityType) {
        queryBuilder.andWhere("activityLog.entityType = :entityType", {
          entityType: filters.entityType,
        });
      }

      if (filters.entityId) {
        queryBuilder.andWhere("activityLog.entityId = :entityId", {
          entityId: filters.entityId,
        });
      }

      if (filters.fromDate && filters.toDate) {
        queryBuilder.andWhere(
          "activityLog.createdAt BETWEEN :fromDate AND :toDate",
          {
            fromDate: filters.fromDate,
            toDate: filters.toDate,
          }
        );
      }

      if (filters.search) {
        queryBuilder.andWhere("activityLog.description LIKE :search", {
          search: `%${filters.search}%`,
        });
      }
    }

    // Apply pagination
    const skip = (page - 1) * limit;
    queryBuilder.skip(skip).take(limit);

    const [data, total] = await queryBuilder.getManyAndCount();

    return {
      data,
      total,
      page,
      limit,
    };
  }
}
