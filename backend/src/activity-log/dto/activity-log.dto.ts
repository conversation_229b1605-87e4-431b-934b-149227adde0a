import { ApiProperty, ApiPropertyOptional } from "@nestjs/swagger";
import { IsString, IsUUID, IsOptional, IsEnum, IsDateString } from "class-validator";

export enum ActivityType {
  USER_CREATED = "user_created",
  USER_UPDATED = "user_updated",
  USER_DELETED = "user_deleted",
  JOB_CREATED = "job_created",
  JOB_UPDATED = "job_updated",
  JOB_DELETED = "job_deleted",
  JOB_COMPLETED = "job_completed",
  APPLICATION_CREATED = "application_created",
  APPLICATION_UPDATED = "application_updated",
  APPLICATION_ACCEPTED = "application_accepted",
  APPLICATION_REJECTED = "application_rejected",
  PAYMENT_PROCESSED = "payment_processed",
  PAYOUT_PROCESSED = "payout_processed",
  DOCUMENT_UPLOADED = "document_uploaded",
  DOCUMENT_VERIFIED = "document_verified",
  DOCUMENT_REJECTED = "document_rejected",
  LOGIN = "login",
  LOGOUT = "logout",
  PASSWORD_RESET = "password_reset",
  ADMIN_ACTION = "admin_action",
  SYSTEM = "system",
}

export class ActivityLogFilterDto {
  @ApiPropertyOptional({
    description: "Filter by user ID",
    example: "550e8400-e29b-41d4-a716-************",
  })
  @IsUUID()
  @IsOptional()
  userId?: string;

  @ApiPropertyOptional({
    description: "Filter by activity type",
    enum: ActivityType,
    example: ActivityType.JOB_CREATED,
  })
  @IsEnum(ActivityType)
  @IsOptional()
  type?: ActivityType;

  @ApiPropertyOptional({
    description: "Filter by related entity ID (job, application, etc.)",
    example: "550e8400-e29b-41d4-a716-************",
  })
  @IsString()
  @IsOptional()
  entityId?: string;

  @ApiPropertyOptional({
    description: "Filter by start date (ISO format)",
    example: "2023-01-01T00:00:00.000Z",
  })
  @IsDateString()
  @IsOptional()
  startDate?: string;

  @ApiPropertyOptional({
    description: "Filter by end date (ISO format)",
    example: "2023-12-31T23:59:59.999Z",
  })
  @IsDateString()
  @IsOptional()
  endDate?: string;
}

export class ActivityLogResponseDto {
  @ApiProperty({
    description: "Unique identifier for the activity log",
    example: "550e8400-e29b-41d4-a716-************",
  })
  id: string;

  @ApiProperty({
    description: "User ID who performed the action",
    example: "550e8400-e29b-41d4-a716-************",
  })
  userId: string;

  @ApiProperty({
    description: "Type of activity",
    enum: ActivityType,
    example: ActivityType.JOB_CREATED,
  })
  type: ActivityType;

  @ApiProperty({
    description: "Description of the activity",
    example: "User created a new job",
  })
  description: string;

  @ApiPropertyOptional({
    description: "Related entity ID (job, application, etc.)",
    example: "550e8400-e29b-41d4-a716-************",
  })
  entityId?: string;

  @ApiPropertyOptional({
    description: "Related entity type",
    example: "job",
  })
  entityType?: string;

  @ApiPropertyOptional({
    description: "Additional metadata about the activity",
    example: { jobTitle: "Inventory Audit", location: "New York" },
  })
  metadata?: Record<string, any>;

  @ApiProperty({
    description: "IP address of the user",
    example: "***********",
  })
  ipAddress: string;

  @ApiProperty({
    description: "User agent of the client",
    example: "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36",
  })
  userAgent: string;

  @ApiProperty({
    description: "Timestamp when the activity occurred",
    example: "2023-01-01T00:00:00.000Z",
  })
  createdAt: Date;
}
