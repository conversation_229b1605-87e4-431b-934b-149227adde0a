import { ApiProperty, ApiPropertyOptional } from "@nestjs/swagger"
import { IsString, IsOptional, IsObject, IsUUID, Length } from "class-validator"

export class CreateActivityLogDto {
  @ApiPropertyOptional({ description: "User ID associated with this activity" })
  @IsOptional()
  @IsUUID()
  userId?: string

  @ApiProperty({ description: "Action performed (e.g., 'create', 'update', 'delete')" })
  @IsString()
  action: string

  @ApiPropertyOptional({ description: "Entity type (e.g., 'user', 'job', 'application')" })
  @IsOptional()
  @IsString()
  entityType?: string

  @ApiPropertyOptional({ description: "Entity ID" })
  @IsOptional()
  @IsString()
  entityId?: string

  @ApiPropertyOptional({ description: "Description of the activity" })
  @IsOptional()
  @IsString()
  @Length(0, 1000)
  description?: string

  @ApiPropertyOptional({ description: "Additional metadata as JSON" })
  @IsOptional()
  @IsObject()
  metadata?: Record<string, any>

  @ApiPropertyOptional({ description: "IP address" })
  @IsOptional()
  @IsString()
  ipAddress?: string

  @ApiPropertyOptional({ description: "User agent" })
  @IsOptional()
  @IsString()
  userAgent?: string
}
