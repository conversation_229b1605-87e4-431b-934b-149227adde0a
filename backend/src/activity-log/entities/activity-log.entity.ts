import { <PERSON><PERSON><PERSON>, PrimaryGeneratedColumn, Column, CreateDateColumn, ManyToOne, JoinColumn, Index } from "typeorm"
import { User } from "../../users/entities/user.entity"

@Entity("activity_logs")
export class ActivityLog {
  @PrimaryGeneratedColumn("uuid")
  id: string

  @Column({ nullable: true })
  @Index()
  userId?: string

  @ManyToOne(() => User)
  @JoinColumn({ name: "userId" })
  user?: User

  @Column()
  action: string

  @Column({ nullable: true })
  entityType?: string

  @Column({ nullable: true })
  entityId?: string

  @Column({ type: "text", nullable: true })
  description?: string

  @Column({ type: "jsonb", nullable: true })
  metadata?: Record<string, any>

  @Column({ nullable: true })
  ipAddress?: string

  @Column({ nullable: true })
  userAgent?: string

  @CreateDateColumn()
  createdAt: Date
}
