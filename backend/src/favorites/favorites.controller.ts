import { Controller, Get, Post, Delete, Param, UseGuards, Req, Body, Inject } from "@nestjs/common"
import { JwtAuthGuard } from "../auth/guards/jwt-auth.guard"
import { RolesGuard } from "../auth/guards/roles.guard"
import { Roles } from "../auth/decorators/roles.decorator"
import { FavoritesService } from "./favorites.service"
import { UserRole } from "@shared/types"

@Controller("favorites")
@UseGuards(JwtAuthGuard, RolesGuard)
export class FavoritesController {
  constructor(
    @Inject(FavoritesService)
    private favoritesService: FavoritesService
  ) {}

  @Post()
  @Roles(UserRole.WORKER)
  async addFavorite(@Req() req: any, @Body() body: { jobId: string }) {
    const { jobId } = body
    return this.favoritesService.addFavorite(req.user.id, jobId)
  }

  @Delete(":jobId")
  @Roles(UserRole.WORKER)
  async removeFavorite(@Req() req: any, @Param("jobId") jobId: string) {
    await this.favoritesService.removeFavorite(req.user.id, jobId)
    return { success: true }
  }

  @Get()
  @Roles(UserRole.WORKER)
  async getUserFavorites(@Req() req: any) {
    return this.favoritesService.getUserFavorites(req.user.id)
  }

  @Get("check/:jobId")
  @Roles(UserRole.WORKER)
  async checkIsFavorite(@Req() req: any, @Param("jobId") jobId: string) {
    const isFavorite = await this.favoritesService.checkIsFavorite(req.user.id, jobId)
    return { isFavorite }
  }
}
