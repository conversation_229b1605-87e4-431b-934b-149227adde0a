import { Modu<PERSON> } from "@nestjs/common";
import { TypeOrmModule } from "@nestjs/typeorm";
import { Favorite } from "./entities/favorite.entity";
import { FavoritesService } from "./favorites.service";
import { FavoritesController } from "./favorites.controller";
import { UsersModule } from "../users/users.module";
import { JobsModule } from "../jobs/jobs.module";

@Module({
  imports: [TypeOrmModule.forFeature([Favorite]), UsersModule, JobsModule],
  providers: [FavoritesService],
  controllers: [FavoritesController],
  exports: [FavoritesService],
})
export class FavoritesModule {}
