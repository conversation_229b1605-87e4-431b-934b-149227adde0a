import {
  Injectable,
  NotFoundException,
  BadRequestException,
  Inject,
} from "@nestjs/common";
import { InjectRepository } from "@nestjs/typeorm";
import type { Repository } from "typeorm";
import { Favorite } from "./entities/favorite.entity";
import { UsersService } from "../users/users.service";
import { JobsService } from "../jobs/jobs.service";
import { UserRole } from "@shared/types";

@Injectable()
export class FavoritesService {
  constructor(
    @InjectRepository(Favorite)
    private favoritesRepository: Repository<Favorite>,
    @Inject(UsersService)
    private readonly usersService: UsersService,
    @Inject(JobsService)
    private readonly jobsService: JobsService
  ) {}

  async addFavorite(workerId: string, jobId: string): Promise<Favorite> {
    // Check if user is a worker
    const user = await this.usersService.findOne(workerId);
    if (user.role !== UserRole.WORKER) {
      throw new BadRequestException("Only workers can add favorites");
    }

    // Check if job exists
    await this.jobsService.findOne(jobId);

    // Check if already favorited
    const existingFavorite = await this.favoritesRepository.findOne({
      where: { workerId, jobId },
    });

    if (existingFavorite) {
      throw new BadRequestException("Job is already in favorites");
    }

    // Create favorite
    const favorite = this.favoritesRepository.create({
      workerId,
      jobId,
    });

    return this.favoritesRepository.save(favorite);
  }

  async removeFavorite(workerId: string, jobId: string): Promise<void> {
    const favorite = await this.favoritesRepository.findOne({
      where: { workerId, jobId },
    });

    if (!favorite) {
      throw new NotFoundException("Favorite not found");
    }

    await this.favoritesRepository.remove(favorite);
  }

  async getUserFavorites(workerId: string): Promise<Favorite[]> {
    return this.favoritesRepository.find({
      where: { workerId },
      relations: ["job", "job.company"],
      order: { createdAt: "DESC" },
    });
  }

  async checkIsFavorite(workerId: string, jobId: string): Promise<boolean> {
    const count = await this.favoritesRepository.count({
      where: { workerId, jobId },
    });
    return count > 0;
  }
}
