import { ApiPropertyOptional } from "@nestjs/swagger"
import { IsBoolean, IsOptional, IsDate } from "class-validator"
import { Type } from "class-transformer"

export class UpdateNotificationDto {
  @ApiPropertyOptional({ description: "Mark notification as read" })
  @IsOptional()
  @IsBoolean()
  isRead?: boolean

  @ApiPropertyOptional({ description: "Time when notification was read" })
  @IsOptional()
  @IsDate()
  @Type(() => Date)
  readAt?: Date
}
