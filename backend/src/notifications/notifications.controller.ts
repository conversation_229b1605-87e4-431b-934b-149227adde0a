import {
  <PERSON>,
  Get,
  Patch,
  Delete,
  Param,
  UseGuards,
  Request,
  Inject,
} from "@nestjs/common";
import { JwtAuthGuard } from "../auth/guards/jwt-auth.guard";
import { NotificationsService } from "./notifications.service";
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiBearerAuth,
} from "@nestjs/swagger";

@ApiTags("notifications")
@Controller("notifications")
@UseGuards(JwtAuthGuard)
@ApiBearerAuth()
export class NotificationsController {
  constructor(
    @Inject(NotificationsService)
    private readonly notificationsService: NotificationsService
  ) {}

  @Get()
  @ApiOperation({ summary: "Get all notifications for the current user" })
  @ApiResponse({ status: 200, description: "Return all notifications" })
  async findAll(@Request() req): Promise<any> {
    return this.notificationsService.findAllForUser(req.user.id);
  }

  @Get("unread")
  @ApiOperation({ summary: "Get unread notifications for the current user" })
  @ApiResponse({ status: 200, description: "Return unread notifications" })
  async findUnread(@Request() req): Promise<any> {
    return this.notificationsService.findAllForUser(req.user.id, false);
  }

  @Patch(":id/read")
  @ApiOperation({ summary: "Mark notification as read" })
  @ApiResponse({ status: 200, description: "Notification marked as read" })
  async markAsRead(@Param("id") id: string, @Request() req): Promise<any> {
    return this.notificationsService.markAsRead(id, req.user.id);
  }

  @Patch("read-all")
  @ApiOperation({ summary: "Mark all notifications as read" })
  @ApiResponse({ status: 200, description: "All notifications marked as read" })
  async markAllAsRead(@Request() req): Promise<any> {
    return this.notificationsService.markAllAsRead(req.user.id);
  }

  @Delete(":id")
  @ApiOperation({ summary: "Delete notification" })
  @ApiResponse({ status: 200, description: "Notification deleted" })
  async delete(@Param("id") id: string): Promise<any> {
    return this.notificationsService.deleteNotification(id);
  }
}
