import { Injectable, Logger, Inject, forwardRef } from "@nestjs/common";
import { ConfigService } from "@nestjs/config";
import * as admin from "firebase-admin";
import { UsersService } from "../users/users.service";
import { UserRole } from "@shared/types";

interface PushNotificationPayload {
  title: string;
  body: string;
  data?: Record<string, string>;
  imageUrl?: string;
}

@Injectable()
export class PushNotificationService {
  private readonly logger = new Logger(PushNotificationService.name);
  private firebaseApp: admin.app.App;

  constructor(
    @Inject(ConfigService) private configService: ConfigService,
    @Inject(forwardRef(() => UsersService)) private usersService: UsersService
  ) {
    this.initializeFirebaseApp();
  }

  private initializeFirebaseApp() {
    try {
      // Check if Firebase is already initialized
      if (admin.apps.length === 0) {
        const firebaseConfig = this.configService.get("FIREBASE_CONFIG");

        // If we have a JSON string config, parse it
        if (firebaseConfig) {
          const parsedConfig = JSON.parse(firebaseConfig);

          this.firebaseApp = admin.initializeApp({
            credential: admin.credential.cert(parsedConfig),
            databaseURL: this.configService.get("FIREBASE_DATABASE_URL"),
          });

          this.logger.log("Firebase Admin SDK initialized successfully");
        } else {
          // Try to initialize with service account file
          const serviceAccountPath = this.configService.get(
            "FIREBASE_SERVICE_ACCOUNT_PATH"
          );

          if (serviceAccountPath) {
            this.firebaseApp = admin.initializeApp({
              credential: admin.credential.cert(serviceAccountPath),
              databaseURL: this.configService.get("FIREBASE_DATABASE_URL"),
            });

            this.logger.log(
              "Firebase Admin SDK initialized with service account file"
            );
          } else {
            this.logger.warn(
              "Firebase configuration not found, push notifications will not work"
            );
          }
        }
      } else {
        this.firebaseApp = admin.apps[0];
      }
    } catch (error) {
      this.logger.error("Error initializing Firebase Admin SDK", error);
    }
  }

  /**
   * Send a push notification to a specific user
   */
  async sendToUser(
    userId: string,
    payload: PushNotificationPayload
  ): Promise<boolean> {
    try {
      if (!this.firebaseApp) {
        this.logger.warn(
          "Firebase not initialized, skipping push notification"
        );
        return false;
      }

      // Get the user's device tokens
      const user = await this.usersService.findOne(userId);

      if (!user.deviceTokens || user.deviceTokens.length === 0) {
        this.logger.debug(`User ${userId} has no device tokens registered`);
        return false;
      }

      // Prepare the message
      const message: admin.messaging.MulticastMessage = {
        tokens: user.deviceTokens,
        notification: {
          title: payload.title,
          body: payload.body,
          imageUrl: payload.imageUrl,
        },
        data: payload.data || {},
        android: {
          priority: "high",
          notification: {
            sound: "default",
            clickAction: "FLUTTER_NOTIFICATION_CLICK",
          },
        },
        apns: {
          payload: {
            aps: {
              sound: "default",
              badge: 1,
              contentAvailable: true,
            },
          },
        },
      };

      // Send the message
      const response = await this.firebaseApp
        .messaging()
        .sendEachForMulticast(message);

      this.logger.debug(
        `Push notification sent to user ${userId}: ${response.successCount} successful, ${response.failureCount} failed`
      );

      // Handle failed tokens
      if (response.failureCount > 0) {
        const failedTokens: string[] = [];

        response.responses.forEach((resp, idx) => {
          if (!resp.success) {
            failedTokens.push(user.deviceTokens[idx]);
            this.logger.debug(
              `Failed to send to token: ${user.deviceTokens[idx]}, error: ${resp.error}`
            );
          }
        });

        // Remove failed tokens from the user's device tokens
        if (failedTokens.length > 0) {
          const validTokens = user.deviceTokens.filter(
            (token) => !failedTokens.includes(token)
          );
          await this.usersService.updateDeviceTokens(userId, validTokens);
        }
      }

      return response.successCount > 0;
    } catch (error) {
      this.logger.error(
        `Error sending push notification to user ${userId}:`,
        error
      );
      return false;
    }
  }

  /**
   * Send a push notification to multiple users
   */
  async sendToUsers(
    userIds: string[],
    payload: PushNotificationPayload
  ): Promise<number> {
    let successCount = 0;

    for (const userId of userIds) {
      const success = await this.sendToUser(userId, payload);
      if (success) successCount++;
    }

    return successCount;
  }

  /**
   * Send a push notification to all users with a specific role
   */
  async sendToRole(
    role: UserRole,
    payload: PushNotificationPayload
  ): Promise<number> {
    try {
      // Get all users with the specified role
      const users = await this.usersService.findByRole(role);
      const userIds = users.map((user) => user.id);

      return this.sendToUsers(userIds, payload);
    } catch (error) {
      this.logger.error(
        `Error sending push notification to role ${role}:`,
        error
      );
      return 0;
    }
  }
}
