import {
  Injectable,
  NotFoundException,
  BadRequestException,
  ForbiddenException,
  Inject,
  Logger,
} from "@nestjs/common";
import { InjectRepository } from "@nestjs/typeorm";
import { type Repository, Less<PERSON>hanOrEqual, More<PERSON>hanOrEqual, In } from "typeorm";
import { Job } from "./entities/job.entity";
import type { CreateJobDto } from "./dto/create-job.dto";
import type { UpdateJobDto } from "./dto/update-job.dto";
import type { QueryJobsDto } from "./dto/query-jobs.dto";
import { JobStatus } from "../common/enums/job-status.enum";
import { UserRole } from "../common/enums/user-role.enum";
import { ApplicationStatus } from "../common/enums/application-status.enum";
import { User } from "../users/entities/user.entity";
import { Application } from "../applications/entities/application.entity";
import { ActivityLogService } from "../activity-log/activity-log.service";
import { NotificationsService } from "../notifications/notifications.service";
import { PaginationService } from "../common/services/pagination.service";
import { SanitizationService } from "../common/services/sanitization.service";
import {
  GeolocationService,
  Coordinates,
} from "../common/services/geolocation.service";

// Define the pagination response interface
export interface PaginatedResponse<T> {
  success: boolean;
  data: T[];
  message: string;
  meta: {
    total: number;
    page: number;
    limit: number;
    totalPages: number;
    hasNextPage: boolean;
    hasPreviousPage: boolean;
  };
}

@Injectable()
export class JobsService {
  private readonly logger = new Logger(JobsService.name);

  constructor(
    @InjectRepository(Job)
    private jobsRepository: Repository<Job>,
    @InjectRepository(User)
    private usersRepository: Repository<User>,
    @InjectRepository(Application)
    private applicationsRepository: Repository<Application>,
    @Inject(ActivityLogService)
    private activityLogService: ActivityLogService,
    @Inject(NotificationsService)
    private notificationsService: NotificationsService,
    @Inject(PaginationService)
    private paginationService: PaginationService,
    @Inject(SanitizationService)
    private sanitizationService: SanitizationService,
    @Inject(GeolocationService)
    private geolocationService: GeolocationService
  ) {}

  async create(companyId: string, createJobDto: CreateJobDto): Promise<Job> {
    // Sanitize input
    const sanitizedDto = this.sanitizationService.sanitizeObject(createJobDto);

    // Verify company exists and is a company
    const company = await this.usersRepository.findOne({
      where: { id: companyId },
    });
    if (!company) {
      throw new NotFoundException(`Company with ID ${companyId} not found`);
    }
    if (company.role !== UserRole.COMPANY) {
      throw new BadRequestException("Only companies can create jobs");
    }

    // Validate dates
    if (sanitizedDto.startDateTime >= sanitizedDto.endDateTime) {
      throw new BadRequestException("End date must be after start date");
    }

    // Create job
    const job = this.jobsRepository.create({
      ...sanitizedDto,
      companyId,
    });

    const savedJob = await this.jobsRepository.save(job);

    // Log activity
    await this.activityLogService.logActivity({
      userId: companyId,
      action: "create_job",
      entityType: "job",
      entityId: savedJob.id,
      description: `Created job: ${savedJob.title}`,
    });

    // Notify workers about new job if it's an emergency job
    if (job.isEmergencyJob) {
      const eligibleWorkers = await this.usersRepository.find({
        where: {
          role: UserRole.WORKER,
          isActive: true,
          isBanned: false,
          trustScore: LessThanOrEqual(40), // Emergency jobs are for workers with low trust scores
        },
      });

      for (const worker of eligibleWorkers) {
        await this.notificationsService.create({
          userId: worker.id,
          title: "Emergency Job Available",
          message: `New emergency job: ${job.title}`,
          type: "job",
          metadata: { jobId: savedJob.id },
          link: `/jobs/${savedJob.id}`,
        });
      }
    } else if (job.trustScoreRequired > 0) {
      // Notify eligible workers about new job
      const eligibleWorkers = await this.usersRepository.find({
        where: {
          role: UserRole.WORKER,
          isActive: true,
          isBanned: false,
          trustScore: MoreThanOrEqual(job.trustScoreRequired),
        },
      });

      for (const worker of eligibleWorkers) {
        await this.notificationsService.create({
          userId: worker.id,
          title: "New Job Available",
          message: `New job matching your profile: ${job.title}`,
          type: "job",
          metadata: { jobId: savedJob.id },
          link: `/jobs/${savedJob.id}`,
        });
      }
    }

    return savedJob;
  }

  /**
   * Safely handle query parameters and return a sanitized query DTO
   * @param queryJobsDto The query parameters
   * @returns A sanitized query DTO
   */
  sanitizeQueryDto(queryJobsDto: QueryJobsDto): QueryJobsDto {
    // Ensure queryJobsDto is always an object even if no query params are provided
    return queryJobsDto || {};
  }

  /**
   * Create an empty paginated response
   * @param message The message to include in the response
   * @param page The page number
   * @param limit The limit per page
   * @returns An empty paginated response
   */
  createEmptyPaginatedResponse<T>(
    message: string = "No matching jobs found",
    page: number = 1,
    limit: number = 10
  ): PaginatedResponse<T> {
    return {
      success: true,
      data: [],
      message,
      meta: {
        total: 0,
        page,
        limit,
        totalPages: 0,
        hasNextPage: false,
        hasPreviousPage: page > 1,
      },
    };
  }

  /**
   * Handle query errors and return an empty paginated response
   * @param error The error that occurred
   * @param queryJobsDto The original query parameters
   * @returns An empty paginated response
   */
  handleQueryError<T>(
    error: any,
    queryJobsDto: QueryJobsDto = {}
  ): PaginatedResponse<T> {
    this.logger.error(
      `Error processing query parameters: ${error.message}`,
      error.stack
    );
    return this.createEmptyPaginatedResponse<T>(
      "No matching jobs found due to invalid query parameters",
      queryJobsDto.page || 1,
      queryJobsDto.limit || 10
    );
  }

  /**
   * Find all jobs with pagination and filtering
   * @param queryJobsDto The query parameters
   * @returns A paginated response with jobs
   */
  async findAll(queryJobsDto: QueryJobsDto): Promise<PaginatedResponse<Job>> {
    try {
      // Ensure queryJobsDto is always an object even if no query params are provided
      const sanitizedQueryDto = this.sanitizeQueryDto(queryJobsDto);

      // Destructure and provide defaults for pagination parameters
      const {
        page = 1,
        limit = 10,
        sortBy,
        sortOrder = "DESC",
        status,
        companyId,
        trustScoreMin,
        trustScoreMax,
        isEmergency,
        startDateMin,
        startDateMax,
        city,
        search,
        requiresLaptop,
        requiresSmartphone,
      } = sanitizedQueryDto;

      // Use a two-step approach to avoid GROUP BY issues
      // First, get the job IDs that match the criteria
      const jobIdsQuery = this.jobsRepository
        .createQueryBuilder("job")
        .select("job.id");

      // Apply filters - only add conditions for parameters that are provided
      if (status !== undefined) {
        jobIdsQuery.andWhere("job.status = :status", { status });
      }

      if (companyId !== undefined) {
        jobIdsQuery.andWhere("job.companyId = :companyId", { companyId });
      }

      if (trustScoreMin !== undefined) {
        jobIdsQuery.andWhere("job.trustScoreRequired >= :trustScoreMin", {
          trustScoreMin,
        });
      }

      if (trustScoreMax !== undefined) {
        jobIdsQuery.andWhere("job.trustScoreRequired <= :trustScoreMax", {
          trustScoreMax,
        });
      }

      if (isEmergency !== undefined) {
        jobIdsQuery.andWhere("job.isEmergencyJob = :isEmergency", {
          isEmergency,
        });
      }

      // Handle date filters safely
      if (startDateMin instanceof Date && startDateMax instanceof Date) {
        jobIdsQuery.andWhere(
          "job.startDateTime BETWEEN :startDateMin AND :startDateMax",
          {
            startDateMin,
            startDateMax,
          }
        );
      } else if (startDateMin instanceof Date) {
        jobIdsQuery.andWhere("job.startDateTime >= :startDateMin", {
          startDateMin,
        });
      } else if (startDateMax instanceof Date) {
        jobIdsQuery.andWhere("job.startDateTime <= :startDateMax", {
          startDateMax,
        });
      }

      if (city) {
        jobIdsQuery.andWhere("job.city ILIKE :city", { city: `%${city}%` });
      }

      if (search) {
        jobIdsQuery.andWhere(
          "(job.title ILIKE :search OR job.description ILIKE :search OR job.location ILIKE :search)",
          { search: `%${search}%` }
        );
      }

      if (requiresLaptop !== undefined) {
        jobIdsQuery.andWhere("job.requiresLaptop = :requiresLaptop", {
          requiresLaptop,
        });
      }

      if (requiresSmartphone !== undefined) {
        jobIdsQuery.andWhere("job.requiresSmartphone = :requiresSmartphone", {
          requiresSmartphone,
        });
      }

      // Apply sorting with validation to prevent SQL injection
      const allowedSortFields = [
        "id",
        "title",
        "description",
        "companyId",
        "location",
        "city",
        "state",
        "country",
        "startDateTime",
        "endDateTime",
        "payRate",
        "trustScoreRequired",
        "isEmergencyJob",
        "requiresLaptop",
        "requiresSmartphone",
        "status",
        "createdAt",
        "updatedAt",
      ];

      if (sortBy && allowedSortFields.includes(sortBy)) {
        jobIdsQuery.orderBy(`job.${sortBy}`, sortOrder);
      } else {
        // Default sorting if sortBy is invalid or not provided
        jobIdsQuery.orderBy("job.createdAt", "DESC");
      }

      // Get the total count
      const total = await jobIdsQuery.getCount();

      // Apply pagination to get the IDs
      const offset = (page - 1) * limit;
      const jobIds = await jobIdsQuery
        .skip(offset)
        .take(limit)
        .getMany()
        .then((jobs) => jobs.map((job) => job.id));

      // If no jobs found, return empty result
      if (jobIds.length === 0) {
        return {
          success: true,
          data: [],
          message: "No matching jobs found",
          meta: {
            total: 0,
            page,
            limit,
            totalPages: 0,
            hasNextPage: false,
            hasPreviousPage: page > 1,
          },
        };
      }

      // Now fetch the full job data with company relations in a separate query
      const jobs = await this.jobsRepository.find({
        where: { id: In(jobIds) },
        relations: ["company"],
        order:
          sortBy && allowedSortFields.includes(sortBy)
            ? { [sortBy]: sortOrder }
            : { createdAt: "DESC" },
      });

      const totalPages = Math.ceil(total / limit);

      return {
        success: true,
        data: jobs,
        message: "Data retrieved successfully",
        meta: {
          total,
          page,
          limit,
          totalPages,
          hasNextPage: page < totalPages,
          hasPreviousPage: page > 1,
        },
      };
    } catch (error) {
      // Handle specific errors
      if (error.name === "QueryFailedError") {
        this.logger.error(
          `Invalid query parameters: ${error.message}`,
          error.stack
        );
        throw new BadRequestException(
          `Invalid query parameters: ${error.message}`
        );
      }

      // Return empty result set instead of throwing error
      return this.handleQueryError<Job>(error, queryJobsDto);
    }
  }

  /**
   * Find emergency jobs with pagination and filtering
   * @param userTrustScore The user's trust score
   * @param queryJobsDto The query parameters
   * @returns A paginated response with emergency jobs
   */
  async findEmergencyJobs(
    userTrustScore: number,
    queryJobsDto: QueryJobsDto
  ): Promise<PaginatedResponse<Job>> {
    try {
      // Ensure queryJobsDto is always an object even if no query params are provided
      const sanitizedQueryDto = this.sanitizeQueryDto(queryJobsDto);

      // Set emergency filter
      sanitizedQueryDto.isEmergency = true;

      // Set status filter to OPEN if not specified
      if (!sanitizedQueryDto.status) {
        sanitizedQueryDto.status = JobStatus.OPEN;
      }

      // Emergency jobs are for workers with low trust scores (<=40)
      if (userTrustScore > 40) {
        return this.createEmptyPaginatedResponse<Job>(
          "No emergency jobs available for your trust score",
          sanitizedQueryDto.page || 1,
          sanitizedQueryDto.limit || 10
        );
      }

      return await this.findAll(sanitizedQueryDto);
    } catch (error) {
      // Log the error and return empty result set
      this.logger.error(
        `Error in findEmergencyJobs method: ${error.message}`,
        error.stack
      );
      return this.handleQueryError<Job>(error, queryJobsDto);
    }
  }

  /**
   * Find favorite jobs for a worker
   * @param queryJobsDto The query parameters
   * @returns A paginated response with favorite jobs
   */
  async findFavoriteJobs(
    queryJobsDto: QueryJobsDto
  ): Promise<PaginatedResponse<Job>> {
    try {
      // Ensure queryJobsDto is always an object even if no query params are provided
      const sanitizedQueryDto = this.sanitizeQueryDto(queryJobsDto);

      // This is a placeholder implementation
      // In a real application, you would call a service method to get favorite jobs
      // with the sanitized query parameters
      return this.createEmptyPaginatedResponse<Job>(
        "Favorite jobs retrieved successfully",
        sanitizedQueryDto.page || 1,
        sanitizedQueryDto.limit || 10
      );
    } catch (error) {
      // Log the error and return empty result set
      this.logger.error(
        `Error in findFavoriteJobs method: ${error.message}`,
        error.stack
      );
      return this.handleQueryError<Job>(error, queryJobsDto);
    }
  }

  /**
   * Find nearby jobs with pagination and filtering
   * @param latitude The latitude coordinate
   * @param longitude The longitude coordinate
   * @param radiusKm The radius in kilometers
   * @param userTrustScore The user's trust score
   * @param queryJobsDto The query parameters
   * @returns A paginated response with nearby jobs
   */
  async findNearbyJobs(
    latitude: number,
    longitude: number,
    radiusKm = 10,
    userTrustScore: number,
    queryJobsDto: QueryJobsDto
  ): Promise<PaginatedResponse<Job>> {
    try {
      // Ensure queryJobsDto is always an object even if no query params are provided
      const sanitizedQueryDto = this.sanitizeQueryDto(queryJobsDto);

      // Validate input parameters
      if (!latitude || !longitude || isNaN(latitude) || isNaN(longitude)) {
        return this.createEmptyPaginatedResponse<Job>(
          "Invalid location coordinates provided",
          sanitizedQueryDto.page || 1,
          sanitizedQueryDto.limit || 10
        );
      }

      // Validate coordinates
      const userLocation: Coordinates = { latitude, longitude };
      if (!this.geolocationService.isValidCoordinates(userLocation)) {
        return this.createEmptyPaginatedResponse<Job>(
          "Invalid location coordinates provided",
          sanitizedQueryDto.page || 1,
          sanitizedQueryDto.limit || 10
        );
      }

      // Get bounding box for efficient database query
      const proximityFilter = this.geolocationService.getProximityWhereClause(
        userLocation,
        radiusKm,
        "job.latitude",
        "job.longitude"
      );

      // Set status filter to OPEN if not specified
      if (!sanitizedQueryDto.status) {
        sanitizedQueryDto.status = JobStatus.OPEN;
      }

      // Use a two-step approach to avoid GROUP BY issues
      // First, get the job IDs that match the criteria
      const jobIdsQuery = this.jobsRepository
        .createQueryBuilder("job")
        .select("job.id");

      // Apply bounding box filter
      jobIdsQuery.andWhere(
        proximityFilter.whereClause,
        proximityFilter.parameters
      );

      // Apply trust score filter
      jobIdsQuery.andWhere("job.trustScoreRequired <= :userTrustScore", {
        userTrustScore,
      });

      // Apply other filters from sanitizedQueryDto
      if (sanitizedQueryDto.status) {
        jobIdsQuery.andWhere("job.status = :status", {
          status: sanitizedQueryDto.status,
        });
      }

      if (sanitizedQueryDto.companyId) {
        jobIdsQuery.andWhere("job.companyId = :companyId", {
          companyId: sanitizedQueryDto.companyId,
        });
      }

      if (sanitizedQueryDto.isEmergency !== undefined) {
        jobIdsQuery.andWhere("job.isEmergencyJob = :isEmergency", {
          isEmergency: sanitizedQueryDto.isEmergency,
        });
      }

      // Handle date filters safely
      if (
        sanitizedQueryDto.startDateMin instanceof Date &&
        sanitizedQueryDto.startDateMax instanceof Date
      ) {
        jobIdsQuery.andWhere(
          "job.startDateTime BETWEEN :startDateMin AND :startDateMax",
          {
            startDateMin: sanitizedQueryDto.startDateMin,
            startDateMax: sanitizedQueryDto.startDateMax,
          }
        );
      } else if (sanitizedQueryDto.startDateMin instanceof Date) {
        jobIdsQuery.andWhere("job.startDateTime >= :startDateMin", {
          startDateMin: sanitizedQueryDto.startDateMin,
        });
      } else if (sanitizedQueryDto.startDateMax instanceof Date) {
        jobIdsQuery.andWhere("job.startDateTime <= :startDateMax", {
          startDateMax: sanitizedQueryDto.startDateMax,
        });
      }

      if (sanitizedQueryDto.city) {
        jobIdsQuery.andWhere("job.city ILIKE :city", {
          city: `%${sanitizedQueryDto.city}%`,
        });
      }

      if (sanitizedQueryDto.search) {
        jobIdsQuery.andWhere(
          "(job.title ILIKE :search OR job.description ILIKE :search OR job.location ILIKE :search)",
          { search: `%${sanitizedQueryDto.search}%` }
        );
      }

      if (sanitizedQueryDto.requiresLaptop !== undefined) {
        jobIdsQuery.andWhere("job.requiresLaptop = :requiresLaptop", {
          requiresLaptop: sanitizedQueryDto.requiresLaptop,
        });
      }

      if (sanitizedQueryDto.requiresSmartphone !== undefined) {
        jobIdsQuery.andWhere("job.requiresSmartphone = :requiresSmartphone", {
          requiresSmartphone: sanitizedQueryDto.requiresSmartphone,
        });
      }

      // Apply sorting with validation to prevent SQL injection
      const allowedSortFields = [
        "id",
        "title",
        "description",
        "companyId",
        "location",
        "city",
        "state",
        "country",
        "startDateTime",
        "endDateTime",
        "payRate",
        "trustScoreRequired",
        "isEmergencyJob",
        "requiresLaptop",
        "requiresSmartphone",
        "status",
        "createdAt",
        "updatedAt",
      ];

      if (
        sanitizedQueryDto.sortBy &&
        allowedSortFields.includes(sanitizedQueryDto.sortBy)
      ) {
        jobIdsQuery.orderBy(
          `job.${sanitizedQueryDto.sortBy}`,
          sanitizedQueryDto.sortOrder
        );
      } else {
        jobIdsQuery.orderBy("job.createdAt", "DESC");
      }

      // Get the total count
      const total = await jobIdsQuery.getCount();

      // Apply pagination to get the IDs
      const offset = (sanitizedQueryDto.page - 1) * sanitizedQueryDto.limit;
      const jobIds = await jobIdsQuery
        .skip(offset)
        .take(sanitizedQueryDto.limit)
        .getMany()
        .then((jobs) => jobs.map((job) => job.id));

      // If no jobs found, return empty result
      if (jobIds.length === 0) {
        return this.createEmptyPaginatedResponse<Job>(
          "No nearby jobs found",
          sanitizedQueryDto.page || 1,
          sanitizedQueryDto.limit || 10
        );
      }

      // Now fetch the full job data with company relations in a separate query
      const jobs = await this.jobsRepository.find({
        where: { id: In(jobIds) },
        relations: ["company"],
        order:
          sanitizedQueryDto.sortBy &&
          allowedSortFields.includes(sanitizedQueryDto.sortBy)
            ? { [sanitizedQueryDto.sortBy]: sanitizedQueryDto.sortOrder }
            : { createdAt: "DESC" },
      });

      const totalPages = Math.ceil(total / sanitizedQueryDto.limit);

      // Create a paginated result object
      const paginatedResults: PaginatedResponse<Job> = {
        success: true,
        data: jobs,
        message: "Data retrieved successfully",
        meta: {
          total,
          page: sanitizedQueryDto.page || 1,
          limit: sanitizedQueryDto.limit || 10,
          totalPages,
          hasNextPage: (sanitizedQueryDto.page || 1) < totalPages,
          hasPreviousPage: (sanitizedQueryDto.page || 1) > 1,
        },
      };

      // Calculate exact distance for each job using geolocation service
      const jobsWithDistance = paginatedResults.data.map((job) => {
        const jobLocation: Coordinates = {
          latitude: job.latitude,
          longitude: job.longitude,
        };
        const distance = this.geolocationService.calculateDistance(
          userLocation,
          jobLocation
        );
        return {
          ...job,
          distance,
        };
      });

      // Filter jobs by exact distance and sort by distance
      const filteredJobs = jobsWithDistance
        .filter((job) => job.distance <= radiusKm)
        .sort((a, b) => a.distance - b.distance);

      // Return the final result with filtered jobs
      return {
        success: true,
        data: filteredJobs,
        message: "Data retrieved successfully",
        meta: {
          total: filteredJobs.length,
          page: sanitizedQueryDto.page || 1,
          limit: sanitizedQueryDto.limit || 10,
          totalPages: Math.ceil(
            filteredJobs.length / (sanitizedQueryDto.limit || 10)
          ),
          hasNextPage:
            (sanitizedQueryDto.page || 1) <
            Math.ceil(filteredJobs.length / (sanitizedQueryDto.limit || 10)),
          hasPreviousPage: (sanitizedQueryDto.page || 1) > 1,
        },
      };
    } catch (error) {
      // Handle specific errors
      if (error.name === "QueryFailedError") {
        this.logger.error(
          `Invalid query parameters: ${error.message}`,
          error.stack
        );
        throw new BadRequestException(
          `Invalid query parameters: ${error.message}`
        );
      }

      // Log the error and return empty result set
      return this.handleQueryError<Job>(error, queryJobsDto);
    }
  }

  async findOne(id: string): Promise<Job> {
    const job = await this.jobsRepository.findOne({
      where: { id },
      relations: ["company"],
    });

    if (!job) {
      throw new NotFoundException(`Job with ID ${id} not found`);
    }

    return job;
  }

  async update(
    id: string,
    companyId: string,
    updateJobDto: UpdateJobDto
  ): Promise<Job> {
    const job = await this.findOne(id);

    // Verify company owns this job
    if (job.companyId !== companyId) {
      throw new ForbiddenException("You can only update your own jobs");
    }

    // Check if job is in a state that can be updated
    if (job.status !== JobStatus.DRAFT && job.status !== JobStatus.OPEN) {
      throw new BadRequestException(
        `Cannot update job in ${job.status} status`
      );
    }

    // Sanitize input
    const sanitizedDto = this.sanitizationService.sanitizeObject(updateJobDto);

    // Validate dates if provided
    if (sanitizedDto.startDateTime && sanitizedDto.endDateTime) {
      if (sanitizedDto.startDateTime >= sanitizedDto.endDateTime) {
        throw new BadRequestException("End date must be after start date");
      }
    } else if (sanitizedDto.startDateTime && job.endDateTime) {
      if (sanitizedDto.startDateTime >= job.endDateTime) {
        throw new BadRequestException("End date must be after start date");
      }
    } else if (sanitizedDto.endDateTime && job.startDateTime) {
      if (job.startDateTime >= sanitizedDto.endDateTime) {
        throw new BadRequestException("End date must be after start date");
      }
    }

    // Don't allow changing company
    delete (sanitizedDto as any).companyId; //TODO: fix this

    // Update job
    Object.assign(job, sanitizedDto);

    const updatedJob = await this.jobsRepository.save(job);

    // Log activity
    await this.activityLogService.logActivity({
      userId: companyId,
      action: "update_job",
      entityType: "job",
      entityId: id,
      description: `Updated job: ${job.title}`,
    });

    return updatedJob;
  }

  async updateStatus(
    id: string,
    companyId: string,
    status: JobStatus
  ): Promise<Job> {
    const job = await this.findOne(id);

    // Verify company owns this job
    if (job.companyId !== companyId) {
      throw new ForbiddenException("You can only update your own jobs");
    }

    // Validate status transition
    this.validateStatusTransition(job.status, status);

    // Update job status
    job.status = status;

    const updatedJob = await this.jobsRepository.save(job);

    // Log activity
    await this.activityLogService.logActivity({
      userId: companyId,
      action: "update_job_status",
      entityType: "job",
      entityId: id,
      description: `Updated job status to ${status}: ${job.title}`,
    });

    return updatedJob;
  }

  async cancelJob(id: string, companyId: string, reason: string): Promise<Job> {
    const job = await this.findOne(id);

    // Verify company owns this job
    if (job.companyId !== companyId) {
      throw new ForbiddenException("You can only cancel your own jobs");
    }

    // Check if job can be cancelled
    if (job.status !== JobStatus.DRAFT && job.status !== JobStatus.OPEN) {
      throw new BadRequestException(
        `Cannot cancel job in ${job.status} status`
      );
    }

    // Sanitize input
    reason = this.sanitizationService.sanitizeHtml(reason);

    // Update job status
    job.status = JobStatus.CANCELLED;
    job.cancelledReason = reason;
    job.cancelledAt = new Date();

    const cancelledJob = await this.jobsRepository.save(job);

    // Update all pending applications to cancelled
    await this.applicationsRepository.update(
      { jobId: id, status: ApplicationStatus.PENDING },
      { status: ApplicationStatus.CANCELLED }
    );

    // Notify all applicants
    const applications = await this.applicationsRepository.find({
      where: { jobId: id },
      relations: ["worker"],
    });

    for (const application of applications) {
      await this.notificationsService.create({
        userId: application.workerId,
        title: "Job Cancelled",
        message: `The job "${job.title}" has been cancelled. Reason: ${reason}`,
        type: "job",
        metadata: { jobId: id, applicationId: application.id },
        link: `/applications/${application.id}`,
      });
    }

    // Log activity
    await this.activityLogService.logActivity({
      userId: companyId,
      action: "cancel_job",
      entityType: "job",
      entityId: id,
      description: `Cancelled job: ${job.title}. Reason: ${reason}`,
    });

    return cancelledJob;
  }

  async completeJob(id: string, companyId: string): Promise<Job> {
    const job = await this.findOne(id);

    // Verify company owns this job
    if (job.companyId !== companyId) {
      throw new ForbiddenException("You can only complete your own jobs");
    }

    // Check if job can be completed
    if (job.status !== JobStatus.IN_PROGRESS) {
      throw new BadRequestException(
        `Cannot complete job in ${job.status} status`
      );
    }

    // Update job status
    job.status = JobStatus.COMPLETED;

    const completedJob = await this.jobsRepository.save(job);

    // Update all accepted applications to completed
    await this.applicationsRepository.update(
      { jobId: id, status: ApplicationStatus.ACCEPTED },
      { status: ApplicationStatus.COMPLETED }
    );

    // Notify all workers
    const applications = await this.applicationsRepository.find({
      where: { jobId: id, status: ApplicationStatus.COMPLETED },
      relations: ["worker"],
    });

    for (const application of applications) {
      await this.notificationsService.create({
        userId: application.workerId,
        title: "Job Completed",
        message: `The job "${job.title}" has been marked as completed. Please rate your experience.`,
        type: "job",
        metadata: { jobId: id, applicationId: application.id },
        link: `/applications/${application.id}/rate`,
      });
    }

    // Log activity
    await this.activityLogService.logActivity({
      userId: companyId,
      action: "complete_job",
      entityType: "job",
      entityId: id,
      description: `Completed job: ${job.title}`,
    });

    return completedJob;
  }

  async remove(id: string, companyId: string): Promise<void> {
    const job = await this.findOne(id);

    // Verify company owns this job
    if (job.companyId !== companyId) {
      throw new ForbiddenException("You can only delete your own jobs");
    }

    // Check if job can be deleted
    if (job.status !== JobStatus.DRAFT) {
      throw new BadRequestException(
        `Cannot delete job in ${job.status} status`
      );
    }

    // Log activity before deletion
    await this.activityLogService.logActivity({
      userId: companyId,
      action: "delete_job",
      entityType: "job",
      entityId: id,
      description: `Deleted job: ${job.title}`,
    });

    await this.jobsRepository.remove(job);
  }

  // Helper methods
  private validateStatusTransition(
    currentStatus: JobStatus,
    newStatus: JobStatus
  ): void {
    const validTransitions = {
      [JobStatus.DRAFT]: [JobStatus.OPEN, JobStatus.CANCELLED],
      [JobStatus.OPEN]: [JobStatus.IN_PROGRESS, JobStatus.CANCELLED],
      [JobStatus.IN_PROGRESS]: [JobStatus.COMPLETED, JobStatus.CANCELLED],
      [JobStatus.COMPLETED]: [],
      [JobStatus.CANCELLED]: [],
    };

    if (!validTransitions[currentStatus].includes(newStatus)) {
      throw new BadRequestException(
        `Cannot transition job from ${currentStatus} to ${newStatus}`
      );
    }
  }
}
