import { Modu<PERSON>, forwardRef } from "@nestjs/common";
import { TypeOrmModule } from "@nestjs/typeorm";
import { JobsService } from "./jobs.service";
import { JobsController } from "./jobs.controller";
import { Job } from "./entities/job.entity";
import { User } from "../users/entities/user.entity";
import { Application } from "../applications/entities/application.entity";
import { ActivityLogModule } from "../activity-log/activity-log.module";
import { NotificationsModule } from "../notifications/notifications.module";
import { CommonModule } from "../common/common.module";

@Module({
  imports: [
    TypeOrmModule.forFeature([Job, User, Application]),
    ActivityLogModule,
    forwardRef(() => NotificationsModule),
    CommonModule,
  ],
  controllers: [JobsController],
  providers: [JobsService],
  exports: [JobsService],
})
export class JobsModule {}
