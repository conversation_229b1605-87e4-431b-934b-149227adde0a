import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  UseGuards,
  Request,
  Query,
  Inject,
  Logger,
} from "@nestjs/common";
import {
  ApiBearerAuth,
  ApiBody,
  ApiOperation,
  ApiParam,
  ApiQuery,
  ApiResponse,
  ApiTags,
} from "@nestjs/swagger";
import { JobsService, PaginatedResponse } from "./jobs.service";
import { CreateJobDto } from "./dto/create-job.dto";
import { UpdateJobDto } from "./dto/update-job.dto";
import { QueryJobsDto } from "./dto/query-jobs.dto";
import { CancelJobDto } from "./dto/cancel-job.dto";
import { JwtAuthGuard } from "../auth/guards/jwt-auth.guard";
import { RolesGuard } from "../auth/guards/roles.guard";
import { Roles } from "../auth/decorators/roles.decorator";
import { UserRole } from "../common/enums/user-role.enum";
import { JobStatus } from "../common/enums/job-status.enum";
import { ApiPaginatedResponse } from "../common/decorators/api-paginated-response.decorator";
import { ApiSuccessResponse } from "../common/decorators/api-response-wrapper.decorator";
import { Job } from "./entities/job.entity";
import { JobResponseSwaggerDto } from "./dto/swagger.dto";
import { RequestWithUser } from "./interfaces/request.interface";

@ApiTags("jobs")
@Controller({
  path: "jobs",
  version: "1",
})
export class JobsController {
  private readonly logger = new Logger(JobsController.name);

  constructor(
    @Inject(JobsService)
    private readonly jobsService: JobsService
  ) {}

  @Post()
  @UseGuards(JwtAuthGuard, RolesGuard)
  @Roles(UserRole.COMPANY)
  @ApiBearerAuth()
  @ApiOperation({ summary: "Create a new job (Company only)" })
  @ApiBody({ type: CreateJobDto })
  @ApiResponse({
    status: 201,
    description: "Job created successfully",
    type: Job,
  })
  @ApiResponse({ status: 400, description: "Bad request" })
  @ApiResponse({ status: 401, description: "Unauthorized" })
  @ApiResponse({ status: 403, description: "Forbidden" })
  create(@Request() req: RequestWithUser, @Body() createJobDto: CreateJobDto) {
    return this.jobsService.create(req.user.id, createJobDto);
  }

  @Get()
  @ApiOperation({ summary: "Get all jobs" })
  @ApiPaginatedResponse(JobResponseSwaggerDto)
  @ApiQuery({ type: QueryJobsDto, required: false })
  findAll(
    @Query() queryJobsDto: QueryJobsDto
  ): Promise<PaginatedResponse<Job>> | PaginatedResponse<Job> {
    try {
      return this.jobsService.findAll(queryJobsDto);
    } catch (error) {
      this.logger.error(
        `Error processing query parameters: ${error.message}`,
        error.stack
      );
      return this.jobsService.handleQueryError<Job>(error, queryJobsDto);
    }
  }

  @Get("emergency")
  @UseGuards(JwtAuthGuard, RolesGuard)
  @Roles(UserRole.WORKER)
  @ApiBearerAuth()
  @ApiOperation({ summary: "Get emergency jobs (Worker only)" })
  @ApiPaginatedResponse(JobResponseSwaggerDto)
  @ApiResponse({
    status: 401,
    description: "Unauthorized",
    schema: {
      properties: {
        success: { type: "boolean", example: false },
        message: { type: "string", example: "Unauthorized" },
        error: { type: "string", example: "Unauthorized" },
        statusCode: { type: "number", example: 401 },
      },
    },
  })
  @ApiResponse({
    status: 403,
    description: "Forbidden",
    schema: {
      properties: {
        success: { type: "boolean", example: false },
        message: { type: "string", example: "Forbidden resource" },
        error: { type: "string", example: "Forbidden" },
        statusCode: { type: "number", example: 403 },
      },
    },
  })
  findEmergencyJobs(
    @Request() req: RequestWithUser,
    @Query() queryJobsDto: QueryJobsDto
  ): Promise<PaginatedResponse<Job>> | PaginatedResponse<Job> {
    try {
      return this.jobsService.findEmergencyJobs(
        req.user.trustScore,
        queryJobsDto
      );
    } catch (error) {
      this.logger.error(
        `Error processing emergency jobs query parameters: ${error.message}`,
        error.stack
      );
      return this.jobsService.handleQueryError<Job>(error, queryJobsDto);
    }
  }

  @Get("favorites")
  @UseGuards(JwtAuthGuard, RolesGuard)
  @Roles(UserRole.WORKER)
  @ApiBearerAuth()
  @ApiOperation({ summary: "Get favorite jobs (Worker only)" })
  @ApiPaginatedResponse(JobResponseSwaggerDto)
  @ApiResponse({
    status: 401,
    description: "Unauthorized",
    schema: {
      properties: {
        success: { type: "boolean", example: false },
        message: { type: "string", example: "Unauthorized" },
        error: { type: "string", example: "Unauthorized" },
        statusCode: { type: "number", example: 401 },
      },
    },
  })
  @ApiResponse({
    status: 403,
    description: "Forbidden",
    schema: {
      properties: {
        success: { type: "boolean", example: false },
        message: { type: "string", example: "Forbidden resource" },
        error: { type: "string", example: "Forbidden" },
        statusCode: { type: "number", example: 403 },
      },
    },
  })
  findFavoriteJobs(
    @Query() queryJobsDto: QueryJobsDto
  ): Promise<PaginatedResponse<Job>> | PaginatedResponse<Job> {
    try {
      return this.jobsService.findFavoriteJobs(queryJobsDto);
    } catch (error) {
      this.logger.error(
        `Error processing favorite jobs query parameters: ${error.message}`,
        error.stack
      );
      return this.jobsService.handleQueryError<Job>(error, queryJobsDto);
    }
  }

  @Get("nearby")
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: "Get nearby jobs" })
  @ApiPaginatedResponse(JobResponseSwaggerDto)
  @ApiQuery({
    name: "latitude",
    type: Number,
    required: true,
    example: 37.7749,
  })
  @ApiQuery({
    name: "longitude",
    type: Number,
    required: true,
    example: -122.4194,
  })
  @ApiQuery({
    name: "radius",
    type: Number,
    required: false,
    description: "Radius in kilometers",
    example: 10,
  })
  @ApiResponse({
    status: 401,
    description: "Unauthorized",
    schema: {
      properties: {
        success: { type: "boolean", example: false },
        message: { type: "string", example: "Unauthorized" },
        error: { type: "string", example: "Unauthorized" },
        statusCode: { type: "number", example: 401 },
      },
    },
  })
  @ApiResponse({
    status: 400,
    description: "Bad Request",
    schema: {
      properties: {
        success: { type: "boolean", example: false },
        message: {
          type: "string",
          example: "Latitude and longitude are required",
        },
        error: { type: "string", example: "Bad Request" },
        statusCode: { type: "number", example: 400 },
      },
    },
  })
  findNearbyJobs(
    @Request() req: RequestWithUser,
    @Query("latitude") latitude: number,
    @Query("longitude") longitude: number,
    @Query("radius") radius: number = 10,
    @Query() queryJobsDto: QueryJobsDto
  ): Promise<PaginatedResponse<Job>> | PaginatedResponse<Job> {
    try {
      if (!latitude || !longitude) {
        return this.jobsService.createEmptyPaginatedResponse<Job>(
          "Latitude and longitude are required",
          queryJobsDto?.page || 1,
          queryJobsDto?.limit || 10
        );
      }

      return this.jobsService.findNearbyJobs(
        latitude,
        longitude,
        radius,
        req.user.trustScore,
        queryJobsDto
      );
    } catch (error) {
      this.logger.error(
        `Error processing nearby jobs query parameters: ${error.message}`,
        error.stack
      );
      return this.jobsService.handleQueryError<Job>(error, queryJobsDto);
    }
  }

  @Get(":id")
  @ApiOperation({ summary: "Get job by ID" })
  @ApiParam({ name: "id", description: "Job ID" })
  @ApiSuccessResponse(JobResponseSwaggerDto)
  @ApiResponse({
    status: 404,
    description: "Job not found",
    schema: {
      properties: {
        success: { type: "boolean", example: false },
        message: { type: "string", example: "Job not found" },
        error: { type: "string", example: "Not Found" },
        statusCode: { type: "number", example: 404 },
      },
    },
  })
  findOne(@Param("id") id: string) {
    return this.jobsService.findOne(id);
  }

  @Patch(":id")
  @UseGuards(JwtAuthGuard, RolesGuard)
  @Roles(UserRole.COMPANY)
  @ApiBearerAuth()
  @ApiOperation({ summary: "Update job (Company only)" })
  @ApiParam({ name: "id", description: "Job ID" })
  @ApiBody({ type: UpdateJobDto })
  @ApiResponse({
    status: 200,
    description: "Job updated successfully",
    type: Job,
  })
  @ApiResponse({ status: 400, description: "Bad request" })
  @ApiResponse({ status: 401, description: "Unauthorized" })
  @ApiResponse({ status: 403, description: "Forbidden" })
  @ApiResponse({ status: 404, description: "Job not found" })
  update(
    @Param("id") id: string,
    @Request() req: RequestWithUser,
    @Body() updateJobDto: UpdateJobDto
  ) {
    return this.jobsService.update(id, req.user.id, updateJobDto);
  }

  @Patch(":id/status")
  @UseGuards(JwtAuthGuard, RolesGuard)
  @Roles(UserRole.COMPANY)
  @ApiBearerAuth()
  @ApiOperation({ summary: "Update job status (Company only)" })
  @ApiParam({ name: "id", description: "Job ID" })
  @ApiBody({
    schema: {
      type: "object",
      properties: {
        status: {
          type: "string",
          enum: Object.values(JobStatus),
          description: "Job status",
        },
      },
      required: ["status"],
    },
  })
  @ApiResponse({
    status: 200,
    description: "Job status updated successfully",
    type: Job,
  })
  @ApiResponse({ status: 400, description: "Bad request" })
  @ApiResponse({ status: 401, description: "Unauthorized" })
  @ApiResponse({ status: 403, description: "Forbidden" })
  @ApiResponse({ status: 404, description: "Job not found" })
  updateStatus(
    @Param("id") id: string,
    @Request() req: RequestWithUser,
    @Body("status") status: JobStatus
  ) {
    return this.jobsService.updateStatus(id, req.user.id, status);
  }

  @Patch(":id/cancel")
  @UseGuards(JwtAuthGuard, RolesGuard)
  @Roles(UserRole.COMPANY)
  @ApiBearerAuth()
  @ApiOperation({ summary: "Cancel job (Company only)" })
  @ApiParam({ name: "id", description: "Job ID" })
  @ApiBody({ type: CancelJobDto })
  @ApiResponse({
    status: 200,
    description: "Job cancelled successfully",
    type: Job,
  })
  @ApiResponse({ status: 400, description: "Bad request" })
  @ApiResponse({ status: 401, description: "Unauthorized" })
  @ApiResponse({ status: 403, description: "Forbidden" })
  @ApiResponse({ status: 404, description: "Job not found" })
  cancelJob(
    @Param("id") id: string,
    @Request() req: RequestWithUser,
    @Body() cancelJobDto: CancelJobDto
  ) {
    return this.jobsService.cancelJob(id, req.user.id, cancelJobDto.reason);
  }

  @Patch(":id/complete")
  @UseGuards(JwtAuthGuard, RolesGuard)
  @Roles(UserRole.COMPANY)
  @ApiBearerAuth()
  @ApiOperation({ summary: "Mark job as completed (Company only)" })
  @ApiParam({ name: "id", description: "Job ID" })
  @ApiResponse({
    status: 200,
    description: "Job marked as completed successfully",
    type: Job,
  })
  @ApiResponse({ status: 400, description: "Bad request" })
  @ApiResponse({ status: 401, description: "Unauthorized" })
  @ApiResponse({ status: 403, description: "Forbidden" })
  @ApiResponse({ status: 404, description: "Job not found" })
  completeJob(@Param("id") id: string, @Request() req: RequestWithUser) {
    return this.jobsService.completeJob(id, req.user.id);
  }

  @Delete(":id")
  @UseGuards(JwtAuthGuard, RolesGuard)
  @Roles(UserRole.COMPANY)
  @ApiBearerAuth()
  @ApiOperation({ summary: "Delete job (Company only)" })
  @ApiParam({ name: "id", description: "Job ID" })
  @ApiResponse({ status: 200, description: "Job deleted successfully" })
  @ApiResponse({ status: 400, description: "Bad request" })
  @ApiResponse({ status: 401, description: "Unauthorized" })
  @ApiResponse({ status: 403, description: "Forbidden" })
  @ApiResponse({ status: 404, description: "Job not found" })
  remove(@Param("id") id: string, @Request() req: RequestWithUser) {
    return this.jobsService.remove(id, req.user.id);
  }
}
