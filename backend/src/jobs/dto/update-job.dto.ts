import { ApiPropertyOptional } from "@nestjs/swagger"
import {
  IsString,
  IsNumber,
  IsBoolean,
  IsDate,
  IsOptional,
  Min,
  Max,
  Length,
  IsEnum,
  IsLatitude,
  IsLongitude,
  IsPositive,
} from "class-validator"
import { Type } from "class-transformer"
import { JobStatus } from "../../common/enums/job-status.enum"

export class UpdateJobDto {
  @ApiPropertyOptional({
    description: "Job title",
    example: "Senior Inventory Specialist for Retail Store",
  })
  @IsOptional()
  @IsString()
  @Length(3, 100)
  title?: string

  @ApiPropertyOptional({
    description: "Job description",
    example: "Updated description: We are looking for an experienced inventory specialist...",
  })
  @IsOptional()
  @IsString()
  @Length(10, 5000)
  description?: string

  @ApiPropertyOptional({
    description: "Job location (address)",
    example: "456 Main Street, New York, NY 10001",
  })
  @IsOptional()
  @IsString()
  location?: string

  @ApiPropertyOptional({
    description: "City",
    example: "New York",
  })
  @IsOptional()
  @IsString()
  city?: string

  @ApiPropertyOptional({
    description: "State/Province",
    example: "NY",
  })
  @IsOptional()
  @IsString()
  state?: string

  @ApiPropertyOptional({
    description: "Country",
    example: "US",
  })
  @IsOptional()
  @IsString()
  country?: string

  @ApiPropertyOptional({
    description: "Latitude coordinate",
    example: 40.7128,
  })
  @IsOptional()
  @IsLatitude()
  latitude?: number

  @ApiPropertyOptional({
    description: "Longitude coordinate",
    example: -74.006,
  })
  @IsOptional()
  @IsLongitude()
  longitude?: number

  @ApiPropertyOptional({
    description: "Job start date and time",
    example: "2023-12-02T09:00:00Z",
  })
  @IsOptional()
  @IsDate()
  @Type(() => Date)
  startDateTime?: Date

  @ApiPropertyOptional({
    description: "Job end date and time",
    example: "2023-12-02T17:00:00Z",
  })
  @IsOptional()
  @IsDate()
  @Type(() => Date)
  endDateTime?: Date

  @ApiPropertyOptional({
    description: "Pay rate amount",
    example: 30.0,
  })
  @IsOptional()
  @IsNumber()
  @IsPositive()
  payRate?: number

  @ApiPropertyOptional({
    description: "Pay rate type (hourly, daily, fixed)",
    example: "daily",
  })
  @IsOptional()
  @IsString()
  @IsEnum(["hourly", "daily", "fixed"])
  payRateType?: string

  @ApiPropertyOptional({
    description: "Minimum trust score required",
    minimum: 0,
    maximum: 100,
    example: 80,
  })
  @IsOptional()
  @IsNumber()
  @Min(0)
  @Max(100)
  trustScoreRequired?: number

  @ApiPropertyOptional({
    description: "Is this an emergency job?",
    example: true,
  })
  @IsOptional()
  @IsBoolean()
  isEmergencyJob?: boolean

  @ApiPropertyOptional({
    description: "Does this job require a laptop?",
    example: false,
  })
  @IsOptional()
  @IsBoolean()
  requiresLaptop?: boolean

  @ApiPropertyOptional({
    description: "Does this job require a smartphone?",
    example: false,
  })
  @IsOptional()
  @IsBoolean()
  requiresSmartphone?: boolean

  @ApiPropertyOptional({
    description: "Required skills (comma separated)",
    example: "Inventory management, Stock counting, Data entry, Team leadership",
  })
  @IsOptional()
  @IsString()
  requiredSkills?: string

  @ApiPropertyOptional({
    description: "Required experience",
    example: "Minimum 3 years in retail inventory",
  })
  @IsOptional()
  @IsString()
  requiredExperience?: string

  @ApiPropertyOptional({
    description: "Required education",
    example: "Associate's degree in business or related field",
  })
  @IsOptional()
  @IsString()
  requiredEducation?: string

  @ApiPropertyOptional({
    description: "Required languages (comma separated)",
    example: "English, Spanish, French",
  })
  @IsOptional()
  @IsString()
  requiredLanguages?: string

  @ApiPropertyOptional({
    description: "Additional requirements",
    example: "Must be able to lift up to 30 pounds and work in cold storage areas",
  })
  @IsOptional()
  @IsString()
  additionalRequirements?: string

  @ApiPropertyOptional({
    description: "Job status",
    enum: JobStatus,
    example: JobStatus.IN_PROGRESS,
  })
  @IsOptional()
  @IsEnum(JobStatus)
  status?: JobStatus

  @ApiPropertyOptional({
    description: "Maximum number of positions to fill",
    minimum: 1,
    example: 5,
  })
  @IsOptional()
  @IsNumber()
  @Min(1)
  maxPositions?: number
}
