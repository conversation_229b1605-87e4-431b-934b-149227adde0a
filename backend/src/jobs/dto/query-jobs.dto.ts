import { ApiPropertyOptional } from "@nestjs/swagger";
import {
  IsEnum,
  IsOptional,
  IsString,
  IsBoolean,
  IsN<PERSON>ber,
  <PERSON><PERSON>ate,
  <PERSON>,
  <PERSON>,
} from "class-validator";
import { Transform, Type } from "class-transformer";
import { JobStatus } from "../../common/enums/job-status.enum";
import { PaginationDto } from "../../common/dto/pagination.dto";

export class QueryJobsDto extends PaginationDto {
  @ApiPropertyOptional({
    description: "Filter jobs by status",
    enum: JobStatus,
    example: JobStatus.OPEN,
  })
  @IsOptional()
  @IsEnum(JobStatus)
  status?: JobStatus;

  @ApiPropertyOptional({
    description: "Filter jobs by company ID",
    example: "550e8400-e29b-41d4-a716-446655440000",
  })
  @IsOptional()
  @IsString()
  companyId?: string;

  @ApiPropertyOptional({
    description: "Filter jobs by minimum trust score required",
    minimum: 0,
    maximum: 100,
    example: 70,
  })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  @Min(0)
  @Max(100)
  trustScoreMin?: number;

  @ApiPropertyOptional({
    description: "Filter jobs by maximum trust score required",
    minimum: 0,
    maximum: 100,
    example: 90,
  })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  @Min(0)
  @Max(100)
  trustScoreMax?: number;

  @ApiPropertyOptional({
    description: "Filter by emergency job status",
    example: true,
  })
  @IsOptional()
  @Transform(({ value }) => {
    if (value === "true") return true;
    if (value === "false") return false;
    if (value === undefined) return undefined;
    return value;
  })
  @IsBoolean()
  isEmergency?: boolean;

  @ApiPropertyOptional({
    description: "Filter jobs by start date (minimum)",
    example: "2023-12-01T00:00:00Z",
  })
  @IsOptional()
  @Type(() => Date)
  @IsDate()
  startDateMin?: Date;

  @ApiPropertyOptional({
    description: "Filter jobs by start date (maximum)",
    example: "2023-12-31T23:59:59Z",
  })
  @IsOptional()
  @Type(() => Date)
  @IsDate()
  startDateMax?: Date;

  @ApiPropertyOptional({
    description: "Filter jobs by city",
    example: "New York",
  })
  @IsOptional()
  @IsString()
  city?: string;

  @ApiPropertyOptional({
    description: "Search jobs by title, description, or location",
    example: "inventory",
  })
  @IsOptional()
  @IsString()
  search?: string;

  @ApiPropertyOptional({
    description: "Filter jobs that require a laptop",
    example: true,
  })
  @IsOptional()
  @Transform(({ value }) => {
    if (value === "true") return true;
    if (value === "false") return false;
    if (value === undefined) return undefined;
    return value;
  })
  @IsBoolean()
  requiresLaptop?: boolean;

  @ApiPropertyOptional({
    description: "Filter jobs that require a smartphone",
    example: true,
  })
  @IsOptional()
  @Transform(({ value }) => {
    if (value === "true") return true;
    if (value === "false") return false;
    if (value === undefined) return undefined;
    return value;
  })
  @IsBoolean()
  requiresSmartphone?: boolean;
}
