import { Test, TestingModule } from "@nestjs/testing";
import { getRepositoryToken } from "@nestjs/typeorm";
import { Repository } from "typeorm";
import { NotFoundException, ForbiddenException } from "@nestjs/common";
import { JobsService } from "./jobs.service";
import { Job } from "./entities/job.entity";
import { GeolocationService } from "../common/services/geolocation.service";
import { SanitizationService } from "../common/services/sanitization.service";
import { JobStatus } from "../common/enums/job-status.enum";
import { UserRole } from "../common/enums/user-role.enum";

describe("JobsService", () => {
  let service: JobsService;
  let jobRepository: jest.Mocked<Repository<Job>>;
  let geolocationService: jest.Mocked<GeolocationService>;
  let sanitizationService: jest.Mocked<SanitizationService>;

  const mockJob = {
    id: "1",
    title: "Test Job",
    description: "Test Description",
    location: "Test Location",
    city: "Test City",
    state: "Test State",
    country: "Test Country",
    postalCode: "12345",
    latitude: 40.7128,
    longitude: -74.006,
    startDateTime: new Date(),
    endDateTime: new Date(),
    payRate: 25.0,
    estimatedHours: 8,
    payRateType: "hourly",
    trustScoreRequired: 0,
    isEmergencyJob: false,
    requiresLaptop: false,
    requiresSmartphone: true,
    requiredSkills: "communication",
    requiredExperience: "1 year",
    requiredEducation: "high school",
    requiredLanguages: "english",
    additionalRequirements: null,
    status: JobStatus.OPEN,
    cancelledReason: null,
    cancelledAt: null,
    maxPositions: 1,
    filledPositions: 0,
    companyId: "company1",
    company: null,
    applications: [],
    favorites: [],
    createdAt: new Date(),
    updatedAt: new Date(),
  };

  const mockUser = {
    id: "user1",
    email: "<EMAIL>",
    role: UserRole.WORKER,
  };

  const mockCompanyUser = {
    id: "company1",
    email: "<EMAIL>",
    role: UserRole.COMPANY,
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        JobsService,
        {
          provide: getRepositoryToken(Job),
          useValue: {
            find: jest.fn(),
            findOne: jest.fn(),
            findOneBy: jest.fn(),
            save: jest.fn(),
            update: jest.fn(),
            delete: jest.fn(),
            create: jest.fn(),
            createQueryBuilder: jest.fn(() => ({
              where: jest.fn().mockReturnThis(),
              andWhere: jest.fn().mockReturnThis(),
              orderBy: jest.fn().mockReturnThis(),
              skip: jest.fn().mockReturnThis(),
              take: jest.fn().mockReturnThis(),
              getMany: jest.fn(),
              getOne: jest.fn(),
              getManyAndCount: jest.fn(),
            })),
          },
        },
        {
          provide: GeolocationService,
          useValue: {
            calculateDistance: jest.fn(),
            getJobsNearLocation: jest.fn(),
          },
        },
        {
          provide: SanitizationService,
          useValue: {
            sanitizeText: jest.fn(),
            sanitizeObject: jest.fn(),
          },
        },
      ],
    }).compile();

    service = module.get<JobsService>(JobsService);
    jobRepository = module.get(getRepositoryToken(Job));
    geolocationService = module.get(GeolocationService);
    sanitizationService = module.get(SanitizationService);
  });

  describe("findAll", () => {
    it("should return paginated jobs", async () => {
      const jobs = [mockJob];
      const total = 1;

      const mockQueryBuilder = {
        where: jest.fn().mockReturnThis(),
        andWhere: jest.fn().mockReturnThis(),
        orderBy: jest.fn().mockReturnThis(),
        skip: jest.fn().mockReturnThis(),
        take: jest.fn().mockReturnThis(),
        getMany: jest.fn().mockResolvedValue(jobs),
        getOne: jest.fn(),
        getManyAndCount: jest.fn().mockResolvedValue([jobs, total]),
      };

      jobRepository.createQueryBuilder.mockReturnValue(mockQueryBuilder as any);

      const result = await service.findAll({ page: 1, limit: 10 });

      expect(result).toEqual({
        data: jobs,
        total,
        page: 1,
        limit: 10,
        totalPages: 1,
      });
    });

    it("should filter jobs by status", async () => {
      const jobs = [mockJob];
      const mockQueryBuilder = {
        where: jest.fn().mockReturnThis(),
        andWhere: jest.fn().mockReturnThis(),
        orderBy: jest.fn().mockReturnThis(),
        skip: jest.fn().mockReturnThis(),
        take: jest.fn().mockReturnThis(),
        getMany: jest.fn().mockResolvedValue(jobs),
        getOne: jest.fn(),
        getManyAndCount: jest.fn().mockResolvedValue([jobs, 1]),
      };

      jobRepository.createQueryBuilder.mockReturnValue(mockQueryBuilder as any);

      await service.findAll({ page: 1, limit: 10, status: JobStatus.OPEN });

      expect(mockQueryBuilder.andWhere).toHaveBeenCalledWith(
        "job.status = :status",
        { status: JobStatus.OPEN }
      );
    });

    it("should filter jobs by type", async () => {
      const jobs = [mockJob];
      const mockQueryBuilder = {
        where: jest.fn().mockReturnThis(),
        andWhere: jest.fn().mockReturnThis(),
        orderBy: jest.fn().mockReturnThis(),
        skip: jest.fn().mockReturnThis(),
        take: jest.fn().mockReturnThis(),
        getMany: jest.fn().mockResolvedValue(jobs),
        getOne: jest.fn(),
        getManyAndCount: jest.fn().mockResolvedValue([jobs, 1]),
      };

      jobRepository.createQueryBuilder.mockReturnValue(mockQueryBuilder as any);

      await service.findAll({
        page: 1,
        limit: 10,
      });
    });
  });

  describe("findOne", () => {
    it("should return a job by id", async () => {
      jobRepository.findOneBy.mockResolvedValue(mockJob as Job);

      const result = await service.findOne("1");

      expect(result).toEqual(mockJob);
      expect(jobRepository.findOneBy).toHaveBeenCalledWith({ id: "1" });
    });

    it("should throw NotFoundException when job not found", async () => {
      jobRepository.findOneBy.mockResolvedValue(null);

      await expect(service.findOne("nonexistent")).rejects.toThrow(
        NotFoundException
      );
    });
  });

  describe("create", () => {
    const createJobDto = {
      title: "New Job",
      description: "New Description",
      location: "New Location",
      startDateTime: new Date(),
      endDateTime: new Date(),
      payRate: 25.0,
      estimatedHours: 8,
    };

    it("should create a new job", async () => {
      const sanitizedDto = { ...createJobDto };
      const newJob = { ...mockJob, ...createJobDto };

      sanitizationService.sanitizeObject.mockReturnValue(sanitizedDto);
      jobRepository.create.mockReturnValue(newJob as Job);
      jobRepository.save.mockResolvedValue(newJob as Job);

      const result = await service.create("company1", createJobDto);

      expect(result).toEqual(newJob);
      expect(sanitizationService.sanitizeObject).toHaveBeenCalledWith(
        createJobDto,
        {
          title: "text",
          description: "text",
          location: "text",
        }
      );
      expect(jobRepository.create).toHaveBeenCalledWith({
        ...sanitizedDto,
        companyId: "company1",
        status: JobStatus.OPEN,
      });
    });

    it("should throw error for invalid company", async () => {
      await expect(
        service.create("invalid-company", createJobDto)
      ).rejects.toThrow();
    });
  });

  describe("update", () => {
    const updateJobDto = {
      title: "Updated Job",
      payRate: 30.0,
    };

    it("should update a job", async () => {
      const updatedJob = { ...mockJob, ...updateJobDto };

      jobRepository.findOneBy.mockResolvedValue(mockJob as Job);
      sanitizationService.sanitizeObject.mockReturnValue(updateJobDto);
      jobRepository.save.mockResolvedValue(updatedJob as Job);

      const result = await service.update("1", "company1", updateJobDto);

      expect(result).toEqual(updatedJob);
      expect(jobRepository.save).toHaveBeenCalledWith({
        ...mockJob,
        ...updateJobDto,
      });
    });

    it("should throw NotFoundException when job not found", async () => {
      jobRepository.findOneBy.mockResolvedValue(null);

      await expect(
        service.update("nonexistent", "company1", updateJobDto)
      ).rejects.toThrow(NotFoundException);
    });

    it("should throw ForbiddenException when company is not the job owner", async () => {
      jobRepository.findOneBy.mockResolvedValue(mockJob as Job);

      await expect(
        service.update("1", "other-company", updateJobDto)
      ).rejects.toThrow(ForbiddenException);
    });
  });

  describe("remove", () => {
    it("should delete a job", async () => {
      jobRepository.findOneBy.mockResolvedValue(mockJob as Job);
      jobRepository.delete.mockResolvedValue({ affected: 1, raw: {} });

      await service.remove("1", "company1");

      expect(jobRepository.delete).toHaveBeenCalledWith("1");
    });

    it("should throw NotFoundException when job not found", async () => {
      jobRepository.findOneBy.mockResolvedValue(null);

      await expect(service.remove("nonexistent", "company1")).rejects.toThrow(
        NotFoundException
      );
    });

    it("should throw ForbiddenException when company is not the job owner", async () => {
      jobRepository.findOneBy.mockResolvedValue(mockJob as Job);

      await expect(service.remove("1", "other-company")).rejects.toThrow(
        ForbiddenException
      );
    });
  });

  describe("findNearbyJobs", () => {
    it("should return jobs near a location", async () => {
      const nearbyJobs = [mockJob];
      const mockQueryBuilder = {
        where: jest.fn().mockReturnThis(),
        andWhere: jest.fn().mockReturnThis(),
        orderBy: jest.fn().mockReturnThis(),
        limit: jest.fn().mockReturnThis(),
        getMany: jest.fn().mockResolvedValue(nearbyJobs),
      };

      jobRepository.createQueryBuilder.mockReturnValue(mockQueryBuilder as any);

      const result = await service.findNearbyJobs(40.7128, -74.006, 10, 100, {
        page: 1,
        limit: 10,
      });

      expect(result).toEqual(nearbyJobs);
    });
  });
});
