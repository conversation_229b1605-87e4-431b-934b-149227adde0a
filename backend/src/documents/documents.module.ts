import { Module } from "@nestjs/common"
import { TypeOrmModule } from "@nestjs/typeorm"
import { Document } from "../users/entities/document.entity"
import { DocumentsService } from "./documents.service"
import { DocumentsController } from "./documents.controller"
import { UsersModule } from "../users/users.module"
import { NotificationsModule } from "../notifications/notifications.module"
import { ActivityLogModule } from "../activity-log/activity-log.module"

@Module({
  imports: [TypeOrmModule.forFeature([Document]), UsersModule, NotificationsModule, ActivityLogModule],
  providers: [DocumentsService],
  controllers: [DocumentsController],
  exports: [DocumentsService],
})
export class DocumentsModule {}
