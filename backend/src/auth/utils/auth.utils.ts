import { JwtService } from "@nestjs/jwt";
import * as bcrypt from "bcrypt";
import { User } from "../../users/entities/user.entity";
import { JwtPayload } from "../strategies/jwt.strategy";

/**
 * Utility class for auth-related operations
 */
export class AuthUtils {
  /**
   * Hash a password
   * @param password Plain text password
   * @returns Hashed password
   */
  static async hashPassword(password: string): Promise<string> {
    return bcrypt.hash(password, 10);
  }

  /**
   * Compare a plain text password with a hashed password
   * @param plainPassword Plain text password
   * @param hashedPassword Hashed password
   * @returns Whether the passwords match
   */
  static async comparePasswords(
    plainPassword: string,
    hashedPassword: string
  ): Promise<boolean> {
    return bcrypt.compare(plainPassword, hashedPassword);
  }

  /**
   * Remove password from user object
   * @param user User object with password
   * @returns User object without password
   */
  static excludePassword(user: User): Omit<User, "password"> {
    const { password, ...userWithoutPassword } = user;
    return userWithoutPassword;
  }

  /**
   * Generate JWT tokens for a user
   * @param user User object
   * @param jwtService JWT service instance
   * @returns Object containing access token and refresh token
   */
  static generateTokens(
    user: User | Omit<User, "password">,
    jwtService: JwtService
  ): { token: string; refreshToken: string } {
    const payload: JwtPayload = {
      email: user.email,
      phone: user.phone,
      sub: user.id,
      role: user.role,
    };

    const token = jwtService.sign(payload);
    const refreshToken = jwtService.sign(payload, { expiresIn: "30d" });

    return { token, refreshToken };
  }

  /**
   * Create auth response object
   * @param user User object
   * @param jwtService JWT service instance
   * @returns Auth response object
   */
  static createAuthResponse(
    user: User,
    jwtService: JwtService,
    isNewUser?: boolean
  ) {
    const userWithoutPassword = this.excludePassword(user);
    const { token, refreshToken } = this.generateTokens(user, jwtService);

    return {
      access_token: token, // Keep for backward compatibility
      token,
      refreshToken,
      user: userWithoutPassword,
      ...(isNewUser !== undefined && { isNewUser }),
    };
  }
}
