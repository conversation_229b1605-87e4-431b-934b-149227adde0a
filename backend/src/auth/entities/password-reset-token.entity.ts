import {
  <PERSON><PERSON><PERSON>,
  PrimaryGeneratedC<PERSON>umn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  Index,
} from "typeorm";
import { ApiProperty } from "@nestjs/swagger";

@Entity("password_reset_tokens")
export class PasswordResetToken {
  @ApiProperty({ description: "Unique identifier" })
  @PrimaryGeneratedColumn("uuid")
  id: string;

  @ApiProperty({ description: "User ID" })
  @Column()
  @Index()
  userId: string;

  @ApiProperty({ description: "Email address (if reset requested via email)" })
  @Column({ nullable: true })
  email?: string;

  @ApiProperty({ description: "Phone number (if reset requested via phone)" })
  @Column({ nullable: true })
  phone?: string;

  @ApiProperty({ description: "Reset token" })
  @Column()
  token: string;

  @ApiProperty({ description: "Token expiration date" })
  @Column()
  expiresAt: Date;

  @ApiProperty({ description: "Whether the token has been used" })
  @Column({ default: false })
  used: boolean;

  @ApiProperty({ description: "Creation date" })
  @CreateDateColumn()
  createdAt: Date;

  @ApiProperty({ description: "Last update date" })
  @UpdateDateColumn()
  updatedAt: Date;
}
