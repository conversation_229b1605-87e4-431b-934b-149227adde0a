import {
  Controller,
  Post,
  Body,
  HttpCode,
  HttpStatus,
  Inject,
} from "@nestjs/common";
import { AuthService } from "./auth.service";
import { ApiTags, ApiOperation, ApiResponse, ApiBody } from "@nestjs/swagger";
import {
  OtpRequestDto,
  OtpVerifyDto,
  AuthResponseDto,
  OtpResponseDto,
  OtpVerifyResponseDto,
  LoginDto,
  RegisterDto,
} from "./dto/auth.dto";
import {
  ForgotPasswordDto,
  ForgotPasswordResponseDto,
} from "./dto/forgot-password.dto";
import {
  ValidateResetTokenDto,
  ValidateResetTokenResponseDto,
  ResetPasswordDto,
  ResetPasswordResponseDto,
} from "./dto/reset-password.dto";

@ApiTags("auth")
@Controller("auth")
export class AuthController {
  constructor(@Inject(AuthService) private authService: AuthService) {}

  @Post("login")
  @HttpCode(HttpStatus.OK)
  @ApiOperation({ summary: "Login with email/phone and password" })
  @ApiBody({ type: LoginDto })
  @ApiResponse({
    status: 200,
    description: "Login successful",
    type: AuthResponseDto,
  })
  @ApiResponse({ status: 401, description: "Invalid credentials" })
  async login(@Body() loginDto: LoginDto) {
    return this.authService.login(loginDto);
  }

  @Post("register")
  @HttpCode(HttpStatus.CREATED)
  @ApiOperation({ summary: "Register a new user" })
  @ApiBody({ type: RegisterDto })
  @ApiResponse({
    status: 201,
    description: "User registered successfully",
    type: AuthResponseDto,
  })
  @ApiResponse({ status: 400, description: "Bad request" })
  async register(@Body() registerDto: RegisterDto) {
    return this.authService.registerWorker(registerDto);
  }

  @Post("otp/request")
  @HttpCode(HttpStatus.OK)
  @ApiOperation({ summary: "Request OTP for phone number" })
  @ApiBody({ type: OtpRequestDto })
  @ApiResponse({
    status: 200,
    description: "OTP sent successfully",
    type: OtpResponseDto,
  })
  async requestOtp(@Body() otpRequestDto: OtpRequestDto) {
    return this.authService.requestOtp(otpRequestDto);
  }

  @Post("otp/verify")
  @HttpCode(HttpStatus.OK)
  @ApiOperation({ summary: "Verify OTP and login/register" })
  @ApiBody({ type: OtpVerifyDto })
  @ApiResponse({
    status: 200,
    description: "OTP verified successfully",
    type: OtpVerifyResponseDto,
  })
  @ApiResponse({ status: 401, description: "Invalid OTP" })
  async verifyOtp(@Body() otpVerifyDto: OtpVerifyDto) {
    return this.authService.verifyOtp(otpVerifyDto);
  }

  @Post("forgot-password")
  @HttpCode(HttpStatus.OK)
  @ApiOperation({ summary: "Request password reset" })
  @ApiBody({ type: ForgotPasswordDto })
  @ApiResponse({
    status: 200,
    description: "Password reset instructions sent",
    type: ForgotPasswordResponseDto,
  })
  async forgotPassword(@Body() forgotPasswordDto: ForgotPasswordDto) {
    return this.authService.forgotPassword(forgotPasswordDto);
  }

  @Post("validate-reset-token")
  @HttpCode(HttpStatus.OK)
  @ApiOperation({ summary: "Validate password reset token" })
  @ApiBody({ type: ValidateResetTokenDto })
  @ApiResponse({
    status: 200,
    description: "Token validation result",
    type: ValidateResetTokenResponseDto,
  })
  async validateResetToken(
    @Body() validateResetTokenDto: ValidateResetTokenDto
  ) {
    return this.authService.validateResetToken(validateResetTokenDto);
  }

  @Post("reset-password")
  @HttpCode(HttpStatus.OK)
  @ApiOperation({ summary: "Reset password with token" })
  @ApiBody({ type: ResetPasswordDto })
  @ApiResponse({
    status: 200,
    description: "Password reset successful",
    type: ResetPasswordResponseDto,
  })
  @ApiResponse({ status: 401, description: "Invalid or expired token" })
  async resetPassword(@Body() resetPasswordDto: ResetPasswordDto) {
    return this.authService.resetPassword(resetPasswordDto);
  }
}
