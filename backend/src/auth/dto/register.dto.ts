import {
  IsEmail,
  IsNot<PERSON>mpty,
  IsString,
  Is<PERSON>num,
  <PERSON><PERSON><PERSON>al,
  <PERSON><PERSON><PERSON><PERSON>,
  ValidateIf,
} from "class-validator";
import { UserRole } from "src/common/enums/user-role.enum";

export class RegisterDto {
  @IsString()
  @IsNotEmpty()
  name: string;

  @IsEmail()
  @IsOptional()
  @ValidateIf((o) => !o.phone)
  email?: string;

  @IsString()
  @IsOptional()
  @ValidateIf((o) => !o.email)
  phone?: string;

  @IsString()
  @IsNotEmpty()
  @MinLength(6)
  password: string;

  // @IsEnum(UserRole)
  // @IsNotEmpty()
  // role: UserRole;

  // @IsString()
  // @IsOptional()
  // @ValidateIf((o) => o.role === UserRole.COMPANY)
  // companyName?: string;
}
