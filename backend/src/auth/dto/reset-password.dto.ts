import { ApiProperty } from "@nestjs/swagger";
import { IsNotEmpty, IsString, MinLength, Matches } from "class-validator";

export class ValidateResetTokenDto {
  @ApiProperty({
    example: "a1b2c3d4-e5f6-7890-abcd-ef1234567890",
    description: "Password reset token",
  })
  @IsString()
  @IsNotEmpty({ message: "Token is required" })
  token: string;
}

export class ValidateResetTokenResponseDto {
  @ApiProperty({
    example: true,
    description: "Whether the token is valid",
  })
  valid: boolean;

  @ApiProperty({
    example: "Token is valid",
    description: "Status message",
  })
  message: string;
}

export class ResetPasswordDto {
  @ApiProperty({
    example: "a1b2c3d4-e5f6-7890-abcd-ef1234567890",
    description: "Password reset token",
  })
  @IsString()
  @IsNotEmpty({ message: "Token is required" })
  token: string;

  @ApiProperty({
    example: "NewPassword123!",
    description: "New password",
  })
  @IsString()
  @MinLength(8, { message: "Password must be at least 8 characters long" })
  @Matches(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]+$/, {
    message:
      "Password must contain at least one uppercase letter, one lowercase letter, one number, and one special character",
  })
  password: string;
}

export class ResetPasswordResponseDto {
  @ApiProperty({
    example: "Password reset successful",
    description: "Success message",
  })
  message: string;
}
