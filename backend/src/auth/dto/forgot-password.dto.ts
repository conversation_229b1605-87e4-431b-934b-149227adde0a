import { ApiProperty, ApiPropertyOptional } from "@nestjs/swagger";
import { IsEmail, IsOptional, IsString, ValidateIf } from "class-validator";

export class ForgotPasswordDto {
  @ApiPropertyOptional({
    example: "<EMAIL>",
    description: "Email address to send password reset link",
  })
  @IsEmail({}, { message: "Please provide a valid email address" })
  @ValidateIf((o) => !o.phone)
  @IsOptional()
  email?: string;

  @ApiPropertyOptional({
    example: "+1234567890",
    description: "Phone number to send password reset OTP",
  })
  @IsString({ message: "Please provide a valid phone number" })
  @ValidateIf((o) => !o.email)
  @IsOptional()
  phone?: string;
}

export class ForgotPasswordResponseDto {
  @ApiProperty({
    example: "Password reset instructions sent successfully",
    description: "Success message",
  })
  message: string;
}
