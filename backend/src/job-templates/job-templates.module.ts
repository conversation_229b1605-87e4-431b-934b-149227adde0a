import { Module } from "@nestjs/common"
import { TypeOrmModule } from "@nestjs/typeorm"
import { JobTemplatesService } from "./job-templates.service"
import { JobTemplatesController } from "./job-templates.controller"
import { JobTemplate } from "./entities/job-template.entity"
import { ActivityLogModule } from "../activity-log/activity-log.module"

@Module({
  imports: [TypeOrmModule.forFeature([JobTemplate]), ActivityLogModule],
  controllers: [JobTemplatesController],
  providers: [JobTemplatesService],
  exports: [JobTemplatesService],
})
export class JobTemplatesModule {}
