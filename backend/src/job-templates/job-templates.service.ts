import { Inject, Injectable, NotFoundException } from "@nestjs/common"
import { InjectRepository } from "@nestjs/typeorm"
import type { Repository } from "typeorm"
import { JobTemplate } from "./entities/job-template.entity"
import { ActivityLogService } from "../activity-log/activity-log.service"
import { CreateJobTemplateDto } from "@shared/validation"

@Injectable()
export class JobTemplatesService {
  constructor(
    @InjectRepository(JobTemplate)
    private jobTemplatesRepository: Repository<JobTemplate>,
    // @Inject(ActivityLogService)
    private activityLogService: ActivityLogService,
  ) {}

  async create(createJobTemplateDto: CreateJobTemplateDto, userId: string): Promise<JobTemplate> {
    const jobTemplate = this.jobTemplatesRepository.create({
      ...createJobTemplateDto,
      postedBy: userId,
    })

    const savedTemplate = await this.jobTemplatesRepository.save(jobTemplate)

    await this.activityLogService.logActivity({
      userId,
      action: "job_template_created",
      description: `Created job template: ${savedTemplate.title}`,
      entityId: savedTemplate.id,
      entityType: "job_template",
    })

    return savedTemplate
  }

  async findAll(userId: string): Promise<JobTemplate[]> {
    return this.jobTemplatesRepository.find({
      where: { postedBy: userId },
      order: { createdAt: "DESC" },
    })
  }

  async findOne(id: string, userId: string): Promise<JobTemplate> {
    const jobTemplate = await this.jobTemplatesRepository.findOne({
      where: { id, postedBy: userId },
    })

    if (!jobTemplate) {
      throw new NotFoundException(`Job template with ID ${id} not found`)
    }

    return jobTemplate
  }

  async update(id: string, updateJobTemplateDto: Partial<CreateJobTemplateDto>, userId: string): Promise<JobTemplate> {
    const jobTemplate = await this.findOne(id, userId)

    Object.assign(jobTemplate, updateJobTemplateDto)

    const updatedTemplate = await this.jobTemplatesRepository.save(jobTemplate)

    await this.activityLogService.logActivity({
      userId,
      action: "job_template_updated",
      description: `Updated job template: ${updatedTemplate.title}`,
      entityId: updatedTemplate.id,
      entityType: "job_template",
    })

    return updatedTemplate
  }

  async remove(id: string, userId: string): Promise<void> {
    const jobTemplate = await this.findOne(id, userId)

    await this.jobTemplatesRepository.remove(jobTemplate)

    await this.activityLogService.logActivity({
      userId,
      action: "job_template_deleted",
      description: `Deleted job template: ${jobTemplate.title}`,
      entityId: id,
      entityType: "job_template",
    })
  }
}
