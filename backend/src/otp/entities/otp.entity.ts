import {
  <PERSON><PERSON><PERSON>,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
} from "typeorm";

@Entity("otps")
export class Otp {
  @PrimaryGeneratedColumn("uuid")
  id!: string;

  //TODO: use a common column to handle both of this types called identifier
  @Column({ nullable: true })
  phone?: string;

  @Column({ nullable: true })
  email?: string;

  @Column()
  code!: string;

  @Column()
  expiresAt!: Date;

  @Column({ default: false })
  verified!: boolean;

  @CreateDateColumn()
  createdAt!: Date;
}
