import { Injectable, Logger } from "@nestjs/common";
import { InjectRepository } from "@nestjs/typeorm";
import type { Repository } from "typeorm";
import { Otp } from "./entities/otp.entity";
import { OTP_CONFIG } from "./constants/otp.constants";

@Injectable()
export class OtpService {
  private readonly logger = new Logger(OtpService.name);

  constructor(
    @InjectRepository(Otp)
    private readonly otpRepository: Repository<Otp>
  ) {}

  async generateAndSendOtp(phone: string): Promise<{ message: string }> {
    // Generate OTP using configured length
    const code = Math.floor(
      OTP_CONFIG.MIN_VALUE +
        Math.random() * (OTP_CONFIG.MAX_VALUE - OTP_CONFIG.MIN_VALUE)
    ).toString();

    // Set expiry time using configured duration
    const expiresAt = new Date();
    expiresAt.setMinutes(expiresAt.getMinutes() + OTP_CONFIG.EXPIRY_MINUTES);

    // Save OTP to database
    await this.otpRepository.save({
      phone,
      code,
      expiresAt,
    });

    // In a real application, you would send the OTP via SMS
    // For development, we'll just log it
    this.logger.debug(`OTP for ${phone}: ${code}`);

    return { message: "OTP sent successfully" };
  }

  async verifyOtp(phone: string, code: string): Promise<boolean> {
    const otp = await this.otpRepository.findOne({
      where: {
        phone,
        code,
        verified: false,
      },
      order: {
        createdAt: "DESC",
      },
    });

    if (!otp) {
      return false;
    }

    // Check if OTP is expired
    if (new Date() > otp.expiresAt) {
      return false;
    }

    // Mark OTP as verified
    otp.verified = true;
    await this.otpRepository.save(otp);

    return true;
  }
}
