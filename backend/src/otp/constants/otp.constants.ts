/**
 * OTP Configuration Constants
 * 
 * This file centralizes all OTP-related constants to avoid magic numbers
 * and provide a single place to manage OTP configuration.
 */

export const OTP_CONFIG = {
  // OTP length (number of digits)
  LENGTH: 6,
  
  // OTP expiry time in minutes
  EXPIRY_MINUTES: 10,
  
  // Minimum value for OTP generation (100000 for 6-digit OTP)
  MIN_VALUE: 100000,
  
  // Maximum value for OTP generation (999999 for 6-digit OTP)
  MAX_VALUE: 999999,
  
  // Maximum attempts allowed for OTP verification
  MAX_ATTEMPTS: 3,
  
  // Cooldown period between OTP requests in minutes
  REQUEST_COOLDOWN_MINUTES: 1,
} as const;

export type OtpConfig = typeof OTP_CONFIG;
