import {
  Controller,
  Post,
  Body,
  HttpCode,
  HttpStatus,
  Inject,
} from "@nestjs/common";
import { ApiTags, ApiOperation, ApiResponse, ApiBody } from "@nestjs/swagger";
import { OtpService } from "./otp.service";
import { Public } from "../auth/decorators/public.decorator";
import { OtpRequestDto, OtpResponseDto, OtpVerifyDto } from "../auth/dto/auth.dto";

@ApiTags("otp")
@Controller("otp")
export class OtpController {
  constructor(
    @Inject(OtpService)
    private readonly otpService: OtpService
  ) {}

  @Public()
  @Post("request")
  @HttpCode(HttpStatus.OK)
  @ApiOperation({ summary: "Request OTP for phone verification" })
  @ApiBody({
    type: OtpRequestDto,
    description: "Phone number to send OTP",
    examples: {
      example1: {
        value: { phone: "+1234567890" },
        summary: "Standard phone number format",
      },
    },
  })
  @ApiResponse({
    status: 200,
    description: "OTP sent successfully",
    type: OtpResponseDto,
  })
  @ApiResponse({ status: 400, description: "Invalid phone number" })
  async requestOtp(@Body() otpRequestDto: OtpRequestDto) {
    return this.otpService.generateAndSendOtp(otpRequestDto.phone);
  }

  @Public()
  @Post("verify")
  @HttpCode(HttpStatus.OK)
  @ApiOperation({ summary: "Verify OTP code" })
  @ApiBody({
    type: OtpVerifyDto,
    description: "Phone number and OTP code to verify",
    examples: {
      example1: {
        value: { phone: "+1234567890", code: "123456" },
        summary: "Standard verification format",
      },
    },
  })
  @ApiResponse({
    status: 200,
    description: "OTP verified successfully",
    schema: {
      type: "object",
      properties: {
        success: { type: "boolean", example: true },
        message: { type: "string", example: "OTP verified successfully" },
      },
    },
  })
  @ApiResponse({ status: 400, description: "Invalid OTP or phone number" })
  @ApiResponse({ status: 401, description: "OTP verification failed" })
  async verifyOtp(@Body() otpVerifyDto: OtpVerifyDto) {
    return this.otpService.verifyOtp(otpVerifyDto.phone, otpVerifyDto.code);
  }
}
