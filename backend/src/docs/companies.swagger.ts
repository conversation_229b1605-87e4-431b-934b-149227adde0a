/**
 * @swagger
 * tags:
 *   name: Companies
 *   description: Company management endpoints
 */

/**
 * @swagger
 * components:
 *   schemas:
 *     Company:
 *       type: object
 *       properties:
 *         id:
 *           type: string
 *           format: uuid
 *           description: Unique identifier
 *         name:
 *           type: string
 *           description: Company name
 *         registrationNumber:
 *           type: string
 *           description: Company registration number
 *         taxId:
 *           type: string
 *           description: Company tax ID
 *         website:
 *           type: string
 *           description: Company website
 *         size:
 *           type: string
 *           description: Company size (number of employees)
 *         industry:
 *           type: string
 *           description: Company industry
 *         description:
 *           type: string
 *           description: Company description
 *         logo:
 *           type: string
 *           description: Company logo URL
 *         address:
 *           type: string
 *           description: Street address
 *         city:
 *           type: string
 *           description: City
 *         state:
 *           type: string
 *           description: State/Province
 *         postalCode:
 *           type: string
 *           description: Postal code
 *         country:
 *           type: string
 *           description: Country
 *         isKycVerified:
 *           type: boolean
 *           description: KYC verification status
 *         isActive:
 *           type: boolean
 *           description: Account active status
 *         isBanned:
 *           type: boolean
 *           description: Account banned status
 *         userId:
 *           type: string
 *           format: uuid
 *           description: Associated user ID
 *         createdAt:
 *           type: string
 *           format: date-time
 *           description: Creation date
 *         updatedAt:
 *           type: string
 *           format: date-time
 *           description: Last update date
 *       required:
 *         - id
 *         - name
 *         - isKycVerified
 *         - isActive
 *         - isBanned
 *         - userId
 *         - createdAt
 *         - updatedAt
 *
 *     CreateCompanyDto:
 *       type: object
 *       properties:
 *         name:
 *           type: string
 *           description: Company name
 *         registrationNumber:
 *           type: string
 *           description: Company registration number
 *         taxId:
 *           type: string
 *           description: Company tax ID
 *         website:
 *           type: string
 *           description: Company website
 *         size:
 *           type: string
 *           description: Company size (number of employees)
 *         industry:
 *           type: string
 *           description: Company industry
 *         description:
 *           type: string
 *           description: Company description
 *         address:
 *           type: string
 *           description: Street address
 *         city:
 *           type: string
 *           description: City
 *         state:
 *           type: string
 *           description: State/Province
 *         postalCode:
 *           type: string
 *           description: Postal code
 *         country:
 *           type: string
 *           description: Country
 *       required:
 *         - name
 *
 *     UpdateCompanyDto:
 *       type: object
 *       properties:
 *         name:
 *           type: string
 *           description: Company name
 *         registrationNumber:
 *           type: string
 *           description: Company registration number
 *         taxId:
 *           type: string
 *           description: Company tax ID
 *         website:
 *           type: string
 *           description: Company website
 *         size:
 *           type: string
 *           description: Company size (number of employees)
 *         industry:
 *           type: string
 *           description: Company industry
 *         description:
 *           type: string
 *           description: Company description
 *         address:
 *           type: string
 *           description: Street address
 *         city:
 *           type: string
 *           description: City
 *         state:
 *           type: string
 *           description: State/Province
 *         postalCode:
 *           type: string
 *           description: Postal code
 *         country:
 *           type: string
 *           description: Country
 *
 *     UpdateCompanyKycDto:
 *       type: object
 *       properties:
 *         registrationNumber:
 *           type: string
 *           description: Company registration number
 *         taxId:
 *           type: string
 *           description: Company tax ID
 *         notes:
 *           type: string
 *           description: Additional notes
 *       required:
 *         - registrationNumber
 *         - taxId
 */

/**
 * @swagger
 * /companies:
 *   post:
 *     summary: Create a company profile
 *     description: Create a company profile for the authenticated user
 *     tags: [Companies]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             $ref: '#/components/schemas/CreateCompanyDto'
 *     responses:
 *       201:
 *         description: Company created successfully
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Company'
 *       400:
 *         description: Bad request
 *       401:
 *         description: Unauthorized
 *       403:
 *         description: Forbidden
 *
 *   get:
 *     summary: Get all companies (Admin only)
 *     description: Get a list of all companies with pagination and filtering
 *     tags: [Companies]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: page
 *         schema:
 *           type: integer
 *           default: 1
 *         description: Page number
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *           default: 10
 *         description: Number of items per page
 *       - in: query
 *         name: search
 *         schema:
 *           type: string
 *         description: Search term for company name
 *       - in: query
 *         name: industry
 *         schema:
 *           type: string
 *         description: Filter by industry
 *       - in: query
 *         name: isKycVerified
 *         schema:
 *           type: boolean
 *         description: Filter by KYC verification status
 *       - in: query
 *         name: isActive
 *         schema:
 *           type: boolean
 *         description: Filter by active status
 *       - in: query
 *         name: sortBy
 *         schema:
 *           type: string
 *           default: createdAt
 *         description: Field to sort by
 *       - in: query
 *         name: sortOrder
 *         schema:
 *           type: string
 *           enum: [ASC, DESC]
 *           default: DESC
 *         description: Sort order
 *     responses:
 *       200:
 *         description: List of companies
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 data:
 *                   type: array
 *                   items:
 *                     $ref: '#/components/schemas/Company'
 *                 meta:
 *                   type: object
 *                   properties:
 *                     total:
 *                       type: integer
 *                     page:
 *                       type: integer
 *                     limit:
 *                       type: integer
 *                     totalPages:
 *                       type: integer
 *       401:
 *         description: Unauthorized
 *       403:
 *         description: Forbidden
 */

/**
 * @swagger
 * /companies/profile:
 *   get:
 *     summary: Get company profile for the authenticated user
 *     description: Get the company profile for the authenticated user
 *     tags: [Companies]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: Company profile found
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Company'
 *       401:
 *         description: Unauthorized
 *       403:
 *         description: Forbidden
 *       404:
 *         description: Company not found
 */

/**
 * @swagger
 * /companies/{id}:
 *   get:
 *     summary: Get company by ID
 *     description: Get a company by its ID
 *     tags: [Companies]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *           format: uuid
 *         description: Company ID
 *     responses:
 *       200:
 *         description: Company found
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Company'
 *       401:
 *         description: Unauthorized
 *       404:
 *         description: Company not found
 *
 *   patch:
 *     summary: Update company
 *     description: Update a company by its ID
 *     tags: [Companies]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *           format: uuid
 *         description: Company ID
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             $ref: '#/components/schemas/UpdateCompanyDto'
 *     responses:
 *       200:
 *         description: Company updated successfully
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Company'
 *       400:
 *         description: Bad request
 *       401:
 *         description: Unauthorized
 *       403:
 *         description: Forbidden
 *       404:
 *         description: Company not found
 *
 *   delete:
 *     summary: Delete company (Admin only)
 *     description: Delete a company by its ID
 *     tags: [Companies]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *           format: uuid
 *         description: Company ID
 *     responses:
 *       200:
 *         description: Company deleted successfully
 *       401:
 *         description: Unauthorized
 *       403:
 *         description: Forbidden
 *       404:
 *         description: Company not found
 */
