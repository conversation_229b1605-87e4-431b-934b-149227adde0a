import { ApiPropertyOptional } from "@nestjs/swagger"
import { IsString, IsInt, IsBoolean, IsOptional, Min, Max, Length } from "class-validator"

export class UpdateRatingDto {
  @ApiPropertyOptional({ description: "Rating stars (1-5)", minimum: 1, maximum: 5 })
  @IsOptional()
  @IsInt()
  @Min(1)
  @Max(5)
  stars?: number

  @ApiPropertyOptional({ description: "Rating comment or feedback" })
  @IsOptional()
  @IsString()
  @Length(0, 1000)
  comment?: string

  @ApiPropertyOptional({ description: "Should this rating be anonymous?" })
  @IsOptional()
  @IsBoolean()
  isAnonymous?: boolean
}
