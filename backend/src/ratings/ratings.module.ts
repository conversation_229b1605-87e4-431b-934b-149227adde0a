import { Modu<PERSON>, forwardRef } from "@nestjs/common";
import { TypeOrmModule } from "@nestjs/typeorm";
import { RatingsService } from "./ratings.service";
import { RatingsController } from "./ratings.controller";
import { Rating } from "./entities/rating.entity";
import { UsersModule } from "../users/users.module";
import { JobsModule } from "../jobs/jobs.module";
import { NotificationsModule } from "../notifications/notifications.module";
import { ActivityLogModule } from "../activity-log/activity-log.module";

@Module({
  imports: [
    TypeOrmModule.forFeature([Rating]),
    forwardRef(() => UsersModule),
    forwardRef(() => JobsModule),
    forwardRef(() => NotificationsModule),
    ActivityLogModule,
  ],
  controllers: [RatingsController],
  providers: [RatingsService],
  exports: [RatingsService],
})
export class RatingsModule {}
