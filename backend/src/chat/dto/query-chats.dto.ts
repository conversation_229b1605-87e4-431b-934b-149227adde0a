import { ApiPropertyOptional } from "@nestjs/swagger";
import { Transform } from "class-transformer";
import { IsBoolean, IsOptional } from "class-validator";
import { PaginationDto } from "../../common/dto/pagination.dto";

export class QueryChatsDto extends PaginationDto {
  @ApiPropertyOptional({
    description: "Filter to show only chats with unread messages",
    type: Boolean,
    example: false,
  })
  @IsOptional()
  @Transform(({ value }) => {
    if (value === "true") return true;
    if (value === "false") return false;
    return value;
  })
  @IsBoolean()
  unreadOnly?: boolean = false;
}
