import {
  WebSocketGateway,
  SubscribeMessage,
  MessageBody,
  WebSocketServer,
  ConnectedSocket,
  OnGatewayConnection,
  OnGatewayDisconnect,
  OnGatewayInit,
} from "@nestjs/websockets";
import { Server, Socket } from "socket.io";
import { Injectable, Logger, UseGuards } from "@nestjs/common";
import { GenericChatService } from "./generic-chat.service";
import { CreateGenericMessageDto } from "./dto/create-generic-message.dto";
import { JoinChatDto } from "./dto/join-chat.dto";
import { WsJwtGuard } from "../auth/guards/ws-jwt.guard";
import { MessageType } from "./enums/chat-type.enum";

interface AuthenticatedSocket extends Socket {
  userId?: string;
  userRole?: string;
}

@Injectable()
@WebSocketGateway({
  cors: {
    origin: [
      "http://localhost:3000",
      "http://localhost:3001",
      "http://localhost:8081",
    ],
    credentials: true,
  },
  namespace: "/chat",
})
export class ChatGateway
  implements OnGatewayInit, OnGatewayConnection, OnGatewayDisconnect
{
  @WebSocketServer()
  server: Server;

  private readonly logger = new Logger(ChatGateway.name);
  private connectedUsers = new Map<string, string>(); // userId -> socketId

  constructor(private readonly genericChatService: GenericChatService) {}

  afterInit(server: Server) {
    this.logger.log("WebSocket Gateway initialized");
  }

  async handleConnection(client: AuthenticatedSocket) {
    try {
      // Extract user info from the socket handshake
      const userId = client.handshake.auth?.userId;
      const userRole = client.handshake.auth?.userRole;

      if (!userId) {
        this.logger.warn(`Client ${client.id} connected without userId`);
        client.disconnect();
        return;
      }

      client.userId = userId;
      client.userRole = userRole;
      this.connectedUsers.set(userId, client.id);

      this.logger.log(`User ${userId} connected with socket ${client.id}`);

      // Join user to their personal room for notifications
      await client.join(`user:${userId}`);

      // Emit connection success
      client.emit("connected", {
        message: "Connected to chat server",
        userId,
        socketId: client.id,
      });
    } catch (error) {
      this.logger.error(`Connection error for client ${client.id}:`, error);
      client.disconnect();
    }
  }

  handleDisconnect(client: AuthenticatedSocket) {
    if (client.userId) {
      this.connectedUsers.delete(client.userId);
      this.logger.log(`User ${client.userId} disconnected`);
    }
  }

  @UseGuards(WsJwtGuard)
  @SubscribeMessage("join_chat")
  async handleJoinChat(
    @ConnectedSocket() client: AuthenticatedSocket,
    @MessageBody() data: JoinChatDto
  ) {
    try {
      const { chatId } = data;
      const userId = client.userId;

      if (!userId) {
        client.emit("error", { message: "User not authenticated" });
        return;
      }

      // Verify user has access to this chat
      const chat = await this.genericChatService.findGenericChatById(
        chatId,
        userId
      );
      if (!chat) {
        client.emit("error", { message: "Chat not found or access denied" });
        return;
      }

      // Join the chat room
      await client.join(`chat:${chatId}`);

      this.logger.log(`User ${userId} joined chat ${chatId}`);

      // Notify other participants that user joined
      client.to(`chat:${chatId}`).emit("user_joined", {
        userId,
        chatId,
        timestamp: new Date().toISOString(),
      });

      // Confirm join to the user
      client.emit("chat_joined", {
        chatId,
        message: "Successfully joined chat",
      });
    } catch (error) {
      this.logger.error("Error joining chat:", error);
      client.emit("error", { message: "Failed to join chat" });
    }
  }

  @UseGuards(WsJwtGuard)
  @SubscribeMessage("leave_chat")
  async handleLeaveChat(
    @ConnectedSocket() client: AuthenticatedSocket,
    @MessageBody() data: { chatId: string }
  ) {
    try {
      const { chatId } = data;
      const userId = client.userId;

      await client.leave(`chat:${chatId}`);

      this.logger.log(`User ${userId} left chat ${chatId}`);

      // Notify other participants that user left
      client.to(`chat:${chatId}`).emit("user_left", {
        userId,
        chatId,
        timestamp: new Date().toISOString(),
      });

      client.emit("chat_left", { chatId });
    } catch (error) {
      this.logger.error("Error leaving chat:", error);
      client.emit("error", { message: "Failed to leave chat" });
    }
  }

  @UseGuards(WsJwtGuard)
  @SubscribeMessage("send_message")
  async handleSendMessage(
    @ConnectedSocket() client: AuthenticatedSocket,
    @MessageBody() data: { chatId: string; message: string }
  ) {
    try {
      const { chatId, message } = data;
      const userId = client.userId;

      if (!userId) {
        client.emit("error", { message: "User not authenticated" });
        return;
      }

      // Create message using the chat service
      const createMessageDto: CreateGenericMessageDto = {
        message,
        messageType: MessageType.TEXT,
      };
      const savedMessage = await this.genericChatService.addGenericMessage(
        chatId,
        userId,
        createMessageDto
      );

      // Emit the message to all participants in the chat
      this.server.to(`chat:${chatId}`).emit("new_message", {
        id: savedMessage.id,
        chatId,
        message: savedMessage.message,
        senderId: savedMessage.senderId,
        sender: savedMessage.sender,
        isRead: savedMessage.isRead,
        createdAt: savedMessage.createdAt,
      });

      this.logger.log(`Message sent in chat ${chatId} by user ${userId}`);
    } catch (error) {
      this.logger.error("Error sending message:", error);
      client.emit("error", { message: "Failed to send message" });
    }
  }

  @UseGuards(WsJwtGuard)
  @SubscribeMessage("mark_messages_read")
  async handleMarkMessagesRead(
    @ConnectedSocket() client: AuthenticatedSocket,
    @MessageBody() data: { chatId: string }
  ) {
    try {
      const { chatId } = data;
      const userId = client.userId;

      if (!userId) {
        client.emit("error", { message: "User not authenticated" });
        return;
      }

      await this.genericChatService.markAsRead(chatId, userId);

      // Notify other participants that messages were read
      client.to(`chat:${chatId}`).emit("messages_read", {
        chatId,
        userId,
        timestamp: new Date().toISOString(),
      });

      client.emit("messages_marked_read", { chatId });
    } catch (error) {
      this.logger.error("Error marking messages as read:", error);
      client.emit("error", { message: "Failed to mark messages as read" });
    }
  }

  @SubscribeMessage("typing_start")
  async handleTypingStart(
    @ConnectedSocket() client: AuthenticatedSocket,
    @MessageBody() data: { chatId: string }
  ) {
    const { chatId } = data;
    const userId = client.userId;

    if (!userId) return;

    // Notify other participants that user is typing
    client.to(`chat:${chatId}`).emit("user_typing", {
      chatId,
      userId,
      isTyping: true,
    });
  }

  @SubscribeMessage("typing_stop")
  async handleTypingStop(
    @ConnectedSocket() client: AuthenticatedSocket,
    @MessageBody() data: { chatId: string }
  ) {
    const { chatId } = data;
    const userId = client.userId;

    if (!userId) return;

    // Notify other participants that user stopped typing
    client.to(`chat:${chatId}`).emit("user_typing", {
      chatId,
      userId,
      isTyping: false,
    });
  }

  /**
   * Send a notification to a specific user
   */
  async sendNotificationToUser(userId: string, notification: any) {
    const socketId = this.connectedUsers.get(userId);
    if (socketId) {
      this.server.to(`user:${userId}`).emit("notification", notification);
    }
  }

  /**
   * Check if a user is online
   */
  isUserOnline(userId: string): boolean {
    return this.connectedUsers.has(userId);
  }

  /**
   * Get all online users
   */
  getOnlineUsers(): string[] {
    return Array.from(this.connectedUsers.keys());
  }
}
