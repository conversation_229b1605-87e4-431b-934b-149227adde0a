import {
  <PERSON><PERSON><PERSON>,
  PrimaryGeneratedColumn,
  Column,
  OneToMany,
  CreateDateColumn,
  UpdateDateColumn,
  ManyToOne,
  JoinColumn,
  Index,
} from "typeorm";
import { ChatMessage } from "./chat-message.entity";
import { ChatParticipant } from "./chat-participant.entity";
import { User } from "src/users/entities/user.entity";
import { Job } from "src/jobs/entities/job.entity";

/**
 * Legacy Chat Entity - Kept for backward compatibility
 * New chats should use GenericChat entity
 * @deprecated Use GenericChat instead
 */
@Entity("chats")
export class Chat {
  @PrimaryGeneratedColumn("uuid")
  id: string;

  @ManyToOne(() => User, { nullable: true })
  @JoinColumn({ name: "workerId" })
  worker?: User;

  @Column({ nullable: true })
  @Index()
  workerId?: string;

  @ManyToOne(() => User, { nullable: true })
  @JoinColumn({ name: "companyId" })
  company?: User;

  @Column({ nullable: true })
  @Index()
  companyId?: string;

  @ManyToOne(() => Job, { nullable: true })
  @JoinColumn({ name: "jobId" })
  job?: Job;

  @Column({ nullable: true })
  @Index()
  jobId?: string;

  @Column({ default: 0 })
  unreadWorkerCount: number;

  @Column({ default: 0 })
  unreadCompanyCount: number;

  @Column({ default: true })
  isActive: boolean;

  @Column({ nullable: true })
  lastMessageAt: Date;

  @OneToMany(() => ChatMessage, (message) => message.chat)
  messages: ChatMessage[];

  @OneToMany(() => ChatParticipant, (participant) => participant.chat, {
    cascade: true,
  })
  participants: ChatParticipant[];

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;
}
