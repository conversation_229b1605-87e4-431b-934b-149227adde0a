import {
  <PERSON><PERSON><PERSON>,
  PrimaryGeneratedColumn,
  Column,
  OneToMany,
  CreateDateColumn,
  UpdateDateColumn,
  Index,
} from "typeorm";
import { ChatMessage } from "./chat-message.entity";
import { ChatParticipant } from "./chat-participant.entity";
import { ChatType } from "../enums/chat-type.enum";

@Entity("generic_chats")
export class GenericChat {
  @PrimaryGeneratedColumn("uuid")
  id: string;

  @Column({
    type: "enum",
    enum: ChatType,
    default: ChatType.GENERAL,
  })
  @Index()
  type: ChatType;

  @Column({ nullable: true })
  title?: string;

  @Column({ nullable: true })
  description?: string;

  @Column({ nullable: true })
  @Index()
  contextId?: string; // Generic context ID (jobId, supportTicketId, etc.)

  @Column({ nullable: true })
  contextType?: string; // Type of context (job, support_ticket, etc.)

  @Column({ type: "jsonb", nullable: true })
  metadata?: Record<string, any>; // Flexible metadata for different chat types

  @Column({ default: true })
  isActive: boolean;

  @Column({ nullable: true })
  @Index()
  createdBy: string; // User ID who created the chat

  @Column({ nullable: true })
  lastMessageAt: Date;

  @OneToMany(() => ChatParticipant, (participant) => participant.chat, {
    cascade: true,
  })
  participants: ChatParticipant[];

  @OneToMany(() => ChatMessage, (message) => message.genericChat)
  messages: ChatMessage[];

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;

  // Helper methods for backward compatibility
  get workerId(): string | undefined {
    const workerParticipant = this.participants?.find(
      (p) => p.role === "worker"
    );
    return workerParticipant?.userId;
  }

  get companyId(): string | undefined {
    const companyParticipant = this.participants?.find(
      (p) => p.role === "company"
    );
    return companyParticipant?.userId;
  }

  get worker(): any {
    const workerParticipant = this.participants?.find(
      (p) => p.role === "worker"
    );
    return workerParticipant?.user;
  }

  get company(): any {
    const companyParticipant = this.participants?.find(
      (p) => p.role === "company"
    );
    return companyParticipant?.user;
  }

  get jobId(): string | undefined {
    return this.contextType === "job" ? this.contextId : undefined;
  }

  get job(): any {
    return this.metadata?.job;
  }

  get unreadWorkerCount(): number {
    const workerParticipant = this.participants?.find(
      (p) => p.role === "worker"
    );
    return workerParticipant?.unreadCount || 0;
  }

  get unreadCompanyCount(): number {
    const companyParticipant = this.participants?.find(
      (p) => p.role === "company"
    );
    return companyParticipant?.unreadCount || 0;
  }
}
