import {
  <PERSON>,
  Get,
  Post,
  Body,
  Param,
  UseGuards,
  Request,
  Query,
  Patch,
} from "@nestjs/common";
import { GenericChatService } from "./generic-chat.service";
import { JwtAuthGuard } from "../auth/guards/jwt-auth.guard";
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiBearerAuth,
  ApiQuery,
  ApiParam,
} from "@nestjs/swagger";
import { CreateGenericChatDto } from "./dto/create-generic-chat.dto";
import { CreateGenericMessageDto } from "./dto/create-generic-message.dto";
import { RequestWithUser } from "../auth/interfaces/request.interface";

@ApiTags("Generic Chat")
@ApiBearerAuth()
@UseGuards(JwtAuthGuard)
@Controller("generic-chats")
export class GenericChatController {
  constructor(private readonly genericChatService: GenericChatService) {}

  @Post()
  @ApiOperation({ summary: "Create a new generic chat" })
  @ApiResponse({
    status: 201,
    description: "Chat created successfully",
  })
  @ApiResponse({
    status: 400,
    description: "Bad request - validation failed",
  })
  async createChat(
    @Request() req: RequestWithUser,
    @Body() createChatDto: CreateGenericChatDto
  ) {
    const chat = await this.genericChatService.createGenericChat(
      req.user.id,
      createChatDto
    );
    return {
      success: true,
      data: chat,
      message: "Chat created successfully",
    };
  }

  @Get()
  @ApiOperation({ summary: "Get all chats for the authenticated user" })
  @ApiQuery({ name: "page", required: false, type: Number, description: "Page number" })
  @ApiQuery({ name: "limit", required: false, type: Number, description: "Items per page" })
  @ApiResponse({
    status: 200,
    description: "Chats retrieved successfully",
  })
  async getChats(
    @Request() req: RequestWithUser,
    @Query("page") page: number = 1,
    @Query("limit") limit: number = 10
  ) {
    return this.genericChatService.findChatsForUser(req.user.id, page, limit);
  }

  @Get(":id")
  @ApiOperation({ summary: "Get a specific chat by ID" })
  @ApiParam({ name: "id", description: "Chat ID" })
  @ApiResponse({
    status: 200,
    description: "Chat retrieved successfully",
  })
  @ApiResponse({
    status: 404,
    description: "Chat not found",
  })
  async getChatById(
    @Request() req: RequestWithUser,
    @Param("id") chatId: string
  ) {
    const chat = await this.genericChatService.findGenericChatById(
      chatId,
      req.user.id
    );
    return {
      success: true,
      data: chat,
      message: "Chat retrieved successfully",
    };
  }

  @Post(":id/messages")
  @ApiOperation({ summary: "Send a message to a chat" })
  @ApiParam({ name: "id", description: "Chat ID" })
  @ApiResponse({
    status: 201,
    description: "Message sent successfully",
  })
  @ApiResponse({
    status: 404,
    description: "Chat not found",
  })
  async sendMessage(
    @Request() req: RequestWithUser,
    @Param("id") chatId: string,
    @Body() createMessageDto: CreateGenericMessageDto
  ) {
    const message = await this.genericChatService.addGenericMessage(
      chatId,
      req.user.id,
      createMessageDto
    );
    return {
      success: true,
      data: message,
      message: "Message sent successfully",
    };
  }

  @Patch(":id/read")
  @ApiOperation({ summary: "Mark chat messages as read" })
  @ApiParam({ name: "id", description: "Chat ID" })
  @ApiResponse({
    status: 200,
    description: "Messages marked as read",
  })
  @ApiResponse({
    status: 404,
    description: "Chat not found",
  })
  async markAsRead(
    @Request() req: RequestWithUser,
    @Param("id") chatId: string
  ) {
    await this.genericChatService.markAsRead(chatId, req.user.id);
    return {
      success: true,
      message: "Messages marked as read",
    };
  }
}
