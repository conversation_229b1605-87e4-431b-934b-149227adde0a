export enum ChatType {
  WORKER_COMPANY = "worker_company",
  SUPPORT = "support",
  GROUP = "group",
  ADMIN_USER = "admin_user",
  GENERAL = "general",
}

export enum ParticipantRole {
  WORKER = "worker",
  COMPANY = "company",
  ADMIN = "admin",
  SUPPORT = "support",
  MODERATOR = "moderator",
  MEMBER = "member",
}

export enum MessageType {
  TEXT = "text",
  IMAGE = "image",
  FILE = "file",
  SYSTEM = "system",
  NOTIFICATION = "notification",
}

export enum MessageStatus {
  SENT = "sent",
  DELIVERED = "delivered",
  READ = "read",
  FAILED = "failed",
}
