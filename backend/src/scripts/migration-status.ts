import { NestFactory } from '@nestjs/core';
import { AppModule } from '../app.module';
import { MigrationService } from '../database/migrations/migration.service';
import { Logger } from '@nestjs/common';

/**
 * Script to check migration status
 */
async function bootstrap() {
  const logger = new Logger('MigrationStatus');
  
  try {
    logger.log('Checking migration status...');
    
    // Create NestJS application
    const app = await NestFactory.createApplicationContext(AppModule);
    
    // Get migration service
    const migrationService = app.get(MigrationService);
    
    // Get migration status
    const status = await migrationService.getAllMigrations();
    
    // Display status
    logger.log('Migration status:');
    logger.log(`Applied migrations: ${status.applied.length}`);
    logger.log(`Pending migrations: ${status.pending.length}`);
    
    if (status.applied.length > 0) {
      logger.log('\nApplied migrations:');
      status.applied.forEach((migration) => {
        const date = new Date(migration.executedAt).toLocaleString();
        logger.log(`- ${migration.name} (applied: ${migration.applied ? 'Yes' : 'No'}, executed: ${date})`);
      });
    }
    
    if (status.pending.length > 0) {
      logger.log('\nPending migrations:');
      status.pending.forEach((migration) => {
        logger.log(`- ${migration}`);
      });
    }
    
    // Close application
    await app.close();
    
    process.exit(0);
  } catch (error) {
    logger.error(`Migration status check failed: ${error.message}`);
    process.exit(1);
  }
}

bootstrap();
