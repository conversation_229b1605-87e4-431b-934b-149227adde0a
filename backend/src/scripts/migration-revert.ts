import { NestFactory } from '@nestjs/core';
import { AppModule } from '../app.module';
import { MigrationService } from '../database/migrations/migration.service';
import { Logger } from '@nestjs/common';

/**
 * Script to revert the last applied migration
 */
async function bootstrap() {
  const logger = new Logger('MigrationRevert');
  
  try {
    logger.log('Starting migration reversion process...');
    
    // Create NestJS application
    const app = await NestFactory.createApplicationContext(AppModule);
    
    // Get migration service
    const migrationService = app.get(MigrationService);
    
    // Revert last migration
    await migrationService.revertLastMigration();
    
    // Close application
    await app.close();
    
    logger.log('Migration reversion process completed successfully.');
    process.exit(0);
  } catch (error) {
    logger.error(`Migration reversion process failed: ${error.message}`);
    process.exit(1);
  }
}

bootstrap();
