import { DataSource } from "typeorm";
import { dataSourceOptions } from "../database/data-source";
import * as path from "path";
import * as fs from "fs";

async function createMigration() {
  const migrationName = process.argv[2];
  
  if (!migrationName) {
    console.error("Please provide a migration name");
    console.error("Usage: npm run migration:create <migration-name>");
    process.exit(1);
  }

  try {
    const dataSource = new DataSource(dataSourceOptions);
    await dataSource.initialize();

    const timestamp = Date.now();
    const fileName = `${timestamp}-${migrationName}.ts`;
    const migrationPath = path.join(__dirname, "../database/migrations/files", fileName);

    const migrationTemplate = `import { MigrationInterface, QueryRunner } from "typeorm";

export class ${migrationName}${timestamp} implements MigrationInterface {
    name = '${migrationName}${timestamp}'

    public async up(queryRunner: QueryRunner): Promise<void> {
        // Add your migration logic here
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        // Add your rollback logic here
    }
}
`;

    // Ensure the migrations directory exists
    const migrationsDir = path.dirname(migrationPath);
    if (!fs.existsSync(migrationsDir)) {
      fs.mkdirSync(migrationsDir, { recursive: true });
    }

    fs.writeFileSync(migrationPath, migrationTemplate);
    
    console.log(`Migration ${fileName} has been created successfully!`);
    console.log(`Path: ${migrationPath}`);
    
    await dataSource.destroy();
  } catch (error) {
    console.error("Error creating migration:", error);
    process.exit(1);
  }
}

createMigration();
