import { ApiPropertyOptional } from "@nestjs/swagger";
import { IsString, IsOptional, IsEnum, Length } from "class-validator";
import { ApplicationStatus } from "@shared/types";

export class UpdateApplicationDto {
  @ApiPropertyOptional({
    description: "Application status",
    enum: ApplicationStatus,
    example: ApplicationStatus.ACCEPTED,
  })
  @IsOptional()
  @IsEnum(ApplicationStatus)
  status?: ApplicationStatus;

  @ApiPropertyOptional({
    description: "Rejection reason (if status is REJECTED)",
    example:
      "We have selected a candidate with more experience in retail inventory.",
  })
  @IsOptional()
  @IsString()
  @Length(0, 500)
  rejectionReason?: string;

  @ApiPropertyOptional({
    description: "Cancellation reason (if status is CANCELLED)",
    example: "I am no longer available on the scheduled date.",
  })
  @IsOptional()
  @IsString()
  @Length(0, 500)
  cancellationReason?: string;
}
