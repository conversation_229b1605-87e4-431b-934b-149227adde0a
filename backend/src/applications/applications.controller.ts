import {
  Controller,
  Get,
  Post,
  Patch,
  Param,
  Body,
  Query,
  UseGuards,
  Request,
  Inject,
} from "@nestjs/common";
import { ApplicationsService } from "./applications.service";
import { JwtAuthGuard } from "../auth/guards/jwt-auth.guard";
import { RolesGuard } from "../auth/guards/roles.guard";
import { Roles } from "../auth/decorators/roles.decorator";
import { CreateApplicationDto } from "./dto/create-application.dto";
import { UpdateApplicationDto } from "./dto/update-application.dto";
import { ApplicationStatus, UserRole } from "@shared/types";
import { RequestWithUser } from "../auth/interfaces/request.interface";
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiBearerAuth,
  ApiParam,
  ApiBody,
  ApiQuery,
} from "@nestjs/swagger";

@ApiTags("applications")
@Controller("applications")
export class ApplicationsController {
  constructor(
    @Inject(ApplicationsService)
    private applicationsService: ApplicationsService
  ) {}

  @Post()
  @UseGuards(JwtAuthGuard, RolesGuard)
  @Roles(UserRole.WORKER)
  @ApiBearerAuth()
  @ApiOperation({ summary: "Create a new application (Worker only)" })
  @ApiResponse({ status: 201, description: "Application created successfully" })
  @ApiBody({ type: CreateApplicationDto })
  async create(
    @Request() req: RequestWithUser,
    @Body() createApplicationDto: CreateApplicationDto
  ) {
    return this.applicationsService.apply(
      req.user.id,
      createApplicationDto.jobId,
      createApplicationDto.coverLetter
    );
  }

  @Get()
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: "Get all applications" })
  @ApiResponse({ status: 200, description: "Return all applications" })
  @ApiQuery({ name: "jobId", type: String, required: false })
  @ApiQuery({ name: "workerId", type: String, required: false })
  @ApiQuery({ name: "status", enum: ApplicationStatus, required: false })
  async findAll(
    @Request() req: RequestWithUser,
    @Query("page") page: number = 1,
    @Query("limit") limit: number = 10,
    @Query("jobId") jobId?: string,
    @Query("workerId") workerId?: string,
    @Query("status") status?: ApplicationStatus
  ) {
    return this.applicationsService.findAllWithRoleFiltering(
      req.user.id,
      req.user.role,
      page,
      limit,
      {
        jobId,
        workerId,
        status,
      }
    );
  }

  @Get(":id")
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: "Get application by ID" })
  @ApiResponse({ status: 200, description: "Return application by ID" })
  @ApiResponse({ status: 404, description: "Application not found" })
  @ApiParam({ name: "id", description: "Application ID" })
  async findOne(@Param("id") id: string, @Request() req: RequestWithUser) {
    return this.applicationsService.findOneWithPermissionCheck(
      id,
      req.user.id,
      req.user.role
    );
  }

  @Patch(":id")
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: "Update application" })
  @ApiResponse({ status: 200, description: "Application updated successfully" })
  @ApiResponse({ status: 400, description: "Bad request" })
  @ApiResponse({ status: 404, description: "Application not found" })
  @ApiParam({ name: "id", description: "Application ID" })
  @ApiBody({ type: UpdateApplicationDto })
  async update(
    @Param("id") id: string,
    @Request() req: RequestWithUser,
    @Body() updateApplicationDto: UpdateApplicationDto
  ) {
    return this.applicationsService.update(
      id,
      req.user.id,
      updateApplicationDto
    );
  }

  @Patch(":id/accept")
  @UseGuards(JwtAuthGuard, RolesGuard)
  @Roles(UserRole.COMPANY)
  @ApiBearerAuth()
  @ApiOperation({ summary: "Accept application (Company only)" })
  @ApiResponse({
    status: 200,
    description: "Application accepted successfully",
  })
  @ApiResponse({ status: 400, description: "Bad request" })
  @ApiResponse({ status: 404, description: "Application not found" })
  @ApiParam({ name: "id", description: "Application ID" })
  async acceptApplication(
    @Param("id") id: string,
    @Request() req: RequestWithUser
  ) {
    return this.applicationsService.acceptApplication(id, req.user.id);
  }

  @Patch(":id/reject")
  @UseGuards(JwtAuthGuard, RolesGuard)
  @Roles(UserRole.COMPANY)
  @ApiBearerAuth()
  @ApiOperation({ summary: "Reject application (Company only)" })
  @ApiResponse({
    status: 200,
    description: "Application rejected successfully",
  })
  @ApiResponse({ status: 400, description: "Bad request" })
  @ApiResponse({ status: 404, description: "Application not found" })
  @ApiParam({ name: "id", description: "Application ID" })
  @ApiBody({
    schema: {
      type: "object",
      properties: {
        reason: {
          type: "string",
          description: "Reason for rejection",
        },
      },
      required: ["reason"],
    },
  })
  async rejectApplication(
    @Param("id") id: string,
    @Request() req: RequestWithUser,
    @Body("reason") reason: string
  ) {
    return this.applicationsService.rejectApplication(id, req.user.id, reason);
  }

  @Patch(":id/cancel")
  @UseGuards(JwtAuthGuard, RolesGuard)
  @Roles(UserRole.WORKER)
  @ApiBearerAuth()
  @ApiOperation({ summary: "Cancel application (Worker only)" })
  @ApiResponse({
    status: 200,
    description: "Application cancelled successfully",
  })
  @ApiResponse({ status: 400, description: "Bad request" })
  @ApiResponse({ status: 404, description: "Application not found" })
  @ApiParam({ name: "id", description: "Application ID" })
  @ApiBody({
    schema: {
      type: "object",
      properties: {
        reason: {
          type: "string",
          description: "Reason for cancellation",
        },
      },
      required: ["reason"],
    },
  })
  async cancelApplication(
    @Param("id") id: string,
    @Request() req: RequestWithUser,
    @Body("reason") reason: string
  ) {
    return this.applicationsService.cancelApplication(id, req.user.id, reason);
  }

  @Patch(":id/no-show")
  @UseGuards(JwtAuthGuard, RolesGuard)
  @Roles(UserRole.COMPANY)
  @ApiBearerAuth()
  @ApiOperation({ summary: "Mark worker as no-show (Company only)" })
  @ApiResponse({
    status: 200,
    description: "Worker marked as no-show successfully",
  })
  @ApiResponse({ status: 400, description: "Bad request" })
  @ApiResponse({ status: 404, description: "Application not found" })
  @ApiParam({ name: "id", description: "Application ID" })
  async markNoShow(@Param("id") id: string, @Request() req: RequestWithUser) {
    return this.applicationsService.markNoShow(id, req.user.id);
  }

  @Patch(":id/complete")
  @UseGuards(JwtAuthGuard, RolesGuard)
  @Roles(UserRole.COMPANY)
  @ApiBearerAuth()
  @ApiOperation({ summary: "Mark application as completed (Company only)" })
  @ApiResponse({
    status: 200,
    description: "Application marked as completed successfully",
  })
  @ApiResponse({ status: 400, description: "Bad request" })
  @ApiResponse({ status: 404, description: "Application not found" })
  @ApiParam({ name: "id", description: "Application ID" })
  async markCompleted(
    @Param("id") id: string,
    @Request() req: RequestWithUser
  ) {
    return this.applicationsService.markCompleted(id, req.user.id);
  }
}
