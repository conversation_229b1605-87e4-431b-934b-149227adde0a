import {
  <PERSON><PERSON><PERSON>,
  PrimaryGeneratedColumn,
  Column,
  ManyToOne,
  CreateDateColumn,
  JoinColumn,
} from "typeorm";
import { Badge } from "./badge.entity";
import { User } from "src/users/entities/user.entity";

@Entity("user_badges")
export class UserBadge {
  @PrimaryGeneratedColumn("uuid")
  id!: string;

  @ManyToOne(() => User)
  @JoinColumn({ name: "userId" })
  user!: User;

  @Column()
  userId!: string;

  @ManyToOne(() => Badge, (badge) => badge.userBadges)
  @JoinColumn({ name: "badgeId" })
  badge!: Badge;

  @Column()
  badgeId!: string;

  @Column({ default: 0 })
  progress!: number;

  @Column({ default: false })
  isEarned!: boolean;

  @Column({ nullable: true })
  awardedAt?: Date;

  @CreateDateColumn()
  createdAt!: Date;
}
