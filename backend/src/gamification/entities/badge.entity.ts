import { Entity, PrimaryGeneratedColumn, Column, OneToMany, CreateDateColumn, UpdateDateColumn, Unique, Index } from "typeorm"
import { UserBadge } from "./user-badge.entity"

export enum BadgeCategory {
  JOBS = "jobs",
  TRUST = "trust",
  ACTIVITY = "activity",
  SPECIAL = "special",
}

export enum BadgeRarity {
  COMMON = "common",
  UNCOMMON = "uncommon",
  RARE = "rare",
  EPIC = "epic",
  LEGENDARY = "legendary",
}

@Entity("badges")
@Unique(["name"])
export class Badge {
  @PrimaryGeneratedColumn("uuid")
  id: string

  @Column()
  @Index()
  name: string

  @Column("text")
  description: string

  @Column()
  icon: string

  @Column({
    type: "enum",
    enum: BadgeCategory,
  })
  category: BadgeCategory

  @Column({
    type: "enum",
    enum: BadgeRarity,
    default: BadgeRarity.COMMON,
  })
  rarity: BadgeRarity

  @Column({ nullable: true })
  requiredValue: number

  @Column({ default: true })
  isActive: boolean

  @OneToMany(
    () => UserBadge,
    (userBadge) => userBadge.badge,
  )
  userBadges: UserBadge[]

  @CreateDateColumn()
  createdAt: Date

  @UpdateDateColumn()
  updatedAt: Date
}
