import {
  Injectable,
  NotFoundException,
  BadRequestException,
} from "@nestjs/common";
import { InjectRepository } from "@nestjs/typeorm";
import { Repository } from "typeorm";
import { Badge, BadgeCategory, BadgeRarity } from "./entities/badge.entity";
import { UserBadge } from "./entities/user-badge.entity";
import { NotificationsService } from "../notifications/notifications.service";
import { UsersService } from "../users/users.service";

@Injectable()
export class GamificationService {
  constructor(
    @InjectRepository(Badge)
    private badgeRepository: Repository<Badge>,

    @InjectRepository(UserBadge)
    private userBadgeRepository: Repository<UserBadge>,

    private notificationsService: NotificationsService,
    private usersService: UsersService
  ) {}

  /**
   * Get all available badges
   * @returns List of all badges
   */
  async getAllBadges(): Promise<Badge[]> {
    return this.badgeRepository.find({
      where: { isActive: true },
      order: { category: "ASC", rarity: "ASC" },
    });
  }

  /**
   * Get all badge categories
   * @returns List of badge categories
   */
  async getBadgeCategories(): Promise<{ name: string; description: string }[]> {
    return [
      {
        name: BadgeCategory.JOBS,
        description: "Badges earned by completing jobs",
      },
      {
        name: BadgeCategory.TRUST,
        description: "Badges earned by building trust on the platform",
      },
      {
        name: BadgeCategory.ACTIVITY,
        description: "Badges earned by being active on the platform",
      },
      {
        name: BadgeCategory.SPECIAL,
        description: "Special badges awarded for unique achievements",
      },
    ];
  }

  /**
   * Get badges for a specific user
   * @param userId User ID
   * @returns List of user badges
   */
  async getUserBadges(userId: string): Promise<UserBadge[]> {
    // Check if user exists
    const user = await this.usersService.findOne(userId);
    if (!user) {
      throw new NotFoundException(`User with ID ${userId} not found`);
    }

    return this.userBadgeRepository.find({
      where: { userId },
      relations: ["badge"],
      order: { awardedAt: "DESC" },
    });
  }

  /**
   * Get the badge leaderboard
   * @param limit Maximum number of users to return
   * @param category Optional category filter
   * @returns Leaderboard of users with badge counts
   */
  async getLeaderboard(
    limit: number = 10,
    category?: string
  ): Promise<{ userId: string; userName: string; badgeCount: number }[]> {
    const query = this.userBadgeRepository
      .createQueryBuilder("userBadge")
      .innerJoin("userBadge.badge", "badge")
      .innerJoin("userBadge.user", "user")
      .select("userBadge.userId", "userId")
      .addSelect("user.fullName", "userName")
      .addSelect("COUNT(userBadge.id)", "badgeCount")
      .where("badge.isActive = :isActive", { isActive: true })
      .groupBy("userBadge.userId")
      .addGroupBy("user.fullName")
      .orderBy("badgeCount", "DESC")
      .limit(limit);

    if (category) {
      query.andWhere("badge.category = :category", { category });
    }

    return query.getRawMany();
  }

  /**
   * Create a new badge
   * @param createBadgeDto Badge data
   * @returns The created badge
   */
  async createBadge(createBadgeDto: {
    name: string;
    description: string;
    icon: string;
    category: BadgeCategory;
    rarity: BadgeRarity;
    requiredValue?: number;
    isActive?: boolean;
  }): Promise<Badge> {
    // Check if badge with same name already exists
    const existingBadge = await this.badgeRepository.findOne({
      where: { name: createBadgeDto.name },
    });

    if (existingBadge) {
      throw new BadRequestException(
        `Badge with name ${createBadgeDto.name} already exists`
      );
    }

    const badge = this.badgeRepository.create(createBadgeDto);
    return this.badgeRepository.save(badge);
  }

  /**
   * Award a badge to a user
   * @param userId User ID
   * @param badgeId Badge ID
   * @returns The created user badge
   */
  async awardBadgeToUser(userId: string, badgeId: string): Promise<UserBadge> {
    // Check if user exists
    const user = await this.usersService.findOne(userId);
    if (!user) {
      throw new NotFoundException(`User with ID ${userId} not found`);
    }

    // Check if badge exists
    const badge = await this.badgeRepository.findOne({
      where: { id: badgeId, isActive: true },
    });
    if (!badge) {
      throw new NotFoundException(
        `Badge with ID ${badgeId} not found or is inactive`
      );
    }

    // Check if user already has this badge
    const existingUserBadge = await this.userBadgeRepository.findOne({
      where: { userId, badgeId },
    });
    if (existingUserBadge) {
      throw new BadRequestException(`User already has badge: ${badge.name}`);
    }

    // Create user badge
    const userBadge = this.userBadgeRepository.create({
      userId,
      badgeId,
      awardedAt: new Date(),
    });

    const savedUserBadge = await this.userBadgeRepository.save(userBadge);

    // Send notification to user
    await this.notificationsService.create({
      userId,
      title: "New Badge Earned!",
      message: `Congratulations! You've earned the ${badge.name} badge: ${badge.description}`,
      type: "badge",
      metadata: { badgeId, userBadgeId: savedUserBadge.id },
      link: `/profile/badges`,
    });

    return savedUserBadge;
  }

  /**
   * Check and award badges based on job completion
   * @param userId User ID
   * @param jobCount Number of jobs completed
   */
  async checkAndAwardJobBadges(
    userId: string,
    jobCount: number
  ): Promise<void> {
    const jobBadges = await this.badgeRepository.find({
      where: {
        category: BadgeCategory.JOBS,
        isActive: true,
        requiredValue: jobCount,
      },
    });

    for (const badge of jobBadges) {
      try {
        await this.awardBadgeToUser(userId, badge.id);
      } catch (error) {
        // Skip if user already has this badge
        if (!(error instanceof BadRequestException)) {
          throw error;
        }
      }
    }
  }

  /**
   * Check and award badges based on trust score
   * @param userId User ID
   * @param trustScore User's trust score
   */
  async checkAndAwardTrustBadges(
    userId: string,
    trustScore: number
  ): Promise<void> {
    const trustBadges = await this.badgeRepository.find({
      where: {
        category: BadgeCategory.TRUST,
        isActive: true,
      },
    });

    for (const badge of trustBadges) {
      if (badge.requiredValue && trustScore >= badge.requiredValue) {
        try {
          await this.awardBadgeToUser(userId, badge.id);
        } catch (error) {
          // Skip if user already has this badge
          if (!(error instanceof BadRequestException)) {
            throw error;
          }
        }
      }
    }
  }

  /**
   * Check and award badges based on activity
   * @param userId User ID
   * @param activityType Type of activity (e.g., 'login', 'application', 'message')
   * @param count Count of the activity
   */
  async checkAndAwardActivityBadges(
    userId: string,
    activityType: string,
    count: number
  ): Promise<void> {
    const activityBadges = await this.badgeRepository.find({
      where: {
        category: BadgeCategory.ACTIVITY,
        isActive: true,
      },
    });

    for (const badge of activityBadges) {
      // Check if badge is related to this activity type (based on name or description)
      const isRelevant =
        badge.name.toLowerCase().includes(activityType.toLowerCase()) ||
        badge.description.toLowerCase().includes(activityType.toLowerCase());

      if (isRelevant && badge.requiredValue && count >= badge.requiredValue) {
        try {
          await this.awardBadgeToUser(userId, badge.id);
        } catch (error) {
          // Skip if user already has this badge
          if (!(error instanceof BadRequestException)) {
            throw error;
          }
        }
      }
    }
  }
}
