import { Modu<PERSON> } from "@nestjs/common"
import { TypeOrmModule } from "@nestjs/typeorm"
import { GamificationController } from "./gamification.controller"
import { GamificationService } from "./gamification.service"
import { Badge } from "./entities/badge.entity"
import { UserBadge } from "./entities/user-badge.entity"
import { UsersModule } from "../users/users.module"
import { NotificationsModule } from "../notifications/notifications.module"

@Module({
  imports: [TypeOrmModule.forFeature([Badge, UserBadge]), UsersModule, NotificationsModule],
  controllers: [GamificationController],
  providers: [GamificationService],
  exports: [GamificationService],
})
export class GamificationModule {}
