import {
  Controller,
  Get,
  Post,
  Body,
  Param,
  UseGuards,
  Request,
  Query,
  Inject,
  NotFoundException,
} from "@nestjs/common";
import { GamificationService } from "./gamification.service";
import { JwtAuthGuard } from "../auth/guards/jwt-auth.guard";
import { RolesGuard } from "../auth/guards/roles.guard";
import { Roles } from "../auth/decorators/roles.decorator";
import { UserRole } from "@shared/types";
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiBearerAuth,
  ApiParam,
  ApiQuery,
} from "@nestjs/swagger";

@ApiTags("gamification")
@Controller("gamification")
@UseGuards(JwtAuthGuard)
@ApiBearerAuth()
export class GamificationController {
  constructor(
    @Inject(GamificationService)
    private readonly gamificationService: GamificationService
  ) {}

  @Get("badges")
  @ApiOperation({ summary: "Get all available badges" })
  @ApiResponse({ status: 200, description: "Return all badges" })
  async getAllBadges() {
    return this.gamificationService.getAllBadges();
  }

  @Get("badges/categories")
  @ApiOperation({ summary: "Get all badge categories" })
  @ApiResponse({ status: 200, description: "Return all badge categories" })
  async getBadgeCategories() {
    return this.gamificationService.getBadgeCategories();
  }

  @Get("badges/user")
  @ApiOperation({ summary: "Get badges for the current user" })
  @ApiResponse({ status: 200, description: "Return user badges" })
  async getUserBadges(@Request() req) {
    return this.gamificationService.getUserBadges(req.user.id);
  }

  @Get("badges/user/:userId")
  @ApiOperation({ summary: "Get badges for a specific user" })
  @ApiResponse({ status: 200, description: "Return user badges" })
  @ApiParam({ name: "userId", description: "User ID" })
  async getUserBadgesById(@Param("userId") userId: string) {
    return this.gamificationService.getUserBadges(userId);
  }

  @Get("leaderboard")
  @ApiOperation({ summary: "Get the badge leaderboard" })
  @ApiResponse({ status: 200, description: "Return leaderboard" })
  @ApiQuery({ name: "limit", required: false, type: Number })
  @ApiQuery({ name: "category", required: false, type: String })
  async getLeaderboard(
    @Query("limit") limit?: number,
    @Query("category") category?: string
  ) {
    return this.gamificationService.getLeaderboard(limit, category);
  }

  @Post("badges/admin/create")
  @UseGuards(RolesGuard)
  @Roles(UserRole.ADMIN)
  @ApiOperation({ summary: "Create a new badge (Admin only)" })
  @ApiResponse({ status: 201, description: "Badge created successfully" })
  async createBadge(@Body() createBadgeDto: any) {
    return this.gamificationService.createBadge(createBadgeDto);
  }

  @Post("badges/admin/award")
  @UseGuards(RolesGuard)
  @Roles(UserRole.ADMIN)
  @ApiOperation({ summary: "Award a badge to a user (Admin only)" })
  @ApiResponse({ status: 201, description: "Badge awarded successfully" })
  async awardBadge(@Body() awardBadgeDto: { userId: string; badgeId: string }) {
    return this.gamificationService.awardBadgeToUser(
      awardBadgeDto.userId,
      awardBadgeDto.badgeId
    );
  }

  @Get("badges/all")
  @ApiOperation({ summary: "Get all available badges" })
  @ApiResponse({ status: 200, description: "Return all badges" })
  async getAllAvailableBadges() {
    const badges = await this.gamificationService.getAllBadges();
    return { badges };
  }

  @Get("badges/:id")
  @ApiOperation({ summary: "Get badge by ID" })
  @ApiResponse({ status: 200, description: "Return badge details" })
  @ApiParam({ name: "id", description: "Badge ID" })
  async getBadgeById(@Param("id") id: string) {
    // This is a placeholder implementation
    // In a real application, you would query the database for the badge
    const badges = await this.gamificationService.getAllBadges();
    const badge = badges.find((b) => b.id === id);

    if (!badge) {
      throw new NotFoundException(`Badge with ID ${id} not found`);
    }

    return badge;
  }

  @Get("level")
  @ApiOperation({ summary: "Get current user's level" })
  @ApiResponse({ status: 200, description: "Return user level" })
  async getUserLevel(@Request() req) {
    // This is a placeholder implementation
    // In a real application, you would calculate the user's level based on activity
    return {
      level: 3,
      title: "Skilled Worker",
      points: 750,
      nextLevelPoints: 1000,
      progress: 75,
    };
  }

  @Get("levels")
  @ApiOperation({ summary: "Get all available levels" })
  @ApiResponse({ status: 200, description: "Return all levels" })
  async getAllLevels() {
    // This is a placeholder implementation
    // In a real application, you would fetch levels from the database
    return {
      levels: [
        { level: 1, title: "Beginner", requiredPoints: 0 },
        { level: 2, title: "Apprentice", requiredPoints: 300 },
        { level: 3, title: "Skilled Worker", requiredPoints: 600 },
        { level: 4, title: "Expert", requiredPoints: 1000 },
        { level: 5, title: "Master", requiredPoints: 1500 },
        { level: 6, title: "Grandmaster", requiredPoints: 2200 },
        { level: 7, title: "Legend", requiredPoints: 3000 },
      ],
    };
  }

  @Get("level/progress")
  @ApiOperation({ summary: "Get user's progress towards next level" })
  @ApiResponse({ status: 200, description: "Return level progress" })
  async getLevelProgress(@Request() req) {
    // This is a placeholder implementation
    // In a real application, you would calculate this based on user's points
    return {
      currentLevel: 3,
      currentTitle: "Skilled Worker",
      currentPoints: 750,
      nextLevel: 4,
      nextTitle: "Expert",
      requiredPoints: 1000,
      pointsToNextLevel: 250,
      progress: 75,
    };
  }

  @Get("achievements")
  @ApiOperation({ summary: "Get user's achievements" })
  @ApiResponse({ status: 200, description: "Return user achievements" })
  async getUserAchievements(@Request() req) {
    return this.gamificationService.getUserAchievements(req.user.id);
  }

  @Get("points")
  @ApiOperation({ summary: "Get user's current points" })
  @ApiResponse({ status: 200, description: "Return user points" })
  async getUserPoints(@Request() req) {
    return this.gamificationService.getUserPoints(req.user.id);
  }

  @Get("points/history")
  @ApiOperation({ summary: "Get user's points history" })
  @ApiResponse({ status: 200, description: "Return points history" })
  @ApiQuery({ name: "limit", required: false, type: Number, example: 10 })
  @ApiQuery({ name: "offset", required: false, type: Number, example: 0 })
  async getPointsHistory(
    @Request() req,
    @Query("limit") limit: number = 10,
    @Query("offset") offset: number = 0
  ) {
    // This is a placeholder implementation
    // In a real application, you would fetch user's points history from the database
    return {
      history: [
        {
          id: "1",
          points: 50,
          reason: "Completed job #12345",
          timestamp: new Date(
            Date.now() - 5 * 24 * 60 * 60 * 1000
          ).toISOString(),
        },
        {
          id: "2",
          points: 25,
          reason: "Received 5-star rating",
          timestamp: new Date(
            Date.now() - 4 * 24 * 60 * 60 * 1000
          ).toISOString(),
        },
        {
          id: "3",
          points: 10,
          reason: "Profile completion bonus",
          timestamp: new Date(
            Date.now() - 3 * 24 * 60 * 60 * 1000
          ).toISOString(),
        },
      ],
      total: 3,
      limit,
      offset,
    };
  }
}
