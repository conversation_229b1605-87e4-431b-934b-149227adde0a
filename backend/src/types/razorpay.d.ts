declare module "razorpay" {
  interface RazorpayOrderOptions {
    amount: number;
    currency: string;
    receipt?: string;
    notes?: Record<string, string | number>;
    partial_payment?: boolean;
  }

  interface RazorpayOrder {
    id: string;
    entity: "order";
    amount: number;
    amount_paid: number;
    amount_due: number;
    currency: string;
    receipt: string;
    offer_id?: string;
    status: "created" | "attempted" | "paid";
    attempts: number;
    notes: Record<string, string | number>;
    created_at: number;
  }

  interface RazorpayPayment {
    id: string;
    entity: "payment";
    amount: number;
    currency: string;
    status: "created" | "authorized" | "captured" | "refunded" | "failed";
    order_id: string;
    invoice_id?: string;
    international: boolean;
    method: "card" | "netbanking" | "wallet" | "emi" | "upi";
    amount_refunded: number;
    refund_status?: "null" | "partial" | "full";
    captured: boolean;
    description?: string;
    card_id?: string;
    bank?: string;
    wallet?: string;
    vpa?: string;
    email: string;
    contact: string;
    notes: Record<string, string | number>;
    fee?: number;
    tax?: number;
    error_code?: string;
    error_description?: string;
    error_source?: string;
    error_step?: string;
    error_reason?: string;
    acquirer_data?: Record<string, unknown>;
    created_at: number;
  }

  interface RazorpayRefund {
    id: string;
    entity: "refund";
    amount: number;
    currency: string;
    payment_id: string;
    notes: Record<string, string | number>;
    receipt?: string;
    acquirer_data?: Record<string, unknown>;
    created_at: number;
    batch_id?: string;
    status: "pending" | "processed" | "failed";
    speed_processed: "normal" | "optimum";
    speed_requested: "normal" | "optimum";
  }

  interface RazorpayWebhookEvent {
    entity: string;
    account_id: string;
    event: string;
    contains: string[];
    payload: {
      payment?: {
        entity: RazorpayPayment;
      };
      order?: {
        entity: RazorpayOrder;
      };
      refund?: {
        entity: RazorpayRefund;
      };
    };
    created_at: number;
  }

  interface RazorpayOrdersAPI {
    create(options: RazorpayOrderOptions): Promise<RazorpayOrder>;
    fetch(orderId: string): Promise<RazorpayOrder>;
    all(options?: {
      from?: number;
      to?: number;
      count?: number;
      skip?: number;
    }): Promise<{
      entity: "collection";
      count: number;
      items: RazorpayOrder[];
    }>;
    fetchPayments(orderId: string): Promise<{
      entity: "collection";
      count: number;
      items: RazorpayPayment[];
    }>;
  }

  interface RazorpayPaymentsAPI {
    fetch(paymentId: string): Promise<RazorpayPayment>;
    capture(
      paymentId: string,
      amount: number,
      currency?: string
    ): Promise<RazorpayPayment>;
    refund(
      paymentId: string,
      options?: {
        amount?: number;
        speed?: "normal" | "optimum";
        notes?: Record<string, string | number>;
        receipt?: string;
      }
    ): Promise<RazorpayRefund>;
    all(options?: {
      from?: number;
      to?: number;
      count?: number;
      skip?: number;
    }): Promise<{
      entity: "collection";
      count: number;
      items: RazorpayPayment[];
    }>;
  }

  interface RazorpayRefundsAPI {
    fetch(refundId: string): Promise<RazorpayRefund>;
    all(options?: {
      from?: number;
      to?: number;
      count?: number;
      skip?: number;
    }): Promise<{
      entity: "collection";
      count: number;
      items: RazorpayRefund[];
    }>;
  }

  interface RazorpayConfig {
    key_id: string;
    key_secret: string;
  }

  interface RazorpayUtils {
    generateSignature(body: string, secret: string): string;
  }

  class Razorpay {
    constructor(config: RazorpayConfig);
    orders: RazorpayOrdersAPI;
    payments: RazorpayPaymentsAPI;
    refunds: RazorpayRefundsAPI;
    utils: RazorpayUtils;
  }

  export = Razorpay;
  export {
    RazorpayOrder,
    RazorpayPayment,
    RazorpayRefund,
    RazorpayWebhookEvent,
  };
}
