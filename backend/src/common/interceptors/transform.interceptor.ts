import { Injectable, type NestInterceptor, type ExecutionContext, type <PERSON><PERSON><PERSON><PERSON> } from "@nestjs/common"
import type { Observable } from "rxjs"
import { map } from "rxjs/operators"
import type { ApiResponse } from "../interfaces/api-response.interface"

@Injectable()
export class TransformInterceptor<T> implements NestInterceptor<T, ApiResponse<T>> {
  intercept(context: ExecutionContext, next: CallHandler): Observable<ApiResponse<T>> {
    return next.handle().pipe(
      map((data) => {
        // Check if the response is already in the expected format
        if (data && typeof data === "object" && "success" in data) {
          return data
        }

        // If not, transform it
        return {
          success: true,
          data,
          message: "Operation successful",
        }
      }),
    )
  }
}
