import { applyDecorators, Type } from "@nestjs/common";
import {
  ApiExtraModels,
  ApiOkResponse,
  ApiCreatedResponse,
  getSchemaPath,
} from "@nestjs/swagger";

export const ApiSuccessResponse = <TModel extends Type<any>>(
  model: TModel,
  isCreated = false
) => {
  const decorator = isCreated ? ApiCreatedResponse : ApiOkResponse;

  return applyDecorators(
    ApiExtraModels(model),
    decorator({
      schema: {
        properties: {
          success: {
            type: "boolean",
            example: true,
          },
          message: {
            type: "string",
            example: "Operation successful",
          },
          data: {
            $ref: getSchemaPath(model),
          },
        },
      },
    })
  );
};

export const ApiArrayResponse = <TModel extends Type<any>>(model: TModel) => {
  return applyDecorators(
    ApiExtraModels(model),
    ApiOkResponse({
      schema: {
        properties: {
          success: {
            type: "boolean",
            example: true,
          },
          message: {
            type: "string",
            example: "Data retrieved successfully",
          },
          data: {
            type: "array",
            items: { $ref: getSchemaPath(model) },
          },
        },
      },
    })
  );
};
