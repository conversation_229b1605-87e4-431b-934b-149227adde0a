import { ApiProperty } from "@nestjs/swagger";

export class ApiResponseDto<T> {
  @ApiProperty({ example: true })
  success: boolean;

  @ApiProperty({ example: "Operation completed successfully" })
  message: string;

  data: T;
}

export class TokenResponseDto {
  @ApiProperty({ example: "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..." })
  access_token: string;
}

export class UserResponseDto {
  @ApiProperty({ example: "550e8400-e29b-41d4-a716-446655440000" })
  id: string;

  @ApiProperty({ example: "John Doe" })
  fullName: string;

  @ApiProperty({ example: "<EMAIL>" })
  email: string;

  @ApiProperty({ example: "+1234567890" })
  phone: string;

  @ApiProperty({ example: "worker" })
  role: string;
}

export class AuthResponseDto {
  @ApiProperty()
  access_token: string;

  @ApiProperty()
  user: UserResponseDto;
}

export class OtpResponseDto {
  @ApiProperty({ example: "OTP sent successfully" })
  message: string;
}

export class OtpVerifyResponseDto extends AuthResponseDto {
  @ApiProperty({ example: false })
  isNewUser: boolean;
}