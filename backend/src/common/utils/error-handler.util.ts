import {
  BadRequestException,
  ConflictEx<PERSON>,
  ForbiddenException,
  InternalServerErrorException,
  Logger,
  NotFoundException,
  UnauthorizedException,
} from '@nestjs/common';
import { QueryFailedError } from 'typeorm';

/**
 * Standardized error handling utility
 */
export class ErrorHandler {
  private static readonly logger = new Logger('ErrorHandler');

  /**
   * Handle database errors
   * @param error Error object
   * @param entityName Name of the entity (for error messages)
   */
  static handleDatabaseError(error: any, entityName: string): never {
    this.logger.error(`Database error: ${error.message}`, error.stack);

    // PostgreSQL unique violation
    if (error.code === '23505') {
      const field = error.detail.match(/\(([^)]+)\)/)?.[1] || 'field';
      throw new ConflictException(`${entityName} with this ${field} already exists`);
    }

    // PostgreSQL foreign key violation
    if (error.code === '23503') {
      throw new BadRequestException(`Related ${entityName} not found`);
    }

    // Other database errors
    throw new InternalServerErrorException(`Error processing ${entityName}`);
  }

  /**
   * Handle not found errors
   * @param entityName Name of the entity
   * @param id ID or identifier that was not found
   */
  static handleNotFound(entityName: string, id?: string | number): never {
    const message = id
      ? `${entityName} with ID ${id} not found`
      : `${entityName} not found`;
    
    this.logger.warn(message);
    throw new NotFoundException(message);
  }

  /**
   * Handle unauthorized errors
   * @param message Custom error message
   */
  static handleUnauthorized(message = 'Unauthorized'): never {
    this.logger.warn(message);
    throw new UnauthorizedException(message);
  }

  /**
   * Handle forbidden errors
   * @param message Custom error message
   */
  static handleForbidden(message = 'Forbidden'): never {
    this.logger.warn(message);
    throw new ForbiddenException(message);
  }

  /**
   * Handle bad request errors
   * @param message Custom error message
   */
  static handleBadRequest(message: string): never {
    this.logger.warn(message);
    throw new BadRequestException(message);
  }

  /**
   * Handle general errors with appropriate HTTP exceptions
   * @param error Error object
   * @param entityName Name of the entity (for error messages)
   */
  static handleError(error: any, entityName: string): never {
    this.logger.error(`Error processing ${entityName}: ${error.message}`, error.stack);

    // Handle TypeORM errors
    if (error instanceof QueryFailedError) {
      return this.handleDatabaseError(error, entityName);
    }

    // Handle known HTTP exceptions
    if (
      error instanceof BadRequestException ||
      error instanceof UnauthorizedException ||
      error instanceof ForbiddenException ||
      error instanceof NotFoundException ||
      error instanceof ConflictException
    ) {
      throw error;
    }

    // Handle unknown errors
    throw new InternalServerErrorException(`Error processing ${entityName}`);
  }
}
