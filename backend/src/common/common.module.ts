import { Module, Global } from "@nestjs/common";
import { PaginationService } from "./services/pagination.service";
import { SanitizationService } from "./services/sanitization.service";
import { GeolocationService } from "./services/geolocation.service";
import { SentryService } from "./services/sentry.service";
import { CustomLoggerService } from "./services/logger.service";
import { FileUploadService } from "./services/file-upload.service";
import { RateLimitService } from "./services/rate-limit.service";

@Global()
@Module({
  providers: [
    PaginationService,
    SanitizationService,
    GeolocationService,
    SentryService,
    CustomLoggerService,
    FileUploadService,
    RateLimitService,
  ],
  exports: [
    PaginationService,
    SanitizationService,
    GeolocationService,
    SentryService,
    CustomLoggerService,
    FileUploadService,
    RateLimitService,
  ],
})
export class CommonModule {}
