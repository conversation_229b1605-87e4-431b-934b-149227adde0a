import { Injectable } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import * as nodemailer from 'nodemailer';
import * as aws from '@aws-sdk/client-ses';
import { defaultProvider } from '@aws-sdk/credential-provider-node';
import { EmailOptions, EmailProvider } from '../../interfaces/email.interface';
import { BaseEmailProvider } from './base-email.provider';

@Injectable()
export class SesEmailProvider extends BaseEmailProvider {
  private transporter: nodemailer.Transporter;
  
  constructor(private configService: ConfigService) {
    super('SES');
    this.initializeTransporter();
  }
  
  private initializeTransporter(): void {
    const region = this.configService.get<string>('AWS_SES_REGION');
    const accessKeyId = this.configService.get<string>('AWS_SES_ACCESS_KEY');
    const secretAccessKey = this.configService.get<string>('AWS_SES_SECRET_KEY');
    
    if (!region) {
      this.logger.warn('AWS SES region is missing. Email sending will be disabled.');
      return;
    }
    
    try {
      // Create SES client
      const sesClient = new aws.SESClient({
        region,
        credentials: accessKeyId && secretAccessKey
          ? {
              accessKeyId,
              secretAccessKey,
            }
          : defaultProvider(),
      });
      
      // Create transporter
      this.transporter = nodemailer.createTransport({
        SES: { ses: sesClient, aws },
      });
      
      this.logger.log('Amazon SES transporter initialized successfully');
    } catch (error) {
      this.logger.error('Failed to initialize Amazon SES transporter:', error);
    }
  }
  
  async sendEmail(options: EmailOptions): Promise<boolean> {
    if (!this.transporter) {
      this.logger.warn('Amazon SES transporter not initialized. Email will not be sent.');
      return false;
    }
    
    try {
      const defaultFrom = this.configService.get<string>('EMAIL_FROM');
      const defaultReplyTo = this.configService.get<string>('EMAIL_DEFAULT_REPLY_TO');
      
      const mailOptions = {
        from: options.from || defaultFrom,
        to: options.to,
        subject: options.subject,
        text: options.text,
        html: options.html,
        replyTo: options.replyTo || defaultReplyTo,
        attachments: options.attachments,
      };
      
      await this.transporter.sendMail(mailOptions);
      this.logger.log(`Email sent successfully to ${options.to}`);
      return true;
    } catch (error) {
      this.logger.error(`Failed to send email to ${options.to}:`, error);
      return false;
    }
  }
  
  getProviderName(): string {
    return EmailProvider.SES;
  }
}
