import { Injectable, Logger } from "@nestjs/common";
import { ConfigService } from "@nestjs/config";
import {
  EmailOptions,
  EmailProvider,
  EmailTemplateData,
  IEmailProvider,
  IEmailTemplate,
} from "../../interfaces/email.interface";
import { SmtpEmailProvider } from "./smtp-email.provider";
import { SendgridEmailProvider } from "./sendgrid-email.provider";
import { MailgunEmailProvider } from "./mailgun-email.provider";
import { MailtrapEmailProvider } from "./mailtrap-email.provider";
import { SesEmailProvider } from "./ses-email.provider";
import { WelcomeEmailTemplate } from "./templates/welcome-email.template";
import { PasswordResetEmailTemplate } from "./templates/password-reset-email.template";
import { PasswordResetSuccessEmailTemplate } from "./templates/password-reset-success-email.template";
import { JobApplicationEmailTemplate } from "./templates/job-application-email.template";

@Injectable()
export class EmailService {
  private readonly logger = new Logger(EmailService.name);
  private provider: IEmailProvider;
  private templates: Map<string, IEmailTemplate> = new Map();

  constructor(
    private configService: ConfigService,
    private smtpProvider: SmtpEmailProvider,
    private sendgridProvider: SendgridEmailProvider,
    private mailgunProvider: MailgunEmailProvider,
    private mailtrapProvider: MailtrapEmailProvider,
    private sesProvider: SesEmailProvider,
    private welcomeTemplate: WelcomeEmailTemplate,
    private passwordResetTemplate: PasswordResetEmailTemplate,
    private passwordResetSuccessTemplate: PasswordResetSuccessEmailTemplate,
    private jobApplicationTemplate: JobApplicationEmailTemplate
  ) {
    this.initializeProvider();
    this.registerTemplates();
  }

  private initializeProvider(): void {
    const providerType = this.configService.get<string>(
      "EMAIL_PROVIDER",
      "smtp"
    );

    switch (providerType) {
      case EmailProvider.SMTP:
        this.provider = this.smtpProvider;
        break;
      case EmailProvider.SENDGRID:
        this.provider = this.sendgridProvider;
        break;
      case EmailProvider.MAILGUN:
        this.provider = this.mailgunProvider;
        break;
      case EmailProvider.MAILTRAP:
        this.provider = this.mailtrapProvider;
        break;
      case EmailProvider.SES:
        this.provider = this.sesProvider;
        break;
      default:
        this.logger.warn(
          `Unknown email provider: ${providerType}. Using SMTP as fallback.`
        );
        this.provider = this.smtpProvider;
    }

    this.logger.log(`Using ${this.provider.getProviderName()} email provider`);
  }

  private registerTemplates(): void {
    const templates = [
      this.welcomeTemplate,
      this.passwordResetTemplate,
      this.passwordResetSuccessTemplate,
      this.jobApplicationTemplate,
    ];

    for (const template of templates) {
      this.templates.set(template.getTemplateName(), template);
    }

    this.logger.log(`Registered ${this.templates.size} email templates`);
  }

  /**
   * Send an email with retry logic and fallback
   * @param options Email options
   * @returns Promise resolving to success status
   */
  async sendEmail(options: EmailOptions): Promise<boolean> {
    const maxRetries = 3;
    let lastError: Error | null = null;

    for (let attempt = 1; attempt <= maxRetries; attempt++) {
      try {
        const result = await this.provider.sendEmail(options);
        if (result) {
          if (attempt > 1) {
            this.logger.log(
              `Email sent successfully on attempt ${attempt} to ${options.to}`
            );
          }
          return true;
        }
      } catch (error) {
        lastError = error as Error;
        this.logger.warn(
          `Email send attempt ${attempt} failed for ${options.to}: ${error.message}`
        );

        if (attempt < maxRetries) {
          // Exponential backoff
          const delay = Math.pow(2, attempt) * 1000;
          await new Promise((resolve) => setTimeout(resolve, delay));
        }
      }
    }

    this.logger.error(
      `Failed to send email to ${options.to} after ${maxRetries} attempts`,
      lastError?.stack
    );
    return false;
  }

  /**
   * Send an email using a template
   * @param templateName Template name
   * @param data Template data
   * @param options Email options (without html and text)
   * @returns Promise resolving to success status
   */
  async sendTemplateEmail(
    templateName: string,
    data: EmailTemplateData,
    options: Omit<EmailOptions, "html" | "text">
  ): Promise<boolean> {
    const template = this.templates.get(templateName);

    if (!template) {
      this.logger.error(`Email template "${templateName}" not found`);
      return false;
    }

    const html = template.generateHtml(data);
    const text = template.generateText(data);

    return this.sendEmail({
      ...options,
      html,
      text,
    });
  }

  /**
   * Send a welcome email
   * @param to Recipient email
   * @param username User's name
   * @returns Promise resolving to success status
   */
  async sendWelcomeEmail(to: string, username: string): Promise<boolean> {
    return this.sendTemplateEmail(
      "welcome",
      { username },
      { to, subject: "Welcome to Job Platform" }
    );
  }

  /**
   * Send a password reset email
   * @param to Recipient email
   * @param token Reset token
   * @param username User's name
   * @returns Promise resolving to success status
   */
  async sendPasswordResetEmail(
    to: string,
    token: string,
    username: string
  ): Promise<boolean> {
    return this.sendTemplateEmail(
      "password-reset",
      { username, token },
      { to, subject: "Password Reset Request" }
    );
  }

  /**
   * Send a password reset success email
   * @param to Recipient email
   * @param username User's name
   * @returns Promise resolving to success status
   */
  async sendPasswordResetSuccessEmail(
    to: string,
    username: string
  ): Promise<boolean> {
    return this.sendTemplateEmail(
      "password-reset-success",
      { username },
      { to, subject: "Password Reset Successful" }
    );
  }

  /**
   * Send a job application email
   * @param to Recipient email
   * @param companyName Company name
   * @param jobTitle Job title
   * @param applicantName Applicant name
   * @param applicationId Application ID
   * @returns Promise resolving to success status
   */
  async sendJobApplicationEmail(
    to: string,
    companyName: string,
    jobTitle: string,
    applicantName: string,
    applicationId: string
  ): Promise<boolean> {
    return this.sendTemplateEmail(
      "job-application",
      { companyName, jobTitle, applicantName, applicationId },
      { to, subject: `New Job Application: ${jobTitle}` }
    );
  }
}
