import { EmailTemplateData, IEmailTemplate } from '../../../interfaces/email.interface';

/**
 * Base email template class
 */
export abstract class BaseEmailTemplate implements IEmailTemplate {
  /**
   * Generate HTML content for the email
   * @param data Template data
   * @returns HTML content
   */
  abstract generateHtml(data: EmailTemplateData): string;
  
  /**
   * Generate plain text content for the email
   * @param data Template data
   * @returns Plain text content
   */
  abstract generateText(data: EmailTemplateData): string;
  
  /**
   * Get template name
   */
  abstract getTemplateName(): string;
  
  /**
   * Generate common header HTML
   * @returns HTML header
   */
  protected getHeaderHtml(): string {
    return `
      <!DOCTYPE html>
      <html>
      <head>
        <meta charset="utf-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>Job Platform</title>
        <style>
          body {
            font-family: Arial, sans-serif;
            line-height: 1.6;
            color: #333;
            margin: 0;
            padding: 0;
          }
          .container {
            max-width: 600px;
            margin: 0 auto;
            padding: 20px;
          }
          .header {
            background-color: #4CAF50;
            padding: 20px;
            text-align: center;
          }
          .header h1 {
            color: white;
            margin: 0;
          }
          .content {
            padding: 20px;
            background-color: #f9f9f9;
          }
          .button {
            display: inline-block;
            padding: 10px 20px;
            background-color: #4CAF50;
            color: white;
            text-decoration: none;
            border-radius: 5px;
            margin: 20px 0;
          }
          .footer {
            padding: 20px;
            text-align: center;
            font-size: 12px;
            color: #666;
          }
        </style>
      </head>
      <body>
        <div class="container">
          <div class="header">
            <h1>Job Platform</h1>
          </div>
          <div class="content">
    `;
  }
  
  /**
   * Generate common footer HTML
   * @returns HTML footer
   */
  protected getFooterHtml(): string {
    return `
          </div>
          <div class="footer">
            <p>&copy; ${new Date().getFullYear()} Job Platform. All rights reserved.</p>
            <p>If you have any questions, please contact our support team.</p>
          </div>
        </div>
      </body>
      </html>
    `;
  }
}
