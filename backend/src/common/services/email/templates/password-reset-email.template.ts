import { Injectable } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { EmailTemplateData } from '../../../interfaces/email.interface';
import { BaseEmailTemplate } from './base-email.template';

@Injectable()
export class PasswordResetEmailTemplate extends BaseEmailTemplate {
  constructor(private configService: ConfigService) {
    super();
  }
  
  generateHtml(data: EmailTemplateData): string {
    const { username, token } = data;
    const appUrl = this.configService.get<string>('APP_URL', 'http://localhost:3000');
    const resetLink = `${appUrl}/reset-password?token=${token}`;
    
    return `
      ${this.getHeaderHtml()}
      <h2>Password Reset</h2>
      <p>Hello ${username},</p>
      <p>We received a request to reset your password. If you didn't make this request, you can ignore this email.</p>
      <p>To reset your password, click the link below:</p>
      <p><a href="${resetLink}" class="button">Reset Password</a></p>
      <p>Or copy and paste this URL into your browser:</p>
      <p>${resetLink}</p>
      <p>This link will expire in 1 hour.</p>
      <p>Thank you,</p>
      <p>The Job Platform Team</p>
      ${this.getFooterHtml()}
    `;
  }
  
  generateText(data: EmailTemplateData): string {
    const { username, token } = data;
    const appUrl = this.configService.get<string>('APP_URL', 'http://localhost:3000');
    const resetLink = `${appUrl}/reset-password?token=${token}`;
    
    return `
      Password Reset
      
      Hello ${username},
      
      We received a request to reset your password. If you didn't make this request, you can ignore this email.
      
      To reset your password, visit this link:
      ${resetLink}
      
      This link will expire in 1 hour.
      
      Thank you,
      The Job Platform Team
      
      © ${new Date().getFullYear()} Job Platform. All rights reserved.
    `;
  }
  
  getTemplateName(): string {
    return 'password-reset';
  }
}
