import { Injectable } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { EmailTemplateData } from '../../../interfaces/email.interface';
import { BaseEmailTemplate } from './base-email.template';

@Injectable()
export class WelcomeEmailTemplate extends BaseEmailTemplate {
  constructor(private configService: ConfigService) {
    super();
  }
  
  generateHtml(data: EmailTemplateData): string {
    const { username } = data;
    const appUrl = this.configService.get<string>('APP_URL', 'http://localhost:3000');
    
    return `
      ${this.getHeaderHtml()}
      <h2>Welcome to Job Platform!</h2>
      <p>Hello ${username},</p>
      <p>Thank you for joining our platform. We're excited to have you on board!</p>
      <p>To get started, visit our platform and complete your profile:</p>
      <p><a href="${appUrl}" class="button">Visit Job Platform</a></p>
      <p>If you have any questions, feel free to contact our support team.</p>
      <p>Best regards,</p>
      <p>The Job Platform Team</p>
      ${this.getFooterHtml()}
    `;
  }
  
  generateText(data: EmailTemplateData): string {
    const { username } = data;
    const appUrl = this.configService.get<string>('APP_URL', 'http://localhost:3000');
    
    return `
      Welcome to Job Platform!
      
      Hello ${username},
      
      Thank you for joining our platform. We're excited to have you on board!
      
      To get started, visit our platform and complete your profile:
      ${appUrl}
      
      If you have any questions, feel free to contact our support team.
      
      Best regards,
      The Job Platform Team
      
      © ${new Date().getFullYear()} Job Platform. All rights reserved.
    `;
  }
  
  getTemplateName(): string {
    return 'welcome';
  }
}
