import { Injectable } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { EmailTemplateData } from '../../../interfaces/email.interface';
import { BaseEmailTemplate } from './base-email.template';

@Injectable()
export class JobApplicationEmailTemplate extends BaseEmailTemplate {
  constructor(private configService: ConfigService) {
    super();
  }
  
  generateHtml(data: EmailTemplateData): string {
    const { companyName, jobTitle, applicantName, applicationId } = data;
    const appUrl = this.configService.get<string>('APP_URL', 'http://localhost:3000');
    const applicationLink = `${appUrl}/applications/${applicationId}`;
    
    return `
      ${this.getHeaderHtml()}
      <h2>New Job Application</h2>
      <p>Hello ${companyName},</p>
      <p>You have received a new application for the job: <strong>${jobTitle}</strong></p>
      <p>Applicant: ${applicantName}</p>
      <p>To view the application details and respond, click the button below:</p>
      <p><a href="${applicationLink}" class="button">View Application</a></p>
      <p>Thank you for using our platform!</p>
      <p>Best regards,</p>
      <p>The Job Platform Team</p>
      ${this.getFooterHtml()}
    `;
  }
  
  generateText(data: EmailTemplateData): string {
    const { companyName, jobTitle, applicantName, applicationId } = data;
    const appUrl = this.configService.get<string>('APP_URL', 'http://localhost:3000');
    const applicationLink = `${appUrl}/applications/${applicationId}`;
    
    return `
      New Job Application

      Hello ${companyName},

      You have received a new application for the job: ${jobTitle}

      Applicant: ${applicantName}

      To view the application details and respond, visit:
      ${applicationLink}

      Thank you for using our platform!

      Best regards,
      The Job Platform Team
      
      © ${new Date().getFullYear()} Job Platform. All rights reserved.
    `;
  }
  
  getTemplateName(): string {
    return 'job-application';
  }
}
