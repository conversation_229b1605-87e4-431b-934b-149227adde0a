import { Injectable } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { EmailTemplateData } from '../../../interfaces/email.interface';
import { BaseEmailTemplate } from './base-email.template';

@Injectable()
export class PasswordResetSuccessEmailTemplate extends BaseEmailTemplate {
  constructor(private configService: ConfigService) {
    super();
  }
  
  generateHtml(data: EmailTemplateData): string {
    const { username } = data;
    const appUrl = this.configService.get<string>('APP_URL', 'http://localhost:3000');
    
    return `
      ${this.getHeaderHtml()}
      <h2>Password Reset Successful</h2>
      <p>Hello ${username},</p>
      <p>Your password has been reset successfully.</p>
      <p>You can now log in with your new password:</p>
      <p><a href="${appUrl}/login" class="button">Log In</a></p>
      <p>If you did not request this change, please contact our support team immediately.</p>
      <p>Thank you,</p>
      <p>The Job Platform Team</p>
      ${this.getFooterHtml()}
    `;
  }
  
  generateText(data: EmailTemplateData): string {
    const { username } = data;
    const appUrl = this.configService.get<string>('APP_URL', 'http://localhost:3000');
    
    return `
      Password Reset Successful

      Hello ${username},

      Your password has been reset successfully.

      You can now log in with your new password:
      ${appUrl}/login

      If you did not request this change, please contact our support team immediately.

      Thank you,
      The Job Platform Team
      
      © ${new Date().getFullYear()} Job Platform. All rights reserved.
    `;
  }
  
  getTemplateName(): string {
    return 'password-reset-success';
  }
}
