import { Modu<PERSON> } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';
import { EmailService } from './email.service';
import { SmtpEmailProvider } from './smtp-email.provider';
import { SendgridEmailProvider } from './sendgrid-email.provider';
import { MailgunEmailProvider } from './mailgun-email.provider';
import { MailtrapEmailProvider } from './mailtrap-email.provider';
import { SesEmailProvider } from './ses-email.provider';
import { WelcomeEmailTemplate } from './templates/welcome-email.template';
import { PasswordResetEmailTemplate } from './templates/password-reset-email.template';
import { PasswordResetSuccessEmailTemplate } from './templates/password-reset-success-email.template';
import { JobApplicationEmailTemplate } from './templates/job-application-email.template';

@Module({
  imports: [ConfigModule],
  providers: [
    // Email providers
    SmtpEmailProvider,
    SendgridEmailProvider,
    MailgunEmailProvider,
    MailtrapEmailProvider,
    SesE<PERSON>Provider,
    
    // Email templates
    WelcomeEmailTemplate,
    PasswordResetEmailTemplate,
    PasswordResetSuccessEmailTemplate,
    JobApplicationEmailTemplate,
    
    // Main email service
    EmailService,
  ],
  exports: [EmailService],
})
export class EmailModule {}
