import { Injectable } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import * as nodemailer from 'nodemailer';
import * as mg from 'nodemailer-mailgun-transport';
import { EmailOptions, EmailProvider } from '../../interfaces/email.interface';
import { BaseEmailProvider } from './base-email.provider';

@Injectable()
export class MailgunEmailProvider extends BaseEmailProvider {
  private transporter: nodemailer.Transporter;
  
  constructor(private configService: ConfigService) {
    super('Mailgun');
    this.initializeTransporter();
  }
  
  private initializeTransporter(): void {
    const apiKey = this.configService.get<string>('MAILGUN_API_KEY');
    const domain = this.configService.get<string>('MAILGUN_DOMAIN');
    
    if (!apiKey || !domain) {
      this.logger.warn('Mailgun configuration is incomplete. Email sending will be disabled.');
      return;
    }
    
    try {
      const auth = {
        auth: {
          api_key: apiKey,
          domain,
        },
      };
      
      this.transporter = nodemailer.createTransport(mg(auth));
      
      this.logger.log('Mailgun transporter initialized successfully');
    } catch (error) {
      this.logger.error('Failed to initialize Mailgun transporter:', error);
    }
  }
  
  async sendEmail(options: EmailOptions): Promise<boolean> {
    if (!this.transporter) {
      this.logger.warn('Mailgun transporter not initialized. Email will not be sent.');
      return false;
    }
    
    try {
      const defaultFrom = this.configService.get<string>('EMAIL_FROM');
      const defaultReplyTo = this.configService.get<string>('EMAIL_DEFAULT_REPLY_TO');
      
      const mailOptions = {
        from: options.from || defaultFrom,
        to: options.to,
        subject: options.subject,
        text: options.text,
        html: options.html,
        'h:Reply-To': options.replyTo || defaultReplyTo,
        attachments: options.attachments,
      };
      
      await this.transporter.sendMail(mailOptions);
      this.logger.log(`Email sent successfully to ${options.to}`);
      return true;
    } catch (error) {
      this.logger.error(`Failed to send email to ${options.to}:`, error);
      return false;
    }
  }
  
  getProviderName(): string {
    return EmailProvider.MAILGUN;
  }
}
