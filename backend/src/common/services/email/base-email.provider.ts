import { Logger } from '@nestjs/common';
import { EmailOptions, IEmailProvider } from '../../interfaces/email.interface';

/**
 * Base email provider class
 */
export abstract class BaseEmailProvider implements IEmailProvider {
  protected readonly logger: Logger;
  
  constructor(providerName: string) {
    this.logger = new Logger(`${providerName}EmailProvider`);
  }
  
  /**
   * Send an email
   * @param options Email options
   * @returns Promise resolving to success status
   */
  abstract sendEmail(options: EmailOptions): Promise<boolean>;
  
  /**
   * Get provider name
   */
  abstract getProviderName(): string;
}
