import { Injectable } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import * as nodemailer from 'nodemailer';
import { EmailOptions, EmailProvider } from '../../interfaces/email.interface';
import { BaseEmailProvider } from './base-email.provider';

@Injectable()
export class SmtpEmailProvider extends BaseEmailProvider {
  private transporter: nodemailer.Transporter;
  
  constructor(private configService: ConfigService) {
    super('SMTP');
    this.initializeTransporter();
  }
  
  private initializeTransporter(): void {
    const host = this.configService.get<string>('EMAIL_HOST');
    const port = this.configService.get<number>('EMAIL_PORT');
    const user = this.configService.get<string>('EMAIL_USER');
    const pass = this.configService.get<string>('EMAIL_PASSWORD');
    const secure = this.configService.get<boolean>('EMAIL_SECURE', false);
    
    if (!host || !port || !user || !pass) {
      this.logger.warn('SMTP configuration is incomplete. Email sending will be disabled.');
      return;
    }
    
    try {
      this.transporter = nodemailer.createTransport({
        host,
        port,
        secure,
        auth: {
          user,
          pass,
        },
      });
      
      this.logger.log('SMTP transporter initialized successfully');
    } catch (error) {
      this.logger.error('Failed to initialize SMTP transporter:', error);
    }
  }
  
  async sendEmail(options: EmailOptions): Promise<boolean> {
    if (!this.transporter) {
      this.logger.warn('SMTP transporter not initialized. Email will not be sent.');
      return false;
    }
    
    try {
      const defaultFrom = this.configService.get<string>('EMAIL_FROM');
      const defaultReplyTo = this.configService.get<string>('EMAIL_DEFAULT_REPLY_TO');
      
      const mailOptions = {
        from: options.from || defaultFrom,
        to: options.to,
        subject: options.subject,
        text: options.text,
        html: options.html,
        replyTo: options.replyTo || defaultReplyTo,
        attachments: options.attachments,
      };
      
      await this.transporter.sendMail(mailOptions);
      this.logger.log(`Email sent successfully to ${options.to}`);
      return true;
    } catch (error) {
      this.logger.error(`Failed to send email to ${options.to}:`, error);
      return false;
    }
  }
  
  getProviderName(): string {
    return EmailProvider.SMTP;
  }
}
