import { Injectable, LoggerService as NestLoggerService } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { SentryService } from './sentry.service';

export enum LogLevel {
  ERROR = 'error',
  WARN = 'warn',
  INFO = 'info',
  DEBUG = 'debug',
  VERBOSE = 'verbose',
}

export interface LogContext {
  userId?: string;
  requestId?: string;
  method?: string;
  url?: string;
  userAgent?: string;
  ip?: string;
  duration?: number;
  statusCode?: number;
  [key: string]: any;
}

@Injectable()
export class CustomLoggerService implements NestLoggerService {
  private readonly logLevel: LogLevel;
  private readonly isProduction: boolean;

  constructor(
    private configService: ConfigService,
    private sentryService: SentryService,
  ) {
    this.logLevel = this.configService.get<LogLevel>('LOG_LEVEL', LogLevel.INFO);
    this.isProduction = this.configService.get<string>('NODE_ENV') === 'production';
  }

  private shouldLog(level: LogLevel): boolean {
    const levels = [LogLevel.ERROR, LogLevel.WARN, LogLevel.INFO, LogLevel.DEBUG, LogLevel.VERBOSE];
    const currentLevelIndex = levels.indexOf(this.logLevel);
    const messageLevelIndex = levels.indexOf(level);
    return messageLevelIndex <= currentLevelIndex;
  }

  private formatMessage(level: LogLevel, message: string, context?: LogContext): string {
    const timestamp = new Date().toISOString();
    const contextStr = context ? JSON.stringify(context) : '';
    
    if (this.isProduction) {
      // Structured logging for production
      return JSON.stringify({
        timestamp,
        level,
        message,
        context,
        pid: process.pid,
      });
    } else {
      // Human-readable logging for development
      return `[${timestamp}] [${level.toUpperCase()}] ${message} ${contextStr}`;
    }
  }

  private writeLog(level: LogLevel, message: string, context?: LogContext): void {
    if (!this.shouldLog(level)) return;

    const formattedMessage = this.formatMessage(level, message, context);

    switch (level) {
      case LogLevel.ERROR:
        console.error(formattedMessage);
        break;
      case LogLevel.WARN:
        console.warn(formattedMessage);
        break;
      case LogLevel.DEBUG:
      case LogLevel.VERBOSE:
        console.debug(formattedMessage);
        break;
      default:
        console.log(formattedMessage);
    }
  }

  log(message: string, context?: LogContext): void {
    this.writeLog(LogLevel.INFO, message, context);
  }

  error(message: string, trace?: string, context?: LogContext): void {
    this.writeLog(LogLevel.ERROR, message, { ...context, trace });
    
    // Send errors to Sentry in production
    if (this.isProduction && trace) {
      const error = new Error(message);
      error.stack = trace;
      this.sentryService.captureException(error, context);
    }
  }

  warn(message: string, context?: LogContext): void {
    this.writeLog(LogLevel.WARN, message, context);
  }

  debug(message: string, context?: LogContext): void {
    this.writeLog(LogLevel.DEBUG, message, context);
  }

  verbose(message: string, context?: LogContext): void {
    this.writeLog(LogLevel.VERBOSE, message, context);
  }

  // HTTP request logging
  logRequest(method: string, url: string, statusCode: number, duration: number, context?: LogContext): void {
    const level = statusCode >= 400 ? LogLevel.WARN : LogLevel.INFO;
    const message = `${method} ${url} ${statusCode} - ${duration}ms`;
    
    this.writeLog(level, message, {
      ...context,
      method,
      url,
      statusCode,
      duration,
      type: 'http_request',
    });
  }

  // Database operation logging
  logDatabaseOperation(operation: string, table: string, duration: number, context?: LogContext): void {
    const message = `DB ${operation} on ${table} - ${duration}ms`;
    
    this.writeLog(LogLevel.DEBUG, message, {
      ...context,
      operation,
      table,
      duration,
      type: 'database_operation',
    });
  }

  // Business logic logging
  logBusinessEvent(event: string, details: any, context?: LogContext): void {
    const message = `Business Event: ${event}`;
    
    this.writeLog(LogLevel.INFO, message, {
      ...context,
      event,
      details,
      type: 'business_event',
    });
  }

  // Security event logging
  logSecurityEvent(event: string, severity: 'low' | 'medium' | 'high', context?: LogContext): void {
    const level = severity === 'high' ? LogLevel.ERROR : severity === 'medium' ? LogLevel.WARN : LogLevel.INFO;
    const message = `Security Event: ${event} (${severity})`;
    
    this.writeLog(level, message, {
      ...context,
      event,
      severity,
      type: 'security_event',
    });

    // Send high severity security events to Sentry
    if (severity === 'high') {
      this.sentryService.captureMessage(message, 'error', context);
    }
  }

  // Performance monitoring
  logPerformance(operation: string, duration: number, threshold: number, context?: LogContext): void {
    const level = duration > threshold ? LogLevel.WARN : LogLevel.DEBUG;
    const message = `Performance: ${operation} took ${duration}ms (threshold: ${threshold}ms)`;
    
    this.writeLog(level, message, {
      ...context,
      operation,
      duration,
      threshold,
      type: 'performance',
    });
  }

  // User activity logging
  logUserActivity(userId: string, action: string, resource?: string, context?: LogContext): void {
    const message = `User Activity: ${action}${resource ? ` on ${resource}` : ''}`;
    
    this.writeLog(LogLevel.INFO, message, {
      ...context,
      userId,
      action,
      resource,
      type: 'user_activity',
    });
  }
}
