import { Injectable } from '@nestjs/common';

export interface Coordinates {
  latitude: number;
  longitude: number;
}

export interface LocationBounds {
  northEast: Coordinates;
  southWest: Coordinates;
}

export interface ProximityFilter {
  center: Coordinates;
  radiusKm: number;
}

@Injectable()
export class GeolocationService {
  /**
   * Calculate distance between two points using Haversine formula
   * @param point1 First coordinate point
   * @param point2 Second coordinate point
   * @returns Distance in kilometers
   */
  calculateDistance(point1: Coordinates, point2: Coordinates): number {
    const R = 6371; // Earth's radius in kilometers
    const dLat = this.toRadians(point2.latitude - point1.latitude);
    const dLon = this.toRadians(point2.longitude - point1.longitude);

    const a =
      Math.sin(dLat / 2) * Math.sin(dLat / 2) +
      Math.cos(this.toRadians(point1.latitude)) *
        Math.cos(this.toRadians(point2.latitude)) *
        Math.sin(dLon / 2) *
        Math.sin(dLon / 2);

    const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));
    const distance = R * c;

    return Math.round(distance * 100) / 100; // Round to 2 decimal places
  }

  /**
   * Convert degrees to radians
   */
  private toRadians(degrees: number): number {
    return degrees * (Math.PI / 180);
  }

  /**
   * Check if a point is within a circular area
   * @param point Point to check
   * @param center Center of the circle
   * @param radiusKm Radius in kilometers
   * @returns True if point is within the radius
   */
  isWithinRadius(point: Coordinates, center: Coordinates, radiusKm: number): boolean {
    const distance = this.calculateDistance(point, center);
    return distance <= radiusKm;
  }

  /**
   * Get bounding box coordinates for a given center and radius
   * This is useful for database queries to filter results before calculating exact distances
   * @param center Center point
   * @param radiusKm Radius in kilometers
   * @returns Bounding box coordinates
   */
  getBoundingBox(center: Coordinates, radiusKm: number): LocationBounds {
    const latDelta = radiusKm / 111; // Approximately 111 km per degree of latitude
    const lonDelta = radiusKm / (111 * Math.cos(this.toRadians(center.latitude)));

    return {
      northEast: {
        latitude: center.latitude + latDelta,
        longitude: center.longitude + lonDelta,
      },
      southWest: {
        latitude: center.latitude - latDelta,
        longitude: center.longitude - lonDelta,
      },
    };
  }

  /**
   * Generate SQL WHERE clause for proximity search
   * This creates a bounding box filter for efficient database queries
   * @param center Center coordinates
   * @param radiusKm Search radius in kilometers
   * @param latColumn Name of latitude column in database
   * @param lonColumn Name of longitude column in database
   * @returns SQL WHERE clause string and parameters
   */
  getProximityWhereClause(
    center: Coordinates,
    radiusKm: number,
    latColumn: string = 'latitude',
    lonColumn: string = 'longitude',
  ): { whereClause: string; parameters: Record<string, number> } {
    const bounds = this.getBoundingBox(center, radiusKm);

    return {
      whereClause: `${latColumn} BETWEEN :minLat AND :maxLat AND ${lonColumn} BETWEEN :minLon AND :maxLon`,
      parameters: {
        minLat: bounds.southWest.latitude,
        maxLat: bounds.northEast.latitude,
        minLon: bounds.southWest.longitude,
        maxLon: bounds.northEast.longitude,
      },
    };
  }

  /**
   * Generate SQL for calculating distance using Haversine formula
   * @param center Center coordinates
   * @param latColumn Name of latitude column
   * @param lonColumn Name of longitude column
   * @returns SQL expression for distance calculation
   */
  getDistanceSelectClause(
    center: Coordinates,
    latColumn: string = 'latitude',
    lonColumn: string = 'longitude',
  ): { selectClause: string; parameters: Record<string, number> } {
    return {
      selectClause: `
        (6371 * acos(
          cos(radians(:centerLat)) * 
          cos(radians(${latColumn})) * 
          cos(radians(${lonColumn}) - radians(:centerLon)) + 
          sin(radians(:centerLat)) * 
          sin(radians(${latColumn}))
        )) AS distance
      `,
      parameters: {
        centerLat: center.latitude,
        centerLon: center.longitude,
      },
    };
  }

  /**
   * Sort locations by distance from a center point
   * @param locations Array of locations with coordinates
   * @param center Center point for distance calculation
   * @returns Sorted array with distance property added
   */
  sortByDistance<T extends { latitude: number; longitude: number }>(
    locations: T[],
    center: Coordinates,
  ): (T & { distance: number })[] {
    return locations
      .map((location) => ({
        ...location,
        distance: this.calculateDistance(center, {
          latitude: location.latitude,
          longitude: location.longitude,
        }),
      }))
      .sort((a, b) => a.distance - b.distance);
  }

  /**
   * Filter locations within a radius and sort by distance
   * @param locations Array of locations
   * @param center Center point
   * @param radiusKm Maximum distance in kilometers
   * @returns Filtered and sorted locations
   */
  filterAndSortByProximity<T extends { latitude: number; longitude: number }>(
    locations: T[],
    center: Coordinates,
    radiusKm: number,
  ): (T & { distance: number })[] {
    return this.sortByDistance(locations, center).filter(
      (location) => location.distance <= radiusKm,
    );
  }

  /**
   * Get the center point (centroid) of multiple coordinates
   * @param coordinates Array of coordinate points
   * @returns Center point coordinates
   */
  getCentroid(coordinates: Coordinates[]): Coordinates {
    if (coordinates.length === 0) {
      throw new Error('Cannot calculate centroid of empty coordinates array');
    }

    const sum = coordinates.reduce(
      (acc, coord) => ({
        latitude: acc.latitude + coord.latitude,
        longitude: acc.longitude + coord.longitude,
      }),
      { latitude: 0, longitude: 0 },
    );

    return {
      latitude: sum.latitude / coordinates.length,
      longitude: sum.longitude / coordinates.length,
    };
  }

  /**
   * Validate coordinates
   * @param coordinates Coordinates to validate
   * @returns True if coordinates are valid
   */
  isValidCoordinates(coordinates: Coordinates): boolean {
    const { latitude, longitude } = coordinates;
    
    return (
      typeof latitude === 'number' &&
      typeof longitude === 'number' &&
      latitude >= -90 &&
      latitude <= 90 &&
      longitude >= -180 &&
      longitude <= 180 &&
      !isNaN(latitude) &&
      !isNaN(longitude)
    );
  }

  /**
   * Get popular search radii for different contexts
   */
  getDefaultRadii() {
    return {
      WALKING: 2, // 2 km - walking distance
      CYCLING: 10, // 10 km - cycling distance
      LOCAL: 25, // 25 km - local area
      CITY: 50, // 50 km - city-wide
      REGIONAL: 100, // 100 km - regional
      EMERGENCY: 5, // 5 km - emergency jobs
    };
  }

  /**
   * Get appropriate search radius based on job type and urgency
   * @param isEmergency Whether the job is an emergency
   * @param jobType Type of job (optional)
   * @returns Recommended search radius in kilometers
   */
  getRecommendedRadius(isEmergency: boolean = false, jobType?: string): number {
    const radii = this.getDefaultRadii();

    if (isEmergency) {
      return radii.EMERGENCY;
    }

    // You can extend this logic based on job types
    switch (jobType?.toLowerCase()) {
      case 'delivery':
      case 'food_delivery':
        return radii.CYCLING;
      case 'cleaning':
      case 'maintenance':
        return radii.LOCAL;
      case 'construction':
      case 'moving':
        return radii.CITY;
      default:
        return radii.LOCAL;
    }
  }
}
