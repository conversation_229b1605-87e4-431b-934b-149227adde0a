import { Injectable } from "@nestjs/common";
import sanitizeHtml from "sanitize-html";

@Injectable()
export class SanitizationService {
  /**
   * Sanitize HTML content to prevent XSS attacks
   */
  sanitizeHtml(dirty: string): string {
    if (!dirty) return "";

    return sanitizeHtml(dirty, {
      allowedTags: ["b", "i", "em", "strong", "a", "p", "br"],
      allowedAttributes: {
        a: ["href"],
      },
      allowedIframeHostnames: [],
    });
  }

  /**
   * Sanitize plain text by removing HTML tags and special characters
   */
  sanitizeText(text: string): string {
    if (!text) return "";

    // Remove HTML tags
    let sanitized = text.replace(/<[^>]*>/g, "");

    // Remove potentially dangerous characters
    sanitized = sanitized.replace(/[<>'"&]/g, "");

    // Trim whitespace
    sanitized = sanitized.trim();

    return sanitized;
  }

  /**
   * Sanitize email addresses
   */
  sanitizeEmail(email: string): string {
    if (!email) return "";

    // Basic email sanitization
    return email.toLowerCase().trim();
  }

  /**
   * Sanitize phone numbers
   */
  sanitizePhone(phone: string): string {
    if (!phone) return "";

    // Remove all non-digit characters except +
    return phone.replace(/[^\d+]/g, "");
  }

  /**
   * Sanitize file names
   */
  sanitizeFileName(fileName: string): string {
    if (!fileName) return "";

    // Remove path traversal attempts and dangerous characters
    return fileName
      .replace(/[\/\\:*?"<>|]/g, "")
      .replace(/\.\./g, "")
      .trim();
  }

  /**
   * Sanitize URL
   */
  sanitizeUrl(url: string): string {
    if (!url) return "";

    // Basic URL validation and sanitization
    try {
      const urlObj = new URL(url);

      // Only allow http and https protocols
      if (!["http:", "https:"].includes(urlObj.protocol)) {
        return "";
      }

      return urlObj.toString();
    } catch {
      return "";
    }
  }

  /**
   * Sanitize search query
   */
  sanitizeSearchQuery(query: string): string {
    if (!query) return "";

    // Remove special characters that could be used for injection
    return query
      .replace(/[<>'"&%]/g, "")
      .replace(/\s+/g, " ")
      .trim()
      .substring(0, 100); // Limit length
  }

  /**
   * Enhanced sanitizeObject with type-specific sanitization
   */
  sanitizeObject<T>(obj: T, schema?: Record<string, string>): T {
    if (!obj || typeof obj !== "object") {
      return obj;
    }

    const result = { ...obj };

    for (const key in result) {
      if (Object.prototype.hasOwnProperty.call(result, key)) {
        const value = result[key];
        const sanitizationType = schema?.[key];

        if (typeof value === "string") {
          switch (sanitizationType) {
            case "text":
              (result as Record<string, unknown>)[key] =
                this.sanitizeText(value);
              break;
            case "email":
              (result as Record<string, unknown>)[key] =
                this.sanitizeEmail(value);
              break;
            case "phone":
              (result as Record<string, unknown>)[key] =
                this.sanitizePhone(value);
              break;
            case "url":
              (result as Record<string, unknown>)[key] =
                this.sanitizeUrl(value);
              break;
            case "search":
              (result as Record<string, unknown>)[key] =
                this.sanitizeSearchQuery(value);
              break;
            case "filename":
              (result as Record<string, unknown>)[key] =
                this.sanitizeFileName(value);
              break;
            case "html":
            default:
              (result as Record<string, unknown>)[key] =
                this.sanitizeHtml(value);
          }
        } else if (typeof value === "object" && value !== null) {
          (result as Record<string, unknown>)[key] = this.sanitizeObject(
            value,
            schema
          );
        }
      }
    }

    return result;
  }
}
