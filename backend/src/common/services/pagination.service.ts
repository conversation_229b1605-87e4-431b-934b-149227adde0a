import { Injectable } from "@nestjs/common"
import type { SelectQueryBuilder } from "typeorm"
import type { PaginatedApiResponse } from "../interfaces/api-response.interface"

@Injectable()
export class PaginationService {
  async paginate<T>(queryBuilder: SelectQueryBuilder<T>, page = 1, limit = 10): Promise<PaginatedApiResponse<T>> {
    const offset = (page - 1) * limit

    // Clone the query builder to get the total count
    const countQueryBuilder = queryBuilder.clone()

    // Get total count
    const total = await countQueryBuilder
      .select("COUNT(*)", "count")
      .getRawOne()
      .then((result) => Number.parseInt(result.count, 10))

    // Apply pagination
    const data = await queryBuilder.skip(offset).take(limit).getMany()

    const totalPages = Math.ceil(total / limit)

    return {
      success: true,
      data,
      message: "Data retrieved successfully",
      meta: {
        total,
        page,
        limit,
        totalPages,
        hasNextPage: page < totalPages,
        hasPreviousPage: page > 1,
      },
    }
  }
}
