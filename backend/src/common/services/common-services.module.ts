import { Module, Global } from "@nestjs/common";
import { ConfigModule } from "@nestjs/config";
import { SanitizationService } from "./sanitization.service";
import { PaginationService } from "./pagination.service";
import { CustomLoggerService } from "./logger.service";
import { SentryService } from "./sentry.service";
import { EmailModule } from "./email/email.module";

@Global()
@Module({
  imports: [ConfigModule, EmailModule],
  providers: [
    SanitizationService,
    PaginationService,
    CustomLoggerService,
    SentryService,
  ],
  exports: [
    SanitizationService,
    PaginationService,
    CustomLoggerService,
    SentryService,
    EmailModule,
  ],
})
export class CommonServicesModule {}
