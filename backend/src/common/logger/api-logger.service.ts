import { Injectable, LoggerService, Scope } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';

/**
 * Enhanced logger service for API requests and responses
 * Can be extended to log to files or external services in production
 */
@Injectable({ scope: Scope.TRANSIENT })
export class ApiLoggerService implements LoggerService {
  private context?: string;

  constructor(private configService: ConfigService) {}

  /**
   * Set the context for the logger
   * @param context The context to set
   */
  setContext(context: string) {
    this.context = context;
  }

  /**
   * Log a message at the 'log' level
   * @param message The message to log
   * @param context Optional context override
   */
  log(message: any, context?: string) {
    this.printMessage(message, 'log', context);
  }

  /**
   * Log a message at the 'error' level
   * @param message The message to log
   * @param trace Optional stack trace
   * @param context Optional context override
   */
  error(message: any, trace?: string, context?: string) {
    this.printMessage(message, 'error', context);
    if (trace) {
      console.error(trace);
    }
  }

  /**
   * Log a message at the 'warn' level
   * @param message The message to log
   * @param context Optional context override
   */
  warn(message: any, context?: string) {
    this.printMessage(message, 'warn', context);
  }

  /**
   * Log a message at the 'debug' level
   * @param message The message to log
   * @param context Optional context override
   */
  debug(message: any, context?: string) {
    // Only log debug messages in development
    if (this.configService.get('NODE_ENV') !== 'production') {
      this.printMessage(message, 'debug', context);
    }
  }

  /**
   * Log a message at the 'verbose' level
   * @param message The message to log
   * @param context Optional context override
   */
  verbose(message: any, context?: string) {
    // Only log verbose messages in development
    if (this.configService.get('NODE_ENV') !== 'production') {
      this.printMessage(message, 'verbose', context);
    }
  }

  /**
   * Print a message to the console with the appropriate formatting
   * @param message The message to print
   * @param level The log level
   * @param context Optional context override
   */
  private printMessage(message: any, level: string, context?: string) {
    const finalContext = context || this.context;
    const timestamp = new Date().toISOString();
    const formattedMessage = `[${timestamp}] [${level.toUpperCase()}] ${finalContext ? `[${finalContext}]` : ''} ${message}`;

    // In a production environment, you could send logs to a file or external service
    // For now, we'll just use console methods
    switch (level) {
      case 'log':
        console.log(formattedMessage);
        break;
      case 'error':
        console.error(formattedMessage);
        break;
      case 'warn':
        console.warn(formattedMessage);
        break;
      case 'debug':
        console.debug(formattedMessage);
        break;
      case 'verbose':
        console.info(formattedMessage);
        break;
      default:
        console.log(formattedMessage);
    }
  }
}
