/**
 * Email provider types
 */
export enum EmailProvider {
  SMTP = 'smtp',
  SENDGRID = 'sendgrid',
  MAILGUN = 'mailgun',
  MAILTRAP = 'mailtrap',
  SES = 'ses',
}

/**
 * Email attachment interface
 */
export interface EmailAttachment {
  filename: string;
  content: string | Buffer;
  contentType?: string;
}

/**
 * Email options interface
 */
export interface EmailOptions {
  to: string | string[];
  subject: string;
  text?: string;
  html?: string;
  from?: string;
  replyTo?: string;
  attachments?: EmailAttachment[];
}

/**
 * Email provider interface
 */
export interface IEmailProvider {
  /**
   * Send an email
   * @param options Email options
   * @returns Promise resolving to success status
   */
  sendEmail(options: EmailOptions): Promise<boolean>;
  
  /**
   * Get provider name
   */
  getProviderName(): string;
}

/**
 * Email template data interface
 */
export interface EmailTemplateData {
  [key: string]: any;
}

/**
 * Email template interface
 */
export interface IEmailTemplate {
  /**
   * Generate HTML content for the email
   * @param data Template data
   * @returns HTML content
   */
  generateHtml(data: EmailTemplateData): string;
  
  /**
   * Generate plain text content for the email
   * @param data Template data
   * @returns Plain text content
   */
  generateText(data: EmailTemplateData): string;
  
  /**
   * Get template name
   */
  getTemplateName(): string;
}
