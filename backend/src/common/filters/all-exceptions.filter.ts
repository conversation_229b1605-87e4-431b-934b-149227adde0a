import {
  type ExceptionFilter,
  Catch,
  type ArgumentsHost,
  HttpException,
  HttpStatus,
  Logger,
  Inject,
  Optional,
} from "@nestjs/common";
import type { Request, Response } from "express";
import { SentryService } from "../services/sentry.service";

@Catch()
export class AllExceptionsFilter implements ExceptionFilter {
  private readonly logger = new Logger(AllExceptionsFilter.name);

  constructor(
    @Optional()
    @Inject(SentryService)
    private readonly sentryService?: SentryService
  ) {}

  catch(exception: unknown, host: ArgumentsHost) {
    const ctx = host.switchToHttp();
    const response = ctx.getResponse<Response>();
    const request = ctx.getRequest<Request>();

    let status: number;
    let message: string;
    let error: string;

    if (exception instanceof HttpException) {
      status = exception.getStatus();
      const exceptionResponse = exception.getResponse();

      if (typeof exceptionResponse === "string") {
        message = exceptionResponse;
        error = HttpStatus[status];
      } else if (typeof exceptionResponse === "object") {
        message = (exceptionResponse as any).message || "An error occurred";
        error = (exceptionResponse as any).error || HttpStatus[status];
      } else {
        message = "An error occurred";
        error = HttpStatus[status];
      }
    } else {
      status = HttpStatus.INTERNAL_SERVER_ERROR;
      message = "Internal server error";
      error = "Internal Server Error";

      // Log unexpected errors
      this.logger.error(
        `Unhandled exception: ${
          exception instanceof Error ? exception.message : exception
        }`,
        exception instanceof Error ? exception.stack : undefined
      );

      // Send to Sentry for production monitoring
      if (exception instanceof Error && this.sentryService) {
        this.sentryService.captureException(exception, {
          method: request.method,
          url: request.url,
          userAgent: request.get("User-Agent"),
          ip: request.ip,
        });
      }
    }

    response.status(status).json({
      success: false,
      error,
      message: Array.isArray(message) ? message[0] : message,
      path: request.url,
      timestamp: new Date().toISOString(),
    });
  }
}
