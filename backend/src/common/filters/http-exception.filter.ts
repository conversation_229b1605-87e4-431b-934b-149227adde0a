import { type ExceptionFilter, Catch, type ArgumentsHost, HttpException, HttpStatus } from "@nestjs/common"
import type { Request, Response } from "express"

@Catch(HttpException)
export class HttpExceptionFilter implements ExceptionFilter {
  catch(exception: HttpException, host: ArgumentsHost) {
    const ctx = host.switchToHttp()
    const response = ctx.getResponse<Response>()
    const request = ctx.getRequest<Request>()
    const status = exception.getStatus()
    const exceptionResponse = exception.getResponse()

    let message: string
    let error: any

    if (typeof exceptionResponse === "string") {
      message = exceptionResponse
    } else if (typeof exceptionResponse === "object") {
      message = (exceptionResponse as any).message || "An error occurred"
      error = (exceptionResponse as any).error || undefined
    } else {
      message = "An error occurred"
    }

    response.status(status).json({
      success: false,
      error: error || HttpStatus[status],
      message: Array.isArray(message) ? message[0] : message,
      path: request.url,
      timestamp: new Date().toISOString(),
    })
  }
}
