import {
  Injectable,
  NestMiddleware,
  Logger,
  Inject,
  Optional,
} from "@nestjs/common";
import { Request, Response, NextFunction } from "express";
import { CustomLoggerService } from "../services/logger.service";
import { v4 as uuidv4 } from "uuid";

export interface RequestWithId extends Request {
  id: string;
  startTime: number;
}

/**
 * Enhanced middleware that logs HTTP requests and responses
 * Captures:
 * - HTTP method
 * - Request path
 * - Request timestamp
 * - Request IP address
 * - Response status code
 * - Response time
 * - User information
 * - Request ID for tracing
 * - Security events
 * - Performance monitoring
 */
@Injectable()
export class RequestLoggerMiddleware implements NestMiddleware {
  private readonly logger = new Logger("HTTP");

  constructor(
    @Optional()
    @Inject(CustomLoggerService)
    private customLogger?: CustomLoggerService
  ) {}

  use(req: RequestWithId, res: Response, next: NextFunction): void {
    // Generate unique request ID and set start time
    req.id = uuidv4();
    req.startTime = Date.now();

    // Add request ID to response headers for tracing
    res.setHeader("X-Request-ID", req.id);

    // Get request details
    const { method, originalUrl } = req;
    const userAgent = req.get("user-agent") || "";
    const clientIp = this.getClientIp(req);
    const timestamp = new Date().toISOString();

    // Extract user info if available
    const userId = (req as any).user?.id;
    const userRole = (req as any).user?.role;

    // Log request start
    this.logger.log(
      `[REQUEST] ${method} ${originalUrl} - ${clientIp} - ${timestamp} - ID: ${req.id}`
    );

    // Use custom logger if available
    if (this.customLogger) {
      this.customLogger.debug(`Request started: ${method} ${originalUrl}`, {
        requestId: req.id,
        method,
        url: originalUrl,
        userAgent,
        ip: clientIp,
        userId,
        userRole,
      });
    }

    // Add response listener to log after response is sent
    res.on("finish", () => {
      // Calculate response time
      const responseTime = Date.now() - req.startTime;
      const contentLength = res.get("content-length") || 0;
      const { statusCode } = res;

      // Determine log level based on status code
      const logLevel =
        statusCode >= 500 ? "error" : statusCode >= 400 ? "warn" : "log";

      // Log response
      this.logger[logLevel](
        `[RESPONSE] ${method} ${originalUrl} - ${statusCode} - ${responseTime}ms - ${contentLength} bytes - ID: ${req.id}`
      );

      // Enhanced logging with custom logger
      if (this.customLogger) {
        this.customLogger.logRequest(
          method,
          originalUrl,
          statusCode,
          responseTime,
          {
            requestId: req.id,
            userAgent,
            ip: clientIp,
            userId,
            userRole,
            contentLength,
          }
        );

        // Log slow requests
        if (responseTime > 1000) {
          this.customLogger.logPerformance(
            `${method} ${originalUrl}`,
            responseTime,
            1000,
            {
              requestId: req.id,
              userId,
            }
          );
        }

        // Log security events
        if (statusCode === 401 || statusCode === 403) {
          this.customLogger.logSecurityEvent(
            "Unauthorized access attempt",
            "medium",
            {
              requestId: req.id,
              method,
              url: originalUrl,
              ip: clientIp,
              userAgent,
              statusCode,
            }
          );
        }

        // Log server errors
        if (statusCode >= 500) {
          this.customLogger.error(
            `Server error: ${method} ${originalUrl}`,
            undefined,
            {
              requestId: req.id,
              method,
              url: originalUrl,
              statusCode,
              userId,
            }
          );
        }
      }
    });

    next();
  }

  private getClientIp(req: Request): string {
    return (
      (req.headers["x-forwarded-for"] as string)?.split(",")[0] ||
      req.connection.remoteAddress ||
      req.socket.remoteAddress ||
      "unknown"
    );
  }
}
