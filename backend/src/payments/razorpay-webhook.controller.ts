import {
  <PERSON>,
  Post,
  Body,
  Headers,
  BadRequestException,
  Logger,
  HttpCode,
} from "@nestjs/common";
import { ApiTags, ApiOperation, ApiResponse } from "@nestjs/swagger";
import { PaymentsService } from "./payments.service";
import { ConfigService } from "@nestjs/config";
import { createHmac } from "crypto";
// Define types locally since our declaration file isn't being recognized as a module
type RazorpayWebhookEvent = {
  entity: string;
  account_id: string;
  event: string;
  contains: string[];
  payload: {
    payment?: {
      entity: RazorpayPayment;
    };
    order?: {
      entity: RazorpayOrder;
    };
    refund?: {
      entity: {
        id: string;
        entity: "refund";
        amount: number;
        currency: string;
        payment_id: string;
        notes: Record<string, string | number>;
        created_at: number;
        status: "pending" | "processed" | "failed";
      };
    };
  };
  created_at: number;
};

type RazorpayPayment = {
  id: string;
  entity: "payment";
  amount: number;
  currency: string;
  status: "created" | "authorized" | "captured" | "refunded" | "failed";
  order_id: string;
  method: "card" | "netbanking" | "wallet" | "emi" | "upi";
  amount_refunded: number;
  captured: boolean;
  description?: string;
  email: string;
  contact: string;
  notes: Record<string, string | number>;
  created_at: number;
};

type RazorpayOrder = {
  id: string;
  entity: "order";
  amount: number;
  amount_paid: number;
  amount_due: number;
  currency: string;
  receipt: string;
  status: "created" | "attempted" | "paid";
  attempts: number;
  notes: Record<string, string | number>;
  created_at: number;
};

@ApiTags("webhooks")
@Controller("webhooks/razorpay")
export class RazorpayWebhookController {
  private readonly logger = new Logger(RazorpayWebhookController.name);

  constructor(
    private readonly paymentsService: PaymentsService,
    private readonly configService: ConfigService
  ) {}

  @Post()
  @HttpCode(200)
  @ApiOperation({
    summary: "Handle Razorpay webhook events",
    description:
      "Process webhook events from Razorpay for payment status updates",
  })
  @ApiResponse({
    status: 200,
    description: "Webhook processed successfully",
  })
  @ApiResponse({
    status: 400,
    description: "Invalid webhook signature or payload",
  })
  async handleWebhook(
    @Body() payload: RazorpayWebhookEvent,
    @Headers("x-razorpay-signature") signature: string
  ) {
    try {
      // Verify webhook signature
      const webhookSecret = this.configService.get<string>(
        "RAZORPAY_WEBHOOK_SECRET"
      );
      if (!webhookSecret) {
        throw new BadRequestException("Webhook secret not configured");
      }

      const expectedSignature = createHmac("sha256", webhookSecret)
        .update(JSON.stringify(payload))
        .digest("hex");

      if (signature !== expectedSignature) {
        this.logger.warn("Invalid webhook signature received");
        throw new BadRequestException("Invalid signature");
      }

      // Process the webhook event
      const { event, payload: eventPayload } = payload;

      this.logger.log(`Processing Razorpay webhook event: ${event}`);

      switch (event) {
        case "payment.captured":
          await this.handlePaymentCaptured(eventPayload.payment.entity);
          break;

        case "payment.failed":
          await this.handlePaymentFailed(eventPayload.payment.entity);
          break;

        case "order.paid":
          await this.handleOrderPaid(eventPayload.order.entity);
          break;

        case "refund.created":
          await this.handleRefundCreated(eventPayload.refund.entity);
          break;

        case "refund.processed":
          await this.handleRefundProcessed(eventPayload.refund.entity);
          break;

        default:
          this.logger.log(`Unhandled webhook event: ${event}`);
      }

      return { success: true };
    } catch (error) {
      this.logger.error("Error processing Razorpay webhook:", error);
      throw new BadRequestException("Failed to process webhook");
    }
  }

  private async handlePaymentCaptured(payment: RazorpayPayment) {
    this.logger.log(`Payment captured: ${payment.id}`);

    // Update payment status in database
    // This would typically involve updating the escrow or payout records
    // based on the payment metadata

    const { notes } = payment;
    if (notes?.escrowId) {
      // Update escrow status
      this.logger.log(`Updating escrow ${notes.escrowId} for captured payment`);
    }

    if (notes?.payoutId) {
      // Update payout status
      this.logger.log(`Updating payout ${notes.payoutId} for captured payment`);
    }
  }

  private async handlePaymentFailed(payment: RazorpayPayment) {
    this.logger.log(`Payment failed: ${payment.id}`);

    const { notes } = payment;
    if (notes?.escrowId) {
      // Update escrow status to failed
      this.logger.log(`Marking escrow ${notes.escrowId} as failed`);
    }

    if (notes?.payoutId) {
      // Update payout status to failed
      this.logger.log(`Marking payout ${notes.payoutId} as failed`);
    }
  }

  private async handleOrderPaid(order: RazorpayOrder) {
    this.logger.log(`Order paid: ${order.id}`);

    // Handle order completion
    const { notes } = order;
    if (notes?.jobId) {
      this.logger.log(`Order for job ${notes.jobId} has been paid`);
    }
  }

  private async handleRefundCreated(refund: {
    id: string;
    notes: Record<string, string | number>;
  }) {
    this.logger.log(`Refund created: ${refund.id}`);

    // Handle refund creation
    const { notes } = refund;
    if (notes?.escrowId) {
      this.logger.log(`Refund created for escrow ${notes.escrowId}`);
    }
  }

  private async handleRefundProcessed(refund: {
    id: string;
    notes: Record<string, string | number>;
  }) {
    this.logger.log(`Refund processed: ${refund.id}`);

    // Handle refund completion
    const { notes } = refund;
    if (notes?.escrowId) {
      this.logger.log(`Refund processed for escrow ${notes.escrowId}`);
    }
  }
}
