import { Test, TestingModule } from '@nestjs/testing';
import { ConfigService } from '@nestjs/config';
import { PaymentsService } from '../payments.service';
import { PaymentGatewayFactory } from '../factories/payment-gateway.factory';
import { StripeGateway } from '../gateways/stripe.gateway';
import { RazorpayGateway } from '../gateways/razorpay.gateway';
import { PaymentGateway, PaymentStatus } from '@shared/types';

describe('Dual Payment Gateway Integration', () => {
  let service: PaymentsService;
  let factory: PaymentGatewayFactory;
  let stripeGateway: StripeGateway;
  let razorpayGateway: RazorpayGateway;
  let configService: ConfigService;

  const mockConfigService = {
    get: jest.fn((key: string) => {
      const config = {
        STRIPE_SECRET_KEY: 'sk_test_mock_stripe_key',
        RAZORPAY_KEY_ID: 'rzp_test_mock_key_id',
        RAZORPAY_KEY_SECRET: 'mock_razorpay_secret',
        STRIPE_WEBHOOK_SECRET: 'whsec_mock_stripe_webhook',
        RAZORPAY_WEBHOOK_SECRET: 'mock_razorpay_webhook',
      };
      return config[key];
    }),
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        PaymentsService,
        PaymentGatewayFactory,
        StripeGateway,
        RazorpayGateway,
        {
          provide: ConfigService,
          useValue: mockConfigService,
        },
      ],
    }).compile();

    service = module.get<PaymentsService>(PaymentsService);
    factory = module.get<PaymentGatewayFactory>(PaymentGatewayFactory);
    stripeGateway = module.get<StripeGateway>(StripeGateway);
    razorpayGateway = module.get<RazorpayGateway>(RazorpayGateway);
    configService = module.get<ConfigService>(ConfigService);
  });

  describe('Payment Gateway Factory', () => {
    it('should create Stripe gateway instance', () => {
      const gateway = factory.createGateway(PaymentGateway.STRIPE);
      expect(gateway).toBeInstanceOf(StripeGateway);
    });

    it('should create Razorpay gateway instance', () => {
      const gateway = factory.createGateway(PaymentGateway.RAZORPAY);
      expect(gateway).toBeInstanceOf(RazorpayGateway);
    });

    it('should return available gateways', () => {
      const availableGateways = factory.getAvailableGateways();
      expect(availableGateways).toContain(PaymentGateway.STRIPE);
      expect(availableGateways).toContain(PaymentGateway.RAZORPAY);
    });

    it('should select Razorpay as default for INR', () => {
      const defaultGateway = factory.getDefaultGateway('INR');
      expect(defaultGateway).toBe(PaymentGateway.RAZORPAY);
    });

    it('should select Stripe as default for USD', () => {
      const defaultGateway = factory.getDefaultGateway('USD');
      expect(defaultGateway).toBe(PaymentGateway.STRIPE);
    });

    it('should select best gateway based on location and currency', () => {
      const bestGateway = factory.getBestGateway('INR', 10000, 'India');
      expect(bestGateway).toBe(PaymentGateway.RAZORPAY);

      const bestGatewayUS = factory.getBestGateway('USD', 10000, 'United States');
      expect(bestGatewayUS).toBe(PaymentGateway.STRIPE);
    });
  });

  describe('Gateway Configuration', () => {
    it('should return correct Stripe configuration', () => {
      const config = stripeGateway.getConfig();
      expect(config.gateway).toBe(PaymentGateway.STRIPE);
      expect(config.supportedCurrencies).toContain('USD');
      expect(config.supportedCurrencies).toContain('EUR');
      expect(config.defaultCurrency).toBe('USD');
    });

    it('should return correct Razorpay configuration', () => {
      const config = razorpayGateway.getConfig();
      expect(config.gateway).toBe(PaymentGateway.RAZORPAY);
      expect(config.supportedCurrencies).toContain('INR');
      expect(config.defaultCurrency).toBe('INR');
    });

    it('should check currency support correctly', () => {
      expect(stripeGateway.isCurrencySupported('USD')).toBe(true);
      expect(stripeGateway.isCurrencySupported('INR')).toBe(true);
      expect(razorpayGateway.isCurrencySupported('INR')).toBe(true);
      expect(razorpayGateway.isCurrencySupported('USD')).toBe(false);
    });
  });

  describe('Payment Processing', () => {
    it('should process Stripe payment with correct parameters', async () => {
      const stripeParams = {
        amount: 2000,
        currency: 'USD',
        description: 'Test payment',
        paymentMethodId: 'pm_card_visa',
        metadata: { jobId: 'job_123' },
      };

      // Mock Stripe payment processing
      jest.spyOn(stripeGateway, 'processPayment').mockResolvedValue({
        success: true,
        transactionId: 'pi_test_123',
        orderId: 'pi_test_123',
        status: PaymentStatus.COMPLETED,
        message: 'Payment processed successfully',
        gateway: PaymentGateway.STRIPE,
        paymentMethod: 'stripe',
        gatewayResponse: { id: 'pi_test_123', status: 'succeeded' },
      });

      const result = await service.processPayment(stripeParams, PaymentGateway.STRIPE);

      expect(result.success).toBe(true);
      expect(result.gateway).toBe(PaymentGateway.STRIPE);
      expect(result.transactionId).toBe('pi_test_123');
    });

    it('should process Razorpay payment with correct parameters', async () => {
      const razorpayParams = {
        amount: 50000,
        currency: 'INR',
        description: 'Test payment',
        customerEmail: '<EMAIL>',
        customerPhone: '+919876543210',
        metadata: { jobId: 'job_123' },
      };

      // Mock Razorpay payment processing
      jest.spyOn(razorpayGateway, 'processPayment').mockResolvedValue({
        success: true,
        transactionId: 'order_test_123',
        orderId: 'order_test_123',
        status: PaymentStatus.PENDING,
        message: 'Order ready for payment',
        gateway: PaymentGateway.RAZORPAY,
        paymentMethod: 'razorpay',
        gatewayResponse: { id: 'order_test_123', status: 'created' },
      });

      const result = await service.processPayment(razorpayParams, PaymentGateway.RAZORPAY);

      expect(result.success).toBe(true);
      expect(result.gateway).toBe(PaymentGateway.RAZORPAY);
      expect(result.transactionId).toBe('order_test_123');
    });

    it('should automatically select gateway based on currency', async () => {
      const usdParams = {
        amount: 2000,
        currency: 'USD',
        description: 'Test payment',
        paymentMethodId: 'pm_card_visa',
      };

      const inrParams = {
        amount: 50000,
        currency: 'INR',
        description: 'Test payment',
        customerEmail: '<EMAIL>',
      };

      // Mock both gateways
      jest.spyOn(stripeGateway, 'processPayment').mockResolvedValue({
        success: true,
        transactionId: 'pi_test_123',
        orderId: 'pi_test_123',
        status: PaymentStatus.COMPLETED,
        gateway: PaymentGateway.STRIPE,
        paymentMethod: 'stripe',
        message: 'Success',
      });

      jest.spyOn(razorpayGateway, 'processPayment').mockResolvedValue({
        success: true,
        transactionId: 'order_test_123',
        orderId: 'order_test_123',
        status: PaymentStatus.PENDING,
        gateway: PaymentGateway.RAZORPAY,
        paymentMethod: 'razorpay',
        message: 'Success',
      });

      const usdResult = await service.processPayment(usdParams);
      const inrResult = await service.processPayment(inrParams);

      expect(usdResult.gateway).toBe(PaymentGateway.STRIPE);
      expect(inrResult.gateway).toBe(PaymentGateway.RAZORPAY);
    });
  });

  describe('Refund Processing', () => {
    it('should process Stripe refund', async () => {
      const refundParams = {
        transactionId: 'pi_test_123',
        amount: 1000,
        reason: 'Customer request',
      };

      jest.spyOn(stripeGateway, 'refundPayment').mockResolvedValue({
        success: true,
        refundId: 're_test_123',
        amount: 1000,
        status: 'succeeded',
        gateway: PaymentGateway.STRIPE,
      });

      const result = await service.refundPayment(refundParams);

      expect(result.success).toBe(true);
      expect(result.gateway).toBe(PaymentGateway.STRIPE);
      expect(result.refundId).toBe('re_test_123');
    });

    it('should process Razorpay refund', async () => {
      const refundParams = {
        transactionId: 'pay_test_123',
        amount: 25000,
        reason: 'Customer request',
      };

      jest.spyOn(razorpayGateway, 'refundPayment').mockResolvedValue({
        success: true,
        refundId: 'rfnd_test_123',
        amount: 25000,
        status: 'processed',
        gateway: PaymentGateway.RAZORPAY,
      });

      const result = await service.refundPayment(refundParams);

      expect(result.success).toBe(true);
      expect(result.gateway).toBe(PaymentGateway.RAZORPAY);
      expect(result.refundId).toBe('rfnd_test_123');
    });
  });

  describe('Webhook Signature Verification', () => {
    it('should verify Stripe webhook signature', () => {
      const payload = 'test_payload';
      const signature = 'test_signature';
      const secret = 'whsec_test_secret';

      jest.spyOn(stripeGateway, 'verifyWebhookSignature').mockReturnValue(true);

      const isValid = stripeGateway.verifyWebhookSignature(payload, signature, secret);
      expect(isValid).toBe(true);
    });

    it('should verify Razorpay webhook signature', () => {
      const payload = 'test_payload';
      const signature = 'test_signature';
      const secret = 'test_secret';

      jest.spyOn(razorpayGateway, 'verifyWebhookSignature').mockReturnValue(true);

      const isValid = razorpayGateway.verifyWebhookSignature(payload, signature, secret);
      expect(isValid).toBe(true);
    });
  });

  describe('Error Handling', () => {
    it('should handle gateway not available error', () => {
      expect(() => {
        factory.createGateway('invalid_gateway' as PaymentGateway);
      }).toThrow('Payment gateway invalid_gateway is not available');
    });

    it('should handle unsupported currency error', () => {
      expect(() => {
        factory.getDefaultGateway('INVALID_CURRENCY');
      }).toThrow('No available payment gateway supports currency: INVALID_CURRENCY');
    });

    it('should handle payment processing errors gracefully', async () => {
      const params = {
        amount: 2000,
        currency: 'USD',
        description: 'Test payment',
      };

      jest.spyOn(stripeGateway, 'processPayment').mockRejectedValue(new Error('Payment failed'));

      await expect(service.processPayment(params, PaymentGateway.STRIPE)).rejects.toThrow('Payment processing failed');
    });
  });
});
