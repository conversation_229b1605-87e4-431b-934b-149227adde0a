import {
  <PERSON>,
  Get,
  Post,
  Body,
  Param,
  Patch,
  UseGuards,
  Request,
  BadRequestException,
  ForbiddenException,
} from "@nestjs/common";
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiBearerAuth,
  ApiParam,
  ApiBody,
} from "@nestjs/swagger";
import { EscrowService } from "./escrow.service";
import { JwtAuthGuard } from "../auth/guards/jwt-auth.guard";
import { RolesGuard } from "../auth/guards/roles.guard";
import { Roles } from "../auth/decorators/roles.decorator";
import { UserRole } from "@shared/types/user";
import {
  CreateEscrowDto,
  FundEscrowDto,
  ReleaseEscrowDto,
  RefundEscrowDto,
  EscrowResponseDto,
  EscrowStatsDto,
} from "./dto/escrow.dto";
import { RequestWithUser } from "../auth/interfaces/request.interface";

@ApiTags("escrow")
@Controller("escrow")
@UseGuards(JwtAuthGuard)
@ApiBearerAuth()
export class EscrowController {
  constructor(private readonly escrowService: EscrowService) {}

  @Post()
  @UseGuards(RolesGuard)
  @Roles(UserRole.COMPANY, UserRole.ADMIN)
  @ApiOperation({
    summary: "Create an escrow account",
    description:
      "Create an escrow account for a job. Only companies and admins can create escrow accounts.",
  })
  @ApiBody({ type: CreateEscrowDto })
  @ApiResponse({
    status: 201,
    description: "Escrow account created successfully",
    type: EscrowResponseDto,
  })
  @ApiResponse({
    status: 400,
    description: "Bad request - invalid data or escrow already exists",
  })
  @ApiResponse({
    status: 403,
    description: "Forbidden - insufficient permissions",
  })
  async createEscrow(
    @Request() req: RequestWithUser,
    @Body() createEscrowDto: CreateEscrowDto
  ) {
    // Ensure company can only create escrow for their own jobs
    if (
      req.user.role === UserRole.COMPANY &&
      createEscrowDto.companyId !== req.user.companyId
    ) {
      throw new ForbiddenException(
        "You can only create escrow for your own company"
      );
    }

    return await this.escrowService.createEscrow(createEscrowDto);
  }

  @Post(":id/fund")
  @UseGuards(RolesGuard)
  @Roles(UserRole.COMPANY, UserRole.ADMIN)
  @ApiOperation({
    summary: "Fund an escrow account",
    description: "Fund an escrow account using a payment method.",
  })
  @ApiParam({ name: "id", description: "Escrow account ID" })
  @ApiBody({ type: FundEscrowDto })
  @ApiResponse({
    status: 200,
    description: "Escrow account funded successfully",
    type: EscrowResponseDto,
  })
  @ApiResponse({
    status: 400,
    description:
      "Bad request - invalid payment data or escrow not in pending status",
  })
  @ApiResponse({
    status: 404,
    description: "Escrow account not found",
  })
  async fundEscrow(
    @Request() req: RequestWithUser,
    @Param("id") escrowId: string,
    @Body() fundEscrowDto: FundEscrowDto
  ) {
    // Verify user has permission to fund this escrow
    const escrow = await this.escrowService.findById(escrowId);

    if (
      req.user.role === UserRole.COMPANY &&
      escrow.companyId !== req.user.companyId
    ) {
      throw new ForbiddenException(
        "You can only fund your own escrow accounts"
      );
    }

    const { paymentMethod, ...paymentData } = fundEscrowDto;
    return await this.escrowService.fundEscrow(
      escrowId,
      paymentMethod,
      paymentData
    );
  }

  @Patch(":id/release")
  @UseGuards(RolesGuard)
  @Roles(UserRole.COMPANY, UserRole.ADMIN)
  @ApiOperation({
    summary: "Release escrow funds to worker",
    description: "Release held escrow funds to a worker upon job completion.",
  })
  @ApiParam({ name: "id", description: "Escrow account ID" })
  @ApiBody({ type: ReleaseEscrowDto })
  @ApiResponse({
    status: 200,
    description: "Escrow funds released successfully",
    type: EscrowResponseDto,
  })
  @ApiResponse({
    status: 400,
    description: "Bad request - escrow not in held status",
  })
  @ApiResponse({
    status: 404,
    description: "Escrow account not found",
  })
  async releaseEscrow(
    @Request() req: RequestWithUser,
    @Param("id") escrowId: string,
    @Body() releaseEscrowDto: ReleaseEscrowDto
  ) {
    // Verify user has permission to release this escrow
    const escrow = await this.escrowService.findById(escrowId);

    if (
      req.user.role === UserRole.COMPANY &&
      escrow.companyId !== req.user.companyId
    ) {
      throw new ForbiddenException(
        "You can only release your own escrow accounts"
      );
    }

    // Set the releasedById to the current user
    releaseEscrowDto.releasedById = req.user.id;

    return await this.escrowService.releaseEscrow(escrowId, releaseEscrowDto);
  }

  @Patch(":id/refund")
  @UseGuards(RolesGuard)
  @Roles(UserRole.COMPANY, UserRole.ADMIN)
  @ApiOperation({
    summary: "Refund escrow funds to company",
    description: "Refund held escrow funds back to the company.",
  })
  @ApiParam({ name: "id", description: "Escrow account ID" })
  @ApiBody({ type: RefundEscrowDto })
  @ApiResponse({
    status: 200,
    description: "Escrow funds refunded successfully",
    type: EscrowResponseDto,
  })
  @ApiResponse({
    status: 400,
    description: "Bad request - escrow not in held status",
  })
  @ApiResponse({
    status: 404,
    description: "Escrow account not found",
  })
  async refundEscrow(
    @Request() req: RequestWithUser,
    @Param("id") escrowId: string,
    @Body() refundEscrowDto: RefundEscrowDto
  ) {
    // Verify user has permission to refund this escrow
    const escrow = await this.escrowService.findById(escrowId);

    if (
      req.user.role === UserRole.COMPANY &&
      escrow.companyId !== req.user.companyId
    ) {
      throw new ForbiddenException(
        "You can only refund your own escrow accounts"
      );
    }

    // Set the refundedById to the current user
    refundEscrowDto.refundedById = req.user.id;

    return await this.escrowService.refundEscrow(escrowId, refundEscrowDto);
  }

  @Get(":id")
  @ApiOperation({
    summary: "Get escrow account details",
    description: "Get details of a specific escrow account.",
  })
  @ApiParam({ name: "id", description: "Escrow account ID" })
  @ApiResponse({
    status: 200,
    description: "Escrow account details",
    type: EscrowResponseDto,
  })
  @ApiResponse({
    status: 404,
    description: "Escrow account not found",
  })
  async getEscrow(
    @Request() req: RequestWithUser,
    @Param("id") escrowId: string
  ) {
    const escrow = await this.escrowService.findById(escrowId);

    // Check permissions - users can only view escrows they're involved in
    const canView =
      req.user.role === UserRole.ADMIN ||
      escrow.companyId === req.user.companyId ||
      escrow.workerId === req.user.id;

    if (!canView) {
      throw new ForbiddenException(
        "You do not have permission to view this escrow account"
      );
    }

    return escrow;
  }

  @Get("job/:jobId")
  @ApiOperation({
    summary: "Get escrow account for a job",
    description: "Get the escrow account associated with a specific job.",
  })
  @ApiParam({ name: "jobId", description: "Job ID" })
  @ApiResponse({
    status: 200,
    description: "Escrow account for the job",
    type: EscrowResponseDto,
  })
  @ApiResponse({
    status: 404,
    description: "Escrow account not found for this job",
  })
  async getEscrowByJobId(
    @Request() req: RequestWithUser,
    @Param("jobId") jobId: string
  ) {
    const escrow = await this.escrowService.findByJobId(jobId);

    if (!escrow) {
      throw new BadRequestException("No escrow account found for this job");
    }

    // Check permissions
    const canView =
      req.user.role === UserRole.ADMIN ||
      escrow.companyId === req.user.companyId ||
      escrow.workerId === req.user.id;

    if (!canView) {
      throw new ForbiddenException(
        "You do not have permission to view this escrow account"
      );
    }

    return escrow;
  }

  @Get("company/:companyId/stats")
  @UseGuards(RolesGuard)
  @Roles(UserRole.COMPANY, UserRole.ADMIN)
  @ApiOperation({
    summary: "Get escrow statistics for a company",
    description:
      "Get aggregated statistics for all escrow accounts of a company.",
  })
  @ApiParam({ name: "companyId", description: "Company ID" })
  @ApiResponse({
    status: 200,
    description: "Escrow statistics",
    type: EscrowStatsDto,
  })
  async getEscrowStats(
    @Request() req: RequestWithUser,
    @Param("companyId") companyId: string
  ) {
    // Ensure company can only view their own stats
    if (
      req.user.role === UserRole.COMPANY &&
      companyId !== req.user.companyId
    ) {
      throw new ForbiddenException(
        "You can only view your own escrow statistics"
      );
    }

    return await this.escrowService.getEscrowStats(companyId);
  }

  @Get("company/:companyId")
  @UseGuards(RolesGuard)
  @Roles(UserRole.COMPANY, UserRole.ADMIN)
  @ApiOperation({
    summary: "Get all escrow accounts for a company",
    description: "Get all escrow accounts associated with a company.",
  })
  @ApiParam({ name: "companyId", description: "Company ID" })
  @ApiResponse({
    status: 200,
    description: "List of escrow accounts",
    type: [EscrowResponseDto],
  })
  async getCompanyEscrows(
    @Request() req: RequestWithUser,
    @Param("companyId") companyId: string
  ) {
    // Ensure company can only view their own escrows
    if (
      req.user.role === UserRole.COMPANY &&
      companyId !== req.user.companyId
    ) {
      throw new ForbiddenException(
        "You can only view your own escrow accounts"
      );
    }

    return await this.escrowService.findByCompanyId(companyId);
  }
}
