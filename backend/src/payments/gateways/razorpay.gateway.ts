import { Injectable, Logger, BadRequestException } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import Razorpay from 'razorpay';
import {
  PaymentGateway,
  PaymentMethod,
  PaymentStatus,
  PaymentResult,
  RefundResult,
  StripePaymentParams,
  RazorpayPaymentParams,
  RefundParams,
  PaymentGatewayConfig,
  RazorpayOrderResponse,
  RazorpayPaymentResponse,
} from '@shared/types';
import { IPaymentGateway } from '../interfaces/payment-gateway.interface';
import { createHmac } from 'crypto';

@Injectable()
export class RazorpayGateway implements IPaymentGateway {
  private readonly logger = new Logger(RazorpayGateway.name);
  private razorpay: Razorpay | null = null;
  private readonly config: PaymentGatewayConfig;

  constructor(private configService: ConfigService) {
    this.config = {
      gateway: PaymentGateway.RAZORPAY,
      enabled: !!(
        this.configService.get<string>('RAZORPAY_KEY_ID') &&
        this.configService.get<string>('RAZORPAY_KEY_SECRET')
      ),
      supportedCurrencies: ['INR'],
      supportedMethods: [
        PaymentMethod.RAZORPAY,
        PaymentMethod.UPI,
        PaymentMethod.CARD,
        PaymentMethod.WALLET,
        PaymentMethod.BANK_TRANSFER,
      ],
      defaultCurrency: 'INR',
      webhookUrl: '/webhooks/razorpay',
    };
  }

  getConfig(): PaymentGatewayConfig {
    return this.config;
  }

  async initialize(): Promise<void> {
    const keyId = this.configService.get<string>('RAZORPAY_KEY_ID');
    const keySecret = this.configService.get<string>('RAZORPAY_KEY_SECRET');

    if (!keyId || !keySecret) {
      this.logger.warn('Razorpay credentials not configured');
      return;
    }

    try {
      this.razorpay = new Razorpay({
        key_id: keyId,
        key_secret: keySecret,
      });

      this.logger.log('Razorpay gateway initialized successfully');
    } catch (error) {
      this.logger.error('Failed to initialize Razorpay gateway:', error);
      throw new BadRequestException('Failed to initialize Razorpay gateway');
    }
  }

  isConfigured(): boolean {
    return this.razorpay !== null && this.config.enabled;
  }

  async createPaymentIntent(params: StripePaymentParams | RazorpayPaymentParams): Promise<PaymentResult> {
    if (!this.isConfigured()) {
      throw new BadRequestException('Razorpay is not configured');
    }

    const razorpayParams = params as RazorpayPaymentParams;

    try {
      const orderOptions = {
        amount: Math.round(razorpayParams.amount * 100), // Convert to paise
        currency: razorpayParams.currency.toUpperCase(),
        receipt: razorpayParams.receipt || `order_${Date.now()}`,
        notes: this.sanitizeNotes(razorpayParams.metadata || {}),
      };

      const order = await this.razorpay!.orders.create(orderOptions);

      return {
        success: true,
        transactionId: order.id,
        orderId: order.id,
        status: this.mapRazorpayStatusToPaymentStatus(order.status),
        message: 'Order created successfully',
        gateway: PaymentGateway.RAZORPAY,
        paymentMethod: PaymentMethod.RAZORPAY,
        gatewayResponse: order,
      };
    } catch (error) {
      this.logger.error('Razorpay order creation failed:', error);
      return {
        success: false,
        status: PaymentStatus.FAILED,
        message: error instanceof Error ? error.message : 'Order creation failed',
        gateway: PaymentGateway.RAZORPAY,
        paymentMethod: PaymentMethod.RAZORPAY,
      };
    }
  }

  async processPayment(params: StripePaymentParams | RazorpayPaymentParams): Promise<PaymentResult> {
    const razorpayParams = params as RazorpayPaymentParams;

    // If orderId is provided, fetch the order, otherwise create a new one
    let order: RazorpayOrderResponse;
    if (razorpayParams.orderId) {
      try {
        order = await this.razorpay!.orders.fetch(razorpayParams.orderId);
      } catch (error) {
        this.logger.error('Failed to fetch Razorpay order:', error);
        return {
          success: false,
          status: PaymentStatus.FAILED,
          message: 'Invalid order ID',
          gateway: PaymentGateway.RAZORPAY,
          paymentMethod: PaymentMethod.RAZORPAY,
        };
      }
    } else {
      const orderResult = await this.createPaymentIntent(params);
      if (!orderResult.success) {
        return orderResult;
      }
      order = orderResult.gatewayResponse as RazorpayOrderResponse;
    }

    // For Razorpay, the actual payment processing happens on the frontend
    // This method returns the order details for frontend processing
    return {
      success: true,
      transactionId: order.id,
      orderId: order.id,
      status: this.mapRazorpayStatusToPaymentStatus(order.status),
      message: 'Order ready for payment',
      gateway: PaymentGateway.RAZORPAY,
      paymentMethod: PaymentMethod.RAZORPAY,
      gatewayResponse: order,
    };
  }

  async capturePayment(paymentId: string, amount?: number): Promise<PaymentResult> {
    if (!this.isConfigured()) {
      throw new BadRequestException('Razorpay is not configured');
    }

    try {
      const captureAmount = amount ? Math.round(amount * 100) : undefined;
      const payment = await this.razorpay!.payments.capture(paymentId, captureAmount || 0, 'INR');

      return {
        success: payment.status === 'captured',
        transactionId: payment.id,
        orderId: payment.order_id,
        status: this.mapRazorpayPaymentStatusToPaymentStatus(payment.status),
        message: 'Payment captured successfully',
        gateway: PaymentGateway.RAZORPAY,
        paymentMethod: PaymentMethod.RAZORPAY,
        gatewayResponse: payment,
      };
    } catch (error) {
      this.logger.error('Razorpay payment capture failed:', error);
      return {
        success: false,
        status: PaymentStatus.FAILED,
        message: error instanceof Error ? error.message : 'Payment capture failed',
        gateway: PaymentGateway.RAZORPAY,
        paymentMethod: PaymentMethod.RAZORPAY,
      };
    }
  }

  async refundPayment(params: RefundParams): Promise<RefundResult> {
    if (!this.isConfigured()) {
      throw new BadRequestException('Razorpay is not configured');
    }

    try {
      const refundParams: any = {
        payment_id: params.transactionId,
        notes: this.sanitizeNotes(params.metadata || {}),
      };

      if (params.amount) {
        refundParams.amount = Math.round(params.amount * 100);
      }

      if (params.reason) {
        refundParams.notes.reason = params.reason;
      }

      const refund = await this.razorpay!.payments.refund(params.transactionId, refundParams);

      return {
        success: refund.status === 'processed',
        refundId: refund.id,
        amount: refund.amount / 100,
        status: refund.status as 'pending' | 'succeeded' | 'failed',
        gateway: PaymentGateway.RAZORPAY,
        gatewayResponse: refund,
      };
    } catch (error) {
      this.logger.error('Razorpay refund failed:', error);
      throw new BadRequestException(`Refund failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  verifyWebhookSignature(payload: string, signature: string, secret: string): boolean {
    try {
      const expectedSignature = createHmac('sha256', secret)
        .update(payload)
        .digest('hex');
      
      return signature === expectedSignature;
    } catch (error) {
      this.logger.error('Webhook signature verification failed:', error);
      return false;
    }
  }

  async getPaymentDetails(transactionId: string): Promise<PaymentResult> {
    if (!this.isConfigured()) {
      throw new BadRequestException('Razorpay is not configured');
    }

    try {
      // Try to fetch as payment first, then as order
      let result: RazorpayPaymentResponse | RazorpayOrderResponse;
      let isPayment = true;

      try {
        result = await this.razorpay!.payments.fetch(transactionId);
      } catch {
        // If payment fetch fails, try order fetch
        result = await this.razorpay!.orders.fetch(transactionId);
        isPayment = false;
      }

      const status = isPayment 
        ? this.mapRazorpayPaymentStatusToPaymentStatus((result as RazorpayPaymentResponse).status)
        : this.mapRazorpayStatusToPaymentStatus((result as RazorpayOrderResponse).status);

      return {
        success: true,
        transactionId: result.id,
        orderId: isPayment ? (result as RazorpayPaymentResponse).order_id : result.id,
        status,
        message: 'Payment details retrieved successfully',
        gateway: PaymentGateway.RAZORPAY,
        paymentMethod: PaymentMethod.RAZORPAY,
        gatewayResponse: result,
      };
    } catch (error) {
      this.logger.error('Failed to retrieve Razorpay payment details:', error);
      throw new BadRequestException('Failed to retrieve payment details');
    }
  }

  getSupportedCurrencies(): string[] {
    return this.config.supportedCurrencies;
  }

  getSupportedPaymentMethods(): string[] {
    return this.config.supportedMethods;
  }

  isCurrencySupported(currency: string): boolean {
    return this.config.supportedCurrencies.includes(currency.toUpperCase());
  }

  isPaymentMethodSupported(method: string): boolean {
    return this.config.supportedMethods.includes(method as PaymentMethod);
  }

  private mapRazorpayStatusToPaymentStatus(razorpayStatus: string): PaymentStatus {
    switch (razorpayStatus) {
      case 'paid':
        return PaymentStatus.COMPLETED;
      case 'attempted':
        return PaymentStatus.PROCESSING;
      case 'created':
        return PaymentStatus.PENDING;
      default:
        return PaymentStatus.PENDING;
    }
  }

  private mapRazorpayPaymentStatusToPaymentStatus(razorpayStatus: string): PaymentStatus {
    switch (razorpayStatus) {
      case 'captured':
        return PaymentStatus.COMPLETED;
      case 'authorized':
        return PaymentStatus.PROCESSING;
      case 'created':
        return PaymentStatus.PENDING;
      case 'failed':
        return PaymentStatus.FAILED;
      case 'refunded':
        return PaymentStatus.REFUNDED;
      default:
        return PaymentStatus.PENDING;
    }
  }

  private sanitizeNotes(metadata: Record<string, unknown>): Record<string, string> {
    const sanitized: Record<string, string> = {};
    for (const [key, value] of Object.entries(metadata)) {
      if (value !== null && value !== undefined) {
        sanitized[key] = String(value);
      }
    }
    return sanitized;
  }
}
