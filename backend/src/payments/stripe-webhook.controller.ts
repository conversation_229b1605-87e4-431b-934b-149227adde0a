import {
  Controller,
  Post,
  Body,
  Headers,
  HttpCode,
  BadRequestException,
  Logger,
  RawBodyRequest,
  Req,
} from "@nestjs/common";
import { ConfigService } from "@nestjs/config";
import { ApiTags, ApiOperation, ApiResponse } from "@nestjs/swagger";
import { Request } from "express";
import Stripe from "stripe";
import {
  PaymentStatus,
  PaymentGateway,
  PaymentMethod,
  WebhookEventType,
  StripeWebhookPayload,
  StripePaymentIntentResponse,
  StripeRefundResponse,
} from "@shared/types";
import { PaymentsService } from "./payments.service";
import { EscrowService } from "./escrow.service";
import { PayoutsService } from "../payouts/payouts.service";

@ApiTags("webhooks")
@Controller("webhooks/stripe")
export class StripeWebhookController {
  private readonly logger = new Logger(StripeWebhookController.name);
  private stripe: Stripe | null = null;

  constructor(
    private readonly paymentsService: PaymentsService,
    private readonly configService: ConfigService,
    private readonly escrowService: EscrowService,
    private readonly payoutService: PayoutsService
  ) {
    const secretKey = this.configService.get<string>("STRIPE_SECRET_KEY");
    if (secretKey) {
      this.stripe = new Stripe(secretKey, {
        apiVersion: "2024-06-20",
        typescript: true,
      });
    }
  }

  @Post()
  @HttpCode(200)
  @ApiOperation({
    summary: "Handle Stripe webhook events",
    description:
      "Process webhook events from Stripe for payment status updates",
  })
  @ApiResponse({
    status: 200,
    description: "Webhook processed successfully",
  })
  @ApiResponse({
    status: 400,
    description: "Invalid webhook signature or payload",
  })
  async handleWebhook(
    @Req() request: RawBodyRequest<Request>,
    @Headers("stripe-signature") signature: string
  ) {
    try {
      if (!this.stripe) {
        throw new BadRequestException("Stripe is not configured");
      }

      const webhookSecret = this.configService.get<string>(
        "STRIPE_WEBHOOK_SECRET"
      );
      if (!webhookSecret) {
        throw new BadRequestException("Stripe webhook secret not configured");
      }

      // Verify webhook signature
      let event: Stripe.Event;
      try {
        event = this.stripe.webhooks.constructEvent(
          request.rawBody || request.body,
          signature,
          webhookSecret
        );
      } catch (error) {
        this.logger.warn("Invalid Stripe webhook signature received");
        throw new BadRequestException("Invalid signature");
      }

      this.logger.log(`Processing Stripe webhook event: ${event.type}`);

      // Process the webhook event
      switch (event.type) {
        case "payment_intent.succeeded":
          await this.handlePaymentIntentSucceeded(
            event.data.object as Stripe.PaymentIntent
          );
          break;

        case "payment_intent.payment_failed":
          await this.handlePaymentIntentFailed(
            event.data.object as Stripe.PaymentIntent
          );
          break;

        case "payment_intent.requires_action":
          await this.handlePaymentIntentRequiresAction(
            event.data.object as Stripe.PaymentIntent
          );
          break;

        case "payment_intent.canceled":
          await this.handlePaymentIntentCanceled(
            event.data.object as Stripe.PaymentIntent
          );
          break;

        case "charge.succeeded":
          await this.handleChargeSucceeded(event.data.object as Stripe.Charge);
          break;

        case "charge.failed":
          await this.handleChargeFailed(event.data.object as Stripe.Charge);
          break;

        case "charge.refunded":
          await this.handleChargeRefunded(event.data.object as Stripe.Charge);
          break;

        case "refund.created":
          await this.handleRefundCreated(event.data.object as Stripe.Refund);
          break;

        case "refund.updated":
          await this.handleRefundUpdated(event.data.object as Stripe.Refund);
          break;

        case "charge.dispute.created":
          await this.handleDisputeCreated(event.data.object as Stripe.Dispute);
          break;

        case "charge.dispute.updated":
          await this.handleDisputeUpdated(event.data.object as Stripe.Dispute);
          break;

        default:
          this.logger.log(`Unhandled Stripe webhook event: ${event.type}`);
      }

      return { success: true };
    } catch (error) {
      this.logger.error("Error processing Stripe webhook:", error);
      throw new BadRequestException("Failed to process webhook");
    }
  }

  private async handlePaymentIntentSucceeded(
    paymentIntent: Stripe.PaymentIntent
  ) {
    this.logger.log(`Payment intent succeeded: ${paymentIntent.id}`);

    const { metadata } = paymentIntent;

    // Update escrow status if this is an escrow funding
    if (metadata?.escrowId) {
      this.logger.log(
        `Updating escrow ${metadata.escrowId} for successful payment`
      );
      try {
        // Update escrow status to HELD
        await this.escrowService.updateEscrowStatus(metadata.escrowId, "HELD");
      } catch (error) {
        this.logger.error(`Failed to update escrow status: ${error.message}`);
      }
    }

    // Update payout status if this is a payout
    if (metadata?.payoutId) {
      this.logger.log(
        `Updating payout ${metadata.payoutId} for successful payment`
      );
      try {
        // Update payout status to COMPLETED
        await this.payoutService.updatePayoutStatus(
          metadata.payoutId,
          "COMPLETED"
        );
      } catch (error) {
        this.logger.error(`Failed to update payout status: ${error.message}`);
      }
    }

    // Send notification
    if (metadata?.companyId || metadata?.workerId) {
      // TODO: Send payment success notification
      this.logger.log("Sending payment success notification");
    }
  }

  private async handlePaymentIntentFailed(paymentIntent: Stripe.PaymentIntent) {
    this.logger.log(`Payment intent failed: ${paymentIntent.id}`);

    const { metadata } = paymentIntent;

    // Update escrow status if this is an escrow funding
    if (metadata?.escrowId) {
      this.logger.log(
        `Updating escrow ${metadata.escrowId} for failed payment`
      );
      // TODO: Update escrow status to FAILED
      // await this.escrowService.updateEscrowStatus(metadata.escrowId, EscrowStatus.FAILED);
    }

    // Update payout status if this is a payout
    if (metadata?.payoutId) {
      this.logger.log(
        `Updating payout ${metadata.payoutId} for failed payment`
      );
      // TODO: Update payout status
      // await this.payoutService.updatePayoutStatus(metadata.payoutId, PaymentStatus.FAILED);
    }

    // Send notification
    if (metadata?.companyId || metadata?.workerId) {
      // TODO: Send payment failure notification
      this.logger.log("Sending payment failure notification");
    }
  }

  private async handlePaymentIntentRequiresAction(
    paymentIntent: Stripe.PaymentIntent
  ) {
    this.logger.log(`Payment intent requires action: ${paymentIntent.id}`);

    const { metadata } = paymentIntent;

    // Send notification about required action (e.g., 3D Secure)
    if (metadata?.companyId || metadata?.workerId) {
      // TODO: Send action required notification
      this.logger.log("Sending payment action required notification");
    }
  }

  private async handlePaymentIntentCanceled(
    paymentIntent: Stripe.PaymentIntent
  ) {
    this.logger.log(`Payment intent canceled: ${paymentIntent.id}`);

    const { metadata } = paymentIntent;

    // Update escrow status if this is an escrow funding
    if (metadata?.escrowId) {
      this.logger.log(
        `Updating escrow ${metadata.escrowId} for canceled payment`
      );
      // TODO: Update escrow status to CANCELLED
      // await this.escrowService.updateEscrowStatus(metadata.escrowId, EscrowStatus.CANCELLED);
    }
  }

  private async handleChargeSucceeded(charge: Stripe.Charge) {
    this.logger.log(`Charge succeeded: ${charge.id}`);
    // Additional charge-specific logic if needed
  }

  private async handleChargeFailed(charge: Stripe.Charge) {
    this.logger.log(`Charge failed: ${charge.id}`);
    // Additional charge-specific logic if needed
  }

  private async handleChargeRefunded(charge: Stripe.Charge) {
    this.logger.log(`Charge refunded: ${charge.id}`);

    const { metadata } = charge;

    // Update escrow status if this is an escrow refund
    if (metadata?.escrowId) {
      this.logger.log(
        `Updating escrow ${metadata.escrowId} for refunded payment`
      );
      // TODO: Update escrow status to REFUNDED
      // await this.escrowService.updateEscrowStatus(metadata.escrowId, EscrowStatus.REFUNDED);
    }
  }

  private async handleRefundCreated(refund: Stripe.Refund) {
    this.logger.log(`Refund created: ${refund.id}`);

    const { metadata } = refund;

    // Send refund notification
    if (metadata?.companyId || metadata?.workerId) {
      // TODO: Send refund notification
      this.logger.log("Sending refund created notification");
    }
  }

  private async handleRefundUpdated(refund: Stripe.Refund) {
    this.logger.log(`Refund updated: ${refund.id}, status: ${refund.status}`);

    const { metadata } = refund;

    // Send refund status update notification
    if (metadata?.companyId || metadata?.workerId) {
      // TODO: Send refund status update notification
      this.logger.log("Sending refund status update notification");
    }
  }

  private async handleDisputeCreated(dispute: Stripe.Dispute) {
    this.logger.log(`Dispute created: ${dispute.id}`);

    const charge = dispute.charge as Stripe.Charge;
    const { metadata } = charge;

    // Update payment status to disputed
    if (metadata?.escrowId) {
      this.logger.log(
        `Updating escrow ${metadata.escrowId} for disputed payment`
      );
      // TODO: Update escrow status to DISPUTED
      // await this.escrowService.updateEscrowStatus(metadata.escrowId, EscrowStatus.DISPUTED);
    }

    // Send dispute notification
    if (metadata?.companyId || metadata?.workerId) {
      // TODO: Send dispute notification
      this.logger.log("Sending dispute created notification");
    }
  }

  private async handleDisputeUpdated(dispute: Stripe.Dispute) {
    this.logger.log(
      `Dispute updated: ${dispute.id}, status: ${dispute.status}`
    );

    const charge = dispute.charge as Stripe.Charge;
    const { metadata } = charge;

    // Send dispute status update notification
    if (metadata?.companyId || metadata?.workerId) {
      // TODO: Send dispute status update notification
      this.logger.log("Sending dispute status update notification");
    }
  }
}
