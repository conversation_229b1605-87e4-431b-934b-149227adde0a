import { Injectable, Logger, BadRequestException } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { PaymentGateway } from '@shared/types';
import { IPaymentGateway, IPaymentGatewayFactory } from '../interfaces/payment-gateway.interface';
import { StripeGateway } from '../gateways/stripe.gateway';
import { RazorpayGateway } from '../gateways/razorpay.gateway';

@Injectable()
export class PaymentGatewayFactory implements IPaymentGatewayFactory {
  private readonly logger = new Logger(PaymentGatewayFactory.name);
  private readonly gateways = new Map<PaymentGateway, IPaymentGateway>();

  constructor(
    private configService: ConfigService,
    private stripeGateway: StripeGateway,
    private razorpayGateway: RazorpayGateway,
  ) {
    this.initializeGateways();
  }

  private async initializeGateways(): Promise<void> {
    try {
      // Initialize Stripe
      await this.stripeGateway.initialize();
      this.gateways.set(PaymentGateway.STRIPE, this.stripeGateway);

      // Initialize Razorpay
      await this.razorpayGateway.initialize();
      this.gateways.set(PaymentGateway.RAZORPAY, this.razorpayGateway);

      this.logger.log('Payment gateways initialized successfully');
    } catch (error) {
      this.logger.error('Failed to initialize payment gateways:', error);
    }
  }

  createGateway(gateway: PaymentGateway): IPaymentGateway {
    const gatewayInstance = this.gateways.get(gateway);
    
    if (!gatewayInstance) {
      throw new BadRequestException(`Payment gateway ${gateway} is not available`);
    }

    if (!gatewayInstance.isConfigured()) {
      throw new BadRequestException(`Payment gateway ${gateway} is not properly configured`);
    }

    return gatewayInstance;
  }

  getAvailableGateways(): PaymentGateway[] {
    const availableGateways: PaymentGateway[] = [];
    
    for (const [gateway, instance] of this.gateways.entries()) {
      if (instance.isConfigured()) {
        availableGateways.push(gateway);
      }
    }

    return availableGateways;
  }

  getDefaultGateway(currency: string): PaymentGateway {
    const upperCurrency = currency.toUpperCase();
    
    // Default gateway selection logic based on currency
    if (upperCurrency === 'INR') {
      // For Indian Rupee, prefer Razorpay
      if (this.isGatewayAvailable(PaymentGateway.RAZORPAY)) {
        return PaymentGateway.RAZORPAY;
      }
    }
    
    // For other currencies, prefer Stripe
    if (this.isGatewayAvailable(PaymentGateway.STRIPE)) {
      const stripeGateway = this.gateways.get(PaymentGateway.STRIPE);
      if (stripeGateway?.isCurrencySupported(upperCurrency)) {
        return PaymentGateway.STRIPE;
      }
    }

    // Fallback to Razorpay if Stripe doesn't support the currency
    if (this.isGatewayAvailable(PaymentGateway.RAZORPAY)) {
      const razorpayGateway = this.gateways.get(PaymentGateway.RAZORPAY);
      if (razorpayGateway?.isCurrencySupported(upperCurrency)) {
        return PaymentGateway.RAZORPAY;
      }
    }

    throw new BadRequestException(`No available payment gateway supports currency: ${currency}`);
  }

  getBestGateway(currency: string, amount: number, userLocation?: string): PaymentGateway {
    const upperCurrency = currency.toUpperCase();
    
    // Enhanced gateway selection logic
    
    // 1. Location-based selection
    if (userLocation) {
      const countryCode = this.extractCountryCode(userLocation);
      
      // For Indian users, strongly prefer Razorpay
      if (countryCode === 'IN' && upperCurrency === 'INR') {
        if (this.isGatewayAvailable(PaymentGateway.RAZORPAY)) {
          return PaymentGateway.RAZORPAY;
        }
      }
      
      // For other countries, prefer Stripe
      if (countryCode !== 'IN' && this.isGatewayAvailable(PaymentGateway.STRIPE)) {
        const stripeGateway = this.gateways.get(PaymentGateway.STRIPE);
        if (stripeGateway?.isCurrencySupported(upperCurrency)) {
          return PaymentGateway.STRIPE;
        }
      }
    }

    // 2. Currency-based selection
    if (upperCurrency === 'INR') {
      if (this.isGatewayAvailable(PaymentGateway.RAZORPAY)) {
        return PaymentGateway.RAZORPAY;
      }
    }

    // 3. Amount-based selection (for very large amounts, might prefer certain gateways)
    if (amount > 1000000) { // Large amounts (10,000 USD equivalent)
      // For large amounts, prefer the gateway with better enterprise features
      if (this.isGatewayAvailable(PaymentGateway.STRIPE)) {
        const stripeGateway = this.gateways.get(PaymentGateway.STRIPE);
        if (stripeGateway?.isCurrencySupported(upperCurrency)) {
          return PaymentGateway.STRIPE;
        }
      }
    }

    // 4. Fallback to default gateway selection
    return this.getDefaultGateway(currency);
  }

  private isGatewayAvailable(gateway: PaymentGateway): boolean {
    const gatewayInstance = this.gateways.get(gateway);
    return gatewayInstance?.isConfigured() || false;
  }

  private extractCountryCode(userLocation: string): string {
    // Simple country code extraction
    // In a real implementation, you might use a geolocation service
    // or parse more complex location data
    
    if (userLocation.includes('India') || userLocation.includes('IN')) {
      return 'IN';
    }
    
    if (userLocation.includes('United States') || userLocation.includes('US')) {
      return 'US';
    }
    
    if (userLocation.includes('United Kingdom') || userLocation.includes('UK') || userLocation.includes('GB')) {
      return 'GB';
    }
    
    // Add more country mappings as needed
    return 'UNKNOWN';
  }

  /**
   * Get gateway instance by type (for internal use)
   */
  getGatewayInstance(gateway: PaymentGateway): IPaymentGateway | undefined {
    return this.gateways.get(gateway);
  }

  /**
   * Check if any gateway is available
   */
  hasAvailableGateways(): boolean {
    return this.getAvailableGateways().length > 0;
  }

  /**
   * Get gateway configurations for all available gateways
   */
  getGatewayConfigurations() {
    const configs = [];
    
    for (const [gateway, instance] of this.gateways.entries()) {
      if (instance.isConfigured()) {
        configs.push(instance.getConfig());
      }
    }
    
    return configs;
  }

  /**
   * Get supported currencies across all gateways
   */
  getAllSupportedCurrencies(): string[] {
    const currencies = new Set<string>();
    
    for (const [gateway, instance] of this.gateways.entries()) {
      if (instance.isConfigured()) {
        instance.getSupportedCurrencies().forEach(currency => currencies.add(currency));
      }
    }
    
    return Array.from(currencies);
  }

  /**
   * Get supported payment methods across all gateways
   */
  getAllSupportedPaymentMethods(): string[] {
    const methods = new Set<string>();
    
    for (const [gateway, instance] of this.gateways.entries()) {
      if (instance.isConfigured()) {
        instance.getSupportedPaymentMethods().forEach(method => methods.add(method));
      }
    }
    
    return Array.from(methods);
  }

  /**
   * Get available payment methods for a specific currency
   */
  getAvailablePaymentMethodsForCurrency(currency: string): string[] {
    const methods = new Set<string>();
    const upperCurrency = currency.toUpperCase();
    
    for (const [gateway, instance] of this.gateways.entries()) {
      if (instance.isConfigured() && instance.isCurrencySupported(upperCurrency)) {
        instance.getSupportedPaymentMethods().forEach(method => methods.add(method));
      }
    }
    
    return Array.from(methods);
  }
}
