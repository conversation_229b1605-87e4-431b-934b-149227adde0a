import { Module } from "@nestjs/common";
import { TypeOrmModule } from "@nestjs/typeorm";
import { PaymentsService } from "./payments.service";
import { PaymentsController } from "./payments.controller";
import { EscrowController } from "./escrow.controller";
import { EscrowService } from "./escrow.service";
import { RazorpayWebhookController } from "./razorpay-webhook.controller";
import { StripeWebhookController } from "./stripe-webhook.controller";
import { EscrowAccount } from "./entities/escrow.entity";
import { ConfigModule } from "@nestjs/config";
import { NotificationsModule } from "../notifications/notifications.module";

// Gateway implementations
import { StripeGateway } from "./gateways/stripe.gateway";
import { RazorpayGateway } from "./gateways/razorpay.gateway";
import { PaymentGatewayFactory } from "./factories/payment-gateway.factory";

@Module({
  imports: [
    ConfigModule,
    TypeOrmModule.forFeature([EscrowAccount]),
    NotificationsModule,
  ],
  controllers: [
    PaymentsController,
    EscrowController,
    RazorpayWebhookController,
    StripeWebhookController,
  ],
  providers: [
    PaymentsService,
    EscrowService,
    StripeGateway,
    RazorpayGateway,
    PaymentGatewayFactory,
  ],
  exports: [
    PaymentsService,
    EscrowService,
    PaymentGatewayFactory,
    StripeGateway,
    RazorpayGateway,
  ],
})
export class PaymentsModule {}
