import { Test, TestingModule } from '@nestjs/testing';
import { ConfigService } from '@nestjs/config';
import { PaymentsService } from './payments.service';
import { PaymentMethod } from './enums/payment-method.enum';
import { PaymentStatus } from './enums/payment-status.enum';

// Mock Razorpay
const mockRazorpay = {
  orders: {
    create: jest.fn(),
    fetch: jest.fn(),
  },
  payments: {
    capture: jest.fn(),
    refund: jest.fn(),
  },
  utils: {
    generateSignature: jest.fn(),
  },
};

jest.mock('razorpay', () => {
  return jest.fn().mockImplementation(() => mockRazorpay);
});

describe('PaymentsService - Razorpay Integration', () => {
  let service: PaymentsService;
  let configService: ConfigService;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        PaymentsService,
        {
          provide: ConfigService,
          useValue: {
            get: jest.fn((key: string) => {
              switch (key) {
                case 'RAZORPAY_KEY_ID':
                  return 'rzp_test_key_id';
                case 'RAZORPAY_KEY_SECRET':
                  return 'test_key_secret';
                case 'RAZORPAY_WEBHOOK_SECRET':
                  return 'webhook_secret';
                default:
                  return null;
              }
            }),
          },
        },
      ],
    }).compile();

    service = module.get<PaymentsService>(PaymentsService);
    configService = module.get<ConfigService>(ConfigService);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('createRazorpayOrder', () => {
    it('should create a Razorpay order successfully', async () => {
      const mockOrder = {
        id: 'order_test123',
        amount: 100000,
        currency: 'INR',
        status: 'created',
      };

      mockRazorpay.orders.create.mockResolvedValue(mockOrder);

      const result = await service.createRazorpayOrder({
        amount: 100000,
        currency: 'INR',
        description: 'Test order',
        metadata: { jobId: 'job123' },
      });

      expect(result.success).toBe(true);
      expect(result.transactionId).toBe('order_test123');
      expect(result.status).toBe(PaymentStatus.PENDING);
      expect(result.paymentMethod).toBe(PaymentMethod.RAZORPAY);
      expect(mockRazorpay.orders.create).toHaveBeenCalledWith({
        amount: 100000,
        currency: 'INR',
        receipt: expect.stringContaining('order_'),
        notes: { jobId: 'job123' },
      });
    });

    it('should handle order creation failure', async () => {
      mockRazorpay.orders.create.mockRejectedValue(new Error('Order creation failed'));

      const result = await service.createRazorpayOrder({
        amount: 100000,
        currency: 'INR',
        description: 'Test order',
      });

      expect(result.success).toBe(false);
      expect(result.status).toBe(PaymentStatus.FAILED);
      expect(result.message).toBe('Order creation failed');
    });
  });

  describe('processRazorpayPayment', () => {
    it('should process payment with existing order', async () => {
      const mockOrder = {
        id: 'order_test123',
        amount: 100000,
        currency: 'INR',
        status: 'created',
      };

      mockRazorpay.orders.fetch.mockResolvedValue(mockOrder);

      const result = await service.processRazorpayPayment({
        amount: 100000,
        currency: 'INR',
        description: 'Test payment',
        orderId: 'order_test123',
      });

      expect(result.success).toBe(true);
      expect(result.transactionId).toBe('order_test123');
      expect(mockRazorpay.orders.fetch).toHaveBeenCalledWith('order_test123');
    });

    it('should create new order if orderId not provided', async () => {
      const mockOrder = {
        id: 'order_new123',
        amount: 100000,
        currency: 'INR',
        status: 'created',
      };

      mockRazorpay.orders.create.mockResolvedValue(mockOrder);

      const result = await service.processRazorpayPayment({
        amount: 100000,
        currency: 'INR',
        description: 'Test payment',
      });

      expect(result.success).toBe(true);
      expect(result.transactionId).toBe('order_new123');
      expect(mockRazorpay.orders.create).toHaveBeenCalled();
    });
  });

  describe('captureRazorpayPayment', () => {
    it('should capture payment successfully', async () => {
      const mockPayment = {
        id: 'pay_test123',
        amount: 100000,
        status: 'captured',
      };

      mockRazorpay.payments.capture.mockResolvedValue(mockPayment);

      const result = await service.captureRazorpayPayment('pay_test123', 100000);

      expect(result.success).toBe(true);
      expect(result.transactionId).toBe('pay_test123');
      expect(result.status).toBe(PaymentStatus.COMPLETED);
      expect(mockRazorpay.payments.capture).toHaveBeenCalledWith('pay_test123', 100000);
    });

    it('should handle capture failure', async () => {
      mockRazorpay.payments.capture.mockRejectedValue(new Error('Capture failed'));

      const result = await service.captureRazorpayPayment('pay_test123', 100000);

      expect(result.success).toBe(false);
      expect(result.status).toBe(PaymentStatus.FAILED);
      expect(result.message).toBe('Capture failed');
    });
  });

  describe('refundRazorpayPayment', () => {
    it('should process refund successfully', async () => {
      const mockRefund = {
        id: 'rfnd_test123',
        amount: 50000,
        status: 'processed',
      };

      mockRazorpay.payments.refund.mockResolvedValue(mockRefund);

      const result = await service.refundRazorpayPayment('pay_test123', 50000, {
        reason: 'Customer request',
      });

      expect(result.success).toBe(true);
      expect(result.transactionId).toBe('rfnd_test123');
      expect(result.status).toBe(PaymentStatus.REFUNDED);
      expect(mockRazorpay.payments.refund).toHaveBeenCalledWith('pay_test123', {
        amount: 50000,
        notes: { reason: 'Customer request' },
      });
    });

    it('should process full refund when amount not specified', async () => {
      const mockRefund = {
        id: 'rfnd_test123',
        status: 'processed',
      };

      mockRazorpay.payments.refund.mockResolvedValue(mockRefund);

      const result = await service.refundRazorpayPayment('pay_test123');

      expect(result.success).toBe(true);
      expect(mockRazorpay.payments.refund).toHaveBeenCalledWith('pay_test123', {
        notes: {},
      });
    });
  });

  describe('verifyRazorpaySignature', () => {
    it('should verify signature successfully', () => {
      const orderId = 'order_test123';
      const paymentId = 'pay_test123';
      const signature = 'test_signature';
      const expectedSignature = 'test_signature';

      mockRazorpay.utils.generateSignature.mockReturnValue(expectedSignature);

      const result = service.verifyRazorpaySignature(orderId, paymentId, signature);

      expect(result).toBe(true);
      expect(mockRazorpay.utils.generateSignature).toHaveBeenCalledWith(
        `${orderId}|${paymentId}`,
        'webhook_secret'
      );
    });

    it('should return false for invalid signature', () => {
      const orderId = 'order_test123';
      const paymentId = 'pay_test123';
      const signature = 'invalid_signature';
      const expectedSignature = 'valid_signature';

      mockRazorpay.utils.generateSignature.mockReturnValue(expectedSignature);

      const result = service.verifyRazorpaySignature(orderId, paymentId, signature);

      expect(result).toBe(false);
    });

    it('should handle verification error', () => {
      mockRazorpay.utils.generateSignature.mockImplementation(() => {
        throw new Error('Verification error');
      });

      const result = service.verifyRazorpaySignature('order_123', 'pay_123', 'sig_123');

      expect(result).toBe(false);
    });
  });

  describe('mapRazorpayStatusToPaymentStatus', () => {
    it('should map Razorpay statuses correctly', () => {
      // Access private method for testing
      const mapStatus = (service as any).mapRazorpayStatusToPaymentStatus.bind(service);

      expect(mapStatus('captured')).toBe(PaymentStatus.COMPLETED);
      expect(mapStatus('paid')).toBe(PaymentStatus.COMPLETED);
      expect(mapStatus('authorized')).toBe(PaymentStatus.PROCESSING);
      expect(mapStatus('created')).toBe(PaymentStatus.PENDING);
      expect(mapStatus('attempted')).toBe(PaymentStatus.PENDING);
      expect(mapStatus('failed')).toBe(PaymentStatus.FAILED);
      expect(mapStatus('refunded')).toBe(PaymentStatus.REFUNDED);
      expect(mapStatus('unknown')).toBe(PaymentStatus.PENDING);
    });
  });

  describe('Configuration', () => {
    it('should handle missing Razorpay configuration', async () => {
      // Create service with missing config
      const moduleWithoutConfig = await Test.createTestingModule({
        providers: [
          PaymentsService,
          {
            provide: ConfigService,
            useValue: {
              get: jest.fn().mockReturnValue(null),
            },
          },
        ],
      }).compile();

      const serviceWithoutConfig = moduleWithoutConfig.get<PaymentsService>(PaymentsService);

      const result = await serviceWithoutConfig.createRazorpayOrder({
        amount: 100000,
        currency: 'INR',
        description: 'Test order',
      });

      expect(result.success).toBe(false);
      expect(result.message).toBe('Razorpay is not configured');
    });
  });
});
