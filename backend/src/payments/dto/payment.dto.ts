import { ApiProperty, ApiPropertyOptional } from "@nestjs/swagger";
import {
  IsNotEmpty,
  IsNumber,
  IsString,
  IsOptional,
  IsObject,
  IsEnum,
} from "class-validator";
import { PaymentMethod } from "../enums/payment-method.enum";
import { PaymentStatus } from "../enums/payment-status.enum";
import { PaymentGateway } from "@shared/types";

export class StripePaymentDto {
  @ApiProperty({
    description: "Amount to charge in smallest currency unit (e.g., cents)",
    example: 2000,
  })
  @IsNumber()
  @IsNotEmpty()
  amount: number;

  @ApiProperty({
    description: "Currency code (ISO 4217)",
    example: "USD",
  })
  @IsString()
  @IsNotEmpty()
  currency: string;

  @ApiPropertyOptional({
    description: "Description of the payment",
    example: "Payment for Job #12345",
  })
  @IsString()
  @IsOptional()
  description?: string;

  @ApiPropertyOptional({
    description: "Stripe payment method ID",
    example: "pm_1234567890",
  })
  @IsString()
  @IsOptional()
  paymentMethodId?: string;

  @ApiPropertyOptional({
    description: "Stripe customer ID",
    example: "cus_1234567890",
  })
  @IsString()
  @IsOptional()
  customerId?: string;

  @ApiPropertyOptional({
    description: "Additional metadata for the payment",
    example: { jobId: "job_12345", userId: "user_12345" },
  })
  @IsObject()
  @IsOptional()
  metadata?: Record<string, any>;
}

export class UpiPaymentDto {
  @ApiProperty({
    description: "Amount to charge in smallest currency unit (e.g., paise)",
    example: 100000,
  })
  @IsNumber()
  @IsNotEmpty()
  amount: number;

  @ApiProperty({
    description: "UPI Virtual Payment Address",
    example: "user@upi",
  })
  @IsString()
  @IsNotEmpty()
  vpa: string;

  @ApiPropertyOptional({
    description: "Description of the payment",
    example: "Payment for Job #12345",
  })
  @IsString()
  @IsOptional()
  description?: string;

  @ApiPropertyOptional({
    description: "Reference ID for the payment",
    example: "payment-1234567890",
  })
  @IsString()
  @IsOptional()
  reference?: string;

  @ApiPropertyOptional({
    description: "Additional metadata for the payment",
    example: { jobId: "job_12345", userId: "user_12345" },
  })
  @IsObject()
  @IsOptional()
  metadata?: Record<string, any>;
}

export class RazorpayPaymentDto {
  @ApiProperty({
    description: "Amount to charge in smallest currency unit (e.g., paise)",
    example: 100000,
  })
  @IsNumber()
  @IsNotEmpty()
  amount: number;

  @ApiProperty({
    description: "Currency code (ISO 4217)",
    example: "INR",
  })
  @IsString()
  @IsNotEmpty()
  currency: string;

  @ApiPropertyOptional({
    description: "Description of the payment",
    example: "Payment for Job #12345",
  })
  @IsString()
  @IsOptional()
  description?: string;

  @ApiPropertyOptional({
    description: "Customer email address",
    example: "<EMAIL>",
  })
  @IsString()
  @IsOptional()
  customerEmail?: string;

  @ApiPropertyOptional({
    description: "Customer phone number",
    example: "+919876543210",
  })
  @IsString()
  @IsOptional()
  customerPhone?: string;

  @ApiPropertyOptional({
    description: "Additional metadata for the payment",
    example: { jobId: "job_12345", userId: "user_12345" },
  })
  @IsObject()
  @IsOptional()
  metadata?: Record<string, any>;

  @ApiPropertyOptional({
    description: "Razorpay payment ID for capture/refund operations",
    example: "pay_1234567890",
  })
  @IsString()
  @IsOptional()
  paymentId?: string;

  @ApiPropertyOptional({
    description: "Razorpay order ID",
    example: "order_1234567890",
  })
  @IsString()
  @IsOptional()
  orderId?: string;
}

export class PaymentResultDto {
  @ApiProperty({
    description: "Whether the payment was successful",
    example: true,
  })
  success: boolean;

  @ApiPropertyOptional({
    description: "Transaction ID from the payment provider",
    example: "txn_1234567890",
  })
  transactionId?: string;

  @ApiProperty({
    description: "Status of the payment",
    enum: PaymentStatus,
    example: PaymentStatus.COMPLETED,
  })
  status: PaymentStatus;

  @ApiPropertyOptional({
    description: "Message about the payment result",
    example: "Payment processed successfully",
  })
  message?: string;

  @ApiProperty({
    description: "Payment method used",
    enum: PaymentMethod,
    example: PaymentMethod.STRIPE,
  })
  paymentMethod: PaymentMethod;

  @ApiPropertyOptional({
    description: "Raw response from the payment gateway",
    example: { id: "ch_1234567890", status: "succeeded" },
  })
  gatewayResponse?: Record<string, unknown>;
}

// New unified payment DTOs
export class UnifiedPaymentDto {
  @ApiProperty({
    description: "Amount to charge in smallest currency unit",
    example: 2000,
  })
  @IsNumber()
  @IsNotEmpty()
  amount: number;

  @ApiProperty({
    description: "Currency code (ISO 4217)",
    example: "USD",
  })
  @IsString()
  @IsNotEmpty()
  currency: string;

  @ApiPropertyOptional({
    description: "Description of the payment",
    example: "Payment for Job #12345",
  })
  @IsString()
  @IsOptional()
  description?: string;

  @ApiPropertyOptional({
    description: "Preferred payment gateway",
    enum: PaymentGateway,
    example: PaymentGateway.STRIPE,
  })
  @IsEnum(PaymentGateway)
  @IsOptional()
  preferredGateway?: PaymentGateway;

  @ApiPropertyOptional({
    description: "Additional metadata for the payment",
    example: { jobId: "job_12345", userId: "user_12345" },
  })
  @IsObject()
  @IsOptional()
  metadata?: Record<string, unknown>;

  // Stripe-specific fields
  @ApiPropertyOptional({
    description: "Stripe payment method ID (required for Stripe)",
    example: "pm_1234567890",
  })
  @IsString()
  @IsOptional()
  paymentMethodId?: string;

  @ApiPropertyOptional({
    description: "Stripe customer ID",
    example: "cus_1234567890",
  })
  @IsString()
  @IsOptional()
  customerId?: string;

  // Razorpay-specific fields
  @ApiPropertyOptional({
    description: "Customer email address (for Razorpay)",
    example: "<EMAIL>",
  })
  @IsString()
  @IsOptional()
  customerEmail?: string;

  @ApiPropertyOptional({
    description: "Customer phone number (for Razorpay)",
    example: "+919876543210",
  })
  @IsString()
  @IsOptional()
  customerPhone?: string;

  @ApiPropertyOptional({
    description: "Razorpay order ID",
    example: "order_1234567890",
  })
  @IsString()
  @IsOptional()
  orderId?: string;
}

export class RefundDto {
  @ApiProperty({
    description: "Transaction ID to refund",
    example: "txn_1234567890",
  })
  @IsString()
  @IsNotEmpty()
  transactionId: string;

  @ApiPropertyOptional({
    description: "Amount to refund (partial refund if specified)",
    example: 1000,
  })
  @IsNumber()
  @IsOptional()
  amount?: number;

  @ApiPropertyOptional({
    description: "Reason for the refund",
    example: "Customer requested refund",
  })
  @IsString()
  @IsOptional()
  reason?: string;

  @ApiPropertyOptional({
    description: "Additional metadata for the refund",
    example: { disputeId: "dispute_12345" },
  })
  @IsObject()
  @IsOptional()
  metadata?: Record<string, unknown>;
}

export class PaymentGatewayConfigDto {
  @ApiProperty({
    description: "Payment gateway type",
    enum: PaymentGateway,
    example: PaymentGateway.STRIPE,
  })
  gateway: PaymentGateway;

  @ApiProperty({
    description: "Whether the gateway is enabled",
    example: true,
  })
  enabled: boolean;

  @ApiProperty({
    description: "Supported currencies",
    example: ["USD", "EUR", "GBP"],
  })
  supportedCurrencies: string[];

  @ApiProperty({
    description: "Supported payment methods",
    example: ["stripe", "card"],
  })
  supportedMethods: string[];

  @ApiProperty({
    description: "Default currency for this gateway",
    example: "USD",
  })
  defaultCurrency: string;
}
