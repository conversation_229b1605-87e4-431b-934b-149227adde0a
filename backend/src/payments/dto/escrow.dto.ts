import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { IsUUID, IsNumber, IsString, IsOptional, IsEnum, Min } from 'class-validator';
import { PaymentMethod } from '@shared/types';

export class CreateEscrowDto {
  @ApiProperty({
    description: 'ID of the job for which escrow is created',
    example: '123e4567-e89b-12d3-a456-************',
  })
  @IsUUID()
  jobId: string;

  @ApiProperty({
    description: 'ID of the company creating the escrow',
    example: '123e4567-e89b-12d3-a456-************',
  })
  @IsUUID()
  companyId: string;

  @ApiProperty({
    description: 'Amount to be held in escrow',
    example: 5000,
  })
  @IsNumber()
  @Min(1)
  amount: number;

  @ApiPropertyOptional({
    description: 'Currency code',
    example: 'INR',
    default: 'INR',
  })
  @IsString()
  @IsOptional()
  currency?: string;

  @ApiPropertyOptional({
    description: 'Description of the escrow',
    example: 'Payment for web development project',
  })
  @IsString()
  @IsOptional()
  description?: string;

  @ApiPropertyOptional({
    description: 'Additional metadata',
    example: { projectType: 'web_development', deadline: '2024-01-15' },
  })
  @IsOptional()
  metadata?: Record<string, any>;
}

export class FundEscrowDto {
  @ApiProperty({
    description: 'Payment method to use for funding',
    enum: PaymentMethod,
    example: PaymentMethod.STRIPE,
  })
  @IsEnum(PaymentMethod)
  paymentMethod: PaymentMethod;

  @ApiPropertyOptional({
    description: 'Stripe payment method ID (required for Stripe payments)',
    example: 'pm_1234567890',
  })
  @IsString()
  @IsOptional()
  paymentMethodId?: string;

  @ApiPropertyOptional({
    description: 'Stripe customer ID (optional for Stripe payments)',
    example: 'cus_1234567890',
  })
  @IsString()
  @IsOptional()
  customerId?: string;

  @ApiPropertyOptional({
    description: 'UPI Virtual Payment Address (required for UPI payments)',
    example: 'user@paytm',
  })
  @IsString()
  @IsOptional()
  vpa?: string;
}

export class ReleaseEscrowDto {
  @ApiProperty({
    description: 'ID of the worker to receive the funds',
    example: '123e4567-e89b-12d3-a456-************',
  })
  @IsUUID()
  workerId: string;

  @ApiProperty({
    description: 'ID of the user releasing the funds',
    example: '123e4567-e89b-12d3-a456-************',
  })
  @IsUUID()
  releasedById: string;

  @ApiPropertyOptional({
    description: 'Reason for releasing the funds',
    example: 'Job completed successfully',
  })
  @IsString()
  @IsOptional()
  reason?: string;
}

export class RefundEscrowDto {
  @ApiProperty({
    description: 'ID of the user initiating the refund',
    example: '123e4567-e89b-12d3-a456-************',
  })
  @IsUUID()
  refundedById: string;

  @ApiProperty({
    description: 'Reason for the refund',
    example: 'Job cancelled by company',
  })
  @IsString()
  reason: string;
}

export class EscrowResponseDto {
  @ApiProperty({
    description: 'Escrow account ID',
    example: '123e4567-e89b-12d3-a456-************',
  })
  id: string;

  @ApiProperty({
    description: 'Job ID',
    example: '123e4567-e89b-12d3-a456-************',
  })
  jobId: string;

  @ApiProperty({
    description: 'Company ID',
    example: '123e4567-e89b-12d3-a456-************',
  })
  companyId: string;

  @ApiProperty({
    description: 'Escrow amount',
    example: 5000,
  })
  amount: number;

  @ApiProperty({
    description: 'Currency',
    example: 'INR',
  })
  currency: string;

  @ApiProperty({
    description: 'Escrow status',
    example: 'held',
  })
  status: string;

  @ApiPropertyOptional({
    description: 'Worker ID (if funds are released)',
    example: '123e4567-e89b-12d3-a456-************',
  })
  workerId?: string;

  @ApiPropertyOptional({
    description: 'Transaction ID from payment gateway',
    example: 'txn_1234567890',
  })
  transactionId?: string;

  @ApiPropertyOptional({
    description: 'Date when funds were released',
    example: '2024-01-15T10:30:00Z',
  })
  releasedAt?: string;

  @ApiPropertyOptional({
    description: 'Date when funds were refunded',
    example: '2024-01-15T10:30:00Z',
  })
  refundedAt?: string;

  @ApiProperty({
    description: 'Creation date',
    example: '2024-01-10T10:30:00Z',
  })
  createdAt: string;

  @ApiProperty({
    description: 'Last update date',
    example: '2024-01-15T10:30:00Z',
  })
  updatedAt: string;
}

export class EscrowStatsDto {
  @ApiProperty({
    description: 'Total number of escrow accounts',
    example: 25,
  })
  totalEscrows: number;

  @ApiProperty({
    description: 'Total amount across all escrows',
    example: 125000,
  })
  totalAmount: number;

  @ApiProperty({
    description: 'Amount currently held in escrow',
    example: 50000,
  })
  heldAmount: number;

  @ApiProperty({
    description: 'Amount released to workers',
    example: 60000,
  })
  releasedAmount: number;

  @ApiProperty({
    description: 'Amount refunded to company',
    example: 15000,
  })
  refundedAmount: number;

  @ApiProperty({
    description: 'Number of pending escrows',
    example: 3,
  })
  pendingCount: number;

  @ApiProperty({
    description: 'Number of held escrows',
    example: 8,
  })
  heldCount: number;

  @ApiProperty({
    description: 'Number of released escrows',
    example: 12,
  })
  releasedCount: number;

  @ApiProperty({
    description: 'Number of refunded escrows',
    example: 2,
  })
  refundedCount: number;
}
