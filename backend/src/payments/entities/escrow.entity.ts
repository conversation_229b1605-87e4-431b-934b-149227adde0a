import {
  <PERSON><PERSON><PERSON>,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  ManyToOne,
  JoinColumn,
  Index,
} from "typeorm";
import { Job } from "../../jobs/entities/job.entity";
import { Company } from "../../companies/entities/company.entity";
import { User } from "../../users/entities/user.entity";
import { EscrowStatus } from "@shared/types";

@Entity("escrow_accounts")
export class EscrowAccount {
  @PrimaryGeneratedColumn("uuid")
  id!: string;

  @Column()
  @Index()
  jobId!: string;

  @ManyToOne(() => Job)
  @JoinColumn({ name: "jobId" })
  job!: Job;

  @Column()
  @Index()
  companyId!: string;

  @ManyToOne(() => Company)
  @JoinColumn({ name: "companyId" })
  company!: Company;

  @Column({ type: "decimal", precision: 10, scale: 2 })
  amount!: number;

  @Column({ length: 3, default: "INR" })
  currency!: string;

  @Column({
    type: "enum",
    enum: EscrowStatus,
    default: EscrowStatus.PENDING,
  })
  @Index()
  status!: EscrowStatus;

  @Column({ nullable: true })
  paymentIntentId?: string; // Stripe payment intent ID

  @Column({ nullable: true })
  transactionId?: string; // Payment gateway transaction ID

  @Column({ type: "text", nullable: true })
  description?: string;

  @Column({ type: "json", nullable: true })
  metadata?: Record<string, any>;

  @Column({ nullable: true })
  workerId?: string; // Worker who will receive the funds

  @ManyToOne(() => User, { nullable: true })
  @JoinColumn({ name: "workerId" })
  worker?: User;

  @Column({ type: "timestamp", nullable: true })
  releasedAt?: Date;

  @Column({ nullable: true })
  releasedById?: string; // User who released the funds

  @ManyToOne(() => User, { nullable: true })
  @JoinColumn({ name: "releasedById" })
  releasedBy?: User;

  @Column({ type: "timestamp", nullable: true })
  refundedAt?: Date;

  @Column({ nullable: true })
  refundedById?: string; // User who initiated the refund

  @ManyToOne(() => User, { nullable: true })
  @JoinColumn({ name: "refundedById" })
  refundedBy?: User;

  @Column({ type: "text", nullable: true })
  refundReason?: string;

  @Column({ type: "text", nullable: true })
  releaseReason?: string;

  @CreateDateColumn()
  createdAt!: Date;

  @UpdateDateColumn()
  updatedAt!: Date;
}
