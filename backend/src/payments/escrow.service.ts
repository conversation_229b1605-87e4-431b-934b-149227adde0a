import {
  Injectable,
  NotFoundException,
  BadRequestException,
  Logger,
  Inject,
} from "@nestjs/common";
import { InjectRepository } from "@nestjs/typeorm";
import { Repository } from "typeorm";
import { EscrowAccount } from "./entities/escrow.entity";
import { PaymentsService } from "./payments.service";
import { NotificationsService } from "../notifications/notifications.service";
import { EscrowStatus, PaymentMethod } from "@shared/types/payment";
import {
  CreateEscrowDto,
  ReleaseEscrowDto,
  RefundEscrowDto,
} from "./dto/escrow.dto";

@Injectable()
export class EscrowService {
  private readonly logger = new Logger(EscrowService.name);

  constructor(
    @InjectRepository(EscrowAccount)
    private readonly escrowRepository: Repository<EscrowAccount>,
    @Inject(PaymentsService)
    private readonly paymentsService: PaymentsService,
    @Inject(NotificationsService)
    private readonly notificationsService: NotificationsService
  ) {}

  /**
   * Create an escrow account for a job
   */
  async createEscrow(createEscrowDto: CreateEscrowDto): Promise<EscrowAccount> {
    const {
      jobId,
      companyId,
      amount,
      currency = "INR",
      description,
      metadata,
    } = createEscrowDto;

    // Check if escrow already exists for this job
    const existingEscrow = await this.escrowRepository.findOne({
      where: { jobId, companyId },
    });

    if (existingEscrow) {
      throw new BadRequestException(
        "Escrow account already exists for this job"
      );
    }

    // Create escrow account
    const escrow = this.escrowRepository.create({
      jobId,
      companyId,
      amount,
      currency,
      description,
      metadata,
      status: EscrowStatus.PENDING,
    });

    const savedEscrow = await this.escrowRepository.save(escrow);

    this.logger.log(
      `Created escrow account ${savedEscrow.id} for job ${jobId}`
    );

    return savedEscrow;
  }

  /**
   * Fund an escrow account using payment gateway
   */
  async fundEscrow(
    escrowId: string,
    paymentMethod: PaymentMethod,
    paymentData: any
  ): Promise<EscrowAccount> {
    const escrow = await this.findById(escrowId);

    if (escrow.status !== EscrowStatus.PENDING) {
      throw new BadRequestException("Escrow account is not in pending status");
    }

    let paymentResult;

    try {
      // Process payment based on method
      if (paymentMethod === PaymentMethod.STRIPE) {
        paymentResult = await this.paymentsService.processStripePayment({
          amount: Number(escrow.amount),
          currency: escrow.currency,
          description: `Escrow funding for job: ${
            escrow.job?.title || escrow.jobId
          }`,
          paymentMethodId: paymentData.paymentMethodId,
          customerId: paymentData.customerId,
          metadata: {
            escrowId: escrow.id,
            jobId: escrow.jobId,
            companyId: escrow.companyId,
            type: "escrow_funding",
          },
        });
      } else if (paymentMethod === PaymentMethod.UPI) {
        paymentResult = await this.paymentsService.processUpiPayment({
          amount: Number(escrow.amount),
          vpa: paymentData.vpa,
          description: `Escrow funding for job: ${
            escrow.job?.title || escrow.jobId
          }`,
          reference: `escrow-${escrow.id}`,
          metadata: {
            escrowId: escrow.id,
            jobId: escrow.jobId,
            companyId: escrow.companyId,
            type: "escrow_funding",
          },
        });
      } else if (paymentMethod === PaymentMethod.RAZORPAY) {
        paymentResult = await this.paymentsService.processRazorpayPayment({
          amount: Number(escrow.amount),
          currency: escrow.currency,
          description: `Escrow funding for job: ${
            escrow.job?.title || escrow.jobId
          }`,
          customerEmail: paymentData.customerEmail,
          customerPhone: paymentData.customerPhone,
          metadata: {
            escrowId: escrow.id,
            jobId: escrow.jobId,
            companyId: escrow.companyId,
            type: "escrow_funding",
          },
          orderId: paymentData.orderId,
        });
      } else {
        throw new BadRequestException(
          `Payment method ${paymentMethod} not supported for escrow`
        );
      }

      // Update escrow with payment result
      escrow.transactionId = paymentResult.transactionId;
      escrow.paymentIntentId = paymentResult.gatewayResponse?.id;

      if (paymentResult.success) {
        escrow.status = EscrowStatus.HELD;
        this.logger.log(`Escrow ${escrowId} successfully funded`);

        // Send notification to company
        await this.notificationsService.create({
          userId: escrow.companyId,
          title: "Escrow Funded",
          message: `Escrow account for job has been successfully funded with ${escrow.currency} ${escrow.amount}`,
          type: "payment",
          metadata: { escrowId: escrow.id, jobId: escrow.jobId },
          link: `/jobs/${escrow.jobId}`,
        });
      } else {
        escrow.status = EscrowStatus.PENDING;
        this.logger.error(
          `Failed to fund escrow ${escrowId}: ${paymentResult.message}`
        );
      }

      return await this.escrowRepository.save(escrow);
    } catch (error) {
      this.logger.error(`Error funding escrow ${escrowId}:`, error);
      throw new BadRequestException(`Failed to fund escrow: ${error.message}`);
    }
  }

  /**
   * Release escrow funds to worker
   */
  async releaseEscrow(
    escrowId: string,
    releaseData: ReleaseEscrowDto
  ): Promise<EscrowAccount> {
    const escrow = await this.findById(escrowId);

    if (escrow.status !== EscrowStatus.HELD) {
      throw new BadRequestException(
        "Escrow funds are not held and cannot be released"
      );
    }

    if (!releaseData.workerId) {
      throw new BadRequestException(
        "Worker ID is required to release escrow funds"
      );
    }

    try {
      // Update escrow status
      escrow.status = EscrowStatus.RELEASED;
      escrow.workerId = releaseData.workerId;
      escrow.releasedAt = new Date();
      escrow.releasedById = releaseData.releasedById;
      escrow.releaseReason = releaseData.reason;

      const updatedEscrow = await this.escrowRepository.save(escrow);

      this.logger.log(
        `Released escrow ${escrowId} to worker ${releaseData.workerId}`
      );

      // Send notifications
      await Promise.all([
        // Notify company
        this.notificationsService.create({
          userId: escrow.companyId,
          title: "Escrow Released",
          message: `Escrow funds have been released to the worker for job completion`,
          type: "payment",
          metadata: {
            escrowId: escrow.id,
            jobId: escrow.jobId,
            workerId: releaseData.workerId,
          },
          link: `/jobs/${escrow.jobId}`,
        }),
        // Notify worker
        this.notificationsService.create({
          userId: releaseData.workerId,
          title: "Payment Released",
          message: `You have received payment of ${escrow.currency} ${escrow.amount} for job completion`,
          type: "payment",
          metadata: { escrowId: escrow.id, jobId: escrow.jobId },
          link: `/jobs/${escrow.jobId}`,
        }),
      ]);

      return updatedEscrow;
    } catch (error) {
      this.logger.error(`Error releasing escrow ${escrowId}:`, error);
      throw new BadRequestException(
        `Failed to release escrow: ${error.message}`
      );
    }
  }

  /**
   * Refund escrow funds to company
   */
  async refundEscrow(
    escrowId: string,
    refundData: RefundEscrowDto
  ): Promise<EscrowAccount> {
    const escrow = await this.findById(escrowId);

    if (escrow.status !== EscrowStatus.HELD) {
      throw new BadRequestException(
        "Escrow funds are not held and cannot be refunded"
      );
    }

    try {
      // Process refund through payment gateway
      // Note: This is a simplified implementation
      // In production, you would need to implement actual refund logic with Stripe/UPI

      escrow.status = EscrowStatus.REFUNDED;
      escrow.refundedAt = new Date();
      escrow.refundedById = refundData.refundedById;
      escrow.refundReason = refundData.reason;

      const updatedEscrow = await this.escrowRepository.save(escrow);

      this.logger.log(
        `Refunded escrow ${escrowId} to company ${escrow.companyId}`
      );

      // Send notification to company
      await this.notificationsService.create({
        userId: escrow.companyId,
        title: "Escrow Refunded",
        message: `Escrow funds of ${escrow.currency} ${escrow.amount} have been refunded`,
        type: "payment",
        metadata: { escrowId: escrow.id, jobId: escrow.jobId },
        link: `/jobs/${escrow.jobId}`,
      });

      return updatedEscrow;
    } catch (error) {
      this.logger.error(`Error refunding escrow ${escrowId}:`, error);
      throw new BadRequestException(
        `Failed to refund escrow: ${error.message}`
      );
    }
  }

  /**
   * Find escrow by ID
   */
  async findById(escrowId: string): Promise<EscrowAccount> {
    const escrow = await this.escrowRepository.findOne({
      where: { id: escrowId },
      relations: ["job", "company", "worker", "releasedBy", "refundedBy"],
    });

    if (!escrow) {
      throw new NotFoundException(
        `Escrow account with ID ${escrowId} not found`
      );
    }

    return escrow;
  }

  /**
   * Find escrow by job ID
   */
  async findByJobId(jobId: string): Promise<EscrowAccount | null> {
    return await this.escrowRepository.findOne({
      where: { jobId },
      relations: ["job", "company", "worker", "releasedBy", "refundedBy"],
    });
  }

  /**
   * Get all escrow accounts for a company
   */
  async findByCompanyId(companyId: string): Promise<EscrowAccount[]> {
    return await this.escrowRepository.find({
      where: { companyId },
      relations: ["job", "worker"],
      order: { createdAt: "DESC" },
    });
  }

  /**
   * Get escrow statistics for a company
   */
  async getEscrowStats(companyId: string) {
    const escrows = await this.findByCompanyId(companyId);

    const stats = {
      totalEscrows: escrows.length,
      totalAmount: 0,
      heldAmount: 0,
      releasedAmount: 0,
      refundedAmount: 0,
      pendingCount: 0,
      heldCount: 0,
      releasedCount: 0,
      refundedCount: 0,
    };

    escrows.forEach((escrow) => {
      const amount = Number(escrow.amount);
      stats.totalAmount += amount;

      switch (escrow.status) {
        case EscrowStatus.PENDING:
          stats.pendingCount++;
          break;
        case EscrowStatus.HELD:
          stats.heldCount++;
          stats.heldAmount += amount;
          break;
        case EscrowStatus.RELEASED:
          stats.releasedCount++;
          stats.releasedAmount += amount;
          break;
        case EscrowStatus.REFUNDED:
          stats.refundedCount++;
          stats.refundedAmount += amount;
          break;
      }
    });

    return stats;
  }
}
