import {
  PaymentGateway,
  PaymentResult,
  RefundResult,
  StripePaymentParams,
  RazorpayPaymentParams,
  UpiPaymentParams,
  RefundParams,
  PaymentGatewayConfig,
} from '@shared/types';

/**
 * Base payment gateway interface that all payment gateways must implement
 */
export interface IPaymentGateway {
  /**
   * Get gateway configuration
   */
  getConfig(): PaymentGatewayConfig;

  /**
   * Initialize the gateway with configuration
   */
  initialize(): Promise<void>;

  /**
   * Check if the gateway is properly configured and ready to use
   */
  isConfigured(): boolean;

  /**
   * Create a payment intent/order
   */
  createPaymentIntent(params: StripePaymentParams | RazorpayPaymentParams): Promise<PaymentResult>;

  /**
   * Process a payment
   */
  processPayment(params: StripePaymentParams | RazorpayPaymentParams): Promise<PaymentResult>;

  /**
   * Capture a payment (for manual capture)
   */
  capturePayment(paymentId: string, amount?: number): Promise<PaymentResult>;

  /**
   * Refund a payment
   */
  refundPayment(params: RefundParams): Promise<RefundResult>;

  /**
   * Verify webhook signature
   */
  verifyWebhookSignature(payload: string, signature: string, secret: string): boolean;

  /**
   * Get payment details by transaction ID
   */
  getPaymentDetails(transactionId: string): Promise<PaymentResult>;

  /**
   * Get supported currencies for this gateway
   */
  getSupportedCurrencies(): string[];

  /**
   * Get supported payment methods for this gateway
   */
  getSupportedPaymentMethods(): string[];

  /**
   * Check if a currency is supported
   */
  isCurrencySupported(currency: string): boolean;

  /**
   * Check if a payment method is supported
   */
  isPaymentMethodSupported(method: string): boolean;
}

/**
 * Payment gateway factory interface
 */
export interface IPaymentGatewayFactory {
  /**
   * Create a payment gateway instance
   */
  createGateway(gateway: PaymentGateway): IPaymentGateway;

  /**
   * Get all available gateways
   */
  getAvailableGateways(): PaymentGateway[];

  /**
   * Get the default gateway for a currency
   */
  getDefaultGateway(currency: string): PaymentGateway;

  /**
   * Get the best gateway for a specific use case
   */
  getBestGateway(currency: string, amount: number, userLocation?: string): PaymentGateway;
}

/**
 * Payment service interface that orchestrates multiple gateways
 */
export interface IPaymentService {
  /**
   * Process payment using the best available gateway
   */
  processPayment(
    params: StripePaymentParams | RazorpayPaymentParams,
    preferredGateway?: PaymentGateway
  ): Promise<PaymentResult>;

  /**
   * Create payment intent using the best available gateway
   */
  createPaymentIntent(
    params: StripePaymentParams | RazorpayPaymentParams,
    preferredGateway?: PaymentGateway
  ): Promise<PaymentResult>;

  /**
   * Refund payment using the original gateway
   */
  refundPayment(params: RefundParams): Promise<RefundResult>;

  /**
   * Get available payment methods for a currency
   */
  getAvailablePaymentMethods(currency: string): Promise<string[]>;

  /**
   * Get gateway configurations
   */
  getGatewayConfigurations(): Promise<PaymentGatewayConfig[]>;
}
