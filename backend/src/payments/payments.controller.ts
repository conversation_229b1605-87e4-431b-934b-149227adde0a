import {
  Controller,
  Get,
  Post,
  Body,
  Param,
  Patch,
  Delete,
  UseGuards,
  Inject,
  BadRequestException,
  Query,
  Request,
  NotFoundException,
} from "@nestjs/common";
import { JwtAuthGuard } from "../auth/guards/jwt-auth.guard";
import { RolesGuard } from "../auth/guards/roles.guard";
import { Roles } from "../auth/decorators/roles.decorator";
import { UserRole } from "@shared/types";
import { PaymentsService } from "./payments.service";
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiBearerAuth,
  ApiBody,
  ApiParam,
  ApiQuery,
} from "@nestjs/swagger";
import {
  StripePaymentDto,
  UpiPaymentDto,
  RazorpayPaymentDto,
  PaymentResultDto,
  UnifiedPaymentDto,
  RefundDto,
  PaymentGatewayConfigDto,
} from "./dto/payment.dto";

@ApiTags("payments")
@Controller("payments")
@UseGuards(JwtAuthGuard)
@ApiBearerAuth()
export class PaymentsController {
  constructor(
    @Inject(PaymentsService)
    private readonly paymentsService: PaymentsService
  ) {}

  // New unified payment endpoints
  @Post("process")
  @UseGuards(RolesGuard)
  @Roles(UserRole.ADMIN, UserRole.COMPANY)
  @ApiOperation({
    summary: "Process a payment using the best available gateway",
    description:
      "Process a payment using the optimal gateway based on currency and preferences. Requires admin or company role.",
  })
  @ApiResponse({
    status: 200,
    description: "Payment processed successfully",
    type: PaymentResultDto,
  })
  @ApiResponse({
    status: 400,
    description: "Bad request - invalid payment data",
  })
  @ApiBody({ type: UnifiedPaymentDto })
  async processUnifiedPayment(
    @Body() paymentData: UnifiedPaymentDto
  ): Promise<PaymentResultDto> {
    if (!paymentData.amount || !paymentData.currency) {
      throw new BadRequestException("Amount and currency are required");
    }

    // Convert to appropriate gateway-specific params
    if (
      paymentData.preferredGateway === "stripe" ||
      (!paymentData.preferredGateway &&
        paymentData.currency.toUpperCase() !== "INR")
    ) {
      const stripeParams = {
        amount: paymentData.amount,
        currency: paymentData.currency,
        description: paymentData.description,
        paymentMethodId: paymentData.paymentMethodId,
        customerId: paymentData.customerId,
        metadata: paymentData.metadata,
      };
      return this.paymentsService.processPayment(stripeParams, "stripe");
    } else {
      const razorpayParams = {
        amount: paymentData.amount,
        currency: paymentData.currency,
        description: paymentData.description,
        customerEmail: paymentData.customerEmail,
        customerPhone: paymentData.customerPhone,
        metadata: paymentData.metadata,
        orderId: paymentData.orderId,
      };
      return this.paymentsService.processPayment(razorpayParams, "razorpay");
    }
  }

  @Post("intent")
  @UseGuards(RolesGuard)
  @Roles(UserRole.ADMIN, UserRole.COMPANY)
  @ApiOperation({
    summary: "Create a payment intent using the best available gateway",
    description:
      "Create a payment intent for frontend processing. Requires admin or company role.",
  })
  @ApiResponse({
    status: 200,
    description: "Payment intent created successfully",
    type: PaymentResultDto,
  })
  @ApiResponse({
    status: 400,
    description: "Bad request - invalid payment data",
  })
  @ApiBody({ type: UnifiedPaymentDto })
  async createUnifiedPaymentIntent(
    @Body() paymentData: UnifiedPaymentDto
  ): Promise<PaymentResultDto> {
    if (!paymentData.amount || !paymentData.currency) {
      throw new BadRequestException("Amount and currency are required");
    }

    // Convert to appropriate gateway-specific params
    if (
      paymentData.preferredGateway === "stripe" ||
      (!paymentData.preferredGateway &&
        paymentData.currency.toUpperCase() !== "INR")
    ) {
      const stripeParams = {
        amount: paymentData.amount,
        currency: paymentData.currency,
        description: paymentData.description,
        paymentMethodId: paymentData.paymentMethodId,
        customerId: paymentData.customerId,
        metadata: paymentData.metadata,
      };
      return this.paymentsService.createPaymentIntent(stripeParams, "stripe");
    } else {
      const razorpayParams = {
        amount: paymentData.amount,
        currency: paymentData.currency,
        description: paymentData.description,
        customerEmail: paymentData.customerEmail,
        customerPhone: paymentData.customerPhone,
        metadata: paymentData.metadata,
        orderId: paymentData.orderId,
      };
      return this.paymentsService.createPaymentIntent(
        razorpayParams,
        "razorpay"
      );
    }
  }

  @Post("refund")
  @UseGuards(RolesGuard)
  @Roles(UserRole.ADMIN, UserRole.COMPANY)
  @ApiOperation({
    summary: "Refund a payment",
    description:
      "Refund a payment using the original gateway. Requires admin or company role.",
  })
  @ApiResponse({
    status: 200,
    description: "Refund processed successfully",
    schema: {
      type: "object",
      properties: {
        success: { type: "boolean" },
        refundId: { type: "string" },
        amount: { type: "number" },
        status: { type: "string" },
        gateway: { type: "string" },
      },
    },
  })
  @ApiResponse({
    status: 400,
    description: "Bad request - invalid refund data",
  })
  @ApiBody({ type: RefundDto })
  async refundPayment(@Body() refundData: RefundDto) {
    if (!refundData.transactionId) {
      throw new BadRequestException("Transaction ID is required");
    }

    return this.paymentsService.refundPayment(refundData);
  }

  @Get("gateways")
  @ApiOperation({
    summary: "Get available payment gateways",
    description: "Get configuration for all available payment gateways.",
  })
  @ApiResponse({
    status: 200,
    description: "Gateway configurations retrieved successfully",
    type: [PaymentGatewayConfigDto],
  })
  async getGatewayConfigurations(): Promise<PaymentGatewayConfigDto[]> {
    return this.paymentsService.getGatewayConfigurations();
  }

  @Get("methods/available")
  @ApiOperation({
    summary: "Get available payment methods for a currency",
    description: "Get all available payment methods for a specific currency.",
  })
  @ApiResponse({
    status: 200,
    description: "Available payment methods retrieved successfully",
    schema: {
      type: "object",
      properties: {
        currency: { type: "string" },
        methods: { type: "array", items: { type: "string" } },
      },
    },
  })
  @ApiQuery({
    name: "currency",
    required: true,
    description: "Currency code (ISO 4217)",
    example: "USD",
  })
  async getAvailablePaymentMethods(@Query("currency") currency: string) {
    if (!currency) {
      throw new BadRequestException("Currency is required");
    }

    const methods = await this.paymentsService.getAvailablePaymentMethods(
      currency
    );
    return { currency, methods };
  }

  // Legacy endpoints (deprecated but maintained for backward compatibility)
  @Post("stripe")
  @UseGuards(RolesGuard)
  @Roles(UserRole.ADMIN, UserRole.COMPANY)
  @ApiOperation({
    summary: "Process a payment using Stripe (deprecated)",
    description:
      "Process a payment using Stripe payment gateway. Use /payments/process instead. Requires admin or company role.",
    deprecated: true,
  })
  @ApiBody({
    type: StripePaymentDto,
    description: "Payment details for Stripe",
  })
  @ApiResponse({
    status: 201,
    description: "Payment processed successfully",
    type: PaymentResultDto,
  })
  @ApiResponse({
    status: 400,
    description: "Bad request - missing required fields or invalid data",
  })
  @ApiResponse({
    status: 401,
    description: "Unauthorized - invalid or missing authentication",
  })
  @ApiResponse({
    status: 403,
    description: "Forbidden - user does not have required role",
  })
  @ApiResponse({
    status: 500,
    description: "Internal server error - payment processing failed",
  })
  async processStripePayment(
    @Body() paymentData: StripePaymentDto
  ): Promise<PaymentResultDto> {
    if (!paymentData.amount || !paymentData.currency) {
      throw new BadRequestException("Amount and currency are required");
    }

    return this.paymentsService.processStripePayment({
      amount: paymentData.amount,
      currency: paymentData.currency,
      description: paymentData.description || "Payment for services",
      paymentMethodId: paymentData.paymentMethodId,
      customerId: paymentData.customerId,
      metadata: paymentData.metadata,
    });
  }

  @Post("upi")
  @UseGuards(RolesGuard)
  @Roles(UserRole.ADMIN, UserRole.COMPANY)
  @ApiOperation({
    summary: "Process a payment using UPI",
    description:
      "Process a payment using UPI (Unified Payments Interface). Requires admin or company role.",
  })
  @ApiBody({
    type: UpiPaymentDto,
    description: "Payment details for UPI",
  })
  @ApiResponse({
    status: 201,
    description: "Payment processed successfully",
    type: PaymentResultDto,
  })
  @ApiResponse({
    status: 400,
    description: "Bad request - missing required fields or invalid data",
  })
  @ApiResponse({
    status: 401,
    description: "Unauthorized - invalid or missing authentication",
  })
  @ApiResponse({
    status: 403,
    description: "Forbidden - user does not have required role",
  })
  @ApiResponse({
    status: 500,
    description: "Internal server error - payment processing failed",
  })
  async processUpiPayment(
    @Body() paymentData: UpiPaymentDto
  ): Promise<PaymentResultDto> {
    if (!paymentData.amount || !paymentData.vpa) {
      throw new BadRequestException("Amount and VPA (UPI ID) are required");
    }

    return this.paymentsService.processUpiPayment({
      amount: paymentData.amount,
      vpa: paymentData.vpa,
      description: paymentData.description || "Payment for services",
      reference: paymentData.reference || `payment-${Date.now()}`,
      metadata: paymentData.metadata,
    });
  }

  @Post("razorpay/order")
  @UseGuards(RolesGuard)
  @Roles(UserRole.ADMIN, UserRole.COMPANY)
  @ApiOperation({
    summary: "Create a Razorpay order",
    description:
      "Create a Razorpay order for payment processing. Requires admin or company role.",
  })
  @ApiBody({
    type: RazorpayPaymentDto,
    description: "Order details for Razorpay",
  })
  @ApiResponse({
    status: 201,
    description: "Order created successfully",
    type: PaymentResultDto,
  })
  @ApiResponse({
    status: 400,
    description: "Bad request - missing required fields or invalid data",
  })
  @ApiResponse({
    status: 401,
    description: "Unauthorized - invalid or missing authentication",
  })
  @ApiResponse({
    status: 403,
    description: "Forbidden - user does not have required role",
  })
  @ApiResponse({
    status: 500,
    description: "Internal server error - order creation failed",
  })
  async createRazorpayOrder(
    @Body() paymentData: RazorpayPaymentDto
  ): Promise<PaymentResultDto> {
    if (!paymentData.amount || !paymentData.currency) {
      throw new BadRequestException("Amount and currency are required");
    }

    return this.paymentsService.createRazorpayOrder({
      amount: paymentData.amount,
      currency: paymentData.currency,
      description: paymentData.description || "Payment for services",
      customerEmail: paymentData.customerEmail,
      customerPhone: paymentData.customerPhone,
      metadata: paymentData.metadata,
    });
  }

  @Post("razorpay")
  @UseGuards(RolesGuard)
  @Roles(UserRole.ADMIN, UserRole.COMPANY)
  @ApiOperation({
    summary: "Process a payment using Razorpay",
    description:
      "Process a payment using Razorpay payment gateway. Requires admin or company role.",
  })
  @ApiBody({
    type: RazorpayPaymentDto,
    description: "Payment details for Razorpay",
  })
  @ApiResponse({
    status: 201,
    description: "Payment processed successfully",
    type: PaymentResultDto,
  })
  @ApiResponse({
    status: 400,
    description: "Bad request - missing required fields or invalid data",
  })
  @ApiResponse({
    status: 401,
    description: "Unauthorized - invalid or missing authentication",
  })
  @ApiResponse({
    status: 403,
    description: "Forbidden - user does not have required role",
  })
  @ApiResponse({
    status: 500,
    description: "Internal server error - payment processing failed",
  })
  async processRazorpayPayment(
    @Body() paymentData: RazorpayPaymentDto
  ): Promise<PaymentResultDto> {
    if (!paymentData.amount || !paymentData.currency) {
      throw new BadRequestException("Amount and currency are required");
    }

    return this.paymentsService.processRazorpayPayment({
      amount: paymentData.amount,
      currency: paymentData.currency,
      description: paymentData.description || "Payment for services",
      customerEmail: paymentData.customerEmail,
      customerPhone: paymentData.customerPhone,
      metadata: paymentData.metadata,
      orderId: paymentData.orderId,
    });
  }

  @Post("razorpay/capture/:paymentId")
  @UseGuards(RolesGuard)
  @Roles(UserRole.ADMIN, UserRole.COMPANY)
  @ApiOperation({
    summary: "Capture a Razorpay payment",
    description:
      "Capture an authorized Razorpay payment. Requires admin or company role.",
  })
  @ApiParam({
    name: "paymentId",
    description: "Razorpay payment ID to capture",
  })
  @ApiBody({
    schema: {
      type: "object",
      properties: {
        amount: {
          type: "number",
          description: "Amount to capture in paise",
          example: 100000,
        },
      },
      required: ["amount"],
    },
  })
  @ApiResponse({
    status: 200,
    description: "Payment captured successfully",
    type: PaymentResultDto,
  })
  @ApiResponse({
    status: 400,
    description: "Bad request - invalid payment ID or amount",
  })
  @ApiResponse({
    status: 401,
    description: "Unauthorized - invalid or missing authentication",
  })
  @ApiResponse({
    status: 403,
    description: "Forbidden - user does not have required role",
  })
  @ApiResponse({
    status: 500,
    description: "Internal server error - payment capture failed",
  })
  async captureRazorpayPayment(
    @Param("paymentId") paymentId: string,
    @Body("amount") amount: number
  ): Promise<PaymentResultDto> {
    if (!amount) {
      throw new BadRequestException("Amount is required");
    }

    return this.paymentsService.captureRazorpayPayment(paymentId, amount);
  }

  @Post("razorpay/refund/:paymentId")
  @UseGuards(RolesGuard)
  @Roles(UserRole.ADMIN, UserRole.COMPANY)
  @ApiOperation({
    summary: "Refund a Razorpay payment",
    description:
      "Refund a captured Razorpay payment. Requires admin or company role.",
  })
  @ApiParam({
    name: "paymentId",
    description: "Razorpay payment ID to refund",
  })
  @ApiBody({
    schema: {
      type: "object",
      properties: {
        amount: {
          type: "number",
          description:
            "Amount to refund in paise (optional, full refund if not provided)",
          example: 50000,
        },
        notes: {
          type: "object",
          description: "Additional notes for the refund",
          example: { reason: "Customer request" },
        },
      },
    },
  })
  @ApiResponse({
    status: 200,
    description: "Payment refunded successfully",
    type: PaymentResultDto,
  })
  @ApiResponse({
    status: 400,
    description: "Bad request - invalid payment ID",
  })
  @ApiResponse({
    status: 401,
    description: "Unauthorized - invalid or missing authentication",
  })
  @ApiResponse({
    status: 403,
    description: "Forbidden - user does not have required role",
  })
  @ApiResponse({
    status: 500,
    description: "Internal server error - refund failed",
  })
  async refundRazorpayPayment(
    @Param("paymentId") paymentId: string,
    @Body("amount") amount?: number,
    @Body("notes") notes?: Record<string, any>
  ): Promise<PaymentResultDto> {
    return this.paymentsService.refundRazorpayPayment(paymentId, amount, notes);
  }

  @Post("razorpay/verify")
  @UseGuards(RolesGuard)
  @Roles(UserRole.ADMIN, UserRole.COMPANY, UserRole.WORKER)
  @ApiOperation({
    summary: "Verify Razorpay payment signature",
    description:
      "Verify the authenticity of a Razorpay payment using signature verification.",
  })
  @ApiBody({
    schema: {
      type: "object",
      properties: {
        orderId: {
          type: "string",
          description: "Razorpay order ID",
          example: "order_1234567890",
        },
        paymentId: {
          type: "string",
          description: "Razorpay payment ID",
          example: "pay_1234567890",
        },
        signature: {
          type: "string",
          description: "Razorpay payment signature",
          example: "signature_string",
        },
      },
      required: ["orderId", "paymentId", "signature"],
    },
  })
  @ApiResponse({
    status: 200,
    description: "Signature verification result",
    schema: {
      type: "object",
      properties: {
        verified: {
          type: "boolean",
          description: "Whether the signature is valid",
        },
      },
    },
  })
  @ApiResponse({
    status: 400,
    description: "Bad request - missing required fields",
  })
  async verifyRazorpaySignature(
    @Body("orderId") orderId: string,
    @Body("paymentId") paymentId: string,
    @Body("signature") signature: string
  ) {
    if (!orderId || !paymentId || !signature) {
      throw new BadRequestException(
        "Order ID, payment ID, and signature are required"
      );
    }

    const verified = this.paymentsService.verifyRazorpaySignature(
      orderId,
      paymentId,
      signature
    );

    return { verified };
  }

  @Get()
  @ApiOperation({ summary: "Get all payments" })
  @ApiResponse({ status: 200, description: "Returns all payments" })
  @ApiQuery({
    name: "status",
    required: false,
    description: "Payment status filter",
  })
  @ApiQuery({
    name: "limit",
    required: false,
    type: Number,
    description: "Number of records to return",
  })
  @ApiQuery({
    name: "offset",
    required: false,
    type: Number,
    description: "Number of records to skip",
  })
  async getPayments(
    @Request() req,
    @Query("status") status?: string,
    @Query("limit") limit: number = 10,
    @Query("offset") offset: number = 0
  ) {
    // If user is a worker, they can only see their own payments
    const userId = req.user.id;
    const userRole = req.user.role;

    // This is a placeholder implementation
    // In a real application, you would query the database for payments
    return {
      payments: [
        {
          id: "1",
          amount: 5000,
          currency: "INR",
          status: "completed",
          method: "upi",
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString(),
        },
      ],
      total: 1,
      limit,
      offset,
    };
  }

  @Get(":id")
  @ApiOperation({ summary: "Get payment by ID" })
  @ApiResponse({ status: 200, description: "Returns payment details" })
  @ApiResponse({ status: 404, description: "Payment not found" })
  @ApiParam({ name: "id", description: "Payment ID" })
  async getPaymentById(@Param("id") id: string, @Request() req) {
    // This is a placeholder implementation
    // In a real application, you would query the database for the payment

    // Mock payment for demonstration
    const payment = {
      id,
      amount: 5000,
      currency: "INR",
      status: "completed",
      method: "upi",
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
    };

    if (!payment) {
      throw new NotFoundException(`Payment with ID ${id} not found`);
    }

    return payment;
  }

  @Get("summary")
  @ApiOperation({ summary: "Get payment summary" })
  @ApiResponse({ status: 200, description: "Returns payment summary" })
  async getPaymentSummary(@Request() req) {
    // This is a placeholder implementation
    // In a real application, you would calculate this from the database

    return {
      totalSpent: 25000,
      totalPayments: 5,
      pendingPayments: 1,
      completedPayments: 4,
      failedPayments: 0,
      lastPaymentDate: new Date().toISOString(),
    };
  }

  @Get("methods")
  @ApiOperation({ summary: "Get payment methods" })
  @ApiResponse({ status: 200, description: "Returns payment methods" })
  async getPaymentMethods(@Request() req) {
    // This is a placeholder implementation
    // In a real application, you would fetch this from the database

    return [
      {
        id: "1",
        type: "upi",
        details: {
          vpa: "user@upi",
        },
        isDefault: true,
        createdAt: new Date().toISOString(),
      },
      {
        id: "2",
        type: "card",
        details: {
          last4: "4242",
          brand: "visa",
          expiryMonth: 12,
          expiryYear: 2025,
        },
        isDefault: false,
        createdAt: new Date().toISOString(),
      },
    ];
  }

  @Post("methods")
  @ApiOperation({ summary: "Add payment method" })
  @ApiResponse({
    status: 201,
    description: "Payment method added successfully",
  })
  async addPaymentMethod(@Body() data: any, @Request() req) {
    // This is a placeholder implementation
    // In a real application, you would save this to the database

    return {
      id: "3",
      type: data.type,
      details: data.details,
      isDefault: false,
      createdAt: new Date().toISOString(),
    };
  }

  @Patch("methods/:id")
  @ApiOperation({ summary: "Update payment method" })
  @ApiResponse({
    status: 200,
    description: "Payment method updated successfully",
  })
  @ApiResponse({ status: 404, description: "Payment method not found" })
  @ApiParam({ name: "id", description: "Payment method ID" })
  async updatePaymentMethod(
    @Param("id") id: string,
    @Body() data: any,
    @Request() req
  ) {
    // This is a placeholder implementation
    // In a real application, you would update this in the database

    return {
      id,
      type: data.type || "upi",
      details: data.details || { vpa: "updated@upi" },
      isDefault: false,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
    };
  }

  @Delete("methods/:id")
  @ApiOperation({ summary: "Delete payment method" })
  @ApiResponse({
    status: 200,
    description: "Payment method deleted successfully",
  })
  @ApiResponse({ status: 404, description: "Payment method not found" })
  @ApiParam({ name: "id", description: "Payment method ID" })
  async deletePaymentMethod(@Param("id") id: string, @Request() req) {
    // This is a placeholder implementation
    // In a real application, you would delete this from the database

    return { success: true };
  }

  @Post("methods/:id/default")
  @ApiOperation({ summary: "Set default payment method" })
  @ApiResponse({
    status: 200,
    description: "Default payment method set successfully",
  })
  @ApiResponse({ status: 404, description: "Payment method not found" })
  @ApiParam({ name: "id", description: "Payment method ID" })
  async setDefaultPaymentMethod(@Param("id") id: string, @Request() req) {
    // This is a placeholder implementation
    // In a real application, you would update this in the database

    return { success: true };
  }
}
