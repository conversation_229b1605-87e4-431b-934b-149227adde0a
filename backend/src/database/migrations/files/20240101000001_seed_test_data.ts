import { QueryRunner } from "typeorm";
import * as bcrypt from "bcrypt";

/**
 * Migration to seed comprehensive test data
 */
export const up = async (queryRunner: QueryRunner): Promise<void> => {
  // Hash passwords for test users
  const adminPassword = await bcrypt.hash("admin123", 10);
  const companyPassword = await bcrypt.hash("company123", 10);
  const workerPassword = await bcrypt.hash("worker123", 10);

  // ==================== ADMIN USERS ====================
  console.log("Creating admin users...");

  // Insert admin users (3 admins)
  let adminIds = [];
  const adminEmails = [
    "<EMAIL>",
    "<EMAIL>",
    "<EMAIL>",
  ];
  const adminNames = ["Admin User", "Support Admin", "Content Moderator"];

  for (let i = 0; i < adminEmails.length; i++) {
    const result = await queryRunner.query(`
      INSERT INTO users (
        id, email, phone, password, "fullName", role,
        "trustScore", "isKycVerified", "isActive", "emailVerified", "phoneVerified",
        bio, "profilePicture"
      ) VALUES (
        uuid_generate_v4(), '${adminEmails[i]}', '+1${9000000 + i}',
        '${adminPassword}', '${adminNames[i]}', 'admin',
        100, TRUE, TRUE, TRUE, TRUE,
        'Platform administrator with full system access.', 'https://randomuser.me/api/portraits/men/${
          20 + i
        }.jpg'
      ) RETURNING id;
    `);
    adminIds.push(result[0].id);
  }

  // Make sure we have at least one admin ID
  if (adminIds.length === 0) {
    // Create a default admin if none were created
    const defaultAdminResult = await queryRunner.query(`
      INSERT INTO users (
        id, email, phone, password, "fullName", role,
        "trustScore", "isKycVerified", "isActive", "emailVerified", "phoneVerified"
      ) VALUES (
        uuid_generate_v4(), '<EMAIL>', '+19999999999',
        '${adminPassword}', 'Default Admin', 'admin',
        100, TRUE, TRUE, TRUE, TRUE
      ) RETURNING id;
    `);
    adminIds.push(defaultAdminResult[0].id);
  }

  // ==================== COMPANY USERS ====================
  console.log("Creating company users...");

  // Company data
  const companyNames = [
    "TechSolutions Inc.",
    "GreenLeaf Landscaping",
    "QuickServe Restaurants",
    "BuildRight Construction",
    "DataDrive Analytics",
    "HealthPlus Medical Services",
    "EduSmart Learning Center",
    "FashionForward Retail",
    "LogiTech Transport",
    "CreativeMinds Design",
  ];

  const companyIndustries = [
    "Technology",
    "Landscaping",
    "Food Service",
    "Construction",
    "Data Analytics",
    "Healthcare",
    "Education",
    "Retail",
    "Logistics",
    "Design",
  ];

  const companySizes = [
    "1-10",
    "11-50",
    "51-200",
    "201-500",
    "501-1000",
    "1000+",
  ];
  const companyLocations = [
    "New York, NY",
    "San Francisco, CA",
    "Chicago, IL",
    "Austin, TX",
    "Seattle, WA",
    "Miami, FL",
    "Denver, CO",
    "Boston, MA",
    "Atlanta, GA",
    "Los Angeles, CA",
  ];

  // Insert company users (10 companies)
  const companyUserIds = [];

  for (let i = 0; i < companyNames.length; i++) {
    // Create company user
    const companyUserResult = await queryRunner.query(`
      INSERT INTO users (
        id, email, phone, password, "fullName", role,
        "trustScore", "isKycVerified", "isActive", "emailVerified", "phoneVerified",
        name, bio
      ) VALUES (
        uuid_generate_v4(), 'company${i + 1}@example.com', '+1${8000000 + i}',
        '${companyPassword}', '${companyNames[i]} Admin', 'company',
        ${70 + i * 3 > 100 ? 100 : 70 + i * 3}, ${
      i < 8 ? "TRUE" : "FALSE"
    }, TRUE, TRUE, ${i < 8 ? "TRUE" : "FALSE"},
        '${companyNames[i]}', 'Official account for ${companyNames[i]}.'
      ) RETURNING id;
    `);

    const companyUserId = companyUserResult[0].id;
    companyUserIds.push(companyUserId);

    // Create company profile
    const city = companyLocations[i].split(",")[0];
    const state = companyLocations[i].split(",")[1].trim();

    await queryRunner.query(`
      INSERT INTO companies (
        id, "userId", name, website, description,
        industry, size, "foundedYear", "logoUrl",
        "headquartersAddress", "headquartersCity", "headquartersState", "headquartersCountry", "isVerified"
      ) VALUES (
        uuid_generate_v4(), '${companyUserId}', '${companyNames[i]}',
        'https://${companyNames[i]
          .toLowerCase()
          .replace(/[^a-z0-9]/g, "")}.example.com',
        'A leading provider of ${companyIndustries[
          i
        ].toLowerCase()} services with a focus on quality and customer satisfaction.',
        '${companyIndustries[i]}', '${
      companySizes[i % companySizes.length]
    }', ${2015 + (i % 8)}, 'https://example.com/logos/company_${i}.png',
        '123 Business Ave, Suite ${100 + i}', '${city}', '${state}', 'USA', ${
      i < 7 ? "TRUE" : "FALSE"
    }
      );
    `);

    // Add company documents for verification
    if (i < 8) {
      // Get the company ID first
      const companyResult = await queryRunner.query(`
        SELECT id FROM companies WHERE "userId" = '${companyUserId}'
      `);
      const companyId = companyResult[0].id;

      await queryRunner.query(`
        INSERT INTO documents (
          id, "companyId", "documentType", "documentUrl", "documentNumber", "verificationStatus",
          "verifiedBy", "verifiedAt"
        ) VALUES (
          uuid_generate_v4(), '${companyId}', 'business_license',
          'https://example.com/documents/business_license_${i}.pdf', 'BL${
        10000 + i
      }',
          '${i < 7 ? "verified" : "pending"}',
          ${i < 7 ? `'${adminIds[0]}'` : "NULL"}, ${i < 7 ? "NOW()" : "NULL"}
        );
      `);

      await queryRunner.query(`
        INSERT INTO documents (
          id, "companyId", "documentType", "documentUrl", "documentNumber", "verificationStatus",
          "verifiedBy", "verifiedAt"
        ) VALUES (
          uuid_generate_v4(), '${companyId}', 'tax_certificate',
          'https://example.com/documents/tax_cert_${i}.pdf', 'TC${20000 + i}',
          '${i < 7 ? "verified" : "pending"}',
          ${i < 7 ? `'${adminIds[0]}'` : "NULL"}, ${i < 7 ? "NOW()" : "NULL"}
        );
      `);
    }
  }

  // ==================== WORKER USERS ====================
  console.log("Creating worker users...");

  // Worker data
  const workerFirstNames = [
    "John",
    "Emma",
    "Michael",
    "Sophia",
    "David",
    "Olivia",
    "James",
    "Ava",
    "Robert",
    "Isabella",
    "William",
    "Mia",
    "Joseph",
    "Charlotte",
    "Daniel",
    "Amelia",
    "Matthew",
    "Harper",
    "Andrew",
    "Evelyn",
  ];
  const workerLastNames = [
    "Smith",
    "Johnson",
    "Williams",
    "Jones",
    "Brown",
    "Davis",
    "Miller",
    "Wilson",
    "Moore",
    "Taylor",
    "Anderson",
    "Thomas",
    "Jackson",
    "White",
    "Harris",
    "Martin",
    "Thompson",
    "Garcia",
    "Martinez",
    "Robinson",
  ];

  const workerCities = [
    "New York",
    "Los Angeles",
    "Chicago",
    "Houston",
    "Phoenix",
    "Philadelphia",
    "San Antonio",
    "San Diego",
    "Dallas",
    "San Jose",
    "Austin",
    "Jacksonville",
    "Fort Worth",
    "Columbus",
    "Charlotte",
    "Indianapolis",
    "San Francisco",
    "Seattle",
    "Denver",
    "Boston",
  ];

  const workerStates = [
    "NY",
    "CA",
    "IL",
    "TX",
    "AZ",
    "PA",
    "TX",
    "CA",
    "TX",
    "CA",
    "TX",
    "FL",
    "TX",
    "OH",
    "NC",
    "IN",
    "CA",
    "WA",
    "CO",
    "MA",
  ];

  // Insert worker users (20 workers)
  const workerIds = [];

  for (let i = 0; i < 20; i++) {
    const firstName = workerFirstNames[i];
    const lastName = workerLastNames[i];
    const fullName = `${firstName} ${lastName}`;
    const trustScore = 50 + Math.floor(Math.random() * 50); // Random score between 50-99

    const result = await queryRunner.query(`
      INSERT INTO users (
        id, email, phone, password, "fullName", role,
        "trustScore", "isKycVerified", "isActive", "emailVerified", "phoneVerified",
        city, state, country, "postalCode", bio, "profilePicture", "dateOfBirth"
      ) VALUES (
        uuid_generate_v4(), 'worker${i + 1}@example.com', '+1${7000000 + i}',
        '${workerPassword}', '${fullName}', 'worker',
        ${trustScore}, ${i < 15 ? "TRUE" : "FALSE"}, TRUE, TRUE, ${
      i < 15 ? "TRUE" : "FALSE"
    },
        '${workerCities[i]}', '${workerStates[i]}', 'USA', '${10000 + i}',
        'Experienced worker looking for flexible job opportunities.',
        'https://randomuser.me/api/portraits/${i % 2 === 0 ? "men" : "women"}/${
      i + 1
    }.jpg',
        '${1980 + (i % 20)}-${String(1 + (i % 12)).padStart(2, "0")}-${String(
      1 + (i % 28)
    ).padStart(2, "0")}'
      ) RETURNING id;
    `);

    workerIds.push(result[0].id);

    // Add worker documents for verification
    if (i < 15) {
      await queryRunner.query(`
        INSERT INTO documents (
          id, "userId", "documentType", "documentUrl", "documentNumber", "verificationStatus",
          "verifiedBy", "verifiedAt"
        ) VALUES (
          uuid_generate_v4(), '${result[0].id}', 'id_card',
          'https://example.com/documents/id_card_${i}.pdf', 'ID${30000 + i}',
          '${i < 12 ? "verified" : "pending"}',
          ${i < 12 ? `'${adminIds[0]}'` : "NULL"}, ${i < 12 ? "NOW()" : "NULL"}
        );
      `);

      if (i < 10) {
        await queryRunner.query(`
          INSERT INTO documents (
            id, "userId", "documentType", "documentUrl", "documentNumber", "verificationStatus",
            "verifiedBy", "verifiedAt"
          ) VALUES (
            uuid_generate_v4(), '${result[0].id}', 'address_proof',
            'https://example.com/documents/address_proof_${i}.pdf', 'AP${
          40000 + i
        }',
            '${i < 8 ? "verified" : "pending"}',
            ${i < 8 ? `'${adminIds[0]}'` : "NULL"}, ${i < 8 ? "NOW()" : "NULL"}
          );
        `);
      }
    }

    // Add trust score logs for some workers
    if (i < 10) {
      const previousScore = trustScore - 5;
      await queryRunner.query(`
        INSERT INTO trust_score_logs (
          id, "userId", "previousScore", "newScore", change, reason, "relatedEntityType", "adminId"
        ) VALUES (
          uuid_generate_v4(), '${result[0].id}', ${previousScore}, ${trustScore}, 5,
          'Completed verification process successfully', 'verification', '${adminIds[0]}'
        );
      `);
    }
  }

  // ==================== JOBS ====================
  console.log("Creating jobs...");

  // Job data
  const jobTitles = [
    "Software Developer Needed",
    "Marketing Assistant",
    "Data Entry Specialist",
    "Customer Service Representative",
    "Warehouse Worker",
    "Delivery Driver",
    "Event Staff",
    "Administrative Assistant",
    "Sales Associate",
    "Retail Associate",
    "Restaurant Server",
    "Bartender",
    "Security Guard",
    "Cleaner",
    "Maintenance Technician",
    "Landscaper",
    "Painter",
    "Construction Helper",
    "Photographer",
    "Videographer",
  ];

  const jobDescriptions = [
    "We need a software developer for a short-term project. Experience with JavaScript and React required.",
    "Looking for a marketing assistant to help with social media campaigns and content creation.",
    "Data entry specialist needed for a 2-day project. Fast typing speed required.",
    "Customer service representative needed to handle incoming calls and support customers.",
    "Warehouse worker needed for inventory management and order fulfillment.",
    "Delivery driver needed for local deliveries. Must have valid driver''s license.",
    "Event staff needed for upcoming corporate event. Professional appearance required.",
    "Administrative assistant needed for general office duties and scheduling.",
    "Sales associate needed for retail location. Experience preferred but not required.",
    "Retail associate needed for busy shopping season. Flexible hours available.",
    "Restaurant server needed for upscale dining establishment. Experience required.",
    "Experienced bartender needed for weekend shifts at popular nightclub.",
    "Security guard needed for office building. Must have security certification.",
    "Cleaner needed for office space. Evening shifts available.",
    "Maintenance technician needed for apartment complex. Must have basic repair skills.",
    "Landscaper needed for commercial property maintenance. Experience with equipment required.",
    "Painter needed for interior painting project. Must have own tools.",
    "Construction helper needed for residential project. No experience necessary.",
    "Photographer needed for corporate event. Must have professional equipment.",
    "Videographer needed for product demonstration videos. Portfolio required.",
  ];

  // Create jobs (20 jobs, 2 per company)
  const jobIds = [];

  for (let i = 0; i < companyUserIds.length; i++) {
    const companyUserId = companyUserIds[i];

    // Get the actual company ID from the companies table
    const companyResult = await queryRunner.query(`
      SELECT id FROM companies WHERE "userId" = '${companyUserId}'
    `);

    if (companyResult.length === 0) {
      console.log(`No company found for user ID: ${companyUserId}`);
      continue;
    }

    const companyId = companyResult[0].id;

    // Create 2 jobs per company
    for (let j = 0; j < 2; j++) {
      const jobIndex = i * 2 + j;
      const title = jobTitles[jobIndex];
      const description = jobDescriptions[jobIndex];

      // Location based on company location
      const location = companyLocations[i];
      const city = location.split(",")[0];
      const state = location.split(",")[1].trim();

      // Job details
      const hourlyRate = 15 + Math.floor(Math.random() * 30); // $15-45/hr
      const estimatedHours = 4 + Math.floor(Math.random() * 36); // 4-40 hours
      const trustScoreRequired = Math.floor(Math.random() * 70); // 0-70 trust score required
      const isEmergencyJob = Math.random() < 0.2; // 20% chance of emergency job
      const requiresLaptop = Math.random() < 0.5; // 50% chance of requiring laptop
      const requiresSmartphone = Math.random() < 0.8; // 80% chance of requiring smartphone
      const maxPositions = 1 + Math.floor(Math.random() * 5); // 1-5 positions

      // Dates
      const startDate = new Date();
      startDate.setDate(
        startDate.getDate() + 1 + Math.floor(Math.random() * 14)
      ); // Start 1-14 days from now

      const endDate = new Date(startDate);
      endDate.setHours(endDate.getHours() + estimatedHours); // End after estimated hours

      // Status - most are open, some are in progress or completed
      let status = "open";
      let filledPositions = 0;

      if (jobIndex < 3) {
        status = "in_progress";
        filledPositions = 1;
      } else if (jobIndex >= 18) {
        status = "completed";
        filledPositions = maxPositions;
      }

      const result = await queryRunner.query(`
        INSERT INTO jobs (
          id, "companyId", title, description, location,
          city, state, country, "postalCode",
          "startDateTime", "endDateTime",
          "hourlyRate", "estimatedHours",
          "trustScoreRequired", "isEmergencyJob",
          "requiresLaptop", "requiresSmartphone",
          status, "maxPositions", "filledPositions"
        ) VALUES (
          uuid_generate_v4(), '${companyId}', '${title}', '${description}', '${location}',
          '${city}', '${state}', 'USA', '${10000 + jobIndex}',
          '${startDate.toISOString()}', '${endDate.toISOString()}',
          ${hourlyRate}, ${estimatedHours},
          ${trustScoreRequired}, ${isEmergencyJob},
          ${requiresLaptop}, ${requiresSmartphone},
          '${status}', ${maxPositions}, ${filledPositions}
        ) RETURNING id;
      `);

      jobIds.push(result[0].id);
    }
  }

  // ==================== APPLICATIONS ====================
  console.log("Creating applications...");

  // Application cover letters
  const coverLetters = [
    "I am interested in this job and have the required skills.",
    "I would like to apply for this position and am available during the required times.",
    "I have extensive experience in this field and would be a great fit for this role.",
    "I am excited about this opportunity and believe my skills match your requirements perfectly.",
    "I am available immediately and have all the qualifications listed in the job description.",
    "I have been looking for exactly this type of position and am confident I can exceed your expectations.",
    "My background in this industry makes me an ideal candidate for this job.",
    "I am highly motivated and would appreciate the opportunity to work with your company.",
    "I have the necessary experience and am available for all the listed shifts.",
    "I am reliable, punctual, and have all the skills required for this position.",
  ];

  // Create applications (each worker applies to 3-5 jobs)
  console.log("Creating applications...");
  const applicationIds = [];

  for (let i = 0; i < workerIds.length; i++) {
    const workerId = workerIds[i];

    // Determine how many applications this worker will submit (3-5)
    const numApplications = 3 + Math.floor(Math.random() * 3);

    // Create a set to track which jobs this worker has already applied to
    const appliedJobs = new Set();

    for (let j = 0; j < numApplications; j++) {
      // Find a job this worker hasn't applied to yet
      let jobIndex: number;
      do {
        jobIndex = Math.floor(Math.random() * jobIds.length);
      } while (appliedJobs.has(jobIndex));

      appliedJobs.add(jobIndex);
      const jobId = jobIds[jobIndex];

      // Determine application status based on various factors
      let status = "pending";

      // First 3 jobs are in_progress, so some applications should be accepted
      if (jobIndex < 3 && i < 5) {
        status = "accepted";
      }
      // Last 2 jobs are completed, so some applications should be completed or no_show
      else if (jobIndex >= 18) {
        status = Math.random() < 0.8 ? "completed" : "no_show";
      }
      // Some applications should be rejected
      else if (Math.random() < 0.2) {
        status = "rejected";
      }

      // Select a random cover letter
      const coverLetterIndex = Math.floor(Math.random() * coverLetters.length);

      const result = await queryRunner.query(`
        INSERT INTO applications (
          id, "jobId", "workerId", status, "coverLetter",
          "createdAt", "updatedAt"
        ) VALUES (
          uuid_generate_v4(), '${jobId}', '${workerId}', '${status}',
          '${coverLetters[coverLetterIndex]}',
          NOW() - INTERVAL '${Math.floor(Math.random() * 14)} days',
          NOW() - INTERVAL '${Math.floor(Math.random() * 7)} days'
        ) RETURNING id;
      `);

      applicationIds.push(result[0].id);
    }
  }

  // ==================== RATINGS ====================
  console.log("Creating ratings...");

  // Create ratings for completed applications
  for (let i = 0; i < applicationIds.length; i++) {
    // Only create ratings for some applications (about 60%)
    if (Math.random() < 0.6) {
      // Get application details
      const applicationResult = await queryRunner.query(`
        SELECT a.id, a.status, a."workerId", j."companyId", j.id as "jobId", c."userId" as "companyUserId"
        FROM applications a
        JOIN jobs j ON a."jobId" = j.id
        JOIN companies c ON j."companyId" = c.id
        WHERE a.id = '${applicationIds[i]}'
      `);

      if (applicationResult.length === 0) continue;

      const application = applicationResult[0];

      // Only create ratings for completed applications
      if (application.status === "completed") {
        const workerId = application.workerId;
        const companyUserId = application.companyUserId;
        const jobId = application.jobId;

        // Worker rates company
        const workerRating = 3 + Math.floor(Math.random() * 3); // 3-5 stars
        const workerIsAnonymous = Math.random() < 0.3;

        await queryRunner.query(`
          INSERT INTO ratings (
            id, "ratedById", "ratedUserId", "jobId", stars, comment, "isAnonymous"
          ) VALUES (
            uuid_generate_v4(), '${workerId}', '${companyUserId}', '${jobId}',
            ${workerRating}, 'Good job opportunity, would work with this company again.', ${workerIsAnonymous}
          );
        `);

        // Company rates worker
        const companyRating = 3 + Math.floor(Math.random() * 3); // 3-5 stars
        await queryRunner.query(`
          INSERT INTO ratings (
            id, "ratedById", "ratedUserId", "jobId", stars, comment, "isAnonymous"
          ) VALUES (
            uuid_generate_v4(), '${companyUserId}', '${workerId}', '${jobId}',
            ${companyRating}, 'Good worker, completed the job as expected.', false
          );
        `);
      }
    }
  }

  // ==================== PAYOUTS ====================
  console.log("Creating payouts...");

  // Create payouts for completed applications
  for (let i = 0; i < applicationIds.length; i++) {
    // Get application details
    const applicationResult = await queryRunner.query(`
      SELECT a.id, a.status, a."workerId", j.id as "jobId", j."hourlyRate", j."estimatedHours"
      FROM applications a
      JOIN jobs j ON a."jobId" = j.id
      WHERE a.id = '${applicationIds[i]}'
    `);

    if (applicationResult.length === 0) continue;

    const application = applicationResult[0];

    // Only create payouts for completed applications
    if (application.status === "completed") {
      const workerId = application.workerId;
      const jobId = application.jobId;
      const hourlyRate = parseFloat(application.hourlyRate);
      const estimatedHours = parseInt(application.estimatedHours);

      // Calculate payout amounts
      const grossAmount = hourlyRate * estimatedHours;
      const platformFee = grossAmount * 0.1; // 10% platform fee
      const netAmount = grossAmount - platformFee;

      // Determine payout status
      let payoutStatus = "pending";
      if (Math.random() < 0.7) {
        payoutStatus = "completed";
      } else if (Math.random() < 0.5) {
        payoutStatus = "processing";
      }

      // Create payout record
      const transactionId =
        payoutStatus === "completed" ? `'TX${1000000 + i}'` : "NULL";
      const paymentDate = payoutStatus === "completed" ? "NOW()" : "NULL";

      await queryRunner.query(`
        INSERT INTO payouts (
          id, "workerId", "jobId", "grossAmount", "commission", "netAmount", "platformFee",
          "taxAmount", status, "paymentMethod", "transactionId", "paymentDate"
        ) VALUES (
          uuid_generate_v4(), '${workerId}', '${jobId}', ${grossAmount}, ${platformFee}, ${netAmount}, ${platformFee},
          0, '${payoutStatus}', 'bank_transfer', ${transactionId}, ${paymentDate}
        );
      `);
    }
  }

  // ==================== DISPUTES ====================
  console.log("Creating disputes...");

  // Create disputes for some applications (about 10%)
  for (let i = 0; i < applicationIds.length; i++) {
    if (Math.random() < 0.1) {
      // Get application details
      const applicationResult = await queryRunner.query(`
        SELECT a.id, a.status, a."workerId", j."companyId", j.id as "jobId", c."userId" as "companyUserId"
        FROM applications a
        JOIN jobs j ON a."jobId" = j.id
        JOIN companies c ON j."companyId" = c.id
        WHERE a.id = '${applicationIds[i]}'
      `);

      if (applicationResult.length === 0) continue;

      const application = applicationResult[0];
      const workerId = application.workerId;
      const companyUserId = application.companyUserId;
      const jobId = application.jobId;

      // Determine who raised the dispute
      const workerRaisedDispute = Math.random() < 0.5;
      const raisedById = workerRaisedDispute ? workerId : companyUserId;
      const againstId = workerRaisedDispute ? companyUserId : workerId;

      // Determine dispute status
      let disputeStatus = "open";
      if (Math.random() < 0.3) {
        disputeStatus = "under-review";
      } else if (Math.random() < 0.2) {
        disputeStatus = "resolved";
      }

      // Create dispute
      const disputeReasons = [
        "Payment dispute",
        "Job conditions were different than described",
        "Worker didn''t complete the job as required",
        "Communication issues",
        "Safety concerns at the job site",
      ];

      const reasonIndex = Math.floor(Math.random() * disputeReasons.length);

      const reason = disputeReasons[reasonIndex];
      const resolvedById =
        disputeStatus === "resolved" ? `'${adminIds[0]}'` : "NULL";
      const resolvedAt = disputeStatus === "resolved" ? "NOW()" : "NULL";
      const resolution =
        disputeStatus === "resolved"
          ? "'Issue resolved through mediation.'"
          : "NULL";

      await queryRunner.query(`
        INSERT INTO disputes (
          id, "raisedById", "againstId", "jobId", reason, description, status,
          "resolvedById", "resolvedAt", resolution
        ) VALUES (
          uuid_generate_v4(), '${raisedById}', '${againstId}', '${jobId}',
          '${reason}', 'Detailed description of the dispute issue.',
          '${disputeStatus}',
          ${resolvedById},
          ${resolvedAt},
          ${resolution}
        );
      `);
    }
  }

  // ==================== FAVORITES ====================
  console.log("Creating favorites...");

  // Create favorites for some workers (each worker saves 2-5 jobs)
  for (let i = 0; i < workerIds.length; i++) {
    const workerId = workerIds[i];

    // Determine how many jobs to favorite
    const numFavorites = 2 + Math.floor(Math.random() * 4);

    // Create a set to track which jobs this worker has already favorited
    const favoritedJobs = new Set();

    for (let j = 0; j < numFavorites; j++) {
      // Find a job this worker hasn't saved yet
      let jobIndex: number;
      do {
        jobIndex = Math.floor(Math.random() * jobIds.length);
      } while (favoritedJobs.has(jobIndex));

      favoritedJobs.add(jobIndex);
      const jobId = jobIds[jobIndex];

      // Create favorite
      const randomDays = Math.floor(Math.random() * 10);

      await queryRunner.query(`
        INSERT INTO favorites (
          id, "workerId", "jobId", "createdAt"
        ) VALUES (
          uuid_generate_v4(), '${workerId}', '${jobId}', NOW() - INTERVAL '${randomDays} days'
        );
      `);
    }
  }

  // ==================== CHATS ====================
  console.log("Creating chats...");

  // Create chats for accepted applications
  for (let i = 0; i < applicationIds.length; i++) {
    // Get application details
    const applicationResult = await queryRunner.query(`
      SELECT a.id, a.status, a."workerId", j."companyId", j.id as "jobId", c."userId" as "companyUserId"
      FROM applications a
      JOIN jobs j ON a."jobId" = j.id
      JOIN companies c ON j."companyId" = c.id
      WHERE a.id = '${applicationIds[i]}'
    `);

    if (applicationResult.length === 0) continue;

    const application = applicationResult[0];

    // Only create chats for accepted or completed applications
    if (
      application.status === "accepted" ||
      application.status === "completed"
    ) {
      const workerId = application.workerId;
      const companyId = application.companyId;
      const companyUserId = application.companyUserId;
      const jobId = application.jobId;

      // Create chat
      const unreadWorkerCount = Math.floor(Math.random() * 5);
      const unreadCompanyCount = Math.floor(Math.random() * 5);

      const chatResult = await queryRunner.query(`
        INSERT INTO chats (
          id, "workerId", "companyId", "jobId", "unreadWorkerCount", "unreadCompanyCount"
        ) VALUES (
          uuid_generate_v4(), '${workerId}', '${companyId}', '${jobId}', ${unreadWorkerCount}, ${unreadCompanyCount}
        ) RETURNING id;
      `);

      const chatId = chatResult[0].id;

      // Create chat messages (3-10 messages per chat)
      const numMessages = 3 + Math.floor(Math.random() * 8);

      const messageTemplates = [
        "Hi there! I''m interested in discussing the job details.",
        "When would you like me to start?",
        "What specific skills are you looking for?",
        "I''m available to start immediately.",
        "Do you have any specific requirements for this job?",
        "I have a question about the job location.",
        "Can you provide more details about the job?",
        "I''m looking forward to working with you!",
        "What time should I arrive on the first day?",
        "Do I need to bring any specific tools or equipment?",
        "Thanks for the opportunity!",
        "I have previous experience with similar jobs.",
        "Is there parking available at the job site?",
        "How many other workers will be on site?",
        "What''s the dress code for this job?",
      ];

      for (let j = 0; j < numMessages; j++) {
        // Determine sender
        const isWorkerMessage = j % 2 === 0;
        const senderId = isWorkerMessage ? workerId : companyUserId;
        const senderType = isWorkerMessage ? "worker" : "company";

        // Select message
        const messageIndex = Math.floor(
          Math.random() * messageTemplates.length
        );
        const message = messageTemplates[messageIndex];

        // Create message
        const isRead = Math.random() < 0.7;
        const randomDays = Math.floor(Math.random() * 5);
        const randomHours = Math.floor(Math.random() * 24);

        await queryRunner.query(`
          INSERT INTO chat_messages (
            id, "chatId", "senderId", "senderType", message, "isRead", "createdAt"
          ) VALUES (
            uuid_generate_v4(), '${chatId}', '${senderId}', '${senderType}', '${message}',
            ${isRead}, NOW() - INTERVAL '${randomDays} days ${randomHours} hours'
          );
        `);
      }
    }
  }

  // ==================== NOTIFICATIONS ====================
  console.log("Creating notifications...");

  // Create notifications for all users
  const allUserIds = [...adminIds, ...companyUserIds, ...workerIds];

  for (let i = 0; i < allUserIds.length; i++) {
    const userId = allUserIds[i];

    // Create 3-8 notifications per user
    const numNotifications = 3 + Math.floor(Math.random() * 6);

    const notificationTitles = [
      "New job application",
      "Application status updated",
      "New message received",
      "Payment processed",
      "Document verification complete",
      "Job completed",
      "New job posted in your area",
      "Profile verification required",
      "Rating received",
      "Dispute update",
    ];

    const notificationTypes = [
      "application",
      "job",
      "chat",
      "payout",
      "document",
      "job",
      "job",
      "verification",
      "rating",
      "dispute",
    ];

    for (let j = 0; j < numNotifications; j++) {
      const titleIndex = Math.floor(Math.random() * notificationTitles.length);
      const title = notificationTitles[titleIndex];
      const type = notificationTypes[titleIndex];
      const isRead = Math.random() < 0.6;

      const message = `This is a notification about ${type}. Please check your account for details.`;
      const isActionRequired = Math.random() < 0.3;

      await queryRunner.query(`
        INSERT INTO notifications (
          id, "userId", title, message, type, "isRead", "readAt", "isActionRequired"
        ) VALUES (
          uuid_generate_v4(), '${userId}', '${title}',
          '${message}', '${type}', ${isRead}, ${
        isRead ? "NOW()" : "NULL"
      }, ${isActionRequired}
        );
      `);
    }
  }

  // ==================== BADGES ====================
  console.log("Creating badges...");

  // Create badge definitions
  const badgeNames = [
    "First Job Completed",
    "5 Jobs Completed",
    "10 Jobs Completed",
    "Perfect Attendance",
    "Top Rated Worker",
    "Quick Responder",
    "Verification Champion",
    "Reliable Worker",
    "Job Explorer",
    "Veteran Worker",
  ];

  const badgeDescriptions = [
    "Completed your first job successfully.",
    "Completed 5 jobs successfully.",
    "Completed 10 jobs successfully.",
    "Never missed a scheduled job.",
    "Maintained a 5-star rating across multiple jobs.",
    "Responds to job inquiries within 1 hour.",
    "Completed all verification steps.",
    "Completed 10 consecutive jobs without issues.",
    "Applied to jobs in 5 different categories.",
    "Been on the platform for over a year.",
  ];

  const badgeCategories = [
    "jobs",
    "jobs",
    "jobs",
    "jobs",
    "trust",
    "activity",
    "trust",
    "jobs",
    "activity",
    "special",
  ];

  const badgeRarities = [
    "common",
    "common",
    "uncommon",
    "rare",
    "epic",
    "uncommon",
    "rare",
    "uncommon",
    "rare",
    "legendary",
  ];

  const badgeIds = [];

  for (let i = 0; i < badgeNames.length; i++) {
    const iconName = `badge_icon_${i + 1}.png`;
    const requiredValue = (i + 1) * 5;

    const result = await queryRunner.query(`
      INSERT INTO badges (
        id, name, description, icon, category, rarity, "requiredValue", "isActive"
      ) VALUES (
        uuid_generate_v4(), '${badgeNames[i]}', '${badgeDescriptions[i]}',
        '${iconName}', '${badgeCategories[i]}', '${badgeRarities[i]}',
        ${requiredValue}, true
      ) RETURNING id;
    `);

    badgeIds.push(result[0].id);
  }

  // Assign badges to some workers
  for (let i = 0; i < workerIds.length; i++) {
    const workerId = workerIds[i];

    // Determine how many badges this worker has earned (0-5)
    const numBadges = Math.floor(Math.random() * 6);

    // Create a set to track which badges this worker has already earned
    const earnedBadges = new Set();

    for (let j = 0; j < numBadges; j++) {
      // Find a badge this worker hasn't earned yet
      let badgeIndex: number;
      do {
        badgeIndex = Math.floor(Math.random() * badgeIds.length);
      } while (earnedBadges.has(badgeIndex));

      earnedBadges.add(badgeIndex);
      const badgeId = badgeIds[badgeIndex];

      // Create user badge
      const randomDays = Math.floor(Math.random() * 30);

      await queryRunner.query(`
        INSERT INTO user_badges (
          id, "userId", "badgeId", "awardedAt"
        ) VALUES (
          uuid_generate_v4(), '${workerId}', '${badgeId}', NOW() - INTERVAL '${randomDays} days'
        );
      `);
    }
  }

  console.log("Seed data creation complete!");
};

/**
 * Migration to remove test data
 */
export const down = async (queryRunner: QueryRunner): Promise<void> => {
  console.log("Removing all test data...");

  // Delete all test data in the correct order to avoid foreign key constraints
  await queryRunner.query(`DELETE FROM user_badges;`);
  await queryRunner.query(`DELETE FROM badges;`);

  await queryRunner.query(`DELETE FROM notifications;`);

  await queryRunner.query(`DELETE FROM chat_messages;`);
  await queryRunner.query(`DELETE FROM chats;`);

  await queryRunner.query(`DELETE FROM favorites;`);

  await queryRunner.query(`DELETE FROM ratings;`);

  await queryRunner.query(`DELETE FROM disputes;`);

  await queryRunner.query(`DELETE FROM payouts;`);

  await queryRunner.query(`DELETE FROM applications;`);

  await queryRunner.query(`DELETE FROM jobs;`);

  await queryRunner.query(`DELETE FROM trust_score_logs;`);

  await queryRunner.query(`DELETE FROM documents;`);

  await queryRunner.query(`DELETE FROM companies;`);

  // Delete all test users
  await queryRunner.query(`
    DELETE FROM users
    WHERE email LIKE '<EMAIL>'
    OR email LIKE '<EMAIL>'
    OR email IN ('<EMAIL>', '<EMAIL>', '<EMAIL>');
  `);

  console.log("All test data removed successfully.");
};
