import { QueryRunner } from "typeorm";

/**
 * Migration to create the initial database schema
 */
export const up = async (queryRunner: QueryRunner): Promise<void> => {
  // Enable UUID extension
  await queryRunner.query(`
    CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
  `);

  // Create users table
  await queryRunner.query(`
    CREATE TABLE IF NOT EXISTS users (
      id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
      email VARCHAR(255) UNIQUE,
      phone VARCHAR(20) UNIQUE,
      password VARCHAR(255) NOT NULL,
      "fullName" VARCHAR(255) NOT NULL,
      role VARCHAR(20) NOT NULL CHECK (role IN ('worker', 'company', 'admin')),
      "dateOfBirth" DATE,
      address TEXT,
      city VARCHAR(100),
      state VARCHAR(100),
      "postalCode" VARCHAR(20),
      country VARCHAR(100),
      "profilePicture" TEXT,
      "trustScore" INTEGER DEFAULT 50 CHECK ("trustScore" >= 0 AND "trustScore" <= 100),
      "isKycVerified" BOOLEAN DEFAULT FALSE,
      "isActive" BOOLEAN DEFAULT TRUE,
      "isBanned" BOOLEAN DEFAULT FALSE,
      "banReason" TEXT,
      name VARCHAR(255),
      bio TEXT,
      "createdAt" TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
      "updatedAt" TIMESTAMP WITH TIME ZONE DEFAULT NOW()
    );
  `);

  // Create companies table
  await queryRunner.query(`
    CREATE TABLE IF NOT EXISTS companies (
      id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
      "userId" UUID UNIQUE NOT NULL REFERENCES users(id) ON DELETE CASCADE,
      name VARCHAR(255) NOT NULL,
      website VARCHAR(255),
      description TEXT,
      "logoUrl" TEXT,
      industry VARCHAR(100),
      size VARCHAR(50),
      "foundedYear" INTEGER,
      "headquartersAddress" TEXT,
      "headquartersCity" VARCHAR(100),
      "headquartersState" VARCHAR(100),
      "headquartersPostalCode" VARCHAR(20),
      "headquartersCountry" VARCHAR(100),
      "isVerified" BOOLEAN DEFAULT FALSE,
      "verificationDate" TIMESTAMP WITH TIME ZONE,
      "createdAt" TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
      "updatedAt" TIMESTAMP WITH TIME ZONE DEFAULT NOW()
    );
  `);

  // Create jobs table
  await queryRunner.query(`
    CREATE TABLE IF NOT EXISTS jobs (
      id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
      "companyId" UUID NOT NULL REFERENCES companies(id) ON DELETE CASCADE,
      title VARCHAR(255) NOT NULL,
      description TEXT NOT NULL,
      location VARCHAR(255) NOT NULL,
      city VARCHAR(100) NOT NULL,
      state VARCHAR(100),
      country VARCHAR(100) NOT NULL,
      "postalCode" VARCHAR(20),
      latitude DECIMAL(10, 8),
      longitude DECIMAL(11, 8),
      "startDateTime" TIMESTAMP WITH TIME ZONE NOT NULL,
      "endDateTime" TIMESTAMP WITH TIME ZONE NOT NULL,
      "hourlyRate" DECIMAL(10, 2) NOT NULL,
      "estimatedHours" INTEGER NOT NULL,
      "trustScoreRequired" INTEGER DEFAULT 0 CHECK ("trustScoreRequired" >= 0 AND "trustScoreRequired" <= 100),
      "isEmergencyJob" BOOLEAN DEFAULT FALSE,
      "requiresLaptop" BOOLEAN DEFAULT FALSE,
      "requiresSmartphone" BOOLEAN DEFAULT FALSE,
      status VARCHAR(20) DEFAULT 'open' CHECK (status IN ('open', 'in_progress', 'completed', 'cancelled')),
      "maxPositions" INTEGER DEFAULT 1,
      "filledPositions" INTEGER DEFAULT 0,
      "createdAt" TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
      "updatedAt" TIMESTAMP WITH TIME ZONE DEFAULT NOW()
    );
  `);

  // Create applications table
  await queryRunner.query(`
    CREATE TABLE IF NOT EXISTS applications (
      id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
      "jobId" UUID NOT NULL REFERENCES jobs(id) ON DELETE CASCADE,
      "workerId" UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
      status VARCHAR(20) DEFAULT 'pending' CHECK (status IN ('pending', 'accepted', 'rejected', 'cancelled', 'completed', 'no_show')),
      "coverLetter" TEXT,
      "rejectionReason" TEXT,
      "cancellationReason" TEXT,
      "isEmergencyJob" BOOLEAN DEFAULT FALSE,
      "createdAt" TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
      "updatedAt" TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
      UNIQUE("jobId", "workerId")
    );
  `);

  // Create documents table
  await queryRunner.query(`
    CREATE TABLE IF NOT EXISTS documents (
      id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
      "userId" UUID REFERENCES users(id) ON DELETE CASCADE,
      "companyId" UUID REFERENCES companies(id) ON DELETE CASCADE,
      "documentType" VARCHAR(50) NOT NULL,
      "documentUrl" TEXT NOT NULL,
      "documentNumber" VARCHAR(100),
      "verificationStatus" VARCHAR(20) DEFAULT 'pending',
      "verifiedBy" UUID REFERENCES users(id),
      "verifiedAt" TIMESTAMP WITH TIME ZONE,
      "rejectionReason" TEXT,
      "createdAt" TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
      "updatedAt" TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
      CHECK (("userId" IS NOT NULL AND "companyId" IS NULL) OR ("userId" IS NULL AND "companyId" IS NOT NULL))
    );
  `);

  // Create ratings table
  await queryRunner.query(`
    CREATE TABLE IF NOT EXISTS ratings (
      id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
      "ratedById" UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
      "ratedUserId" UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
      "jobId" UUID NOT NULL REFERENCES jobs(id) ON DELETE CASCADE,
      stars INTEGER NOT NULL CHECK (stars >= 1 AND stars <= 5),
      comment TEXT,
      "isAnonymous" BOOLEAN DEFAULT FALSE,
      "createdAt" TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
      "updatedAt" TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
      UNIQUE("ratedById", "ratedUserId", "jobId")
    );
  `);

  // Create payouts table
  await queryRunner.query(`
    CREATE TABLE IF NOT EXISTS payouts (
      id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
      "workerId" UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
      "jobId" UUID NOT NULL REFERENCES jobs(id) ON DELETE CASCADE,
      "grossAmount" DECIMAL(10, 2) NOT NULL,
      "commission" DECIMAL(10, 2) NOT NULL,
      "netAmount" DECIMAL(10, 2) NOT NULL,
      "platformFee" DECIMAL(10, 2) NOT NULL,
      "taxAmount" DECIMAL(10, 2) NOT NULL,
      status VARCHAR(20) DEFAULT 'pending',
      "paymentMethod" VARCHAR(50),
      "transactionId" VARCHAR(255),
      "paymentDate" TIMESTAMP WITH TIME ZONE,
      "createdAt" TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
      "updatedAt" TIMESTAMP WITH TIME ZONE DEFAULT NOW()
    );
  `);

  // Create disputes table
  await queryRunner.query(`
    CREATE TABLE IF NOT EXISTS disputes (
      id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
      "raisedById" UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
      "againstId" UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
      "jobId" UUID NOT NULL REFERENCES jobs(id) ON DELETE CASCADE,
      reason TEXT NOT NULL,
      description TEXT,
      status VARCHAR(20) DEFAULT 'open',
      "resolvedById" UUID REFERENCES users(id),
      "resolvedAt" TIMESTAMP WITH TIME ZONE,
      resolution TEXT,
      "createdAt" TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
      "updatedAt" TIMESTAMP WITH TIME ZONE DEFAULT NOW()
    );
  `);

  // Create favorites table
  await queryRunner.query(`
    CREATE TABLE IF NOT EXISTS favorites (
      id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
      "workerId" UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
      "jobId" UUID NOT NULL REFERENCES jobs(id) ON DELETE CASCADE,
      "createdAt" TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
      UNIQUE("workerId", "jobId")
    );
  `);

  // Create trust_score_logs table
  await queryRunner.query(`
    CREATE TABLE IF NOT EXISTS trust_score_logs (
      id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
      "userId" UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
      "previousScore" INTEGER NOT NULL,
      "newScore" INTEGER NOT NULL,
      change INTEGER NOT NULL,
      reason TEXT NOT NULL,
      "relatedEntityType" VARCHAR(50),
      "adminId" UUID REFERENCES users(id),
      "createdAt" TIMESTAMP WITH TIME ZONE DEFAULT NOW()
    );
  `);

  // Create chats table
  await queryRunner.query(`
    CREATE TABLE IF NOT EXISTS chats (
      id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
      "workerId" UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
      "companyId" UUID NOT NULL REFERENCES companies(id) ON DELETE CASCADE,
      "jobId" UUID NOT NULL REFERENCES jobs(id) ON DELETE CASCADE,
      "unreadWorkerCount" INTEGER DEFAULT 0,
      "unreadCompanyCount" INTEGER DEFAULT 0,
      "createdAt" TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
      "updatedAt" TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
      UNIQUE("workerId", "companyId", "jobId")
    );
  `);

  // Create chat_messages table
  await queryRunner.query(`
    CREATE TABLE IF NOT EXISTS chat_messages (
      id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
      "chatId" UUID NOT NULL REFERENCES chats(id) ON DELETE CASCADE,
      "senderId" UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
      "senderType" VARCHAR(20) NOT NULL,
      message TEXT NOT NULL,
      "isRead" BOOLEAN DEFAULT FALSE,
      "createdAt" TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
      "updatedAt" TIMESTAMP WITH TIME ZONE DEFAULT NOW()
    );
  `);

  // Create notifications table
  await queryRunner.query(`
    CREATE TABLE IF NOT EXISTS notifications (
      id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
      "userId" UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
      title VARCHAR(255) NOT NULL,
      message TEXT NOT NULL,
      type VARCHAR(50) NOT NULL,
      "isRead" BOOLEAN DEFAULT FALSE,
      "readAt" TIMESTAMP WITH TIME ZONE,
      "isActionRequired" BOOLEAN DEFAULT FALSE,
      "createdAt" TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
      "updatedAt" TIMESTAMP WITH TIME ZONE DEFAULT NOW()
    );
  `);

  // Create badges table
  await queryRunner.query(`
    CREATE TABLE IF NOT EXISTS badges (
      id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
      name VARCHAR(255) NOT NULL,
      description TEXT NOT NULL,
      icon VARCHAR(255) NOT NULL,
      category VARCHAR(50) NOT NULL,
      rarity VARCHAR(50) NOT NULL,
      "requiredValue" INTEGER,
      "isActive" BOOLEAN DEFAULT TRUE,
      "createdAt" TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
      "updatedAt" TIMESTAMP WITH TIME ZONE DEFAULT NOW()
    );
  `);

  // Create user_badges table
  await queryRunner.query(`
    CREATE TABLE IF NOT EXISTS user_badges (
      id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
      "userId" UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
      "badgeId" UUID NOT NULL REFERENCES badges(id) ON DELETE CASCADE,
      "awardedAt" TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
      "createdAt" TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
      "updatedAt" TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
      UNIQUE("userId", "badgeId")
    );
  `);
};

/**
 * Migration to revert the initial database schema
 */
export const down = async (queryRunner: QueryRunner): Promise<void> => {
  // Drop tables in reverse order
  await queryRunner.query(`DROP TABLE IF EXISTS user_badges;`);
  await queryRunner.query(`DROP TABLE IF EXISTS badges;`);
  await queryRunner.query(`DROP TABLE IF EXISTS notifications;`);
  await queryRunner.query(`DROP TABLE IF EXISTS chat_messages;`);
  await queryRunner.query(`DROP TABLE IF EXISTS chats;`);
  await queryRunner.query(`DROP TABLE IF EXISTS favorites;`);
  await queryRunner.query(`DROP TABLE IF EXISTS ratings;`);
  await queryRunner.query(`DROP TABLE IF EXISTS disputes;`);
  await queryRunner.query(`DROP TABLE IF EXISTS payouts;`);
  await queryRunner.query(`DROP TABLE IF EXISTS applications;`);
  await queryRunner.query(`DROP TABLE IF EXISTS trust_score_logs;`);
  await queryRunner.query(`DROP TABLE IF EXISTS documents;`);
  await queryRunner.query(`DROP TABLE IF EXISTS jobs;`);
  await queryRunner.query(`DROP TABLE IF EXISTS companies;`);
  await queryRunner.query(`DROP TABLE IF EXISTS users;`);
};
