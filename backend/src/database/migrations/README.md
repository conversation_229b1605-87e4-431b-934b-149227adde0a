# Database Migrations

This directory contains the database migration system for the Job Platform application. The migration system follows a db-migrate style approach with versioned migrations, tracking of applied migrations, and support for up/down migrations.

## Migration Structure

Each migration file should export two functions:

- `up`: Function to apply the migration
- `down`: Function to revert the migration

Example migration file:

```typescript
import { QueryRunner } from "typeorm";

export const up = async (queryRunner: QueryRunner): Promise<void> => {
  // Migration code to apply changes
  await queryRunner.query(`
    CREATE TABLE example (
      id SERIAL PRIMARY KEY,
      name VARCHAR(255) NOT NULL
    );
  `);
};

export const down = async (queryRunner: QueryRunner): Promise<void> => {
  // Migration code to revert changes
  await queryRunner.query(`DROP TABLE IF EXISTS example;`);
};
```

## Migration File Naming

Migration files should be named using the following format:

```
YYYYMMDDHHMMSS_description_of_migration.ts
```

For example:

```
20240101000000_create_initial_schema.ts
```

The timestamp at the beginning of the filename is used to order migrations and ensure they are applied in the correct sequence.

## Running Migrations

The following npm scripts are available for working with migrations:

- `npm run migration:run`: Run all pending migrations
- `npm run migration:revert`: Revert the last applied migration
- `npm run migration:status`: Check the status of migrations

## Creating a New Migration

To create a new migration:

1. Create a new file in the `src/database/migrations/files` directory with the naming convention described above
2. Implement the `up` and `down` functions
3. Run the migration using `npm run migration:run`

## Best Practices

1. Always include both `up` and `down` functions in your migrations
2. Keep migrations small and focused on a single change
3. Test migrations thoroughly before applying them to production
4. Use transactions to ensure migrations are atomic
5. Document complex migrations with comments
6. Avoid modifying existing migrations that have already been applied
7. Use SQL queries directly for better control and performance
8. Include validation and error handling in your migrations
