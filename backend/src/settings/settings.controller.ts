import {
  <PERSON>,
  Get,
  Body,
  Patch,
  Param,
  UseGuards,
  Request,
  Inject,
} from "@nestjs/common";
import { SettingsService } from "./settings.service";
import { JwtAuthGuard } from "../auth/guards/jwt-auth.guard";
import { RolesGuard } from "../auth/guards/roles.guard";
import { Roles } from "../auth/decorators/roles.decorator";
import { UserRole } from "../common/enums/user-role.enum";

@Controller("admin/settings")
@UseGuards(JwtAuthGuard, RolesGuard)
@Roles(UserRole.ADMIN)
export class SettingsController {
  constructor(
    @Inject(SettingsService) private readonly settingsService: SettingsService
  ) {}

  @Get()
  findAll() {
    return this.settingsService.findAll();
  }

  @Get(":key")
  findOne(@Param("key") key: string) {
    return this.settingsService.findByKey(key);
  }

  @Patch(":key")
  update(
    @Param("key") key: string,
    @Body("value") value: string,
    @Request() req
  ) {
    return this.settingsService.update(key, value, req.user.userId);
  }
}
