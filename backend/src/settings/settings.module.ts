import { Module } from "@nestjs/common"
import { TypeOrmModule } from "@nestjs/typeorm"
import { SettingsService } from "./settings.service"
import { SettingsController } from "./settings.controller"
import { Setting } from "./entities/setting.entity"
import { ActivityLogModule } from "../activity-log/activity-log.module"

@Module({
  imports: [TypeOrmModule.forFeature([Setting]), ActivityLogModule],
  controllers: [SettingsController],
  providers: [SettingsService],
  exports: [SettingsService],
})
export class SettingsModule {}
