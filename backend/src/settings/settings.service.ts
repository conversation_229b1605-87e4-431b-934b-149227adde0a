import { Inject, Injectable, NotFoundException } from "@nestjs/common"
import { InjectRepository } from "@nestjs/typeorm"
import type { Repository } from "typeorm"
import { Setting } from "./entities/setting.entity"
import { ActivityLogService } from "../activity-log/activity-log.service"

@Injectable()
export class SettingsService {
  constructor(
    @InjectRepository(Setting)
    private settingsRepository: Repository<Setting>,
    @Inject(ActivityLogService)
    private readonly activityLogService: ActivityLogService,
  ) {}

  async onModuleInit() {
    // Initialize default settings if they don't exist
    const defaultSettings = [
      {
        key: "TRUST_SCORE_INITIAL",
        value: "50",
        description: "Initial trust score for new users",
        type: "number",
        isSystem: true,
      },
      {
        key: "TRUST_SCORE_EMERGENCY_THRESHOLD",
        value: "40",
        description: "Trust score threshold for emergency jobs",
        type: "number",
        isSystem: true,
      },
      {
        key: "TRUST_SCORE_EMERGENCY_JOB_COMPLETION",
        value: "10",
        description: "Trust score increase for completing an emergency job",
        type: "number",
        isSystem: true,
      },
      {
        key: "TRUST_SCORE_STANDARD_JOB_COMPLETION",
        value: "5",
        description: "Trust score increase for completing a standard job",
        type: "number",
        isSystem: true,
      },
      {
        key: "TRUST_SCORE_LAST_MINUTE_CANCELLATION",
        value: "-15",
        description: "Trust score decrease for last-minute job cancellation",
        type: "number",
        isSystem: true,
      },
      {
        key: "TRUST_SCORE_NO_SHOW",
        value: "-25",
        description: "Trust score decrease for not showing up to a job",
        type: "number",
        isSystem: true,
      },
      {
        key: "TRUST_SCORE_LOW_RATING",
        value: "-10",
        description: "Trust score decrease for receiving a low rating",
        type: "number",
        isSystem: true,
      },
      {
        key: "PLATFORM_COMMISSION_RATE",
        value: "0.10",
        description: "Platform commission rate (percentage)",
        type: "number",
        isSystem: true,
      },
      {
        key: "PLATFORM_COMMISSION_FIXED",
        value: "200",
        description: "Platform fixed commission amount",
        type: "number",
        isSystem: true,
      },
    ]

    for (const setting of defaultSettings) {
      const existingSetting = await this.settingsRepository.findOne({
        where: { key: setting.key },
      })

      if (!existingSetting) {
        await this.settingsRepository.save(setting)
      }
    }
  }

  async findAll(): Promise<Setting[]> {
    return this.settingsRepository.find()
  }

  async findByKey(key: string): Promise<Setting> {
    const setting = await this.settingsRepository.findOne({
      where: { key },
    })

    if (!setting) {
      throw new NotFoundException(`Setting with key ${key} not found`)
    }

    return setting
  }

  async getValue(key: string): Promise<string | number | boolean | object> {
    const setting = await this.findByKey(key)

    switch (setting.type) {
      case "number":
        return Number.parseFloat(setting.value)
      case "boolean":
        return setting.value === "true"
      case "json":
        return JSON.parse(setting.value)
      default:
        return setting.value
    }
  }

  async update(key: string, value: string, userId: string): Promise<Setting> {
    const setting = await this.findByKey(key)

    // Don't allow changing system settings type
    const oldValue = setting.value
    setting.value = value

    const updatedSetting = await this.settingsRepository.save(setting)

    await this.activityLogService.logActivity({
      userId,
      action: "setting_updated",
      description: `Updated setting: ${key} from ${oldValue} to ${value}`,
      entityId: setting.id,
      entityType: "setting",
      metadata: { oldValue, newValue: value },
    })

    return updatedSetting
  }
}
