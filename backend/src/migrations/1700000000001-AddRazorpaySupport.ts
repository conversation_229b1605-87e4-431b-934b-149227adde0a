import { MigrationInterface, QueryRunner } from "typeorm";

export class AddRazorpaySupport1700000000001 implements MigrationInterface {
  name = "AddRazorpaySupport1700000000001";

  public async up(queryRunner: QueryRunner): Promise<void> {
    // Update payment_method enum to include razorpay
    await queryRunner.query(`
      ALTER TYPE "payment_method_enum" 
      ADD VALUE IF NOT EXISTS 'razorpay'
    `);

    // Update payment_status enum to include refunded
    await queryRunner.query(`
      ALTER TYPE "payment_status_enum" 
      ADD VALUE IF NOT EXISTS 'refunded'
    `);

    // Add razorpay_order_id column to escrow_accounts table
    await queryRunner.query(`
      ALTER TABLE "escrow_accounts" 
      ADD COLUMN IF NOT EXISTS "razorpay_order_id" character varying
    `);

    // Add razorpay_payment_id column to escrow_accounts table
    await queryRunner.query(`
      ALTER TABLE "escrow_accounts" 
      ADD COLUMN IF NOT EXISTS "razorpay_payment_id" character varying
    `);

    // Add razorpay_signature column to escrow_accounts table
    await queryRunner.query(`
      ALTER TABLE "escrow_accounts" 
      ADD COLUMN IF NOT EXISTS "razorpay_signature" character varying
    `);

    // Add razorpay_order_id column to payouts table
    await queryRunner.query(`
      ALTER TABLE "payouts" 
      ADD COLUMN IF NOT EXISTS "razorpay_order_id" character varying
    `);

    // Add razorpay_payment_id column to payouts table
    await queryRunner.query(`
      ALTER TABLE "payouts" 
      ADD COLUMN IF NOT EXISTS "razorpay_payment_id" character varying
    `);

    // Add razorpay_signature column to payouts table
    await queryRunner.query(`
      ALTER TABLE "payouts" 
      ADD COLUMN IF NOT EXISTS "razorpay_signature" character varying
    `);

    // Create payment_transactions table for tracking all payment transactions
    await queryRunner.query(`
      CREATE TABLE IF NOT EXISTS "payment_transactions" (
        "id" uuid NOT NULL DEFAULT uuid_generate_v4(),
        "transaction_id" character varying NOT NULL,
        "payment_method" "payment_method_enum" NOT NULL,
        "payment_status" "payment_status_enum" NOT NULL DEFAULT 'pending',
        "amount" numeric(10,2) NOT NULL,
        "currency" character varying NOT NULL DEFAULT 'INR',
        "description" text,
        "customer_email" character varying,
        "customer_phone" character varying,
        "gateway_order_id" character varying,
        "gateway_payment_id" character varying,
        "gateway_signature" character varying,
        "gateway_response" jsonb,
        "metadata" jsonb,
        "job_id" uuid,
        "escrow_id" uuid,
        "payout_id" uuid,
        "user_id" uuid,
        "company_id" uuid,
        "created_at" TIMESTAMP NOT NULL DEFAULT now(),
        "updated_at" TIMESTAMP NOT NULL DEFAULT now(),
        "processed_at" TIMESTAMP,
        "failed_at" TIMESTAMP,
        "refunded_at" TIMESTAMP,
        CONSTRAINT "PK_payment_transactions" PRIMARY KEY ("id"),
        CONSTRAINT "UQ_payment_transactions_transaction_id" UNIQUE ("transaction_id")
      )
    `);

    // Create indexes for payment_transactions table
    await queryRunner.query(`
      CREATE INDEX IF NOT EXISTS "IDX_payment_transactions_payment_method" 
      ON "payment_transactions" ("payment_method")
    `);

    await queryRunner.query(`
      CREATE INDEX IF NOT EXISTS "IDX_payment_transactions_payment_status" 
      ON "payment_transactions" ("payment_status")
    `);

    await queryRunner.query(`
      CREATE INDEX IF NOT EXISTS "IDX_payment_transactions_job_id" 
      ON "payment_transactions" ("job_id")
    `);

    await queryRunner.query(`
      CREATE INDEX IF NOT EXISTS "IDX_payment_transactions_user_id" 
      ON "payment_transactions" ("user_id")
    `);

    await queryRunner.query(`
      CREATE INDEX IF NOT EXISTS "IDX_payment_transactions_company_id" 
      ON "payment_transactions" ("company_id")
    `);

    await queryRunner.query(`
      CREATE INDEX IF NOT EXISTS "IDX_payment_transactions_created_at" 
      ON "payment_transactions" ("created_at")
    `);

    // Add foreign key constraints
    await queryRunner.query(`
      ALTER TABLE "payment_transactions" 
      ADD CONSTRAINT "FK_payment_transactions_job_id" 
      FOREIGN KEY ("job_id") REFERENCES "jobs"("id") ON DELETE SET NULL
    `);

    await queryRunner.query(`
      ALTER TABLE "payment_transactions" 
      ADD CONSTRAINT "FK_payment_transactions_user_id" 
      FOREIGN KEY ("user_id") REFERENCES "users"("id") ON DELETE SET NULL
    `);

    await queryRunner.query(`
      ALTER TABLE "payment_transactions" 
      ADD CONSTRAINT "FK_payment_transactions_company_id" 
      FOREIGN KEY ("company_id") REFERENCES "companies"("id") ON DELETE SET NULL
    `);

    // Create webhook_events table for tracking webhook events
    await queryRunner.query(`
      CREATE TABLE IF NOT EXISTS "webhook_events" (
        "id" uuid NOT NULL DEFAULT uuid_generate_v4(),
        "provider" character varying NOT NULL,
        "event_type" character varying NOT NULL,
        "event_id" character varying NOT NULL,
        "payload" jsonb NOT NULL,
        "signature" character varying,
        "processed" boolean NOT NULL DEFAULT false,
        "processed_at" TIMESTAMP,
        "error_message" text,
        "retry_count" integer NOT NULL DEFAULT 0,
        "created_at" TIMESTAMP NOT NULL DEFAULT now(),
        "updated_at" TIMESTAMP NOT NULL DEFAULT now(),
        CONSTRAINT "PK_webhook_events" PRIMARY KEY ("id"),
        CONSTRAINT "UQ_webhook_events_provider_event_id" UNIQUE ("provider", "event_id")
      )
    `);

    // Create indexes for webhook_events table
    await queryRunner.query(`
      CREATE INDEX IF NOT EXISTS "IDX_webhook_events_provider" 
      ON "webhook_events" ("provider")
    `);

    await queryRunner.query(`
      CREATE INDEX IF NOT EXISTS "IDX_webhook_events_event_type" 
      ON "webhook_events" ("event_type")
    `);

    await queryRunner.query(`
      CREATE INDEX IF NOT EXISTS "IDX_webhook_events_processed" 
      ON "webhook_events" ("processed")
    `);

    await queryRunner.query(`
      CREATE INDEX IF NOT EXISTS "IDX_webhook_events_created_at" 
      ON "webhook_events" ("created_at")
    `);

    // Update existing records to set default payment method if null
    await queryRunner.query(`
      UPDATE "escrow_accounts" 
      SET "payment_method" = 'stripe' 
      WHERE "payment_method" IS NULL
    `);

    await queryRunner.query(`
      UPDATE "payouts" 
      SET "payment_method" = 'stripe' 
      WHERE "payment_method" IS NULL
    `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    // Drop foreign key constraints
    await queryRunner.query(`
      ALTER TABLE "payment_transactions" 
      DROP CONSTRAINT IF EXISTS "FK_payment_transactions_job_id"
    `);

    await queryRunner.query(`
      ALTER TABLE "payment_transactions" 
      DROP CONSTRAINT IF EXISTS "FK_payment_transactions_user_id"
    `);

    await queryRunner.query(`
      ALTER TABLE "payment_transactions" 
      DROP CONSTRAINT IF EXISTS "FK_payment_transactions_company_id"
    `);

    // Drop tables
    await queryRunner.query(`DROP TABLE IF EXISTS "webhook_events"`);
    await queryRunner.query(`DROP TABLE IF EXISTS "payment_transactions"`);

    // Remove columns from existing tables
    await queryRunner.query(`
      ALTER TABLE "escrow_accounts" 
      DROP COLUMN IF EXISTS "razorpay_order_id"
    `);

    await queryRunner.query(`
      ALTER TABLE "escrow_accounts" 
      DROP COLUMN IF EXISTS "razorpay_payment_id"
    `);

    await queryRunner.query(`
      ALTER TABLE "escrow_accounts" 
      DROP COLUMN IF EXISTS "razorpay_signature"
    `);

    await queryRunner.query(`
      ALTER TABLE "payouts" 
      DROP COLUMN IF EXISTS "razorpay_order_id"
    `);

    await queryRunner.query(`
      ALTER TABLE "payouts" 
      DROP COLUMN IF EXISTS "razorpay_payment_id"
    `);

    await queryRunner.query(`
      ALTER TABLE "payouts" 
      DROP COLUMN IF EXISTS "razorpay_signature"
    `);

    // Note: We cannot remove enum values in PostgreSQL easily
    // They would need to be recreated entirely, which is risky
    // So we leave the enum values in place
  }
}
