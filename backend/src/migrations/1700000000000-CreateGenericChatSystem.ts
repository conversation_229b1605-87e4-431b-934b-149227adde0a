import { MigrationInterface, QueryRunner } from "typeorm";

export class CreateGenericChatSystem1700000000000 implements MigrationInterface {
  name = "CreateGenericChatSystem1700000000000";

  public async up(queryRunner: QueryRunner): Promise<void> {
    // Drop old chat table if it exists
    await queryRunner.query(`DROP TABLE IF EXISTS "chats" CASCADE`);

    // Create generic_chats table
    await queryRunner.query(`
      CREATE TABLE "generic_chats" (
        "id" uuid NOT NULL DEFAULT uuid_generate_v4(),
        "type" character varying NOT NULL DEFAULT 'general',
        "title" character varying,
        "description" text,
        "contextId" character varying,
        "contextType" character varying,
        "metadata" jsonb,
        "isActive" boolean NOT NULL DEFAULT true,
        "createdBy" character varying,
        "lastMessageAt" TIMESTAMP,
        "createdAt" TIMESTAMP NOT NULL DEFAULT now(),
        "updatedAt" TIMESTAMP NOT NULL DEFAULT now(),
        CONSTRAINT "PK_generic_chats" PRIMARY KEY ("id")
      )
    `);

    // Create chat_participants table
    await queryRunner.query(`
      CREATE TABLE "chat_participants" (
        "id" uuid NOT NULL DEFAULT uuid_generate_v4(),
        "chatId" uuid NOT NULL,
        "userId" uuid NOT NULL,
        "role" character varying NOT NULL DEFAULT 'member',
        "unreadCount" integer NOT NULL DEFAULT 0,
        "isActive" boolean NOT NULL DEFAULT true,
        "joinedAt" TIMESTAMP,
        "leftAt" TIMESTAMP,
        "lastReadAt" TIMESTAMP,
        "createdAt" TIMESTAMP NOT NULL DEFAULT now(),
        "updatedAt" TIMESTAMP NOT NULL DEFAULT now(),
        CONSTRAINT "PK_chat_participants" PRIMARY KEY ("id"),
        CONSTRAINT "UQ_chat_participants_chatId_userId" UNIQUE ("chatId", "userId")
      )
    `);

    // Drop old chat_messages table and recreate with new structure
    await queryRunner.query(`DROP TABLE IF EXISTS "chat_messages" CASCADE`);
    
    await queryRunner.query(`
      CREATE TABLE "chat_messages" (
        "id" uuid NOT NULL DEFAULT uuid_generate_v4(),
        "chatId" uuid NOT NULL,
        "messageType" character varying NOT NULL DEFAULT 'text',
        "message" text NOT NULL,
        "fileUrl" character varying,
        "fileName" character varying,
        "fileSize" integer,
        "fileMimeType" character varying,
        "senderId" uuid NOT NULL,
        "senderType" character varying,
        "status" character varying NOT NULL DEFAULT 'sent',
        "isRead" boolean NOT NULL DEFAULT false,
        "readAt" TIMESTAMP,
        "metadata" jsonb,
        "replyToId" uuid,
        "isEdited" boolean NOT NULL DEFAULT false,
        "editedAt" TIMESTAMP,
        "createdAt" TIMESTAMP NOT NULL DEFAULT now(),
        "updatedAt" TIMESTAMP NOT NULL DEFAULT now(),
        CONSTRAINT "PK_chat_messages" PRIMARY KEY ("id")
      )
    `);

    // Create indexes
    await queryRunner.query(`CREATE INDEX "IDX_generic_chats_type" ON "generic_chats" ("type")`);
    await queryRunner.query(`CREATE INDEX "IDX_generic_chats_contextId" ON "generic_chats" ("contextId")`);
    await queryRunner.query(`CREATE INDEX "IDX_generic_chats_createdBy" ON "generic_chats" ("createdBy")`);
    
    await queryRunner.query(`CREATE INDEX "IDX_chat_participants_chatId" ON "chat_participants" ("chatId")`);
    await queryRunner.query(`CREATE INDEX "IDX_chat_participants_userId" ON "chat_participants" ("userId")`);
    
    await queryRunner.query(`CREATE INDEX "IDX_chat_messages_chatId" ON "chat_messages" ("chatId")`);
    await queryRunner.query(`CREATE INDEX "IDX_chat_messages_senderId" ON "chat_messages" ("senderId")`);

    // Create foreign keys
    await queryRunner.query(`
      ALTER TABLE "chat_participants" 
      ADD CONSTRAINT "FK_chat_participants_chatId" 
      FOREIGN KEY ("chatId") REFERENCES "generic_chats"("id") ON DELETE CASCADE
    `);

    await queryRunner.query(`
      ALTER TABLE "chat_participants" 
      ADD CONSTRAINT "FK_chat_participants_userId" 
      FOREIGN KEY ("userId") REFERENCES "users"("id") ON DELETE CASCADE
    `);

    await queryRunner.query(`
      ALTER TABLE "chat_messages" 
      ADD CONSTRAINT "FK_chat_messages_chatId" 
      FOREIGN KEY ("chatId") REFERENCES "generic_chats"("id") ON DELETE CASCADE
    `);

    await queryRunner.query(`
      ALTER TABLE "chat_messages" 
      ADD CONSTRAINT "FK_chat_messages_senderId" 
      FOREIGN KEY ("senderId") REFERENCES "users"("id") ON DELETE CASCADE
    `);

    await queryRunner.query(`
      ALTER TABLE "chat_messages" 
      ADD CONSTRAINT "FK_chat_messages_replyToId" 
      FOREIGN KEY ("replyToId") REFERENCES "chat_messages"("id") ON DELETE SET NULL
    `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    // Drop foreign keys
    await queryRunner.query(`ALTER TABLE "chat_messages" DROP CONSTRAINT IF EXISTS "FK_chat_messages_replyToId"`);
    await queryRunner.query(`ALTER TABLE "chat_messages" DROP CONSTRAINT IF EXISTS "FK_chat_messages_senderId"`);
    await queryRunner.query(`ALTER TABLE "chat_messages" DROP CONSTRAINT IF EXISTS "FK_chat_messages_chatId"`);
    await queryRunner.query(`ALTER TABLE "chat_participants" DROP CONSTRAINT IF EXISTS "FK_chat_participants_userId"`);
    await queryRunner.query(`ALTER TABLE "chat_participants" DROP CONSTRAINT IF EXISTS "FK_chat_participants_chatId"`);

    // Drop indexes
    await queryRunner.query(`DROP INDEX IF EXISTS "IDX_chat_messages_senderId"`);
    await queryRunner.query(`DROP INDEX IF EXISTS "IDX_chat_messages_chatId"`);
    await queryRunner.query(`DROP INDEX IF EXISTS "IDX_chat_participants_userId"`);
    await queryRunner.query(`DROP INDEX IF EXISTS "IDX_chat_participants_chatId"`);
    await queryRunner.query(`DROP INDEX IF EXISTS "IDX_generic_chats_createdBy"`);
    await queryRunner.query(`DROP INDEX IF EXISTS "IDX_generic_chats_contextId"`);
    await queryRunner.query(`DROP INDEX IF EXISTS "IDX_generic_chats_type"`);

    // Drop tables
    await queryRunner.query(`DROP TABLE IF EXISTS "chat_messages"`);
    await queryRunner.query(`DROP TABLE IF EXISTS "chat_participants"`);
    await queryRunner.query(`DROP TABLE IF EXISTS "generic_chats"`);
  }
}
