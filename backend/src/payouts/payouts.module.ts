import { Module } from "@nestjs/common";
import { TypeOrmModule } from "@nestjs/typeorm";
import { Payout } from "./entities/payout.entity";
import { PayoutsService } from "./payouts.service";
import { PayoutsController } from "./payouts.controller";
import { UsersModule } from "../users/users.module";
import { JobsModule } from "../jobs/jobs.module";
import { NotificationsModule } from "../notifications/notifications.module";
import { PaymentsModule } from "../payments/payments.module";

@Module({
  imports: [
    TypeOrmModule.forFeature([Payout]),
    UsersModule,
    JobsModule,
    NotificationsModule,
    PaymentsModule,
  ],
  providers: [PayoutsService],
  controllers: [PayoutsController],
  exports: [PayoutsService],
})
export class PayoutsModule {}
