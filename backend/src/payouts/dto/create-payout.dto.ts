import { ApiProperty, ApiPropertyOptional } from "@nestjs/swagger"
import { IsString, IsNumber, IsDate, IsOptional, Min, IsUUID, IsEnum } from "class-validator"
import { Type } from "class-transformer"
import { PayoutStatus } from "@shared/types"

export class CreatePayoutDto {
  @ApiProperty({ description: "Worker ID to pay" })
  @IsUUID()
  workerId: string

  @ApiProperty({ description: "Job ID associated with this payout" })
  @IsUUID()
  jobId: string

  @ApiProperty({ description: "Gross amount before deductions" })
  @IsNumber()
  @Min(0)
  grossAmount: number

  @ApiProperty({ description: "Net amount after deductions" })
  @IsNumber()
  @Min(0)
  netAmount: number

  @ApiProperty({ description: "Platform fee amount" })
  @IsNumber()
  @Min(0)
  platformFee: number

  @ApiPropertyOptional({ description: "Tax amount" })
  @IsOptional()
  @IsNumber()
  @Min(0)
  taxAmount?: number = 0

  @ApiPropertyOptional({ description: "Payout status" })
  @IsOptional()
  @IsEnum(PayoutStatus)
  status?: PayoutStatus = PayoutStatus.PENDING

  @ApiPropertyOptional({ description: "Payment method" })
  @IsOptional()
  @IsString()
  paymentMethod?: string

  @ApiPropertyOptional({ description: "Transaction ID" })
  @IsOptional()
  @IsString()
  transactionId?: string

  @ApiPropertyOptional({ description: "Payment date" })
  @IsOptional()
  @IsDate()
  @Type(() => Date)
  paymentDate?: Date

  @ApiPropertyOptional({ description: "Additional notes" })
  @IsOptional()
  @IsString()
  notes?: string
}
