import { ApiPropertyOptional } from "@nestjs/swagger"
import { IsString, IsNumber, IsDate, IsOptional, Min, IsEnum } from "class-validator"
import { Type } from "class-transformer"
import { PayoutStatus } from "@shared/types"

export class UpdatePayoutDto {
  @ApiPropertyOptional({ description: "Gross amount before deductions" })
  @IsOptional()
  @IsNumber()
  @Min(0)
  grossAmount?: number

  @ApiPropertyOptional({ description: "Net amount after deductions" })
  @IsOptional()
  @IsNumber()
  @Min(0)
  netAmount?: number

  @ApiPropertyOptional({ description: "Platform fee amount" })
  @IsOptional()
  @IsNumber()
  @Min(0)
  platformFee?: number

  @ApiPropertyOptional({ description: "Tax amount" })
  @IsOptional()
  @IsNumber()
  @Min(0)
  taxAmount?: number

  @ApiPropertyOptional({ description: "Payout status" })
  @IsOptional()
  @IsEnum(PayoutStatus)
  status?: PayoutStatus

  @ApiPropertyOptional({ description: "Payment method" })
  @IsOptional()
  @IsString()
  paymentMethod?: string

  @ApiPropertyOptional({ description: "Transaction ID" })
  @IsOptional()
  @IsString()
  transactionId?: string

  @ApiPropertyOptional({ description: "Payment date" })
  @IsOptional()
  @IsDate()
  @Type(() => Date)
  paymentDate?: Date

  @ApiPropertyOptional({ description: "Additional notes" })
  @IsOptional()
  @IsString()
  notes?: string
}
