import {
  Injectable,
  NotFoundException,
  BadRequestException,
  Inject,
} from "@nestjs/common";
import { InjectRepository } from "@nestjs/typeorm";
import type { Repository } from "typeorm";
import { UsersService } from "../users/users.service";
import { JobsService } from "../jobs/jobs.service";
import { NotificationsService } from "../notifications/notifications.service";
import { PaymentsService } from "../payments/payments.service";
import type { CreatePayoutDto, UpdatePayoutDto } from "@shared/validation";
import { type PayoutStatus, UserRole } from "@shared/types";
import { Payout } from "./entities/payout.entity";
import { PaymentMethod } from "../payments/enums/payment-method.enum";
import { PaymentStatus } from "../payments/enums/payment-status.enum";

@Injectable()
export class PayoutsService {
  constructor(
    @InjectRepository(Payout)
    private readonly payoutsRepository: Repository<Payout>,
    @Inject(UsersService)
    private readonly usersService: UsersService,
    @Inject(JobsService)
    private readonly jobsService: JobsService,
    @Inject(NotificationsService)
    private readonly notificationsService: NotificationsService,
    @Inject(PaymentsService)
    private readonly paymentsService: PaymentsService
  ) {}

  async create(createPayoutDto: CreatePayoutDto): Promise<Payout> {
    // Verify job exists
    const job = await this.jobsService.findOne(createPayoutDto.jobId);

    // Verify worker exists
    const worker = await this.usersService.findOne(createPayoutDto.workerId);

    // Create payout
    const payout = this.payoutsRepository.create(createPayoutDto);
    const savedPayout = await this.payoutsRepository.save(payout);

    // Send notification to worker
    await this.notificationsService.create({
      userId: worker.id,
      title: "New Payout Created",
      message: `A payout of ${createPayoutDto.netPay} has been created for job: ${job.title}`,
      type: "payout",
      metadata: { payoutId: savedPayout.id, jobId: job.id },
      link: `/earnings`,
    });

    return savedPayout;
  }

  async findAll(filters?: {
    workerId?: string;
    jobId?: string;
    status?: PayoutStatus;
  }): Promise<Payout[]> {
    const queryBuilder = this.payoutsRepository
      .createQueryBuilder("payout")
      .leftJoinAndSelect("payout.job", "job")
      .leftJoinAndSelect("payout.worker", "worker")
      .orderBy("payout.createdAt", "DESC");

    if (filters) {
      if (filters.workerId) {
        queryBuilder.andWhere("payout.workerId = :workerId", {
          workerId: filters.workerId,
        });
      }

      if (filters.jobId) {
        queryBuilder.andWhere("payout.jobId = :jobId", {
          jobId: filters.jobId,
        });
      }

      if (filters.status) {
        queryBuilder.andWhere("payout.status = :status", {
          status: filters.status,
        });
      }
    }

    return queryBuilder.getMany();
  }

  async findOne(id: string): Promise<Payout> {
    const payout = await this.payoutsRepository.findOne({
      where: { id },
      relations: ["job", "worker"],
    });

    if (!payout) {
      throw new NotFoundException(`Payout with ID ${id} not found`);
    }

    return payout;
  }

  async update(
    id: string,
    updatePayoutDto: UpdatePayoutDto,
    userRole: UserRole
  ): Promise<Payout> {
    // Only admins can update payouts
    if (userRole !== UserRole.ADMIN) {
      throw new BadRequestException("Only admins can update payouts");
    }

    const payout = await this.findOne(id);

    // Update payout
    Object.assign(payout, updatePayoutDto);
    const updatedPayout = await this.payoutsRepository.save(payout);

    // Send notification if status changed
    if (updatePayoutDto.status) {
      await this.notificationsService.create({
        userId: payout.workerId,
        title: "Payout Status Updated",
        message: `Your payout for job: ${payout.job.title} is now ${updatePayoutDto.status}`,
        type: "payout",
        metadata: { payoutId: id, jobId: payout.jobId },
        link: `/earnings`,
      });
    }

    return updatedPayout;
  }

  /**
   * Process a payout payment
   */
  async processPayout(
    id: string,
    paymentMethod: PaymentMethod
  ): Promise<Payout> {
    const payout = await this.findOne(id);

    // Check if payout is already processed
    if (payout.status !== "pending") {
      throw new BadRequestException(`Payout is already ${payout.status}`);
    }

    // Get worker details
    const worker = await this.usersService.findOne(payout.workerId);

    let paymentResult;

    // Process payment based on method
    if (paymentMethod === PaymentMethod.STRIPE) {
      // For Stripe, we would need customer ID or payment method ID
      // This is a simplified example
      paymentResult = await this.paymentsService.processStripePayment({
        amount: Number(payout.netAmount),
        currency: "inr", // or get from config
        description: `Payout for job: ${payout.job.title}`,
        metadata: {
          payoutId: payout.id,
          jobId: payout.jobId,
          workerId: payout.workerId,
        },
      });
    } else if (paymentMethod === PaymentMethod.UPI) {
      // For UPI, we need the worker's UPI ID
      // This would typically be stored in the worker's profile
      const upiId = worker.upiId || ""; // Assuming this field exists

      if (!upiId) {
        throw new BadRequestException(
          "Worker does not have a UPI ID configured"
        );
      }

      paymentResult = await this.paymentsService.processUpiPayment({
        amount: Number(payout.netAmount),
        vpa: upiId,
        description: `Payout for job: ${payout.job.title}`,
        reference: `payout-${payout.id}`,
        metadata: {
          payoutId: payout.id,
          jobId: payout.jobId,
          workerId: payout.workerId,
        },
      });
    } else if (paymentMethod === PaymentMethod.RAZORPAY) {
      // For Razorpay, we can use the worker's email and phone
      const customerEmail = worker.email;
      const customerPhone = worker.phone;

      paymentResult = await this.paymentsService.processRazorpayPayment({
        amount: Number(payout.netAmount),
        currency: "INR", // Razorpay primarily supports INR
        description: `Payout for job: ${payout.job.title}`,
        customerEmail,
        customerPhone,
        metadata: {
          payoutId: payout.id,
          jobId: payout.jobId,
          workerId: payout.workerId,
        },
      });
    } else {
      throw new BadRequestException(
        `Payment method ${paymentMethod} not supported`
      );
    }

    // Update payout with payment result
    payout.paymentMethod = paymentMethod;
    payout.transactionId = paymentResult.transactionId;
    payout.status = paymentResult.status;

    if (paymentResult.status === PaymentStatus.COMPLETED) {
      payout.paymentDate = new Date();
    }

    const updatedPayout = await this.payoutsRepository.save(payout);

    // Send notification to worker
    await this.notificationsService.create({
      userId: payout.workerId,
      title: "Payout Processed",
      message: `Your payout of ${payout.netAmount} for job: ${payout.job.title} has been processed via ${paymentMethod}`,
      type: "payout",
      metadata: {
        payoutId: payout.id,
        jobId: payout.jobId,
        status: payout.status,
        transactionId: payout.transactionId,
      },
      link: `/earnings`,
    });

    return updatedPayout;
  }
}
