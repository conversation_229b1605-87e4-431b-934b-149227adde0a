import {
  Controller,
  Get,
  Post,
  Patch,
  Param,
  Body,
  Query,
  UseGuards,
  Request,
  Inject,
} from "@nestjs/common";
import { JwtAuthGuard } from "../auth/guards/jwt-auth.guard";
import { RolesGuard } from "../auth/guards/roles.guard";
import { Roles } from "../auth/decorators/roles.decorator";
import { PayoutsService } from "./payouts.service";
import { type PayoutStatus, UserRole } from "@shared/types";
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiBearerAuth,
  ApiBody,
  ApiParam,
  ApiQuery,
} from "@nestjs/swagger";
import {
  CreatePayoutDto,
  UpdatePayoutDto,
  ProcessPayoutDto,
  PayoutResponseDto,
} from "./dto/payout.dto";

@ApiTags("payouts")
@Controller("payouts")
@UseGuards(JwtAuthGuard)
@ApiBearerAuth()
export class PayoutsController {
  constructor(
    @Inject(PayoutsService)
    private readonly payoutsService: PayoutsService
  ) {}

  @Post()
  @UseGuards(RolesGuard)
  @Roles(UserRole.ADMIN)
  @ApiOperation({
    summary: "Create a new payout (Admin only)",
    description: "Create a new payout for a worker. Only accessible by admins.",
  })
  @ApiBody({
    type: CreatePayoutDto,
    description: "Payout details to create",
  })
  @ApiResponse({
    status: 201,
    description: "Payout created successfully",
    type: PayoutResponseDto,
  })
  @ApiResponse({ status: 400, description: "Bad request - invalid input data" })
  @ApiResponse({
    status: 401,
    description: "Unauthorized - invalid or missing authentication",
  })
  @ApiResponse({
    status: 403,
    description: "Forbidden - user does not have required role",
  })
  async create(@Body() createPayoutDto: CreatePayoutDto) {
    return this.payoutsService.create(createPayoutDto);
  }

  @Get()
  @ApiOperation({
    summary: "Get all payouts",
    description:
      "Get all payouts with optional filtering. Workers can only see their own payouts.",
  })
  @ApiQuery({
    name: "workerId",
    required: false,
    description: "Filter by worker ID",
    type: String,
  })
  @ApiQuery({
    name: "jobId",
    required: false,
    description: "Filter by job ID",
    type: String,
  })
  @ApiQuery({
    name: "status",
    required: false,
    description: "Filter by payout status",
    enum: ["pending", "processing", "completed", "failed", "cancelled"],
  })
  @ApiResponse({
    status: 200,
    description: "Return all payouts",
    type: [PayoutResponseDto],
  })
  async findAll(
    @Request() req,
    @Query("workerId") workerId?: string,
    @Query("jobId") jobId?: string,
    @Query("status") status?: PayoutStatus
  ) {
    // If user is a worker, they can only see their own payouts
    if (req.user.role === UserRole.WORKER) {
      workerId = req.user.id;
    }

    return this.payoutsService.findAll({
      workerId,
      jobId,
      status,
    });
  }

  @Get(":id")
  @ApiOperation({
    summary: "Get payout by ID",
    description:
      "Get a specific payout by ID. Workers can only view their own payouts.",
  })
  @ApiParam({
    name: "id",
    description: "Payout ID to retrieve",
    type: String,
    example: "550e8400-e29b-41d4-a716-446655440000",
  })
  @ApiResponse({
    status: 200,
    description: "Return payout by ID",
    type: PayoutResponseDto,
  })
  @ApiResponse({ status: 404, description: "Payout not found" })
  @ApiResponse({
    status: 403,
    description: "Forbidden - workers can only view their own payouts",
  })
  async findOne(@Param("id") id: string, @Request() req) {
    const payout = await this.payoutsService.findOne(id);

    // Check permissions
    if (req.user.role === UserRole.WORKER && payout.workerId !== req.user.id) {
      throw new Error("You can only view your own payouts");
    }

    return payout;
  }

  @Patch(":id")
  @UseGuards(RolesGuard)
  @Roles(UserRole.ADMIN)
  @ApiOperation({
    summary: "Update payout (Admin only)",
    description: "Update a payout's details. Only accessible by admins.",
  })
  @ApiParam({
    name: "id",
    description: "Payout ID to update",
    type: String,
    example: "550e8400-e29b-41d4-a716-446655440000",
  })
  @ApiBody({
    type: UpdatePayoutDto,
    description: "Payout details to update",
  })
  @ApiResponse({
    status: 200,
    description: "Payout updated successfully",
    type: PayoutResponseDto,
  })
  @ApiResponse({ status: 400, description: "Bad request - invalid input data" })
  @ApiResponse({
    status: 401,
    description: "Unauthorized - invalid or missing authentication",
  })
  @ApiResponse({
    status: 403,
    description: "Forbidden - user does not have required role",
  })
  @ApiResponse({ status: 404, description: "Payout not found" })
  async update(
    @Param("id") id: string,
    @Body() updatePayoutDto: UpdatePayoutDto,
    @Request() req
  ) {
    return this.payoutsService.update(id, updatePayoutDto, req.user.role);
  }

  @Patch(":id/process")
  @UseGuards(RolesGuard)
  @Roles(UserRole.ADMIN)
  @ApiOperation({
    summary: "Process payout payment (Admin only)",
    description:
      "Process a pending payout using the specified payment method. Only accessible by admins.",
  })
  @ApiParam({
    name: "id",
    description: "Payout ID to process",
    type: String,
    example: "550e8400-e29b-41d4-a716-446655440000",
  })
  @ApiBody({
    type: ProcessPayoutDto,
    description: "Payment method to use for processing",
  })
  @ApiResponse({
    status: 200,
    description: "Payout processed successfully",
    type: PayoutResponseDto,
  })
  @ApiResponse({
    status: 400,
    description: "Bad request - invalid input data or payout status",
  })
  @ApiResponse({
    status: 401,
    description: "Unauthorized - invalid or missing authentication",
  })
  @ApiResponse({
    status: 403,
    description: "Forbidden - user does not have required role",
  })
  @ApiResponse({ status: 404, description: "Payout not found" })
  @ApiResponse({
    status: 500,
    description: "Internal server error - payment processing failed",
  })
  async processPayout(
    @Param("id") id: string,
    @Body() processPayoutDto: ProcessPayoutDto
  ) {
    return this.payoutsService.processPayout(
      id,
      processPayoutDto.paymentMethod
    );
  }
}
