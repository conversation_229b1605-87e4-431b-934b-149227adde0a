import {
  <PERSON><PERSON><PERSON>,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  ManyToOne,
  JoinColumn,
  Index,
} from "typeorm"
import { User } from "../../users/entities/user.entity"
import { Job } from "../../jobs/entities/job.entity"
import { PayoutStatus } from "@shared/types"

@Entity("payouts")
export class Payout {
  @PrimaryGeneratedColumn("uuid")
  id: string

  @Column()
  @Index()
  workerId: string

  @ManyToOne(
    () => User,
    (user) => user.payouts,
  )
  @JoinColumn({ name: "workerId" })
  worker: User

  @Column()
  @Index()
  jobId: string

  @ManyToOne(() => Job)
  @JoinColumn({ name: "jobId" })
  job: Job

  @Column({ type: "decimal", precision: 10, scale: 2 })
  grossAmount: number

  @Column("decimal", { precision: 10, scale: 2 })
  commission: number

  @Column({ type: "decimal", precision: 10, scale: 2 })
  netAmount: number

  @Column({ type: "decimal", precision: 10, scale: 2 })
  platformFee: number

  @Column({ type: "decimal", precision: 10, scale: 2, default: 0 })
  taxAmount: number

  @Column({
    type: "enum",
    enum: PayoutStatus,
    default: PayoutStatus.PENDING,
  })
  status: PayoutStatus

  @Column({ nullable: true })
  paymentMethod?: string

  @Column({ nullable: true })
  transactionId?: string

  @Column({ nullable: true })
  paymentDate?: Date

  @Column({ nullable: true })
  notes?: string

  @CreateDateColumn()
  createdAt: Date

  @UpdateDateColumn()
  updatedAt: Date
}
