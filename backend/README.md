# Job Platform Backend

This is the backend service for the Job Platform application, built with NestJS.

## Environment Variables

The application requires several environment variables to be set. You can create a `.env` file in the root directory of the project with the following variables:

### Application Configuration

| Variable | Description | Default | Required |
|----------|-------------|---------|----------|
| `NODE_ENV` | Environment (development, production, test) | `development` | Yes |
| `PORT` | Port to run the server on | `3000` | No |

### Database Configuration

| Variable | Description | Default | Required |
|----------|-------------|---------|----------|
| `DATABASE_URL` | PostgreSQL connection URL | - | Yes |

### Authentication

| Variable | Description | Default | Required |
|----------|-------------|---------|----------|
| `JWT_SECRET` | Secret key for JWT token generation | `secret` | Yes |
| `JWT_EXPIRES_IN` | JWT token expiration time | `1d` | No |
| `JWT_REFRESH_EXPIRES_IN` | JWT refresh token expiration time | `30d` | No |

### Email Configuration

| Variable | Description | Default | Required |
|----------|-------------|---------|----------|
| `EMAIL_SERVICE` | Email service provider (smtp, sendgrid, etc.) | - | Yes |
| `EMAIL_HOST` | SMTP host | - | Yes |
| `EMAIL_PORT` | SMTP port | `587` | Yes |
| `EMAIL_USER` | SMTP username | - | Yes |
| `EMAIL_PASSWORD` | SMTP password | - | Yes |
| `EMAIL_FROM` | Default sender email address | - | Yes |

### SMS Configuration

| Variable | Description | Default | Required |
|----------|-------------|---------|----------|
| `SMS_PROVIDER` | SMS provider (twilio, etc.) | - | Yes |
| `TWILIO_ACCOUNT_SID` | Twilio account SID | - | Yes |
| `TWILIO_AUTH_TOKEN` | Twilio auth token | - | Yes |
| `TWILIO_PHONE_NUMBER` | Twilio phone number | - | Yes |

### Payment Gateways

| Variable | Description | Default | Required |
|----------|-------------|---------|----------|
| `STRIPE_SECRET_KEY` | Stripe secret key | - | Yes |
| `STRIPE_WEBHOOK_SECRET` | Stripe webhook secret | - | No |
| `UPI_GATEWAY_URL` | UPI gateway URL | - | Yes |
| `UPI_API_KEY` | UPI API key | - | Yes |

### Firebase (Push Notifications)

| Variable | Description | Default | Required |
|----------|-------------|---------|----------|
| `FIREBASE_CONFIG` | Firebase configuration JSON | - | Yes |
| `FIREBASE_DATABASE_URL` | Firebase database URL | - | Yes |
| `FIREBASE_SERVICE_ACCOUNT_PATH` | Path to Firebase service account key file | - | No |

### Storage

| Variable | Description | Default | Required |
|----------|-------------|---------|----------|
| `STORAGE_TYPE` | Storage type (local, s3) | `local` | No |
| `STORAGE_LOCAL_PATH` | Local storage path | `./uploads` | No |
| `S3_BUCKET_NAME` | S3 bucket name | - | No |
| `S3_ACCESS_KEY` | S3 access key | - | No |
| `S3_SECRET_KEY` | S3 secret key | - | No |
| `S3_REGION` | S3 region | `us-east-1` | No |

### Logging

| Variable | Description | Default | Required |
|----------|-------------|---------|----------|
| `LOG_LEVEL` | Log level (debug, info, warn, error) | `info` | No |

### Rate Limiting

| Variable | Description | Default | Required |
|----------|-------------|---------|----------|
| `THROTTLE_TTL` | Throttle time-to-live in seconds | `60` | No |
| `THROTTLE_LIMIT` | Throttle request limit per TTL | `100` | No |

### CORS

| Variable | Description | Default | Required |
|----------|-------------|---------|----------|
| `CORS_ORIGIN` | Comma-separated list of allowed origins | `*` | No |

### Security

| Variable | Description | Default | Required |
|----------|-------------|---------|----------|
| `HELMET_CONTENT_SECURITY_POLICY` | Enable Helmet CSP | `false` | No |

## Getting Started

1. Clone the repository
2. Copy `.env.example` to `.env` and update the values
3. Install dependencies: `npm install`
4. Start the development server: `npm run start:dev`

## API Documentation

API documentation is available at `/api/docs` when the server is running.

## License

This project is licensed under the MIT License - see the LICENSE file for details.
