# Application
NODE_ENV=development
PORT=3000

# Security
CORS_ORIGIN=http://localhost:3000,http://localhost:3001,http://localhost:3002,http://localhost:19006
ALLOWED_ORIGINS=http://localhost:3000,http://localhost:3001,http://localhost:3002,http://localhost:19006
HELMET_CONTENT_SECURITY_POLICY=false

# Rate Limiting
THROTTLE_TTL=60
THROTTLE_LIMIT=100

# Database
DATABASE_URL=postgres://postgres:@localhost:5432/job-platform

# Authentication
JWT_SECRET=your_jwt_secret_key_here
JWT_EXPIRES_IN=1d
JWT_REFRESH_EXPIRES_IN=30d

# Email Configuration
EMAIL_PROVIDER=smtp # Options: smtp, sendgrid, mailgun, mailtrap, ses
EMAIL_FROM=<EMAIL>
EMAIL_DEFAULT_REPLY_TO=<EMAIL>

# SMTP Configuration (for EMAIL_PROVIDER=smtp or EMAIL_PROVIDER=mailtrap)
EMAIL_HOST=smtp.example.com
EMAIL_PORT=587
EMAIL_USER=<EMAIL>
EMAIL_PASSWORD=your_email_password
EMAIL_SECURE=false # true for 465, false for other ports

# SendGrid Configuration (for EMAIL_PROVIDER=sendgrid)
SENDGRID_API_KEY=your_sendgrid_api_key

# Mailgun Configuration (for EMAIL_PROVIDER=mailgun)
MAILGUN_API_KEY=your_mailgun_api_key
MAILGUN_DOMAIN=your_mailgun_domain

# Amazon SES Configuration (for EMAIL_PROVIDER=ses)
AWS_SES_REGION=us-east-1
AWS_SES_ACCESS_KEY=your_aws_access_key
AWS_SES_SECRET_KEY=your_aws_secret_key

# SMS Configuration
SMS_PROVIDER=twilio
TWILIO_ACCOUNT_SID=your_twilio_account_sid
TWILIO_AUTH_TOKEN=your_twilio_auth_token
TWILIO_PHONE_NUMBER=your_twilio_phone_number

# Payment Gateways
STRIPE_SECRET_KEY=your_stripe_secret_key
STRIPE_WEBHOOK_SECRET=your_stripe_webhook_secret
RAZORPAY_KEY_ID=your_razorpay_key_id
RAZORPAY_KEY_SECRET=your_razorpay_key_secret
RAZORPAY_WEBHOOK_SECRET=your_razorpay_webhook_secret
UPI_GATEWAY_URL=https://upi-gateway.example.com/api
UPI_API_KEY=your_upi_api_key

# Firebase (Push Notifications)
FIREBASE_CONFIG={"type":"service_account","project_id":"your-project-id","private_key_id":"your-private-key-id","private_key":"your-private-key","client_email":"*****************","client_id":"your-client-id","auth_uri":"https://accounts.google.com/o/oauth2/auth","token_uri":"https://oauth2.googleapis.com/token","auth_provider_x509_cert_url":"https://www.googleapis.com/oauth2/v1/certs","client_x509_cert_url":"your-cert-url"}
FIREBASE_DATABASE_URL=https://your-project-id.firebaseio.com
FIREBASE_SERVICE_ACCOUNT_PATH=path/to/serviceAccountKey.json

# Storage
STORAGE_TYPE=local
STORAGE_LOCAL_PATH=./uploads
S3_BUCKET_NAME=your-s3-bucket
S3_ACCESS_KEY=your-s3-access-key
S3_SECRET_KEY=your-s3-secret-key
S3_REGION=us-east-1

# Logging
LOG_LEVEL=debug

# Rate Limiting
THROTTLE_TTL=60
THROTTLE_LIMIT=100

# CORS
CORS_ORIGIN=http://localhost:3000,http://localhost:3001,http://localhost:3002

# Security
HELMET_CONTENT_SECURITY_POLICY=false
