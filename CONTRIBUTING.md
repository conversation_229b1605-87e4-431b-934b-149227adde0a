# Contributing to Job Platform

Thank you for considering contributing to the Job Platform project! This document outlines the process for contributing to this repository.

## Code of Conduct

By participating in this project, you agree to abide by our Code of Conduct. Please be respectful and considerate of others.

## How to Contribute

### Reporting Bugs

If you find a bug, please create an issue with the following information:
- A clear, descriptive title
- Steps to reproduce the issue
- Expected behavior
- Actual behavior
- Screenshots (if applicable)
- Environment details (OS, browser, etc.)

### Suggesting Features

We welcome feature suggestions! Please create an issue with:
- A clear, descriptive title
- Detailed description of the proposed feature
- Any relevant mockups or examples
- Explanation of why this feature would be useful

### Pull Requests

1. Fork the repository
2. Create a new branch (`git checkout -b feature/your-feature-name`)
3. Make your changes
4. Run tests to ensure your changes don't break existing functionality
5. Commit your changes (`git commit -m 'Add some feature'`)
6. Push to the branch (`git push origin feature/your-feature-name`)
7. Open a Pull Request

### Pull Request Guidelines

- Follow the coding style of the project
- Include tests for new features
- Update documentation as needed
- Keep pull requests focused on a single concern
- Link any related issues in the pull request description

## Development Setup

Please refer to the README.md for detailed setup instructions.

## Coding Standards

- Use TypeScript for all new code
- Follow the existing code style
- Write meaningful commit messages
- Document new code using JSDoc comments
- For mobile app, use NativeWind classes for styling (no inline styles or StyleSheet objects)
- For backend, use @Inject() decorator for service and controller constructor parameters

## Testing

- Write tests for all new features
- Ensure all tests pass before submitting a pull request
- Aim for good test coverage

## License

By contributing to this project, you agree that your contributions will be licensed under the project's MIT License.
