# Production Environment Configuration

# Database
POSTGRES_USER=jobplatform_user
POSTGRES_PASSWORD=your_secure_password_here
POSTGRES_DB=job_platform_prod

# Application URLs
NEXT_PUBLIC_API_URL=https://api.jobplatform.com
COMPANY_PORTAL_URL=https://company.jobplatform.com
ADMIN_PORTAL_URL=https://admin.jobplatform.com

# Security
JWT_SECRET=your_super_secure_jwt_secret_here_minimum_32_characters
JWT_EXPIRES_IN=7d
NEXTAUTH_SECRET=your_nextauth_secret_here_minimum_32_characters

# CORS Configuration
CORS_ORIGIN=https://company.jobplatform.com,https://admin.jobplatform.com

# Rate Limiting
THROTTLE_TTL=60
THROTTLE_LIMIT=100

# Email Configuration
EMAIL_PROVIDER=sendgrid
EMAIL_FROM=<EMAIL>
SENDGRID_API_KEY=your_sendgrid_api_key_here

# Payment Configuration
STRIPE_SECRET_KEY=sk_live_your_stripe_secret_key_here
STRIPE_PUBLISHABLE_KEY=pk_live_your_stripe_publishable_key_here

# SMS Configuration
TWILIO_ACCOUNT_SID=your_twilio_account_sid_here
TWILIO_AUTH_TOKEN=your_twilio_auth_token_here
TWILIO_PHONE_NUMBER=your_twilio_phone_number_here

# File Storage (AWS S3)
AWS_ACCESS_KEY_ID=your_aws_access_key_here
AWS_SECRET_ACCESS_KEY=your_aws_secret_key_here
AWS_REGION=us-east-1
AWS_S3_BUCKET=jobplatform-documents-prod

# Monitoring & Logging
SENTRY_DSN=your_sentry_dsn_here
LOG_LEVEL=info

# Redis (for caching and sessions)
REDIS_URL=redis://redis:6379

# Firebase (for push notifications)
FIREBASE_PROJECT_ID=your_firebase_project_id
FIREBASE_PRIVATE_KEY=your_firebase_private_key_here
FIREBASE_CLIENT_EMAIL=your_firebase_client_email_here
