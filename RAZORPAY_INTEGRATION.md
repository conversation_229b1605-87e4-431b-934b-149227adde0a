# Razorpay Payment Gateway Integration

This document provides comprehensive information about the Razorpay payment gateway integration in the job platform.

## Overview

Razorpay has been integrated as a complete payment solution supporting:
- Job bounty escrow funding
- Worker payouts
- Dispute refunds
- Webhook handling for real-time payment updates
- Multi-platform support (React Native mobile app, Next.js web portals)

## Backend Integration (NestJS)

### Environment Configuration

Add the following environment variables to your `.env` file:

```env
# Razorpay Configuration
RAZORPAY_KEY_ID=your_razorpay_key_id
RAZORPAY_KEY_SECRET=your_razorpay_key_secret
RAZORPAY_WEBHOOK_SECRET=your_razorpay_webhook_secret
```

### API Endpoints

#### Create Razorpay Order
```
POST /api/v1/payments/razorpay/order
```

#### Process Razorpay Payment
```
POST /api/v1/payments/razorpay
```

#### Capture Payment
```
POST /api/v1/payments/razorpay/capture/:paymentId
```

#### Refund Payment
```
POST /api/v1/payments/razorpay/refund/:paymentId
```

#### Verify Payment Signature
```
POST /api/v1/payments/razorpay/verify
```

#### Webhook Handler
```
POST /api/v1/webhooks/razorpay
```

### Usage Examples

#### Creating an Order
```typescript
const orderResult = await paymentsService.createRazorpayOrder({
  amount: 100000, // Amount in paise (₹1000)
  currency: 'INR',
  description: 'Job bounty escrow',
  customerEmail: '<EMAIL>',
  customerPhone: '+919876543210',
  metadata: {
    jobId: 'job_123',
    type: 'escrow_funding'
  }
});
```

#### Processing Payment
```typescript
const paymentResult = await paymentsService.processRazorpayPayment({
  amount: 100000,
  currency: 'INR',
  description: 'Job bounty escrow',
  orderId: 'order_123' // Optional, creates new order if not provided
});
```

## Frontend Integration

### React Native (Mobile App)

#### Installation
```bash
npm install react-native-razorpay
```

#### Usage
```typescript
import { RazorpayPayment } from '../components/payments/RazorpayPayment';

<RazorpayPayment
  amount={100000}
  currency="INR"
  description="Job payment"
  customerEmail="<EMAIL>"
  customerPhone="+919876543210"
  onSuccess={(data) => console.log('Payment success:', data)}
  onError={(error) => console.log('Payment error:', error)}
/>
```

#### Using the Hook
```typescript
import { useRazorpayPayment } from '../components/payments/RazorpayPayment';

const { processPayment } = useRazorpayPayment();

const handlePayment = async () => {
  const result = await processPayment({
    amount: 100000,
    currency: 'INR',
    description: 'Job payment',
    customerEmail: '<EMAIL>'
  });
  
  if (result.success) {
    console.log('Payment successful:', result);
  } else {
    console.log('Payment failed:', result.error);
  }
};
```

### Next.js (Company Portal)

#### Installation
```bash
npm install razorpay @types/razorpay
```

#### Environment Variables
```env
NEXT_PUBLIC_RAZORPAY_KEY_ID=your_razorpay_key_id
```

#### Usage
```typescript
import { RazorpayPayment } from '../components/payments/RazorpayPayment';

<RazorpayPayment
  amount={100000}
  currency="INR"
  description="Escrow funding"
  customerEmail="<EMAIL>"
  onSuccess={(data) => console.log('Payment success:', data)}
  onError={(error) => console.log('Payment error:', error)}
/>
```

## Payment Flow

### Escrow Funding Flow
1. Company creates a job with bounty amount
2. Company initiates escrow funding
3. Razorpay order is created on backend
4. Frontend opens Razorpay checkout
5. User completes payment
6. Payment signature is verified
7. Escrow status is updated to "HELD"
8. Notifications are sent

### Payout Flow
1. Job is completed and approved
2. Admin/Company releases escrow
3. Razorpay payout is initiated to worker
4. Worker receives payment
5. Payout status is updated
6. Notifications are sent

### Dispute Refund Flow
1. Dispute is raised and resolved
2. Refund is initiated through Razorpay
3. Refund is processed
4. Escrow status is updated to "REFUNDED"
5. Company receives refund

## Webhook Configuration

### Setting up Webhooks in Razorpay Dashboard

1. Go to Razorpay Dashboard → Settings → Webhooks
2. Add webhook URL: `https://your-domain.com/api/v1/webhooks/razorpay`
3. Select events:
   - `payment.captured`
   - `payment.failed`
   - `order.paid`
   - `refund.created`
   - `refund.processed`
4. Set webhook secret in environment variables

### Webhook Events Handled

- **payment.captured**: Updates payment status to completed
- **payment.failed**: Updates payment status to failed
- **order.paid**: Confirms order completion
- **refund.created**: Initiates refund process
- **refund.processed**: Confirms refund completion

## Security Features

### Payment Verification
- All payments are verified using Razorpay signature verification
- Webhook signatures are validated using HMAC SHA256
- Payment amounts are validated on both frontend and backend

### Data Protection
- Sensitive payment data is never stored locally
- All API calls use HTTPS encryption
- Payment tokens are handled securely

## Testing

### Test Credentials
```env
RAZORPAY_KEY_ID=rzp_test_your_test_key_id
RAZORPAY_KEY_SECRET=your_test_key_secret
```

### Test Cards
- **Success**: 4111 1111 1111 1111
- **Failure**: 4000 0000 0000 0002
- **CVV**: Any 3 digits
- **Expiry**: Any future date

### Test UPI IDs
- **Success**: success@razorpay
- **Failure**: failure@razorpay

## Error Handling

### Common Error Codes
- `payment_cancelled`: User cancelled payment
- `payment_failed`: Payment processing failed
- `invalid_signature`: Signature verification failed
- `order_not_found`: Order ID not found

### Error Response Format
```typescript
{
  success: false,
  error: {
    code: 'payment_failed',
    message: 'Payment processing failed',
    details: { ... }
  }
}
```

## Monitoring and Analytics

### Payment Tracking
- All payments are logged with transaction IDs
- Payment status updates are tracked
- Failed payments are monitored for retry

### Admin Dashboard Integration
- Payment analytics are available in admin portal
- Transaction reports can be generated
- Dispute tracking is integrated

## Support and Troubleshooting

### Common Issues
1. **Script Loading**: Ensure Razorpay script is loaded before payment
2. **Signature Verification**: Check webhook secret configuration
3. **Amount Mismatch**: Verify amount is in correct currency units (paise for INR)

### Debug Mode
Enable debug logging by setting:
```env
NODE_ENV=development
```

### Contact Support
- Razorpay Support: https://razorpay.com/support/
- Platform Support: <EMAIL>

## Migration Notes

### From Other Payment Gateways
- Payment method enum updated to include 'razorpay'
- Database schema supports Razorpay transaction IDs
- Existing payment flows remain unchanged

### Backward Compatibility
- All existing payment methods continue to work
- Razorpay is added as an additional option
- No breaking changes to existing APIs
