"use client";

import React, { createContext, useContext, useCallback } from 'react';
import { useRouter } from 'next/navigation';
import { useAppSelector, useAppDispatch } from '../store';
import { setCredentials, updateUser as updateUserAction, logout as logoutAction } from '../store/slices/authSlice';
import { 
  useLoginMutation, 
  useRegisterMutation, 
  useLogoutMutation,
  useUpdateProfileMutation
} from '../store/api/authApi';
import type { LoginRequest, RegisterRequest, User } from '../types/api';

// Define the auth context type
interface AuthContextType {
  user: User | null;
  isAuthenticated: boolean;
  isLoading: boolean;
  login: (credentials: LoginRequest) => Promise<void>;
  register: (userData: RegisterRequest) => Promise<void>;
  logout: () => Promise<void>;
  updateUser: (userData: Partial<User>) => Promise<void>;
}

// Create the auth context
const AuthContext = createContext<AuthContextType | undefined>(undefined);

// Auth provider component
export const AuthRTKProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const router = useRouter();
  const dispatch = useAppDispatch();
  const { user, isAuthenticated, isLoading } = useAppSelector((state) => state.auth);

  // RTK Query hooks
  const [loginMutation] = useLoginMutation();
  const [registerMutation] = useRegisterMutation();
  const [logoutMutation] = useLogoutMutation();
  const [updateProfileMutation] = useUpdateProfileMutation();

  // Login function
  const login = useCallback(
    async (credentials: LoginRequest) => {
      try {
        const result = await loginMutation(credentials).unwrap();
        const resultData = result.data;
        dispatch(
          setCredentials({
            user: resultData.user,
            token: resultData.token,
            refreshToken: resultData.refreshToken,
          })
        );
        router.push('/dashboard');
      } catch (error) {
        console.error('Login failed:', error);
        throw error;
      }
    },
    [loginMutation, dispatch, router]
  );

  // Register function
  const register = useCallback(
    async (userData: RegisterRequest) => {
      try {
        const result = await registerMutation(userData).unwrap();
        dispatch(
          setCredentials({
            user: result.user,
            token: result.token,
            refreshToken: result.refreshToken,
          })
        );
        router.push('/dashboard');
      } catch (error) {
        console.error('Registration failed:', error);
        throw error;
      }
    },
    [registerMutation, dispatch, router]
  );

  // Logout function
  const logout = useCallback(async () => {
    try {
      if (isAuthenticated) {
        await logoutMutation().unwrap();
      }
    } catch (error) {
      console.error('Logout API call failed:', error);
    } finally {
      dispatch(logoutAction());
      router.push('/login');
    }
  }, [logoutMutation, dispatch, router, isAuthenticated]);

  // Update user function
  const updateUser = useCallback(
    async (userData: Partial<User>) => {
      try {
        const updatedUser = await updateProfileMutation(userData).unwrap();
        dispatch(updateUserAction(updatedUser));
      } catch (error) {
        console.error('Update user failed:', error);
        throw error;
      }
    },
    [updateProfileMutation, dispatch]
  );

  // Create the context value
  const contextValue: AuthContextType = {
    user,
    isAuthenticated,
    isLoading,
    login,
    register,
    logout,
    updateUser,
  };

  return <AuthContext.Provider value={contextValue}>{children}</AuthContext.Provider>;
};

// Custom hook to use the auth context
export const useAuthRTK = () => {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuthRTK must be used within an AuthRTKProvider');
  }
  return context;
};

export default AuthRTKProvider;
