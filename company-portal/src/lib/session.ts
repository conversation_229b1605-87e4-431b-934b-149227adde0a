import { getServerSession } from "next-auth/next"
import { NextAuthOptions, User } from "next-auth"
import { JWT } from "next-auth/jwt"
import CredentialsProvider from "next-auth/providers/credentials"
import GoogleProvider from "next-auth/providers/google"
import { api } from "./api"

/**
 * Session user type that extends the default NextAuth User
 */
export interface SessionUser extends User {
  id: string
  email: string
  name: string
  companyName?: string
  gstNumber?: string
  companyRegistrationNumber?: string
  isVerified: boolean
  token?: string
}

/**
 * NextAuth configuration options
 */
export const authOptions: NextAuthOptions = {
  providers: [
    CredentialsProvider({
      name: "Credentials",
      credentials: {
        email: { label: "Email", type: "email" },
        password: { label: "Password", type: "password" }
      },
      async authorize(credentials) {
        if (!credentials?.email || !credentials?.password) {
          return null
        }

        try {
          const response = await api.post("/auth/login", {
            email: credentials.email,
            password: credentials.password,
          })

          const { user, token } = response.data

          if (user && token) {
            return {
              ...user,
              token,
            }
          }

          return null
        } catch (error) {
          return null
        }
      }
    }),
    GoogleProvider({
      clientId: process.env.GOOGLE_CLIENT_ID || "",
      clientSecret: process.env.GOOGLE_CLIENT_SECRET || "",
    }),
  ],
  pages: {
    signIn: "/login",
    error: "/login",
  },
  session: {
    strategy: "jwt",
    maxAge: 30 * 24 * 60 * 60, // 30 days
  },
  callbacks: {
    async jwt({ token, user }: { token: JWT, user: any }) {
      if (user) {
        // Add user data to the token when signing in
        token.id = user.id
        token.email = user.email
        token.name = user.name
        token.companyName = user.companyName
        token.gstNumber = user.gstNumber
        token.companyRegistrationNumber = user.companyRegistrationNumber
        token.isVerified = user.isVerified
        token.token = user.token
      }
      return token
    },
    async session({ session, token }: { session: any, token: JWT }) {
      // Add token data to the session
      if (token) {
        session.user.id = token.id
        session.user.email = token.email
        session.user.name = token.name
        session.user.companyName = token.companyName
        session.user.gstNumber = token.gstNumber
        session.user.companyRegistrationNumber = token.companyRegistrationNumber
        session.user.isVerified = token.isVerified
        session.user.token = token.token
      }
      return session
    },
  },
  debug: process.env.NODE_ENV === "development",
}

/**
 * Helper function to get the server session
 */
export async function getSession() {
  return await getServerSession(authOptions)
}

/**
 * Check if the user is authenticated
 */
export async function getCurrentUser() {
  const session = await getSession()
  return session?.user as SessionUser | undefined
}