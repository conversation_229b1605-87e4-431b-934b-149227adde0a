import { NextResponse } from "next/server"
import type { NextRequest } from "next/server"
import { getToken } from "next-auth/jwt"

// Paths that don't require authentication
const publicPaths = ["/login", "/register", "/forgot-password", "/reset-password"]

export async function middleware(request: NextRequest) {
  const { pathname } = request.nextUrl
  
  // Check if the path is public
  const isPublicPath = publicPaths.some(path => 
    pathname === path || pathname.startsWith(`${path}/`)
  )
  
  // Get the token from the request
  const token = await getToken({ 
    req: request,
    secret: process.env.NEXTAUTH_SECRET
  })
  
  // Redirect logic
  if (isPublicPath && token) {
    // If user is logged in and tries to access public path, redirect to dashboard
    return NextResponse.redirect(new URL("/dashboard", request.url))
  }
  
  if (!isPublicPath && !token) {
    // If user is not logged in and tries to access protected path, redirect to login
    return NextResponse.redirect(new URL("/login", request.url))
  }
  
  // If KYC is not completed and user tries to access other pages
  if (token && !token.isVerified && pathname !== "/kyc" && !isPublicPath) {
    // Redirect to KYC page if not verified
    return NextResponse.redirect(new URL("/kyc", request.url))
  }
  
  return NextResponse.next()
}

// Configure the middleware to run on specific paths
export const config = {
  matcher: [
    // Match all paths except for:
    // - API routes
    // - Static files (images, fonts, etc)
    // - Favicon
    "/((?!api|_next/static|_next/image|favicon.ico).*)",
  ],
}