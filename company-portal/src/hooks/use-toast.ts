// Adapted from shadcn/ui toast component
import { useState, useEffect, useCallback } from "react";

const TOAST_LIMIT = 5;
const TOAST_REMOVE_DELAY = 1000000;

type ToastProps = {
  id: string;
  title?: string;
  description?: string;
  action?: React.ReactNode;
  variant?: "default" | "destructive";
};

let count = 0;

function genId() {
  count = (count + 1) % Number.MAX_SAFE_INTEGER;
  return count.toString();
}

const toasts: ToastProps[] = [];

type Toast = Omit<ToastProps, "id">;

export function useToast() {
  const [mounted, setMounted] = useState(false);
  const [toasts, setToasts] = useState<ToastProps[]>([]);

  useEffect(() => {
    setMounted(true);
    return () => {
      setMounted(false);
    };
  }, []);

  const toast = useCallback(
    ({ ...props }: Toast) => {
      const id = genId();

      const newToast = {
        id,
        ...props,
      };

      setToasts((toasts) => [newToast, ...toasts].slice(0, TOAST_LIMIT));

      return id;
    },
    [mounted]
  );

  const dismiss = useCallback((toastId?: string) => {
    setToasts((toasts) => toasts.filter((toast) => toast.id !== toastId));
  }, []);

  const dismissAll = useCallback(() => {
    setToasts([]);
  }, []);

  return {
    toast,
    dismiss,
    dismissAll,
    toasts,
  };
}
