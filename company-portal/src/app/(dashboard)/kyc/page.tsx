"use client";

import type React from "react";

import { useState } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { motion } from "framer-motion";
import * as z from "zod";
import { Upload, CheckCircle, AlertCircle } from "lucide-react";
import { Button } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { useToast } from "@/components/ui/use-toast";
import { useAuth } from "@/hooks/use-auth";
import { pageTransition, fadeIn } from "@/utils/animations";
import {
  useUpdateCompanyKycMutation,
  useUploadDocumentMutation,
} from "@/store/api/userApi";

const kycFormSchema = z.object({
  gstNumber: z
    .string()
    .min(15, {
      message: "GST number must be 15 characters",
    })
    .max(15),
  companyRegistrationNumber: z.string().min(3, {
    message: "Company registration number is required",
  }),
});

export default function KycPage() {
  const { toast } = useToast();
  const { user, updateUser } = useAuth();
  const [gstDoc, setGstDoc] = useState<File | null>(null);
  const [registrationDoc, setRegistrationDoc] = useState<File | null>(null);

  const form = useForm<z.infer<typeof kycFormSchema>>({
    resolver: zodResolver(kycFormSchema),
    defaultValues: {
      gstNumber: user?.gstNumber || "",
      companyRegistrationNumber: user?.companyRegistrationNumber || "",
    },
  });

  // Use RTK Query mutations
  const [updateCompanyKyc, { isLoading: isUpdatingKyc }] =
    useUpdateCompanyKycMutation();
  const [uploadDocument, { isLoading: isUploadingDocument }] =
    useUploadDocumentMutation();

  function onSubmit(values: z.infer<typeof kycFormSchema>) {
    // Update KYC details
    updateCompanyKyc(values)
      .unwrap()
      .then((data) => {
        updateUser(data);
        toast({
          title: "KYC details updated",
          description: "Your KYC details have been updated successfully.",
        });
      })
      .catch(() => {
        toast({
          variant: "destructive",
          title: "Error",
          description: "Failed to update KYC details. Please try again.",
        });
      });

    // Upload GST document if selected
    if (gstDoc) {
      uploadDocument({ file: gstDoc, type: "gst" })
        .unwrap()
        .then((data) => {
          updateUser(data);
          toast({
            title: "Document uploaded",
            description: "Your GST document has been uploaded successfully.",
          });
        })
        .catch(() => {
          toast({
            variant: "destructive",
            title: "Error",
            description: "Failed to upload GST document. Please try again.",
          });
        });
    }

    // Upload registration document if selected
    if (registrationDoc) {
      uploadDocument({ file: registrationDoc, type: "registration" })
        .unwrap()
        .then((data) => {
          updateUser(data);
          toast({
            title: "Document uploaded",
            description:
              "Your registration document has been uploaded successfully.",
          });
        })
        .catch(() => {
          toast({
            variant: "destructive",
            title: "Error",
            description:
              "Failed to upload registration document. Please try again.",
          });
        });
    }
  }

  const handleGstDocChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files[0]) {
      setGstDoc(e.target.files[0]);
    }
  };

  const handleRegistrationDocChange = (
    e: React.ChangeEvent<HTMLInputElement>
  ) => {
    if (e.target.files && e.target.files[0]) {
      setRegistrationDoc(e.target.files[0]);
    }
  };

  return (
    <motion.div
      variants={pageTransition}
      initial="hidden"
      animate="visible"
      exit="exit"
      className="space-y-4 p-8 pt-6"
    >
      <div className="flex items-center justify-between">
        <h2 className="text-3xl font-bold tracking-tight">KYC Verification</h2>
      </div>

      <div className="grid gap-4 md:grid-cols-2">
        <motion.div variants={fadeIn}>
          <Card>
            <CardHeader>
              <CardTitle>Business KYC</CardTitle>
              <CardDescription>
                Provide your business details for verification. This is required
                to post jobs on our platform.
              </CardDescription>
            </CardHeader>
            <CardContent>
              <Form {...form}>
                <form
                  onSubmit={form.handleSubmit(onSubmit)}
                  className="space-y-6"
                >
                  <FormField
                    control={form.control}
                    name="gstNumber"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>GST Number</FormLabel>
                        <FormControl>
                          <Input
                            placeholder="Enter your 15-digit GST number"
                            {...field}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="companyRegistrationNumber"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Company Registration Number</FormLabel>
                        <FormControl>
                          <Input
                            placeholder="Enter your company registration number"
                            {...field}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <div className="space-y-2">
                    <FormLabel>GST Certificate</FormLabel>
                    <div className="flex items-center space-x-4">
                      <Input
                        type="file"
                        accept=".pdf,.jpg,.jpeg,.png"
                        onChange={handleGstDocChange}
                        className="hidden"
                        id="gst-doc"
                      />
                      <Button
                        type="button"
                        variant="outline"
                        onClick={() =>
                          document.getElementById("gst-doc")?.click()
                        }
                      >
                        <Upload className="mr-2 h-4 w-4" />
                        {gstDoc ? "Change File" : "Upload File"}
                      </Button>
                      {gstDoc && <span className="text-sm">{gstDoc.name}</span>}
                      {user?.gstDoc && !gstDoc && (
                        <div className="flex items-center text-sm text-green-600">
                          <CheckCircle className="mr-1 h-4 w-4" />
                          Document uploaded
                        </div>
                      )}
                    </div>
                  </div>

                  <div className="space-y-2">
                    <FormLabel>Company Registration Certificate</FormLabel>
                    <div className="flex items-center space-x-4">
                      <Input
                        type="file"
                        accept=".pdf,.jpg,.jpeg,.png"
                        onChange={handleRegistrationDocChange}
                        className="hidden"
                        id="registration-doc"
                      />
                      <Button
                        type="button"
                        variant="outline"
                        onClick={() =>
                          document.getElementById("registration-doc")?.click()
                        }
                      >
                        <Upload className="mr-2 h-4 w-4" />
                        {registrationDoc ? "Change File" : "Upload File"}
                      </Button>
                      {registrationDoc && (
                        <span className="text-sm">{registrationDoc.name}</span>
                      )}
                      {user?.companyDoc && !registrationDoc && (
                        <div className="flex items-center text-sm text-green-600">
                          <CheckCircle className="mr-1 h-4 w-4" />
                          Document uploaded
                        </div>
                      )}
                    </div>
                  </div>

                  <Button
                    type="submit"
                    className="w-full"
                    disabled={isUpdatingKyc || isUploadingDocument}
                  >
                    {isUpdatingKyc || isUploadingDocument
                      ? "Submitting..."
                      : "Submit KYC Details"}
                  </Button>
                </form>
              </Form>
            </CardContent>
          </Card>
        </motion.div>

        <motion.div variants={fadeIn}>
          <Card>
            <CardHeader>
              <CardTitle>KYC Status</CardTitle>
              <CardDescription>
                Check the status of your KYC verification. You will be notified
                once the verification is complete.
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-2">
                  <div className="h-10 w-10 rounded-full bg-gray-100 flex items-center justify-center">
                    <span className="text-lg font-semibold">1</span>
                  </div>
                  <div>
                    <p className="font-medium">Submit KYC Details</p>
                    <p className="text-sm text-muted-foreground">
                      Provide your business information
                    </p>
                  </div>
                </div>
                {user?.gstNumber && user?.companyRegistrationNumber ? (
                  <CheckCircle className="h-5 w-5 text-green-500" />
                ) : (
                  <div className="h-5 w-5 rounded-full border-2 border-gray-300" />
                )}
              </div>

              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-2">
                  <div className="h-10 w-10 rounded-full bg-gray-100 flex items-center justify-center">
                    <span className="text-lg font-semibold">2</span>
                  </div>
                  <div>
                    <p className="font-medium">Upload Documents</p>
                    <p className="text-sm text-muted-foreground">
                      Upload GST and registration certificates
                    </p>
                  </div>
                </div>
                {user?.gstDoc && user?.companyDoc ? (
                  <CheckCircle className="h-5 w-5 text-green-500" />
                ) : (
                  <div className="h-5 w-5 rounded-full border-2 border-gray-300" />
                )}
              </div>

              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-2">
                  <div className="h-10 w-10 rounded-full bg-gray-100 flex items-center justify-center">
                    <span className="text-lg font-semibold">3</span>
                  </div>
                  <div>
                    <p className="font-medium">Verification</p>
                    <p className="text-sm text-muted-foreground">
                      Our team will verify your documents
                    </p>
                  </div>
                </div>
                {user?.isKycVerified ? (
                  <CheckCircle className="h-5 w-5 text-green-500" />
                ) : (
                  <div className="h-5 w-5 rounded-full border-2 border-gray-300" />
                )}
              </div>

              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-2">
                  <div className="h-10 w-10 rounded-full bg-gray-100 flex items-center justify-center">
                    <span className="text-lg font-semibold">4</span>
                  </div>
                  <div>
                    <p className="font-medium">Approval</p>
                    <p className="text-sm text-muted-foreground">
                      Start posting jobs on our platform
                    </p>
                  </div>
                </div>
                {user?.isKycVerified ? (
                  <CheckCircle className="h-5 w-5 text-green-500" />
                ) : (
                  <div className="h-5 w-5 rounded-full border-2 border-gray-300" />
                )}
              </div>

              <div className="rounded-lg border p-4">
                <div className="flex items-center space-x-2">
                  {user?.isKycVerified ? (
                    <>
                      <CheckCircle className="h-5 w-5 text-green-500" />
                      <p className="font-medium text-green-600">KYC Verified</p>
                    </>
                  ) : user?.gstNumber &&
                    user?.companyRegistrationNumber &&
                    user?.gstDoc &&
                    user?.companyDoc ? (
                    <>
                      <AlertCircle className="h-5 w-5 text-yellow-500" />
                      <p className="font-medium text-yellow-600">
                        Verification Pending
                      </p>
                    </>
                  ) : (
                    <>
                      <AlertCircle className="h-5 w-5 text-red-500" />
                      <p className="font-medium text-red-600">KYC Incomplete</p>
                    </>
                  )}
                </div>
                <p className="mt-2 text-sm text-muted-foreground">
                  {user?.isKycVerified
                    ? "Your KYC has been verified. You can now post jobs on our platform."
                    : user?.gstNumber &&
                        user?.companyRegistrationNumber &&
                        user?.gstDoc &&
                        user?.companyDoc
                      ? "Your KYC is under review. This usually takes 1-2 business days."
                      : "Please complete your KYC by providing all the required information and documents."}
                </p>
              </div>
            </CardContent>
          </Card>
        </motion.div>
      </div>
    </motion.div>
  );
}
