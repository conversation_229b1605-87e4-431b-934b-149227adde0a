"use client"

import { useState } from "react"
import { useForm } from "react-hook-form"
import { zodResolver } from "@hookform/resolvers/zod"
import { useMutation } from "@tanstack/react-query"
import { motion } from "framer-motion"
import * as z from "zod"
import { MessageSquare, ThumbsUp, Send } from "lucide-react"
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { Form, FormControl, FormDescription, FormField, FormItem, FormLabel, FormMessage } from "@/components/ui/form"
import { Input } from "@/components/ui/input"
import { Textarea } from "@/components/ui/textarea"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group"
import { api } from "@/lib/api"
import { useToast } from "@/components/ui/use-toast"
import { pageTransition, slideIn } from "@/utils/animations"

const formSchema = z.object({
  category: z.string({
    required_error: "Please select a feedback category",
  }),
  title: z.string().min(5, {
    message: "Title must be at least 5 characters",
  }),
  description: z.string().min(10, {
    message: "Description must be at least 10 characters",
  }),
  satisfaction: z.enum(["very_dissatisfied", "dissatisfied", "neutral", "satisfied", "very_satisfied"], {
    required_error: "Please select your satisfaction level",
  }),
  email: z
    .string()
    .email({
      message: "Please enter a valid email address",
    })
    .optional(),
})

export default function FeedbackPage() {
  const { toast } = useToast()
  const [isSubmitted, setIsSubmitted] = useState(false)

  const form = useForm<z.infer<typeof formSchema>>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      category: "",
      title: "",
      description: "",
      satisfaction: undefined,
      email: "",
    },
  })

  const submitFeedbackMutation = useMutation({
    mutationFn: async (values: z.infer<typeof formSchema>) => {
      return api.post("/feedback", values)
    },
    onSuccess: () => {
      toast({
        title: "Feedback submitted",
        description: "Thank you for your feedback! We appreciate your input.",
      })
      setIsSubmitted(true)
    },
    onError: () => {
      toast({
        variant: "destructive",
        title: "Error",
        description: "Failed to submit feedback. Please try again.",
      })
    },
  })

  function onSubmit(values: z.infer<typeof formSchema>) {
    submitFeedbackMutation.mutate(values)
  }

  return (
    <motion.div variants={pageTransition} initial="hidden" animate="visible" exit="exit" className="space-y-4 p-8 pt-6">
      <div className="flex items-center justify-between">
        <h2 className="text-3xl font-bold tracking-tight">Feedback</h2>
      </div>

      <div className="grid gap-4 md:grid-cols-2">
        <motion.div variants={slideIn("left")}>
          <Card>
            <CardHeader>
              <CardTitle>Share Your Feedback</CardTitle>
              <CardDescription>Help us improve our platform by sharing your thoughts and suggestions.</CardDescription>
            </CardHeader>
            <CardContent>
              {isSubmitted ? (
                <div className="flex flex-col items-center justify-center py-8">
                  <div className="rounded-full bg-green-100 p-3 dark:bg-green-900">
                    <ThumbsUp className="h-8 w-8 text-green-600 dark:text-green-400" />
                  </div>
                  <h3 className="mt-4 text-lg font-medium">Thank You for Your Feedback!</h3>
                  <p className="mt-2 text-center text-muted-foreground">
                    We appreciate your input and will use it to improve our platform.
                  </p>
                  <Button
                    className="mt-6"
                    onClick={() => {
                      setIsSubmitted(false)
                      form.reset()
                    }}
                  >
                    Submit Another Feedback
                  </Button>
                </div>
              ) : (
                <Form {...form}>
                  <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
                    <FormField
                      control={form.control}
                      name="category"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Category</FormLabel>
                          <Select onValueChange={field.onChange} defaultValue={field.value}>
                            <FormControl>
                              <SelectTrigger>
                                <SelectValue placeholder="Select a category" />
                              </SelectTrigger>
                            </FormControl>
                            <SelectContent>
                              <SelectItem value="general">General Feedback</SelectItem>
                              <SelectItem value="bug">Bug Report</SelectItem>
                              <SelectItem value="feature">Feature Request</SelectItem>
                              <SelectItem value="usability">Usability Issue</SelectItem>
                              <SelectItem value="other">Other</SelectItem>
                            </SelectContent>
                          </Select>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={form.control}
                      name="title"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Title</FormLabel>
                          <FormControl>
                            <Input placeholder="Brief summary of your feedback" {...field} />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={form.control}
                      name="description"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Description</FormLabel>
                          <FormControl>
                            <Textarea
                              placeholder="Please provide details about your feedback"
                              className="min-h-[120px]"
                              {...field}
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={form.control}
                      name="satisfaction"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Overall Satisfaction</FormLabel>
                          <FormControl>
                            <RadioGroup
                              onValueChange={field.onChange}
                              defaultValue={field.value}
                              className="flex space-x-1"
                            >
                              <FormItem className="flex items-center space-x-1 space-y-0">
                                <FormControl>
                                  <RadioGroupItem value="very_dissatisfied" />
                                </FormControl>
                                <FormLabel className="font-normal">Very Dissatisfied</FormLabel>
                              </FormItem>
                              <FormItem className="flex items-center space-x-1 space-y-0">
                                <FormControl>
                                  <RadioGroupItem value="dissatisfied" />
                                </FormControl>
                                <FormLabel className="font-normal">Dissatisfied</FormLabel>
                              </FormItem>
                              <FormItem className="flex items-center space-x-1 space-y-0">
                                <FormControl>
                                  <RadioGroupItem value="neutral" />
                                </FormControl>
                                <FormLabel className="font-normal">Neutral</FormLabel>
                              </FormItem>
                              <FormItem className="flex items-center space-x-1 space-y-0">
                                <FormControl>
                                  <RadioGroupItem value="satisfied" />
                                </FormControl>
                                <FormLabel className="font-normal">Satisfied</FormLabel>
                              </FormItem>
                              <FormItem className="flex items-center space-x-1 space-y-0">
                                <FormControl>
                                  <RadioGroupItem value="very_satisfied" />
                                </FormControl>
                                <FormLabel className="font-normal">Very Satisfied</FormLabel>
                              </FormItem>
                            </RadioGroup>
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={form.control}
                      name="email"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Email (Optional)</FormLabel>
                          <FormControl>
                            <Input placeholder="<EMAIL>" type="email" {...field} />
                          </FormControl>
                          <FormDescription>Provide your email if you'd like us to follow up with you.</FormDescription>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <Button type="submit" className="w-full" disabled={submitFeedbackMutation.isLoading}>
                      {submitFeedbackMutation.isLoading ? (
                        <>Submitting...</>
                      ) : (
                        <>
                          <Send className="mr-2 h-4 w-4" />
                          Submit Feedback
                        </>
                      )}
                    </Button>
                  </form>
                </Form>
              )}
            </CardContent>
          </Card>
        </motion.div>

        <motion.div variants={slideIn("right")}>
          <Card>
            <CardHeader>
              <CardTitle>Why Your Feedback Matters</CardTitle>
              <CardDescription>
                We're committed to continuously improving our platform based on your input.
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex items-start space-x-3">
                <div className="rounded-full bg-primary/10 p-2">
                  <MessageSquare className="h-5 w-5 text-primary" />
                </div>
                <div>
                  <h4 className="font-medium">Shape the Platform</h4>
                  <p className="text-sm text-muted-foreground">
                    Your feedback directly influences our development roadmap and feature priorities.
                  </p>
                </div>
              </div>

              <div className="flex items-start space-x-3">
                <div className="rounded-full bg-primary/10 p-2">
                  <MessageSquare className="h-5 w-5 text-primary" />
                </div>
                <div>
                  <h4 className="font-medium">Improve User Experience</h4>
                  <p className="text-sm text-muted-foreground">
                    Help us identify pain points and usability issues to create a better experience.
                  </p>
                </div>
              </div>

              <div className="flex items-start space-x-3">
                <div className="rounded-full bg-primary/10 p-2">
                  <MessageSquare className="h-5 w-5 text-primary" />
                </div>
                <div>
                  <h4 className="font-medium">Report Bugs</h4>
                  <p className="text-sm text-muted-foreground">
                    Let us know about any issues you encounter so we can fix them promptly.
                  </p>
                </div>
              </div>

              <div className="flex items-start space-x-3">
                <div className="rounded-full bg-primary/10 p-2">
                  <MessageSquare className="h-5 w-5 text-primary" />
                </div>
                <div>
                  <h4 className="font-medium">Suggest New Features</h4>
                  <p className="text-sm text-muted-foreground">
                    Share your ideas for new features that would make your work easier.
                  </p>
                </div>
              </div>
            </CardContent>
            <CardFooter>
              <p className="text-sm text-muted-foreground">
                We review all feedback and use it to guide our development efforts. Thank you for helping us improve!
              </p>
            </CardFooter>
          </Card>
        </motion.div>
      </div>
    </motion.div>
  )
}
