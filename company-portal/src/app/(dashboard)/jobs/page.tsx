"use client";

import { useState } from "react";
import { useRouter } from "next/navigation";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Card, CardContent } from "@/components/ui/card";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { JobCard } from "@/components/job-card";
import { DashboardHeader } from "@/components/dashboard-header";
import { DashboardShell } from "@/components/dashboard-shell";
import type { JobStatus } from "@/types";
import { Loader2, Plus, Search } from "lucide-react";
import { useGetJobsQuery } from "@/store/api/jobsApi";

export default function JobsPage() {
  const router = useRouter();
  const [searchQuery, setSearchQuery] = useState("");
  const [statusFilter, setStatusFilter] = useState<JobStatus | "all">("all");

  // Prepare query parameters
  const queryParams = {
    ...(statusFilter !== "all" && { status: statusFilter }),
    ...(searchQuery && { search: searchQuery }),
  };

  // Use RTK Query to fetch jobs
  const { data, isLoading } = useGetJobsQuery(queryParams);
  const jobs = data?.data || [];

  const handleSearch = (e) => {
    if (e.key === "Enter") {
      // The query will automatically refetch when queryParams change
    }
  };

  return (
    <DashboardShell>
      <DashboardHeader
        heading="Jobs"
        text="Manage your inventory and stock audit jobs"
      >
        <Button onClick={() => router.push("/jobs/create")}>
          <Plus className="mr-2 h-4 w-4" />
          Post New Job
        </Button>
      </DashboardHeader>

      <div className="space-y-4">
        <div className="flex flex-col sm:flex-row gap-4">
          <div className="relative flex-1">
            <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
            <Input
              placeholder="Search jobs..."
              className="pl-8"
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              onKeyDown={handleSearch}
            />
          </div>
          <Select
            value={statusFilter}
            onValueChange={(value) =>
              setStatusFilter(value as JobStatus | "all")
            }
          >
            <SelectTrigger className="w-[180px]">
              <SelectValue placeholder="Filter by status" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All Jobs</SelectItem>
              <SelectItem value="open">Open</SelectItem>
              <SelectItem value="in-progress">In Progress</SelectItem>
              <SelectItem value="completed">Completed</SelectItem>
              <SelectItem value="cancelled">Cancelled</SelectItem>
            </SelectContent>
          </Select>
        </div>

        <Tabs defaultValue="grid" className="space-y-4">
          <TabsList>
            <TabsTrigger value="grid">Grid</TabsTrigger>
            <TabsTrigger value="list">List</TabsTrigger>
          </TabsList>

          <TabsContent value="grid" className="space-y-4">
            {isLoading ? (
              <div className="flex justify-center items-center h-64">
                <Loader2 className="h-8 w-8 animate-spin text-primary" />
              </div>
            ) : jobs.length > 0 ? (
              <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
                {jobs.map((job) => (
                  <JobCard
                    key={job.id}
                    job={job}
                    onClick={() => router.push(`/jobs/${job.id}`)}
                  />
                ))}
              </div>
            ) : (
              <Card>
                <CardContent className="flex flex-col items-center justify-center h-64 space-y-4">
                  <div className="text-center space-y-2">
                    <h3 className="text-xl font-semibold">No jobs found</h3>
                    <p className="text-muted-foreground">
                      {statusFilter !== "all"
                        ? `You don't have any ${statusFilter} jobs.`
                        : "You haven't posted any jobs yet."}
                    </p>
                  </div>
                  <Button onClick={() => router.push("/jobs/create")}>
                    <Plus className="mr-2 h-4 w-4" />
                    Post New Job
                  </Button>
                </CardContent>
              </Card>
            )}
          </TabsContent>

          <TabsContent value="list" className="space-y-4">
            {/* List view implementation */}
            {isLoading ? (
              <div className="flex justify-center items-center h-64">
                <Loader2 className="h-8 w-8 animate-spin text-primary" />
              </div>
            ) : jobs.length > 0 ? (
              <div className="space-y-2">
                {jobs.map((job) => (
                  <Card
                    key={job.id}
                    className="cursor-pointer hover:bg-accent/50 transition-colors"
                    onClick={() => router.push(`/jobs/${job.id}`)}
                  >
                    <CardContent className="p-4">
                      <div className="flex justify-between items-center">
                        <div>
                          <h3 className="font-semibold">{job.title}</h3>
                          <p className="text-sm text-muted-foreground">
                            {job.locationCity}, {job.locationCountry}
                          </p>
                        </div>
                        <div className="text-right">
                          <div
                            className={`text-sm font-medium ${
                              job.status === "open"
                                ? "text-green-600"
                                : job.status === "in-progress"
                                  ? "text-blue-600"
                                  : job.status === "completed"
                                    ? "text-gray-600"
                                    : "text-red-600"
                            }`}
                          >
                            {job.status.charAt(0).toUpperCase() +
                              job.status.slice(1).replace(/-/g, " ")}
                          </div>
                          <p className="text-sm text-muted-foreground">
                            {new Date(job.startDateTime).toLocaleDateString()}
                          </p>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                ))}
              </div>
            ) : (
              <Card>
                <CardContent className="flex flex-col items-center justify-center h-64 space-y-4">
                  <div className="text-center space-y-2">
                    <h3 className="text-xl font-semibold">No jobs found</h3>
                    <p className="text-muted-foreground">
                      {statusFilter !== "all"
                        ? `You don't have any ${statusFilter} jobs.`
                        : "You haven't posted any jobs yet."}
                    </p>
                  </div>
                  <Button onClick={() => router.push("/jobs/create")}>
                    <Plus className="mr-2 h-4 w-4" />
                    Post New Job
                  </Button>
                </CardContent>
              </Card>
            )}
          </TabsContent>
        </Tabs>
      </div>
    </DashboardShell>
  );
}
