"use client";

import type React from "react";

import { useState, use<PERSON><PERSON>back, useEffect } from "react";
import { useR<PERSON><PERSON>, useSearchParams } from "next/navigation";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { motion } from "framer-motion";
import type * as z from "zod";
import { GoogleMap, Marker, useJsApiLoader } from "@react-google-maps/api";
import { CalendarIcon, Clock, MapPin } from "lucide-react";
import { Button } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Checkbox } from "@/components/ui/checkbox";
import {
  Pop<PERSON>,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import { Calendar } from "@/components/ui/calendar";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Slider } from "@/components/ui/slider";
import { useToast } from "@/components/ui/use-toast";
import {
  useGetJobTemplateByIdQuery,
  useCreateJobMutation,
} from "@/store/api/jobsApi";
import { createJobSchema } from "shared/validation";
import { format } from "date-fns";
import { pageTransition, fadeIn } from "@/utils/animations";

const libraries = ["places"];

declare global {
  interface Window {
    google: any;
  }
}

export default function CreateJobPage() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const templateId = searchParams.get("templateId");
  const { toast } = useToast();
  const [location, setLocation] = useState("");
  const [coordinates, setCoordinates] = useState({ lat: 28.6139, lng: 77.209 }); // Default to Delhi
  const [mapCenter, setMapCenter] = useState({ lat: 28.6139, lng: 77.209 });
  const [startTime, setStartTime] = useState("09:00");
  const [endTime, setEndTime] = useState("17:00");
  const [duration, setDuration] = useState(8);

  const { isLoaded } = useJsApiLoader({
    id: "google-map-script",
    googleMapsApiKey: process.env.NEXT_PUBLIC_GOOGLE_MAPS_API_KEY || "",
    libraries: libraries as any,
  });

  const form = useForm<z.infer<typeof createJobSchema>>({
    resolver: zodResolver(createJobSchema),
    defaultValues: {
      title: "",
      description: "",
      location: "",
      latitude: 28.6139,
      longitude: 77.209,
      startDateTime: new Date(),
      endDateTime: new Date(),
      duration: 8,
      trustScoreRequired: 50,
      paymentAmount: 1000,
      requiresLaptop: false,
      requiresSmartphone: false,
      isEmergencyJob: false,
    },
  });

  // Use RTK Query to fetch job template
  const { data: template, isLoading: isTemplateLoading } =
    useGetJobTemplateByIdQuery(templateId || "", { skip: !templateId });

  useEffect(() => {
    if (template) {
      form.setValue("title", template.title);
      form.setValue("description", template.description);
      form.setValue("duration", template.duration);
      setDuration(template.duration);
      form.setValue("trustScoreRequired", template.trustScoreRequired);
      form.setValue("paymentAmount", template.paymentAmount);
      form.setValue("requiresLaptop", template.requiresLaptop);
      form.setValue("requiresSmartphone", template.requiresSmartphone);
      form.setValue("isEmergencyJob", template.isEmergencyJob);
    }
  }, [template, form]);

  // Use RTK Query to create job
  const [createJob, { isLoading: isCreatingJob }] = useCreateJobMutation();

  const onSubmit = (values: z.infer<typeof createJobSchema>) => {
    // Combine date and time for start and end
    const startDate = values.startDateTime;
    const [startHours, startMinutes] = startTime.split(":").map(Number);
    startDate.setHours(startHours, startMinutes);

    const endDate = new Date(startDate);
    endDate.setHours(endDate.getHours() + duration);

    const jobData = {
      ...values,
      startDateTime: startDate.toISOString(),
      endDateTime: endDate.toISOString(),
      latitude: coordinates.lat,
      longitude: coordinates.lng,
      location,
    };

    // Use RTK Query to create job
    createJob(jobData)
      .unwrap()
      .then(() => {
        toast({
          title: "Job created",
          description: "Your job has been created successfully.",
        });
        router.push("/jobs");
      })
      .catch(() => {
        toast({
          variant: "destructive",
          title: "Error",
          description: "Failed to create job. Please try again.",
        });
      });
  };

  const handlePlaceSelect = useCallback(() => {
    const autocomplete = new window.google.maps.places.Autocomplete(
      document.getElementById("location") as HTMLInputElement,
      { types: ["geocode"] }
    );

    autocomplete.addListener("place_changed", () => {
      const place = autocomplete.getPlace();
      if (place.geometry && place.geometry.location) {
        const lat = place.geometry.location.lat();
        const lng = place.geometry.location.lng();
        setCoordinates({ lat, lng });
        setMapCenter({ lat, lng });
        setLocation(place.formatted_address || "");
        form.setValue("location", place.formatted_address || "");
        form.setValue("latitude", lat);
        form.setValue("longitude", lng);
      }
    });
  }, [form]);

  useEffect(() => {
    if (isLoaded) {
      handlePlaceSelect();
    }
  }, [isLoaded, handlePlaceSelect]);

  const handleMapClick = (e: google.maps.MapMouseEvent) => {
    if (e.latLng) {
      const lat = e.latLng.lat();
      const lng = e.latLng.lng();
      setCoordinates({ lat, lng });
      form.setValue("latitude", lat);
      form.setValue("longitude", lng);

      // Reverse geocode to get address
      const geocoder = new window.google.maps.Geocoder();
      geocoder.geocode({ location: { lat, lng } }, (results, status) => {
        if (status === "OK" && results && results[0]) {
          setLocation(results[0].formatted_address);
          form.setValue("location", results[0].formatted_address);
        }
      });
    }
  };

  const handleDurationChange = (value: number[]) => {
    const newDuration = value[0];
    setDuration(newDuration);
    form.setValue("duration", newDuration);

    // Update end time based on start time and duration
    const [hours, minutes] = startTime.split(":").map(Number);
    const startDate = new Date();
    startDate.setHours(hours, minutes, 0, 0);
    const endDate = new Date(startDate);
    endDate.setHours(endDate.getHours() + newDuration);
    setEndTime(format(endDate, "HH:mm"));
  };

  const handleStartTimeChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const newStartTime = e.target.value;
    setStartTime(newStartTime);

    // Update end time based on new start time and duration
    const [hours, minutes] = newStartTime.split(":").map(Number);
    const startDate = new Date();
    startDate.setHours(hours, minutes, 0, 0);
    const endDate = new Date(startDate);
    endDate.setHours(endDate.getHours() + duration);
    setEndTime(format(endDate, "HH:mm"));
  };

  return (
    <motion.div
      variants={pageTransition}
      initial="hidden"
      animate="visible"
      exit="exit"
      className="space-y-4 p-8 pt-6"
    >
      <div className="flex items-center justify-between">
        <h2 className="text-3xl font-bold tracking-tight">Create Job</h2>
      </div>

      <Form {...form}>
        <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-8">
          <div className="grid gap-4 md:grid-cols-2">
            <motion.div variants={fadeIn} className="space-y-4">
              <FormField
                control={form.control}
                name="title"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Job Title</FormLabel>
                    <FormControl>
                      <Input placeholder="Enter job title" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="description"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Job Description</FormLabel>
                    <FormControl>
                      <Textarea
                        placeholder="Enter job description"
                        className="min-h-[120px]"
                        {...field}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="location"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Location</FormLabel>
                    <FormControl>
                      <div className="flex items-center">
                        <MapPin className="mr-2 h-4 w-4 text-muted-foreground" />
                        <Input
                          id="location"
                          placeholder="Enter job location"
                          {...field}
                          value={location}
                          onChange={(e) => {
                            setLocation(e.target.value);
                            field.onChange(e);
                          }}
                        />
                      </div>
                    </FormControl>
                    <FormDescription>
                      Search for a location or click on the map to set the job
                      location
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <div className="space-y-2">
                <FormLabel>Job Date and Time</FormLabel>
                <div className="grid gap-4 md:grid-cols-2">
                  <FormField
                    control={form.control}
                    name="startDateTime"
                    render={({ field }) => (
                      <FormItem className="flex flex-col">
                        <Popover>
                          <PopoverTrigger asChild>
                            <FormControl>
                              <Button
                                variant="outline"
                                className="w-full pl-3 text-left font-normal"
                              >
                                <CalendarIcon className="mr-2 h-4 w-4" />
                                {field.value
                                  ? format(field.value, "PPP")
                                  : "Select date"}
                              </Button>
                            </FormControl>
                          </PopoverTrigger>
                          <PopoverContent className="w-auto p-0" align="start">
                            <Calendar
                              mode="single"
                              selected={field.value}
                              onSelect={field.onChange}
                              initialFocus
                            />
                          </PopoverContent>
                        </Popover>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <div className="flex items-center space-x-2">
                    <Clock className="h-4 w-4 text-muted-foreground" />
                    <Input
                      type="time"
                      value={startTime}
                      onChange={handleStartTimeChange}
                      className="flex-1"
                    />
                  </div>
                </div>
              </div>

              <div className="space-y-2">
                <FormLabel>Job Duration: {duration} hours</FormLabel>
                <FormDescription>
                  Job will end at {endTime} ({duration} hours after start time)
                </FormDescription>
                <Slider
                  defaultValue={[8]}
                  min={1}
                  max={12}
                  step={1}
                  value={[duration]}
                  onValueChange={handleDurationChange}
                />
              </div>

              <div className="grid gap-4 md:grid-cols-2">
                <FormField
                  control={form.control}
                  name="trustScoreRequired"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Minimum Trust Score</FormLabel>
                      <Select
                        onValueChange={(value) =>
                          field.onChange(Number.parseInt(value))
                        }
                        defaultValue={field.value.toString()}
                      >
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue placeholder="Select minimum trust score" />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          <SelectItem value="0">0 - No Requirement</SelectItem>
                          <SelectItem value="20">20 - Low</SelectItem>
                          <SelectItem value="40">40 - Medium</SelectItem>
                          <SelectItem value="60">60 - High</SelectItem>
                          <SelectItem value="80">80 - Very High</SelectItem>
                        </SelectContent>
                      </Select>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="paymentAmount"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Payment Amount (₹)</FormLabel>
                      <FormControl>
                        <Input
                          type="number"
                          placeholder="Enter payment amount"
                          {...field}
                          onChange={(e) =>
                            field.onChange(Number(e.target.value))
                          }
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>

              <div className="space-y-4">
                <FormLabel>Equipment Requirements</FormLabel>
                <div className="flex flex-col space-y-2">
                  <FormField
                    control={form.control}
                    name="requiresLaptop"
                    render={({ field }) => (
                      <FormItem className="flex flex-row items-start space-x-3 space-y-0">
                        <FormControl>
                          <Checkbox
                            checked={field.value}
                            onCheckedChange={field.onChange}
                          />
                        </FormControl>
                        <div className="space-y-1 leading-none">
                          <FormLabel>Requires Laptop</FormLabel>
                          <FormDescription>
                            Worker must bring a laptop to the job
                          </FormDescription>
                        </div>
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="requiresSmartphone"
                    render={({ field }) => (
                      <FormItem className="flex flex-row items-start space-x-3 space-y-0">
                        <FormControl>
                          <Checkbox
                            checked={field.value}
                            onCheckedChange={field.onChange}
                          />
                        </FormControl>
                        <div className="space-y-1 leading-none">
                          <FormLabel>Requires Smartphone</FormLabel>
                          <FormDescription>
                            Worker must bring a smartphone to the job
                          </FormDescription>
                        </div>
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="isEmergencyJob"
                    render={({ field }) => (
                      <FormItem className="flex flex-row items-start space-x-3 space-y-0">
                        <FormControl>
                          <Checkbox
                            checked={field.value}
                            onCheckedChange={field.onChange}
                          />
                        </FormControl>
                        <div className="space-y-1 leading-none">
                          <FormLabel>Emergency Job</FormLabel>
                          <FormDescription>
                            This job is available to workers with low trust
                            scores (below 40)
                          </FormDescription>
                        </div>
                      </FormItem>
                    )}
                  />
                </div>
              </div>
            </motion.div>

            <motion.div variants={fadeIn}>
              <Card>
                <CardContent className="p-4">
                  {isLoaded ? (
                    <GoogleMap
                      mapContainerStyle={{ width: "100%", height: "400px" }}
                      center={mapCenter}
                      zoom={13}
                      onClick={handleMapClick}
                    >
                      <Marker position={coordinates} />
                    </GoogleMap>
                  ) : (
                    <div className="flex h-[400px] items-center justify-center bg-gray-100">
                      <p>Loading map...</p>
                    </div>
                  )}
                </CardContent>
              </Card>
            </motion.div>
          </div>

          <div className="flex justify-end space-x-4">
            <Button
              type="button"
              variant="outline"
              onClick={() => router.push("/jobs")}
            >
              Cancel
            </Button>
            <Button type="submit" disabled={isCreatingJob}>
              {isCreatingJob ? "Creating..." : "Create Job"}
            </Button>
          </div>
        </form>
      </Form>
    </motion.div>
  );
}
