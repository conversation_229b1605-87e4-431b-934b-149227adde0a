"use client";

import { useState } from "react";
import { motion } from "framer-motion";
import { Plus, Search, Copy, Edit, Trash2, MoreH<PERSON>zon<PERSON> } from "lucide-react";
import { useRouter } from "next/navigation";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { useToast } from "@/components/ui/use-toast";
import {
  pageTransition,
  staggerContainer,
  staggerItem,
} from "@/utils/animations";
import {
  useGetJobTemplatesQuery,
  useDeleteJobTemplateMutation,
} from "@/store/api/jobsApi";

export default function JobTemplatesPage() {
  const router = useRouter();
  const { toast } = useToast();
  const [searchQuery, setSearchQuery] = useState("");
  const [deleteTemplateId, setDeleteTemplateId] = useState(null);
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);

  const { data: templates, isLoading } = useGetJobTemplatesQuery();

  const [deleteTemplate, { isLoading: isDeleting }] =
    useDeleteJobTemplateMutation();

  const handleDeleteTemplate = (id) => {
    setDeleteTemplateId(id);
    setIsDeleteDialogOpen(true);
  };

  const confirmDeleteTemplate = () => {
    if (deleteTemplateId) {
      deleteTemplate(deleteTemplateId)
        .unwrap()
        .then(() => {
          toast({
            title: "Template deleted",
            description: "The job template has been deleted successfully.",
          });
          setIsDeleteDialogOpen(false);
        })
        .catch(() => {
          toast({
            variant: "destructive",
            title: "Error",
            description: "Failed to delete the template. Please try again.",
          });
        });
    }
  };

  const handleCreateFromTemplate = (templateId) => {
    router.push(`/jobs/create?templateId=${templateId}`);
  };

  const handleEditTemplate = (templateId) => {
    router.push(`/job-templates/${templateId}/edit`);
  };

  const filteredTemplates = templates?.filter((template) =>
    template.title.toLowerCase().includes(searchQuery.toLowerCase())
  );

  return (
    <motion.div
      variants={pageTransition}
      initial="hidden"
      animate="visible"
      exit="exit"
      className="space-y-4 p-8 pt-6"
    >
      <div className="flex items-center justify-between">
        <h2 className="text-3xl font-bold tracking-tight">Job Templates</h2>
        <Button onClick={() => router.push("/job-templates/create")}>
          <Plus className="mr-2 h-4 w-4" />
          Create Template
        </Button>
      </div>

      <div className="flex items-center space-x-2">
        <div className="relative flex-1">
          <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
          <Input
            placeholder="Search templates..."
            className="pl-8"
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
          />
        </div>
      </div>

      {isLoading ? (
        <div className="flex justify-center p-8">
          <div className="h-8 w-8 animate-spin rounded-full border-4 border-primary border-t-transparent"></div>
        </div>
      ) : filteredTemplates?.length > 0 ? (
        <motion.div
          variants={staggerContainer}
          className="grid gap-4 md:grid-cols-2 lg:grid-cols-3"
        >
          {filteredTemplates.map((template) => (
            <motion.div key={template.id} variants={staggerItem}>
              <Card>
                <CardHeader className="pb-2">
                  <div className="flex items-start justify-between">
                    <CardTitle className="text-xl">{template.title}</CardTitle>
                    <DropdownMenu>
                      <DropdownMenuTrigger asChild>
                        <Button variant="ghost" size="icon">
                          <MoreHorizontal className="h-5 w-5" />
                          <span className="sr-only">Actions</span>
                        </Button>
                      </DropdownMenuTrigger>
                      <DropdownMenuContent align="end">
                        <DropdownMenuItem
                          onClick={() => handleCreateFromTemplate(template.id)}
                        >
                          <Copy className="mr-2 h-4 w-4" />
                          Create Job
                        </DropdownMenuItem>
                        <DropdownMenuItem
                          onClick={() => handleEditTemplate(template.id)}
                        >
                          <Edit className="mr-2 h-4 w-4" />
                          Edit Template
                        </DropdownMenuItem>
                        <DropdownMenuItem
                          className="text-destructive focus:text-destructive"
                          onClick={() => handleDeleteTemplate(template.id)}
                        >
                          <Trash2 className="mr-2 h-4 w-4" />
                          Delete Template
                        </DropdownMenuItem>
                      </DropdownMenuContent>
                    </DropdownMenu>
                  </div>
                  <CardDescription>{template.description}</CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="grid gap-2">
                    <div className="flex items-center justify-between">
                      <span className="text-sm text-muted-foreground">
                        Payment
                      </span>
                      <span className="font-medium">
                        ₹{template.paymentAmount}
                      </span>
                    </div>
                    <div className="flex items-center justify-between">
                      <span className="text-sm text-muted-foreground">
                        Duration
                      </span>
                      <span className="font-medium">
                        {template.duration} hours
                      </span>
                    </div>
                    <div className="flex items-center justify-between">
                      <span className="text-sm text-muted-foreground">
                        Trust Score
                      </span>
                      <span className="font-medium">
                        {template.trustScoreRequired}+
                      </span>
                    </div>
                  </div>
                </CardContent>
                <CardFooter>
                  <Button
                    variant="outline"
                    className="w-full"
                    onClick={() => handleCreateFromTemplate(template.id)}
                  >
                    <Copy className="mr-2 h-4 w-4" />
                    Create Job from Template
                  </Button>
                </CardFooter>
              </Card>
            </motion.div>
          ))}
        </motion.div>
      ) : (
        <Card>
          <CardHeader>
            <CardTitle>No templates found</CardTitle>
            <CardDescription>
              {searchQuery
                ? "No templates match your search query"
                : "You haven't created any job templates yet"}
            </CardDescription>
          </CardHeader>
          <CardContent className="flex flex-col items-center justify-center p-6">
            <p className="text-center text-muted-foreground mb-4">
              Job templates help you quickly create new job postings with
              pre-filled information.
            </p>
            <Button onClick={() => router.push("/job-templates/create")}>
              <Plus className="mr-2 h-4 w-4" />
              Create Your First Template
            </Button>
          </CardContent>
        </Card>
      )}

      <Dialog open={isDeleteDialogOpen} onOpenChange={setIsDeleteDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Delete Template</DialogTitle>
            <DialogDescription>
              Are you sure you want to delete this job template? This action
              cannot be undone.
            </DialogDescription>
          </DialogHeader>
          <DialogFooter>
            <Button
              variant="outline"
              onClick={() => setIsDeleteDialogOpen(false)}
            >
              Cancel
            </Button>
            <Button
              variant="destructive"
              onClick={confirmDeleteTemplate}
              disabled={isDeleting}
            >
              {isDeleting ? "Deleting..." : "Delete"}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </motion.div>
  );
}
