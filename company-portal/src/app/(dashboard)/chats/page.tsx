"use client";

import { useState } from "react";
import { motion } from "framer-motion";
import { Search, MessageSquare } from "lucide-react";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { Ta<PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON>ist, TabsTrigger } from "@/components/ui/tabs";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Badge } from "@/components/ui/badge";
import { staggerContainer, staggerItem } from "@/utils/animations";
import ChatWindow from "@/components/chat/chat-window";
import { useGetChatsQuery } from "@/store/api/chatApi";

export default function ChatsPage() {
  const [selectedChat, setSelectedChat] = useState(null);
  const [searchQuery, setSearchQuery] = useState("");

  const { data: chatsData, isLoading } = useGetChatsQuery({
    page: 1,
    limit: 50,
  });

  const chats = chatsData?.data || [];

  const filteredChats = chats?.filter((chat) => {
    // Find the other participant (not current user)
    const otherParticipant = chat.participants?.find(
      (p) => p.userId !== "current-user-id"
    ); // TODO: Get actual current user ID
    const participantName =
      otherParticipant?.user?.fullName || chat.title || "";
    const contextInfo = chat.contextType === "job" ? "Job Chat" : "";

    return (
      participantName.toLowerCase().includes(searchQuery.toLowerCase()) ||
      contextInfo.toLowerCase().includes(searchQuery.toLowerCase())
    );
  });

  const handleChatSelect = (chat) => {
    setSelectedChat(chat);
  };

  return (
    <div className="flex h-[calc(100vh-4rem)]">
      <div className="w-1/3 border-r p-4 flex flex-col">
        <div className="mb-4">
          <div className="relative">
            <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
            <Input
              placeholder="Search chats..."
              className="pl-8"
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
            />
          </div>
        </div>

        <Tabs defaultValue="all" className="flex-1 flex flex-col">
          <TabsList className="grid w-full grid-cols-3">
            <TabsTrigger value="all">All</TabsTrigger>
            <TabsTrigger value="unread">Unread</TabsTrigger>
            <TabsTrigger value="archived">Archived</TabsTrigger>
          </TabsList>

          <TabsContent value="all" className="flex-1 overflow-y-auto mt-4">
            {isLoading ? (
              <div className="flex justify-center p-8">
                <div className="h-8 w-8 animate-spin rounded-full border-4 border-primary border-t-transparent"></div>
              </div>
            ) : filteredChats?.length > 0 ? (
              <motion.div
                variants={staggerContainer}
                initial="hidden"
                animate="visible"
                className="space-y-2"
              >
                {filteredChats.map((chat) => {
                  // Find the other participant (not current user)
                  const otherParticipant = chat.participants?.find(
                    (p) => p.userId !== "current-user-id"
                  ); // TODO: Get actual current user ID
                  const participantName =
                    otherParticipant?.user?.fullName || chat.title || "Unknown";
                  const participantAvatar =
                    otherParticipant?.user?.profilePic || "/placeholder.svg";
                  const lastMessage =
                    chat.messages?.[0]?.message || "No messages yet";
                  const lastMessageTime = chat.lastMessageAt
                    ? new Date(chat.lastMessageAt).toLocaleTimeString([], {
                        hour: "2-digit",
                        minute: "2-digit",
                      })
                    : "";
                  const contextInfo =
                    chat.contextType === "job" ? "Job Chat" : "Direct Message";

                  // Get unread count for current user
                  const currentUserParticipant = chat.participants?.find(
                    (p) => p.userId === "current-user-id"
                  ); // TODO: Get actual current user ID
                  const unreadCount = currentUserParticipant?.unreadCount || 0;

                  return (
                    <motion.div key={chat.id} variants={staggerItem}>
                      <Button
                        variant="ghost"
                        className={`w-full justify-start p-3 ${
                          selectedChat?.id === chat.id ? "bg-accent" : ""
                        }`}
                        onClick={() => handleChatSelect(chat)}
                      >
                        <div className="flex items-start w-full">
                          <Avatar className="h-10 w-10 mr-3">
                            <AvatarImage src={participantAvatar} />
                            <AvatarFallback>
                              {participantName.charAt(0).toUpperCase()}
                            </AvatarFallback>
                          </Avatar>
                          <div className="flex-1 overflow-hidden">
                            <div className="flex justify-between items-center">
                              <p className="font-medium text-left truncate">
                                {participantName}
                              </p>
                              <span className="text-xs text-muted-foreground">
                                {lastMessageTime}
                              </span>
                            </div>
                            <p className="text-sm text-muted-foreground text-left truncate">
                              {lastMessage}
                            </p>
                            <p className="text-xs text-muted-foreground text-left truncate mt-1">
                              {contextInfo}
                            </p>
                          </div>
                          {unreadCount > 0 && (
                            <Badge className="ml-2 bg-primary">
                              {unreadCount}
                            </Badge>
                          )}
                        </div>
                      </Button>
                    </motion.div>
                  );
                })}
              </motion.div>
            ) : (
              <div className="flex flex-col items-center justify-center h-full p-8">
                <MessageSquare className="h-12 w-12 text-muted-foreground mb-4" />
                <h3 className="text-lg font-medium">No chats found</h3>
                <p className="text-sm text-muted-foreground text-center mt-2">
                  {searchQuery
                    ? "No chats match your search query"
                    : "You don't have any active chats yet"}
                </p>
              </div>
            )}
          </TabsContent>

          <TabsContent value="unread" className="flex-1 overflow-y-auto">
            {/* Similar content for unread chats */}
          </TabsContent>

          <TabsContent value="archived" className="flex-1 overflow-y-auto">
            {/* Similar content for archived chats */}
          </TabsContent>
        </Tabs>
      </div>

      <div className="flex-1">
        {selectedChat ? (
          <ChatWindow chat={selectedChat} />
        ) : (
          <div className="flex flex-col items-center justify-center h-full">
            <MessageSquare className="h-16 w-16 text-muted-foreground mb-4" />
            <h2 className="text-xl font-medium">
              Select a chat to start messaging
            </h2>
            <p className="text-muted-foreground mt-2">
              Choose a conversation from the list to view messages
            </p>
          </div>
        )}
      </div>
    </div>
  );
}
