import { UserRole } from "@shared/types";
import {
  ApiResponse as SharedApiResponse,
  PaginatedResponse as SharedPaginatedResponse,
  PaginationMeta as SharedPaginationMeta,
  ApiErrorResponse as SharedApiErrorResponse,
  PaginationParams as SharedPaginationParams,
} from "@shared/types/api-response";
import { Job as SharedJob, JobStatus } from "@shared/types/job";
import {
  Application as SharedApplication,
  ApplicationStatus,
} from "@shared/types/application";

/**
 * Re-export shared API response types
 */
export type ApiResponse<T> = SharedApiResponse<T>;
export type PaginationMeta = SharedPaginationMeta;
export type PaginatedResponse<T> = SharedPaginatedResponse<T>;
export type ApiErrorResponse = SharedApiErrorResponse;
export type PaginationParams = SharedPaginationParams;

/**
 * User profile interface
 */
export interface UserProfile {
  id: string;
  email: string;
  fullName: string;
  role: UserRole;
  isVerified: boolean;
  isKycVerified: boolean;
  profilePic?: string;
  address?: string;
  city?: string;
  state?: string;
  country?: string;
  postalCode?: string;
  createdAt: string;
  updatedAt: string;
  company?: CompanyProfile;
}

/**
 * Company profile interface
 */
export interface CompanyProfile {
  id: string;
  name: string;
  registrationNumber?: string;
  taxId?: string;
  website?: string;
  size?: string;
  industry?: string;
  description?: string;
  logo?: string;
  address?: string;
  city?: string;
  state?: string;
  country?: string;
  postalCode?: string;
  isKycVerified: boolean;
  isActive: boolean;
  isBanned: boolean;
  userId: string;
  createdAt: string;
  updatedAt: string;
}

/**
 * Register request interface
 */
export interface RegisterRequest {
  fullName: string;
  email: string;
  password: string;
  role: UserRole;
  companyName?: string;
  companyRegistrationNumber?: string;
}

/**
 * Login request interface
 */
export interface LoginRequest {
  email: string;
  password: string;
}

/**
 * Auth response interface
 */
export interface AuthResponse {
  token: string;
  refreshToken: string;
  user: UserProfile;
}

/**
 * Update profile request interface
 */
export interface UpdateProfileRequest {
  fullName?: string;
  address?: string;
  city?: string;
  state?: string;
  country?: string;
  postalCode?: string;
}

/**
 * Update company request interface
 */
export interface UpdateCompanyRequest {
  name?: string;
  registrationNumber?: string;
  taxId?: string;
  website?: string;
  size?: string;
  industry?: string;
  description?: string;
  address?: string;
  city?: string;
  state?: string;
  country?: string;
  postalCode?: string;
}

/**
 * KYC update request interface
 */
export interface KycUpdateRequest {
  registrationNumber?: string;
  taxId?: string;
  documentUrls?: string[];
}

/**
 * Document interface
 */
export interface Document {
  id: string;
  userId: string;
  documentType: string;
  documentUrl: string;
  documentNumber?: string;
  isVerified: boolean;
  createdAt: string;
  updatedAt: string;
}

/**
 * Job interface
 */
export interface Job extends SharedJob {
  // Additional fields specific to company portal
}

/**
 * Create job request interface
 */
export interface CreateJobRequest {
  title: string;
  description: string;
  location: string;
  city?: string;
  state?: string;
  country?: string;
  postalCode?: string;
  latitude?: number;
  longitude?: number;
  startDateTime: string;
  endDateTime: string;
  paymentAmount: number;
  duration: number;
  trustScoreRequired: number;
  requiredWorkers: number;
  requiresLaptop: boolean;
  requiresSmartphone: boolean;
  skillsRequired?: string;
  isEmergencyJob: boolean;
}

/**
 * Application interface
 */
export interface Application {
  id: string;
  workerId: string;
  worker: UserProfile;
  jobId: string;
  job: Job;
  status: string;
  coverLetter?: string;
  rejectionReason?: string;
  cancellationReason?: string;
  createdAt: string;
  updatedAt: string;
}

/**
 * Rating interface
 */
export interface Rating {
  id: string;
  jobId: string;
  job: Job;
  ratedById: string;
  ratedBy: UserProfile;
  ratedUserId: string;
  ratedUser: UserProfile;
  rating: number;
  comment?: string;
  createdAt: string;
  updatedAt: string;
}

/**
 * Payout interface
 */
export interface Payout {
  id: string;
  jobId: string;
  job: Job;
  workerId: string;
  worker: UserProfile;
  amount: number;
  status: string;
  paymentMethod?: string;
  transactionId?: string;
  createdAt: string;
  updatedAt: string;
}

/**
 * Job template interface
 */
export interface JobTemplate {
  id: string;
  companyId: string;
  title: string;
  description: string;
  paymentAmount: number;
  duration: number;
  trustScoreRequired: number;
  requiredWorkers: number;
  requiresLaptop: boolean;
  requiresSmartphone: boolean;
  skillsRequired?: string;
  createdAt: string;
  updatedAt: string;
}

/**
 * Notification interface
 */
export interface Notification {
  id: string;
  userId: string;
  title: string;
  message: string;
  type: string;
  isRead: boolean;
  data?: Record<string, string>;
  createdAt: string;
  updatedAt: string;
}

/**
 * Company stats interface
 */
export interface CompanyStats {
  totalJobs: number;
  activeJobs: number;
  completedJobs: number;
  totalWorkers: number;
  totalPayments: number;
  averageRating: number;
}

/**
 * Job stats interface
 */
export interface JobStats {
  jobsCreated: number;
  jobsCompleted: number;
  totalApplications: number;
  acceptedApplications: number;
  rejectedApplications: number;
  noShowApplications: number;
  averagePayment: number;
}

/**
 * Worker stats interface
 */
export interface WorkerStats {
  totalWorkers: number;
  activeWorkers: number;
  topRatedWorkers: UserProfile[];
  mostHiredWorkers: UserProfile[];
}
