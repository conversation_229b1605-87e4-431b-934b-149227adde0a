"use client";

import { api } from "./apiSlice";
import { PaymentGateway } from "@shared/types";

/**
 * Payment-related types
 */
export interface UnifiedPaymentRequest {
  amount: number;
  currency: string;
  description?: string;
  preferredGateway?: PaymentGateway;
  paymentMethodId?: string;
  customerId?: string;
  customerEmail?: string;
  customerPhone?: string;
  orderId?: string;
  metadata?: Record<string, unknown>;
}

export interface PaymentResult {
  success: boolean;
  transactionId?: string;
  orderId?: string;
  status: string;
  message?: string;
  gateway: string;
  paymentMethod: string;
  clientSecret?: string;
  redirectUrl?: string;
  gatewayResponse?: Record<string, unknown>;
}

export interface RefundRequest {
  transactionId: string;
  amount?: number;
  reason?: string;
  metadata?: Record<string, unknown>;
}

export interface RefundResult {
  success: boolean;
  refundId: string;
  amount: number;
  status: "pending" | "succeeded" | "failed";
  gateway: string;
  gatewayResponse?: Record<string, unknown>;
}

export interface PaymentGatewayConfig {
  gateway: string;
  enabled: boolean;
  supportedCurrencies: string[];
  supportedMethods: string[];
  defaultCurrency: string;
}

export interface AvailablePaymentMethods {
  currency: string;
  methods: string[];
}

/**
 * Payments API endpoints using RTK Query
 */
export const paymentsApi = api.injectEndpoints({
  endpoints: (builder) => ({
    // New unified payment endpoints
    processUnifiedPayment: builder.mutation<PaymentResult, UnifiedPaymentRequest>({
      query: (data) => ({
        url: "/payments/process",
        method: "POST",
        body: data,
      }),
      invalidatesTags: ["Payouts"],
    }),

    createUnifiedPaymentIntent: builder.mutation<PaymentResult, UnifiedPaymentRequest>({
      query: (data) => ({
        url: "/payments/intent",
        method: "POST",
        body: data,
      }),
    }),

    refundPayment: builder.mutation<RefundResult, RefundRequest>({
      query: (data) => ({
        url: "/payments/refund",
        method: "POST",
        body: data,
      }),
      invalidatesTags: ["Payouts"],
    }),

    getGatewayConfigurations: builder.query<PaymentGatewayConfig[], void>({
      query: () => "/payments/gateways",
    }),

    getAvailablePaymentMethods: builder.query<AvailablePaymentMethods, string>({
      query: (currency) => ({
        url: "/payments/methods/available",
        params: { currency },
      }),
    }),

    // Legacy endpoints (deprecated but maintained for backward compatibility)
    processStripePayment: builder.mutation<
      {
        success: boolean;
        clientSecret: string;
        paymentIntentId: string;
      },
      {
        jobId: string;
        amount: number;
        currency: string;
        description: string;
      }
    >({
      query: (data) => ({
        url: "/payments/stripe",
        method: "POST",
        body: data,
      }),
      invalidatesTags: ["Payouts"],
    }),

    processRazorpayPayment: builder.mutation<
      {
        success: boolean;
        transactionId: string;
        gatewayResponse: Record<string, unknown>;
      },
      {
        jobId: string;
        amount: number;
        currency: string;
        description: string;
        customerEmail?: string;
        customerPhone?: string;
      }
    >({
      query: (data) => ({
        url: "/payments/razorpay",
        method: "POST",
        body: data,
      }),
      invalidatesTags: ["Payouts"],
    }),

    createRazorpayOrder: builder.mutation<
      {
        success: boolean;
        transactionId: string;
        gatewayResponse: Record<string, unknown>;
      },
      {
        jobId: string;
        amount: number;
        currency: string;
        description: string;
        customerEmail?: string;
        customerPhone?: string;
      }
    >({
      query: (data) => ({
        url: "/payments/razorpay/order",
        method: "POST",
        body: data,
      }),
    }),

    verifyRazorpaySignature: builder.mutation<
      { verified: boolean },
      {
        orderId: string;
        paymentId: string;
        signature: string;
      }
    >({
      query: (data) => ({
        url: "/payments/razorpay/verify",
        method: "POST",
        body: data,
      }),
    }),

    getPaymentMethods: builder.query<
      {
        data: Array<{
          id: string;
          name: string;
          description: string;
          enabled: boolean;
        }>;
      },
      void
    >({
      query: () => "/payments/methods",
    }),
  }),
  overrideExisting: false,
});

export const {
  // New unified hooks
  useProcessUnifiedPaymentMutation,
  useCreateUnifiedPaymentIntentMutation,
  useRefundPaymentMutation,
  useGetGatewayConfigurationsQuery,
  useGetAvailablePaymentMethodsQuery,
  
  // Legacy hooks
  useProcessStripePaymentMutation,
  useProcessRazorpayPaymentMutation,
  useCreateRazorpayOrderMutation,
  useVerifyRazorpaySignatureMutation,
  useGetPaymentMethodsQuery,
} = paymentsApi;
