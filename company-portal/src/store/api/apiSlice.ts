"use client";

import { createApi, fetchBaseQuery } from '@reduxjs/toolkit/query/react';
import type { RootState } from '../index';
import STORAGE_KEYS from '../../constants/storage-keys';

/**
 * Tag types for cache invalidation
 */
export type ApiTagTypes =
  | 'User'
  | 'Company'
  | 'CompanyProfile'
  | 'CompanyDocuments'
  | 'Job'
  | 'Jobs'
  | 'JobTemplate'
  | 'JobTemplates'
  | 'Application'
  | 'Applications'
  | 'Notification'
  | 'Notifications'
  | 'Rating'
  | 'Ratings'
  | 'Payout'
  | 'Payouts'
  | 'Analytics'
  | 'Chat'
  | 'Chats';

/**
 * Base API slice using RTK Query
 * This handles all API requests with proper error handling and caching
 */
export const api = createApi({
  reducerPath: 'api',
  baseQuery: fetchBaseQuery({
    baseUrl: process.env.NEXT_PUBLIC_API_URL || 'http://localhost:3040/api/v1',
    prepareHeaders: (headers, { getState }) => {
      // Try fetching token from state, otherwise localStorage
      const state = getState() as RootState;
      const token = state.auth.token || (typeof window !== 'undefined' ? localStorage.getItem(STORAGE_KEYS.AUTH_TOKEN) : null);

      if (token) {
        headers.set('Authorization', `Bearer ${token}`);
      }

      headers.set('Content-Type', 'application/json');
      return headers;
    },
  }),
  tagTypes: [
    'User',
    'Company',
    'CompanyProfile',
    'CompanyDocuments',
    'Job',
    'Jobs',
    'JobTemplate',
    'JobTemplates',
    'Application',
    'Applications',
    'Notification',
    'Notifications',
    'Rating',
    'Ratings',
    'Payout',
    'Payouts',
    'Analytics',
    'Chat',
    'Chats',
  ],
  endpoints: () => ({}),
});

/**
 * Custom error handler for API requests
 */
export const handleApiError = (error: unknown): ApiError => {
  if (error && typeof error === 'object' && 'data' in error) {
    // RTK Query error format
    return error.data as ApiError;
  }

  // Default error format
  return {
    statusCode: 500,
    message: error instanceof Error ? error.message : 'An unexpected error occurred',
    error: 'Internal Server Error',
  };
};

/**
 * API Error interface
 */
export interface ApiError {
  statusCode: number;
  message: string;
  error?: string;
}
