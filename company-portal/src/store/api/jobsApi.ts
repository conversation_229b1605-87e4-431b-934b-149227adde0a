"use client";

import { api } from "./apiSlice";
import type {
  Job,
  CreateJobRequest,
  Application,
  JobTemplate,
  Rating,
} from "../../types/api";

/**
 * Jobs API endpoints using RTK Query
 */
export const jobsApi = api.injectEndpoints({
  endpoints: (builder) => ({
    getJobs: builder.query<
      ApiResponse<Job[]>,
      { status?: string; page?: number; limit?: number; search?: string } | void
    >({
      query: (params) => ({
        url: "/jobs/company",
        params,
      }),
      providesTags: (result) =>
        result
          ? [
              ...result.data.map(({ id }) => ({ type: "Job" as const, id })),
              { type: "Jobs" as const, id: "LIST" },
            ]
          : [{ type: "Jobs" as const, id: "LIST" }],
    }),

    getJobById: builder.query<Job, string>({
      query: (id) => `/jobs/${id}`,
      providesTags: (result, error, id) => [{ type: "Job", id }],
    }),

    createJob: builder.mutation<Job, CreateJobRequest>({
      query: (data) => ({
        url: "/jobs",
        method: "POST",
        body: data,
      }),
      invalidatesTags: [{ type: "Jobs", id: "LIST" }],
    }),

    updateJob: builder.mutation<
      Job,
      { id: string; data: Partial<CreateJobRequest> }
    >({
      query: ({ id, data }) => ({
        url: `/jobs/${id}`,
        method: "PATCH",
        body: data,
      }),
      invalidatesTags: (result, error, { id }) => [
        { type: "Job", id },
        { type: "Jobs", id: "LIST" },
      ],
    }),

    deleteJob: builder.mutation<{ success: boolean; message: string }, string>({
      query: (id) => ({
        url: `/jobs/${id}`,
        method: "DELETE",
      }),
      invalidatesTags: [{ type: "Jobs", id: "LIST" }],
    }),

    updateJobStatus: builder.mutation<Job, { id: string; status: string }>({
      query: ({ id, status }) => ({
        url: `/jobs/${id}/status`,
        method: "PATCH",
        body: { status },
      }),
      invalidatesTags: (result, error, { id }) => [
        { type: "Job", id },
        { type: "Jobs", id: "LIST" },
      ],
    }),

    getJobApplications: builder.query<Application[], string>({
      query: (jobId) => `/jobs/${jobId}/applications`,
      providesTags: (result) =>
        result
          ? [
              ...result.map(({ id }) => ({ type: "Application" as const, id })),
              { type: "Applications" as const, id: "LIST" },
            ]
          : [{ type: "Applications" as const, id: "LIST" }],
    }),

    updateApplicationStatus: builder.mutation<
      Application,
      { jobId: string; applicationId: string; status: string; reason?: string }
    >({
      query: ({ jobId, applicationId, status, reason }) => ({
        url: `/jobs/${jobId}/applications/${applicationId}/status`,
        method: "PATCH",
        body: { status, reason },
      }),
      invalidatesTags: (result, error, { applicationId }) => [
        { type: "Application", id: applicationId },
        { type: "Applications", id: "LIST" },
      ],
    }),

    // Job Templates
    getJobTemplates: builder.query<JobTemplate[], void>({
      query: () => "/job-templates",
      providesTags: (result) =>
        result
          ? [
              ...result.map(({ id }) => ({ type: "JobTemplate" as const, id })),
              { type: "JobTemplates" as const, id: "LIST" },
            ]
          : [{ type: "JobTemplates" as const, id: "LIST" }],
    }),

    getJobTemplateById: builder.query<JobTemplate, string>({
      query: (id) => `/job-templates/${id}`,
      providesTags: (result, error, id) => [{ type: "JobTemplate", id }],
    }),

    createJobTemplate: builder.mutation<
      JobTemplate,
      {
        title: string;
        description: string;
        paymentAmount: number;
        duration: number;
        trustScoreRequired: number;
        requiredWorkers: number;
        requiresLaptop: boolean;
        requiresSmartphone: boolean;
        skillsRequired?: string;
      }
    >({
      query: (data) => ({
        url: "/job-templates",
        method: "POST",
        body: data,
      }),
      invalidatesTags: [{ type: "JobTemplates", id: "LIST" }],
    }),

    updateJobTemplate: builder.mutation<
      JobTemplate,
      {
        id: string;
        data: Partial<{
          title: string;
          description: string;
          paymentAmount: number;
          duration: number;
          trustScoreRequired: number;
          requiredWorkers: number;
          requiresLaptop: boolean;
          requiresSmartphone: boolean;
          skillsRequired?: string;
        }>;
      }
    >({
      query: ({ id, data }) => ({
        url: `/job-templates/${id}`,
        method: "PATCH",
        body: data,
      }),
      invalidatesTags: (result, error, { id }) => [
        { type: "JobTemplate", id },
        { type: "JobTemplates", id: "LIST" },
      ],
    }),

    deleteJobTemplate: builder.mutation<
      { success: boolean; message: string },
      string
    >({
      query: (id) => ({
        url: `/job-templates/${id}`,
        method: "DELETE",
      }),
      invalidatesTags: [{ type: "JobTemplates", id: "LIST" }],
    }),

    // Ratings
    getRatings: builder.query<Rating[], void>({
      query: () => "/ratings",
      providesTags: ["Ratings"],
    }),

    createRating: builder.mutation<
      Rating,
      { jobId: string; ratedUserId: string; rating: number; comment?: string }
    >({
      query: (data) => ({
        url: "/ratings",
        method: "POST",
        body: data,
      }),
      invalidatesTags: ["Ratings"],
    }),
  }),
  overrideExisting: false,
});

export const {
  useGetJobsQuery,
  useGetJobByIdQuery,
  useCreateJobMutation,
  useUpdateJobMutation,
  useDeleteJobMutation,
  useUpdateJobStatusMutation,
  useGetJobApplicationsQuery,
  useUpdateApplicationStatusMutation,
  useGetJobTemplatesQuery,
  useGetJobTemplateByIdQuery,
  useCreateJobTemplateMutation,
  useUpdateJobTemplateMutation,
  useDeleteJobTemplateMutation,
  useGetRatingsQuery,
  useCreateRatingMutation,
} = jobsApi;
