"use client";

import { api } from './apiSlice';
import type { 
  CompanyProfile, 
  UpdateProfileRequest, 
  KycUpdateRequest, 
  Document,
  CompanyStats,
  JobStats,
  WorkerStats
} from '../../types/api';

/**
 * Company API endpoints using RTK Query
 */
export const companyApi = api.injectEndpoints({
  endpoints: (builder) => ({
    getCompanyProfile: builder.query<CompanyProfile, void>({
      query: () => '/companies/profile',
      providesTags: ['CompanyProfile'],
    }),

    updateCompanyProfile: builder.mutation<CompanyProfile, UpdateProfileRequest>({
      query: (data) => ({
        url: '/companies/profile',
        method: 'PATCH',
        body: data,
      }),
      invalidatesTags: ['CompanyProfile'],
    }),

    updateCompanyKyc: builder.mutation<CompanyProfile, KycUpdateRequest>({
      query: (data) => ({
        url: '/companies/kyc',
        method: 'PATCH',
        body: data,
      }),
      invalidatesTags: ['CompanyProfile'],
    }),

    uploadCompanyDocument: builder.mutation<
      Document,
      { documentType: string; documentUrl: string; documentNumber?: string }
    >({
      query: (data) => ({
        url: '/companies/documents',
        method: 'POST',
        body: data,
      }),
      invalidatesTags: ['CompanyDocuments'],
    }),

    getCompanyDocuments: builder.query<Document[], void>({
      query: () => '/companies/documents',
      providesTags: ['CompanyDocuments'],
    }),

    getCompanyStats: builder.query<CompanyStats, void>({
      query: () => '/analytics/company',
      providesTags: ['Analytics'],
    }),

    getJobStats: builder.query<JobStats, { period?: 'day' | 'week' | 'month' | 'year' }>({
      query: (params) => ({
        url: '/analytics/jobs',
        params,
      }),
      providesTags: ['Analytics'],
    }),

    getWorkerStats: builder.query<WorkerStats, void>({
      query: () => '/analytics/workers',
      providesTags: ['Analytics'],
    }),
  }),
  overrideExisting: false,
});

export const {
  useGetCompanyProfileQuery,
  useUpdateCompanyProfileMutation,
  useUpdateCompanyKycMutation,
  useUploadCompanyDocumentMutation,
  useGetCompanyDocumentsQuery,
  useGetCompanyStatsQuery,
  useGetJobStatsQuery,
  useGetWorkerStatsQuery,
} = companyApi;
