"use client";

import { api } from './apiSlice';
import type { UserProfile, UpdateProfileRequest, Document } from '../../types/api';

/**
 * User API endpoints using RTK Query
 */
export const userApi = api.injectEndpoints({
  endpoints: (builder) => ({
    getUserProfile: builder.query<UserProfile, void>({
      query: () => '/users/profile',
      providesTags: ['User'],
    }),

    updateUserProfile: builder.mutation<UserProfile, UpdateProfileRequest>({
      query: (data) => ({
        url: '/users/profile',
        method: 'PATCH',
        body: data,
      }),
      invalidatesTags: ['User'],
    }),

    getUserDocuments: builder.query<Document[], void>({
      query: () => '/documents',
      providesTags: ['User'],
    }),

    updateCompanyKyc: builder.mutation<
      UserProfile,
      {
        gstNumber: string;
        companyRegistrationNumber: string;
      }
    >({
      query: (data) => ({
        url: '/users/company-kyc',
        method: 'PATCH',
        body: data,
      }),
      invalidatesTags: ['User', 'CompanyProfile'],
    }),

    uploadDocument: builder.mutation<
      UserProfile,
      { file: File; type: string }
    >({
      query: ({ file, type }) => {
        const formData = new FormData();
        formData.append('file', file);
        formData.append('type', type);

        return {
          url: '/users/upload-document',
          method: 'POST',
          body: formData,
          formData: true,
        };
      },
      invalidatesTags: ['User', 'CompanyProfile'],
    }),

    changePassword: builder.mutation<
      { success: boolean; message: string },
      { currentPassword: string; newPassword: string }
    >({
      query: (data) => ({
        url: '/users/change-password',
        method: 'POST',
        body: data,
      }),
    }),

    deleteAccount: builder.mutation<
      { success: boolean; message: string },
      { password: string }
    >({
      query: (data) => ({
        url: '/users/delete-account',
        method: 'POST',
        body: data,
      }),
    }),
  }),
  overrideExisting: false,
});

export const {
  useGetUserProfileQuery,
  useUpdateUserProfileMutation,
  useGetUserDocumentsQuery,
  useUpdateCompanyKycMutation,
  useUploadDocumentMutation,
  useChangePasswordMutation,
  useDeleteAccountMutation,
} = userApi;
