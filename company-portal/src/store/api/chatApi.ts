"use client";

import { api } from "./apiSlice";
import type {
  GenericChat,
  ChatMessage,
  ChatType,
  ParticipantRole,
  MessageType,
} from "@/shared-types";
import { PaginatedResponse } from "@/shared-types";

/**
 * Create generic chat request interface
 */
export interface CreateGenericChatRequest {
  type: ChatType;
  contextId?: string;
  contextType?: string;
  participants: Array<{
    userId: string;
    role: ParticipantRole;
  }>;
  metadata?: Record<string, any>;
}

/**
 * Create message request interface
 */
export interface CreateGenericMessageRequest {
  message: string;
  messageType?: MessageType;
}

/**
 * Chat API endpoints using RTK Query
 */
export const chatApi = api.injectEndpoints({
  endpoints: (builder) => ({
    getChats: builder.query<
      PaginatedResponse<GenericChat>,
      { page?: number; limit?: number }
    >({
      query: (params) => ({
        url: "/generic-chats",
        params,
      }),
    }),

    getChatById: builder.query<GenericChat, string>({
      query: (id) => `/generic-chats/${id}`,
    }),

    getChatMessages: builder.query<
      PaginatedResponse<ChatMessage>,
      { chatId: string; page?: number; limit?: number }
    >({
      query: ({ chatId, ...params }) => ({
        url: `/generic-chats/${chatId}/messages`,
        params,
      }),
    }),

    createChat: builder.mutation<GenericChat, CreateGenericChatRequest>({
      query: (data) => ({
        url: "/generic-chats",
        method: "POST",
        body: data,
      }),
    }),

    sendMessage: builder.mutation<
      ChatMessage,
      { chatId: string; data: CreateGenericMessageRequest }
    >({
      query: ({ chatId, data }) => ({
        url: `/generic-chats/${chatId}/messages`,
        method: "POST",
        body: data,
      }),
    }),

    markChatAsRead: builder.mutation<{ success: boolean }, string>({
      query: (chatId) => ({
        url: `/generic-chats/${chatId}/read`,
        method: "POST",
      }),
    }),
  }),
  overrideExisting: false,
});

export const {
  useGetChatsQuery,
  useGetChatByIdQuery,
  useGetChatMessagesQuery,
  useCreateChatMutation,
  useSendMessageMutation,
  useMarkChatAsReadMutation,
} = chatApi;
