"use client";

import React, { useEffect } from 'react';
import { Provider } from 'react-redux';
import { store } from './index';
import { loadStoredAuth } from './slices/authSlice';
import STORAGE_KEYS from '../constants/storage-keys';
import AuthRTKProvider from '../providers/AuthRTKProvider';

interface ReduxProviderProps {
  children: React.ReactNode;
}

/**
 * Redux Provider component that wraps the app and loads stored auth data
 */
export const ReduxProvider: React.FC<ReduxProviderProps> = ({ children }) => {
  useEffect(() => {
    // Load stored auth data on app start (client-side only)
    const loadAuthData = () => {
      try {
        if (typeof window !== 'undefined') {
          const userString = localStorage.getItem(STORAGE_KEYS.USER_DATA);
          const token = localStorage.getItem(STORAGE_KEYS.AUTH_TOKEN);
          const refreshToken = localStorage.getItem(STORAGE_KEYS.AUTH_REFRESH_TOKEN);

          store.dispatch(
            loadStoredAuth({
              user: userString ? JSON.parse(userString) : null,
              token,
              refreshToken,
            })
          );
        }
      } catch (error) {
        console.error('Error loading stored auth data:', error);
        store.dispatch(
          loadStoredAuth({
            user: null,
            token: null,
            refreshToken: null,
          })
        );
      }
    };

    loadAuthData();
  }, []);

  return (
    <Provider store={store}>
      <AuthRTKProvider>{children}</AuthRTKProvider>
    </Provider>
  );
};

export default ReduxProvider;
