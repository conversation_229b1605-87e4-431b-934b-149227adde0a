import Link from "next/link"
import { format } from "date-fns"
import { CalendarIcon, MapPinIcon, UsersIcon, LaptopIcon, SmartphoneIcon } from "lucide-react"
import { Badge } from "@/components/ui/badge"
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"

const statusColors = {
  open: "bg-blue-500",
  in_progress: "bg-yellow-500",
  completed: "bg-green-500",
  cancelled: "bg-red-500",
}

export function JobCard({ job }) {
  const {
    id,
    title,
    location,
    startDateTime,
    endDateTime,
    paymentAmount,
    status,
    requiresLaptop,
    requiresSmartphone,
    trustScoreRequired,
    applications = [],
  } = job

  const formattedDate = format(new Date(startDateTime), "MMM dd, yyyy")
  const formattedTime = `${format(new Date(startDateTime), "h:mm a")} - ${format(new Date(endDateTime), "h:mm a")}`

  return (
    <Card>
      <CardHeader className="pb-2">
        <div className="flex items-start justify-between">
          <CardTitle className="text-xl">{title}</CardTitle>
          <Badge
            variant={
              status === "open"
                ? "default"
                : status === "in_progress"
                  ? "secondary"
                  : status === "completed"
                    ? "success"
                    : "destructive"
            }
          >
            {status.replace("_", " ")}
          </Badge>
        </div>
        <div className="flex items-center text-sm text-muted-foreground">
          <MapPinIcon className="mr-1 h-4 w-4" />
          {location}
        </div>
      </CardHeader>
      <CardContent className="pb-2">
        <div className="grid gap-2">
          <div className="flex items-center">
            <CalendarIcon className="mr-2 h-4 w-4 text-muted-foreground" />
            <span className="text-sm">{formattedDate}</span>
          </div>
          <div className="flex items-center">
            <CalendarIcon className="mr-2 h-4 w-4 text-muted-foreground" />
            <span className="text-sm">{formattedTime}</span>
          </div>
          <div className="flex items-center">
            <UsersIcon className="mr-2 h-4 w-4 text-muted-foreground" />
            <span className="text-sm">{applications.length} applicants</span>
          </div>
          <div className="mt-2 flex flex-wrap gap-2">
            <Badge variant="outline" className="text-xs">
              Trust Score: {trustScoreRequired}+
            </Badge>
            {requiresLaptop && (
              <Badge variant="outline" className="text-xs">
                <LaptopIcon className="mr-1 h-3 w-3" />
                Laptop Required
              </Badge>
            )}
            {requiresSmartphone && (
              <Badge variant="outline" className="text-xs">
                <SmartphoneIcon className="mr-1 h-3 w-3" />
                Smartphone Required
              </Badge>
            )}
          </div>
        </div>
      </CardContent>
      <CardFooter className="flex justify-between pt-2">
        <div className="text-lg font-bold">₹{paymentAmount}</div>
        <Link href={`/jobs/${id}`}>
          <Button variant="outline" size="sm">
            View Details
          </Button>
        </Link>
      </CardFooter>
    </Card>
  )
}
