'use client';

import React, { useState } from 'react';
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { CheckCircle, AlertCircle, Info } from 'lucide-react';
import { PaymentMethodSelector } from '../payments/PaymentMethodSelector';

interface Job {
  id: string;
  title: string;
  bountyAmount: number;
  currency: string;
  description: string;
}

interface EscrowFundingModalProps {
  job: Job;
  isOpen: boolean;
  onClose: () => void;
  onSuccess: (paymentData: any) => void;
  companyEmail?: string;
  companyPhone?: string;
}

export const EscrowFundingModal: React.FC<EscrowFundingModalProps> = ({
  job,
  isOpen,
  onClose,
  onSuccess,
  companyEmail,
  companyPhone,
}) => {
  const [paymentStatus, setPaymentStatus] = useState<'idle' | 'success' | 'error'>('idle');
  const [paymentData, setPaymentData] = useState<any>(null);
  const [error, setError] = useState<string | null>(null);

  const handlePaymentSuccess = (data: any) => {
    setPaymentData(data);
    setPaymentStatus('success');
    setError(null);
    
    // Call the parent success handler
    onSuccess({
      ...data,
      jobId: job.id,
      escrowAmount: job.bountyAmount,
    });
  };

  const handlePaymentError = (error: any) => {
    console.error('Payment error:', error);
    setPaymentStatus('error');
    setError(error.message || 'Payment failed. Please try again.');
  };

  const handleClose = () => {
    setPaymentStatus('idle');
    setPaymentData(null);
    setError(null);
    onClose();
  };

  const formatAmount = (amount: number, currency: string) => {
    return new Intl.NumberFormat('en-IN', {
      style: 'currency',
      currency: currency,
      minimumFractionDigits: 2,
    }).format(amount / 100);
  };

  return (
    <Dialog open={isOpen} onOpenChange={handleClose}>
      <DialogContent className="max-w-md">
        <DialogHeader>
          <DialogTitle>Fund Job Escrow</DialogTitle>
          <DialogDescription>
            Secure the bounty amount for "{job.title}" in escrow
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-4">
          {/* Job Details */}
          <div className="bg-gray-50 p-4 rounded-lg">
            <h3 className="font-semibold text-gray-900">{job.title}</h3>
            <p className="text-sm text-gray-600 mt-1">{job.description}</p>
            <div className="mt-3 flex justify-between items-center">
              <span className="text-sm text-gray-600">Bounty Amount:</span>
              <span className="font-bold text-lg text-green-600">
                {formatAmount(job.bountyAmount, job.currency)}
              </span>
            </div>
          </div>

          {/* Escrow Information */}
          <Alert>
            <Info className="h-4 w-4" />
            <AlertDescription>
              The bounty amount will be held in escrow and released to the worker
              upon successful job completion. This protects both parties in the transaction.
            </AlertDescription>
          </Alert>

          {/* Payment Status */}
          {paymentStatus === 'success' && (
            <Alert className="border-green-200 bg-green-50">
              <CheckCircle className="h-4 w-4 text-green-600" />
              <AlertDescription className="text-green-800">
                Payment successful! Escrow has been funded.
                <br />
                <span className="text-sm">
                  Transaction ID: {paymentData?.paymentId}
                </span>
              </AlertDescription>
            </Alert>
          )}

          {paymentStatus === 'error' && (
            <Alert variant="destructive">
              <AlertCircle className="h-4 w-4" />
              <AlertDescription>{error}</AlertDescription>
            </Alert>
          )}

          {/* Payment Method Selector */}
          {paymentStatus === 'idle' && (
            <PaymentMethodSelector
              amount={job.bountyAmount}
              currency={job.currency}
              description={`Escrow funding for job: ${job.title}`}
              customerEmail={companyEmail}
              customerPhone={companyPhone}
              metadata={{
                jobId: job.id,
                type: 'escrow_funding',
                jobTitle: job.title,
              }}
              onSuccess={handlePaymentSuccess}
              onError={handlePaymentError}
              availableMethods={['razorpay', 'stripe']}
            />
          )}

          {/* Action Buttons */}
          <div className="flex gap-2 pt-4">
            {paymentStatus === 'success' ? (
              <Button onClick={handleClose} className="w-full">
                Continue
              </Button>
            ) : (
              <>
                <Button
                  variant="outline"
                  onClick={handleClose}
                  className="flex-1"
                >
                  Cancel
                </Button>
                {paymentStatus === 'error' && (
                  <Button
                    onClick={() => {
                      setPaymentStatus('idle');
                      setError(null);
                    }}
                    className="flex-1"
                  >
                    Retry Payment
                  </Button>
                )}
              </>
            )}
          </div>

          {/* Security Notice */}
          <div className="text-xs text-gray-500 text-center space-y-1">
            <p>🔒 Your payment is secured with 256-bit SSL encryption</p>
            <p>💳 We support all major payment methods</p>
            <p>🛡️ Funds are held securely until job completion</p>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
};

export default EscrowFundingModal;
