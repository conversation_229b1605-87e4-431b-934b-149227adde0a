"use client";

import React, { useState, useEffect } from "react";
import { Button } from "@/components/ui/button";
import { Loader2 } from "lucide-react";
import { paymentsAPI } from "@/lib/api";
import { PAYMENT_CONFIG } from "@/constants/payment.constants";

interface RazorpayOptions {
  key: string;
  amount: string;
  currency: string;
  name: string;
  description: string;
  image?: string;
  order_id: string;
  prefill?: {
    name?: string;
    email?: string;
    contact?: string;
  };
  notes?: Record<string, unknown>;
  theme?: {
    color?: string;
  };
  handler: (response: {
    razorpay_payment_id: string;
    razorpay_order_id: string;
    razorpay_signature: string;
  }) => void;
  modal?: {
    ondismiss?: () => void;
  };
}

interface RazorpayInstance {
  open(): void;
}

declare global {
  interface Window {
    Razorpay: new (options: RazorpayOptions) => RazorpayInstance;
  }
}

interface RazorpayPaymentProps {
  amount: number;
  currency?: string;
  description: string;
  customerEmail?: string;
  customerPhone?: string;
  metadata?: Record<string, unknown>;
  onSuccess: (paymentData: Record<string, unknown>) => void;
  onError: (error: Error) => void;
  disabled?: boolean;
  buttonText?: string;
  className?: string;
}

export const RazorpayPayment: React.FC<RazorpayPaymentProps> = ({
  amount,
  currency = "INR",
  description,
  customerEmail,
  customerPhone,
  metadata,
  onSuccess,
  onError,
  disabled = false,
  buttonText = "Pay with Razorpay",
  className = "",
}) => {
  const [isProcessing, setIsProcessing] = useState(false);
  const [isScriptLoaded, setIsScriptLoaded] = useState(false);

  useEffect(() => {
    // Load Razorpay script
    const loadRazorpayScript = () => {
      return new Promise((resolve) => {
        if (window.Razorpay) {
          setIsScriptLoaded(true);
          resolve(true);
          return;
        }

        const script = document.createElement("script");
        script.src = "https://checkout.razorpay.com/v1/checkout.js";
        script.onload = () => {
          setIsScriptLoaded(true);
          resolve(true);
        };
        script.onerror = () => {
          console.error("Failed to load Razorpay script");
          resolve(false);
        };
        document.body.appendChild(script);
      });
    };

    loadRazorpayScript();
  }, []);

  const handlePayment = async () => {
    if (!isScriptLoaded) {
      onError(new Error("Razorpay script not loaded"));
      return;
    }

    try {
      setIsProcessing(true);

      // Step 1: Create Razorpay order
      const orderResponse = await paymentsAPI.createRazorpayOrder({
        jobId: metadata?.jobId || "",
        amount,
        currency,
        description,
        customerEmail,
        customerPhone,
      });

      if (!orderResponse.data.success) {
        throw new Error("Failed to create payment order");
      }

      const order = orderResponse.data.gatewayResponse;

      // Step 2: Open Razorpay checkout
      const options = {
        key: PAYMENT_CONFIG.RAZORPAY.KEY_ID,
        amount: amount.toString(),
        currency,
        name: PAYMENT_CONFIG.COMPANY_NAME,
        description,
        image: PAYMENT_CONFIG.COMPANY_LOGO,
        order_id: order.id,
        prefill: {
          name: "Customer",
          email: customerEmail || "",
          contact: customerPhone || "",
        },
        notes: metadata || {},
        theme: {
          color: PAYMENT_CONFIG.RAZORPAY.THEME_COLOR,
        },
        handler: async (response: any) => {
          try {
            // Step 3: Verify payment signature
            const verificationResponse =
              await paymentsAPI.verifyRazorpaySignature({
                orderId: order.id,
                paymentId: response.razorpay_payment_id,
                signature: response.razorpay_signature,
              });

            if (verificationResponse.data.verified) {
              onSuccess({
                orderId: order.id,
                paymentId: response.razorpay_payment_id,
                signature: response.razorpay_signature,
                amount,
                currency,
                description,
                metadata,
              });
            } else {
              throw new Error("Payment verification failed");
            }
          } catch (error) {
            console.error("Payment verification error:", error);
            onError(error);
          }
        },
        modal: {
          ondismiss: () => {
            setIsProcessing(false);
            onError(new Error("Payment cancelled by user"));
          },
        },
      };

      const razorpay = new window.Razorpay(options);
      razorpay.open();
    } catch (error: any) {
      console.error("Razorpay payment error:", error);
      onError(error);
      setIsProcessing(false);
    }
  };

  return (
    <Button
      onClick={handlePayment}
      disabled={disabled || isProcessing || !isScriptLoaded}
      className={className}
    >
      {isProcessing ? (
        <>
          <Loader2 className="mr-2 h-4 w-4 animate-spin" />
          Processing...
        </>
      ) : (
        buttonText
      )}
    </Button>
  );
};

// Hook for programmatic usage
export const useRazorpayPayment = () => {
  const [isScriptLoaded, setIsScriptLoaded] = useState(false);

  useEffect(() => {
    const loadScript = () => {
      if (window.Razorpay) {
        setIsScriptLoaded(true);
        return;
      }

      const script = document.createElement("script");
      script.src = PAYMENT_CONFIG.RAZORPAY.SCRIPT_URL;
      script.onload = () => setIsScriptLoaded(true);
      document.body.appendChild(script);
    };

    loadScript();
  }, []);

  const processPayment = async (paymentData: {
    amount: number;
    currency?: string;
    description: string;
    customerEmail?: string;
    customerPhone?: string;
    metadata?: Record<string, unknown>;
  }) => {
    if (!isScriptLoaded) {
      throw new Error("Razorpay script not loaded");
    }

    try {
      // Create order
      const orderResponse = await paymentsAPI.createRazorpayOrder({
        jobId: paymentData.metadata?.jobId || "",
        amount: paymentData.amount,
        currency: paymentData.currency || "INR",
        description: paymentData.description,
        customerEmail: paymentData.customerEmail,
        customerPhone: paymentData.customerPhone,
      });

      if (!orderResponse.data.success) {
        throw new Error("Failed to create payment order");
      }

      const order = orderResponse.data.gatewayResponse;

      // Open Razorpay checkout
      return new Promise((resolve, reject) => {
        const options = {
          key: PAYMENT_CONFIG.RAZORPAY.KEY_ID,
          amount: paymentData.amount.toString(),
          currency: paymentData.currency || PAYMENT_CONFIG.DEFAULT_CURRENCY,
          name: PAYMENT_CONFIG.COMPANY_NAME,
          description: paymentData.description,
          order_id: order.id,
          prefill: {
            email: paymentData.customerEmail || "",
            contact: paymentData.customerPhone || "",
          },
          notes: paymentData.metadata || {},
          theme: { color: PAYMENT_CONFIG.RAZORPAY.THEME_COLOR },
          handler: async (response: {
            razorpay_payment_id: string;
            razorpay_order_id: string;
            razorpay_signature: string;
          }) => {
            try {
              const verificationResponse =
                await paymentsAPI.verifyRazorpaySignature({
                  orderId: order.id,
                  paymentId: response.razorpay_payment_id,
                  signature: response.razorpay_signature,
                });

              if (verificationResponse.data.verified) {
                resolve({
                  success: true,
                  orderId: order.id,
                  paymentId: response.razorpay_payment_id,
                  signature: response.razorpay_signature,
                  amount: paymentData.amount,
                  currency:
                    paymentData.currency || PAYMENT_CONFIG.DEFAULT_CURRENCY,
                  description: paymentData.description,
                  metadata: paymentData.metadata,
                });
              } else {
                reject(new Error("Payment verification failed"));
              }
            } catch (error) {
              reject(error);
            }
          },
          modal: {
            ondismiss: () => {
              reject(new Error("Payment cancelled by user"));
            },
          },
        };

        const razorpay = new window.Razorpay(options);
        razorpay.open();
      });
    } catch (error) {
      throw error;
    }
  };

  return { processPayment, isScriptLoaded };
};

export default RazorpayPayment;
