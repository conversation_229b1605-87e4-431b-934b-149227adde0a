"use client";

import React, { useState, useEffect } from "react";
import { <PERSON>, <PERSON>Content, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import { Label } from "@/components/ui/label";
import { CreditCard, Smartphone, Building, Zap, Globe } from "lucide-react";
import { RazorpayPayment } from "./RazorpayPayment";
import { StripePayment } from "./StripePayment";
import { paymentsAPI } from "@/lib/api";
import { PaymentGateway } from "@shared/types";

export type PaymentMethodType = "stripe" | "razorpay" | "upi" | "bank_transfer";

interface PaymentMethod {
  id: PaymentMethodType;
  name: string;
  description: string;
  icon: React.ComponentType<{ className?: string }>;
  enabled: boolean;
  gateway: PaymentGateway;
  currencies: string[];
}

interface PaymentMethodSelectorProps {
  amount: number;
  currency?: string;
  description: string;
  customerEmail?: string;
  customerPhone?: string;
  metadata?: Record<string, unknown>;
  onSuccess: (paymentData: {
    paymentMethod: PaymentMethodType;
    [key: string]: unknown;
  }) => void;
  onError: (error: Error) => void;
  availableMethods?: PaymentMethodType[];
  preferredGateway?: PaymentGateway;
  className?: string;
}

// Payment methods ordered with Razorpay first (default for Indian users)
const allPaymentMethods: PaymentMethod[] = [
  {
    id: "razorpay",
    name: "Razorpay",
    description: "Pay securely with cards, UPI, wallets & more",
    icon: Zap,
    enabled: true,
    gateway: PaymentGateway.RAZORPAY,
    currencies: ["INR"],
  },
  {
    id: "upi",
    name: "UPI",
    description: "Pay using UPI ID or QR code",
    icon: Smartphone,
    enabled: true,
    gateway: PaymentGateway.RAZORPAY,
    currencies: ["INR"],
  },
  {
    id: "stripe",
    name: "International Card",
    description: "Pay with credit/debit card worldwide",
    icon: Globe,
    enabled: true,
    gateway: PaymentGateway.STRIPE,
    currencies: ["USD", "EUR", "GBP", "CAD", "AUD", "SGD"],
  },
  {
    id: "bank_transfer",
    name: "Bank Transfer",
    description: "Direct bank account transfer",
    icon: Building,
    enabled: false, // Disabled for now
    gateway: PaymentGateway.RAZORPAY,
    currencies: ["INR"],
  },
];

export const PaymentMethodSelector: React.FC<PaymentMethodSelectorProps> = ({
  amount,
  currency = "INR",
  description,
  customerEmail,
  customerPhone,
  metadata,
  onSuccess,
  onError,
  availableMethods,
  preferredGateway,
  className = "",
}) => {
  const [selectedMethod, setSelectedMethod] =
    useState<PaymentMethodType>("razorpay");
  const [availableBackendMethods, setAvailableBackendMethods] = useState<
    string[]
  >([]);
  const [gatewayConfigs, setGatewayConfigs] = useState<
    Record<string, unknown>[]
  >([]);
  const [loading, setLoading] = useState(true);
  const [showPayment, setShowPayment] = useState(false);

  useEffect(() => {
    const fetchPaymentMethods = async () => {
      try {
        setLoading(true);

        // Fetch available payment methods for the currency
        const methodsResponse =
          await paymentsAPI.getAvailablePaymentMethods(currency);
        const methods = methodsResponse.data.methods || [];
        setAvailableBackendMethods(methods);

        // Fetch gateway configurations
        const configsResponse = await paymentsAPI.getGatewayConfigurations();
        setGatewayConfigs(configsResponse.data || []);

        // Set default payment method based on currency and available methods
        if (
          preferredGateway === PaymentGateway.STRIPE &&
          methods.includes("stripe")
        ) {
          setSelectedMethod("stripe");
        } else if (
          currency.toUpperCase() === "INR" &&
          methods.includes("razorpay")
        ) {
          setSelectedMethod("razorpay");
        } else if (methods.includes("stripe")) {
          setSelectedMethod("stripe");
        } else if (methods.length > 0) {
          setSelectedMethod(methods[0] as PaymentMethodType);
        }
      } catch (error) {
        console.error("Failed to fetch payment methods:", error);
        onError(
          error instanceof Error
            ? error
            : new Error("Failed to load payment methods")
        );
      } finally {
        setLoading(false);
      }
    };

    fetchPaymentMethods();
  }, [currency, preferredGateway, onError]);

  // Filter payment methods based on availability and currency
  const filteredMethods = allPaymentMethods.filter((method) => {
    // Check if method is available from backend or provided in props
    const isMethodAvailable = availableMethods
      ? availableMethods.includes(method.id)
      : availableBackendMethods.includes(method.id);

    // Check if currency is supported
    const isCurrencySupported =
      method.currencies.includes(currency.toUpperCase()) ||
      (method.gateway === PaymentGateway.STRIPE &&
        currency.toUpperCase() !== "INR");

    return isMethodAvailable && isCurrencySupported && method.enabled;
  });

  const handlePaymentSuccess = (paymentData: Record<string, unknown>) => {
    onSuccess({
      ...paymentData,
      paymentMethod: selectedMethod,
    });
  };

  const formatAmount = (amount: number) => {
    return new Intl.NumberFormat("en-IN", {
      style: "currency",
      currency: currency,
      minimumFractionDigits: 2,
    }).format(amount / 100);
  };

  const handleProceedToPayment = () => {
    setShowPayment(true);
  };

  const handleBackToSelection = () => {
    setShowPayment(false);
  };

  const renderPaymentComponent = () => {
    const selectedMethodConfig = filteredMethods.find(
      (m) => m.id === selectedMethod
    );

    if (selectedMethodConfig?.gateway === PaymentGateway.STRIPE) {
      return (
        <StripePayment
          amount={amount}
          currency={currency}
          description={description}
          customerEmail={customerEmail}
          metadata={metadata}
          onSuccess={handlePaymentSuccess}
          onError={onError}
          buttonText={`Pay ${formatAmount(amount)}`}
          className="w-full"
        />
      );
    } else {
      return (
        <RazorpayPayment
          amount={amount}
          currency={currency}
          description={description}
          customerEmail={customerEmail}
          customerPhone={customerPhone}
          metadata={metadata}
          onSuccess={handlePaymentSuccess}
          onError={onError}
          buttonText={`Pay ${formatAmount(amount)}`}
          className="w-full"
        />
      );
    }
  };

  if (loading) {
    return (
      <Card className={className}>
        <CardContent className="flex items-center justify-center py-8">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
          <span className="ml-2">Loading payment methods...</span>
        </CardContent>
      </Card>
    );
  }

  if (showPayment) {
    return (
      <div className="space-y-4">
        <Button
          variant="outline"
          onClick={handleBackToSelection}
          className="mb-4"
        >
          ← Back to Payment Methods
        </Button>
        {renderPaymentComponent()}
      </div>
    );
  }

  if (filteredMethods.length === 0) {
    return (
      <Card className={className}>
        <CardContent className="text-center py-8">
          <p className="text-gray-500">
            No payment methods available for {currency}
          </p>
          <p className="text-sm text-gray-400 mt-2">
            Please contact support or try a different currency.
          </p>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className={className}>
      <CardHeader>
        <CardTitle className="flex items-center justify-between">
          <span>Payment Method</span>
          <span className="text-2xl font-bold text-primary">
            {formatAmount(amount)}
          </span>
        </CardTitle>
        <p className="text-sm text-gray-600">
          Choose your preferred payment method for {currency}
        </p>
      </CardHeader>
      <CardContent className="space-y-6">
        <RadioGroup
          value={selectedMethod}
          onValueChange={(value) =>
            setSelectedMethod(value as PaymentMethodType)
          }
          className="space-y-3"
        >
          {filteredMethods.map((method) => {
            const IconComponent = method.icon;
            return (
              <div
                key={method.id}
                className="flex items-center space-x-3 rounded-lg border p-4 hover:bg-gray-50"
              >
                <RadioGroupItem value={method.id} id={method.id} />
                <IconComponent className="h-5 w-5 text-gray-600" />
                <div className="flex-1">
                  <Label
                    htmlFor={method.id}
                    className="font-medium cursor-pointer"
                  >
                    {method.name}
                  </Label>
                  <p className="text-sm text-gray-600">{method.description}</p>
                  <p className="text-xs text-blue-600 mt-1">
                    via{" "}
                    {method.gateway === PaymentGateway.STRIPE
                      ? "Stripe"
                      : "Razorpay"}
                  </p>
                </div>
              </div>
            );
          })}
        </RadioGroup>

        <div className="pt-4 border-t">
          <Button onClick={handleProceedToPayment} className="w-full">
            Proceed to Payment
          </Button>
        </div>

        <div className="text-xs text-gray-500 text-center">
          <p>Your payment information is secure and encrypted.</p>
          <p>By proceeding, you agree to our terms and conditions.</p>
        </div>
      </CardContent>
    </Card>
  );
};

export default PaymentMethodSelector;
