"use client";

import React, { useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { CreditCard, Lock } from "lucide-react";
import { paymentsAPI } from "@/lib/api";

interface StripePaymentProps {
  amount: number;
  currency?: string;
  description: string;
  customerEmail?: string;
  metadata?: Record<string, unknown>;
  onSuccess: (paymentData: Record<string, unknown>) => void;
  onError: (error: Error) => void;
  buttonText?: string;
  className?: string;
}

interface CardDetails {
  number: string;
  expiry: string;
  cvc: string;
  name: string;
}

export const StripePayment: React.FC<StripePaymentProps> = ({
  amount,
  currency = "USD",
  description,
  customerEmail,
  metadata,
  onSuccess,
  onError,
  buttonText = "Pay with Card",
  className = "",
}) => {
  const [isProcessing, setIsProcessing] = useState(false);
  const [cardDetails, setCardDetails] = useState<CardDetails>({
    number: "",
    expiry: "",
    cvc: "",
    name: "",
  });
  const [errors, setErrors] = useState<Partial<CardDetails>>({});

  const validateCardDetails = (): boolean => {
    const newErrors: Partial<CardDetails> = {};

    // Card number validation (basic)
    const cardNumber = cardDetails.number.replace(/\s/g, "");
    if (!cardNumber || cardNumber.length < 13 || cardNumber.length > 19) {
      newErrors.number = "Please enter a valid card number";
    }

    // Expiry validation
    const expiryRegex = /^(0[1-9]|1[0-2])\/([0-9]{2})$/;
    if (!cardDetails.expiry || !expiryRegex.test(cardDetails.expiry)) {
      newErrors.expiry = "Please enter expiry as MM/YY";
    }

    // CVC validation
    if (!cardDetails.cvc || cardDetails.cvc.length < 3 || cardDetails.cvc.length > 4) {
      newErrors.cvc = "Please enter a valid CVC";
    }

    // Name validation
    if (!cardDetails.name.trim()) {
      newErrors.name = "Please enter the cardholder name";
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const formatCardNumber = (value: string): string => {
    const v = value.replace(/\s+/g, "").replace(/[^0-9]/gi, "");
    const matches = v.match(/\d{4,16}/g);
    const match = (matches && matches[0]) || "";
    const parts = [];

    for (let i = 0, len = match.length; i < len; i += 4) {
      parts.push(match.substring(i, i + 4));
    }

    if (parts.length) {
      return parts.join(" ");
    } else {
      return v;
    }
  };

  const formatExpiry = (value: string): string => {
    const v = value.replace(/\s+/g, "").replace(/[^0-9]/gi, "");
    if (v.length >= 2) {
      return `${v.substring(0, 2)}/${v.substring(2, 4)}`;
    }
    return v;
  };

  const handleInputChange = (field: keyof CardDetails, value: string) => {
    let formattedValue = value;

    if (field === "number") {
      formattedValue = formatCardNumber(value);
    } else if (field === "expiry") {
      formattedValue = formatExpiry(value);
    } else if (field === "cvc") {
      formattedValue = value.replace(/[^0-9]/g, "").substring(0, 4);
    }

    setCardDetails(prev => ({
      ...prev,
      [field]: formattedValue,
    }));

    // Clear error when user starts typing
    if (errors[field]) {
      setErrors(prev => ({
        ...prev,
        [field]: undefined,
      }));
    }
  };

  const handlePayment = async () => {
    if (!validateCardDetails()) {
      return;
    }

    try {
      setIsProcessing(true);

      // In a real implementation, you would:
      // 1. Create a payment method using Stripe.js
      // 2. Send the payment method ID to your backend
      // For this demo, we'll simulate the process

      const paymentData = {
        amount,
        currency,
        description,
        preferredGateway: "stripe" as const,
        paymentMethodId: "pm_card_visa", // This would come from Stripe.js
        customerId: customerEmail ? `cus_${customerEmail.replace("@", "_")}` : undefined,
        metadata: {
          ...metadata,
          customerEmail,
          cardLast4: cardDetails.number.slice(-4),
        },
      };

      const response = await paymentsAPI.processUnifiedPayment(paymentData);

      if (response.data.success) {
        onSuccess({
          ...response.data,
          paymentMethod: "stripe",
          cardLast4: cardDetails.number.slice(-4),
        });
      } else {
        throw new Error(response.data.message || "Payment failed");
      }
    } catch (error) {
      console.error("Stripe payment error:", error);
      onError(error instanceof Error ? error : new Error("Payment failed"));
    } finally {
      setIsProcessing(false);
    }
  };

  const formatAmount = (amount: number) => {
    return new Intl.NumberFormat("en-US", {
      style: "currency",
      currency: currency,
      minimumFractionDigits: 2,
    }).format(amount / 100);
  };

  return (
    <Card className={className}>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <CreditCard className="h-5 w-5" />
          <span>Card Payment</span>
          <Lock className="h-4 w-4 text-green-600" />
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="space-y-4">
          <div>
            <Label htmlFor="cardNumber">Card Number</Label>
            <Input
              id="cardNumber"
              type="text"
              placeholder="1234 5678 9012 3456"
              value={cardDetails.number}
              onChange={(e) => handleInputChange("number", e.target.value)}
              maxLength={19}
              className={errors.number ? "border-red-500" : ""}
            />
            {errors.number && (
              <p className="text-sm text-red-500 mt-1">{errors.number}</p>
            )}
          </div>

          <div className="grid grid-cols-2 gap-4">
            <div>
              <Label htmlFor="expiry">Expiry Date</Label>
              <Input
                id="expiry"
                type="text"
                placeholder="MM/YY"
                value={cardDetails.expiry}
                onChange={(e) => handleInputChange("expiry", e.target.value)}
                maxLength={5}
                className={errors.expiry ? "border-red-500" : ""}
              />
              {errors.expiry && (
                <p className="text-sm text-red-500 mt-1">{errors.expiry}</p>
              )}
            </div>

            <div>
              <Label htmlFor="cvc">CVC</Label>
              <Input
                id="cvc"
                type="text"
                placeholder="123"
                value={cardDetails.cvc}
                onChange={(e) => handleInputChange("cvc", e.target.value)}
                maxLength={4}
                className={errors.cvc ? "border-red-500" : ""}
              />
              {errors.cvc && (
                <p className="text-sm text-red-500 mt-1">{errors.cvc}</p>
              )}
            </div>
          </div>

          <div>
            <Label htmlFor="cardName">Cardholder Name</Label>
            <Input
              id="cardName"
              type="text"
              placeholder="John Doe"
              value={cardDetails.name}
              onChange={(e) => handleInputChange("name", e.target.value)}
              className={errors.name ? "border-red-500" : ""}
            />
            {errors.name && (
              <p className="text-sm text-red-500 mt-1">{errors.name}</p>
            )}
          </div>
        </div>

        <div className="pt-4 border-t">
          <Button
            onClick={handlePayment}
            disabled={isProcessing}
            className="w-full bg-blue-600 hover:bg-blue-700"
          >
            {isProcessing ? (
              <div className="flex items-center gap-2">
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                Processing...
              </div>
            ) : (
              `${buttonText} ${formatAmount(amount)}`
            )}
          </Button>
        </div>

        <div className="text-xs text-gray-500 text-center">
          <div className="flex items-center justify-center gap-1">
            <Lock className="h-3 w-3" />
            <span>Secured by Stripe</span>
          </div>
          <p className="mt-1">Your payment information is encrypted and secure.</p>
        </div>
      </CardContent>
    </Card>
  );
};

export default StripePayment;
