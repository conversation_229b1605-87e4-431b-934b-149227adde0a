"use client";

import { useState, useEffect, useRef } from "react";
import { motion } from "framer-motion";
import { Send, Paperclip, MoreVertical } from "lucide-react";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Button } from "@/components/ui/button";
import { Textarea } from "@/components/ui/textarea";
import { useAuthRTK } from "@/providers/AuthRTKProvider";
import { staggerContainer, staggerItem } from "@/utils/animations";
import {
  useGetChatMessagesQuery,
  useSendMessageMutation,
} from "@/store/api/chatApi";

export default function ChatWindow({ chat }) {
  const { user } = useAuthRTK();
  const [newMessage, setNewMessage] = useState("");
  const messagesEndRef = useRef(null);

  const { data: messagesData, isLoading } = useGetChatMessagesQuery({
    chatId: chat.id,
    page: 1,
    limit: 50,
  });

  const [sendMessage, { isLoading: isSending }] = useSendMessageMutation();

  const handleSendMessage = () => {
    if (!newMessage.trim()) return;

    sendMessage({
      chatId: chat.id,
      data: {
        message: newMessage,
      },
    });
    setNewMessage("");
  };

  const handleKeyDown = (e) => {
    if (e.key === "Enter" && !e.shiftKey) {
      e.preventDefault();
      handleSendMessage();
    }
  };

  // Get messages from paginated response
  const messages = messagesData?.data || [];

  useEffect(() => {
    scrollToBottom();
  }, [messages]);

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: "smooth" });
  };

  return (
    <div className="flex flex-col h-full">
      <div className="border-b p-4 flex items-center justify-between">
        <div className="flex items-center">
          {(() => {
            // Find the other participant (not the current user)
            const otherParticipant = chat.participants?.find(
              (p) => p.userId !== user.id
            );
            const participantName =
              otherParticipant?.user?.fullName || chat.title || "Unknown";
            const participantAvatar =
              otherParticipant?.user?.profilePic || "/placeholder.svg";

            return (
              <>
                <Avatar className="h-10 w-10 mr-3">
                  <AvatarImage src={participantAvatar} />
                  <AvatarFallback>
                    {participantName.charAt(0).toUpperCase()}
                  </AvatarFallback>
                </Avatar>
                <div>
                  <h3 className="font-medium">{participantName}</h3>
                  <p className="text-sm text-muted-foreground">
                    {chat.contextType === "job" ? "Job Chat" : "Direct Message"}
                  </p>
                </div>
              </>
            );
          })()}
        </div>
        <Button variant="ghost" size="icon">
          <MoreVertical className="h-5 w-5" />
        </Button>
      </div>

      <div className="flex-1 overflow-y-auto p-4">
        {isLoading ? (
          <div className="flex justify-center p-8">
            <div className="h-8 w-8 animate-spin rounded-full border-4 border-primary border-t-transparent"></div>
          </div>
        ) : messages?.length > 0 ? (
          <motion.div
            variants={staggerContainer}
            initial="hidden"
            animate="visible"
            className="space-y-4"
          >
            {messages.map((message) => {
              const isUser = message.senderId === user.id;

              return (
                <motion.div
                  key={message.id}
                  variants={staggerItem}
                  className={`flex ${isUser ? "justify-end" : "justify-start"}`}
                >
                  <div
                    className={`max-w-[70%] rounded-lg p-3 ${
                      isUser
                        ? "bg-primary text-primary-foreground rounded-br-none"
                        : "bg-muted rounded-bl-none"
                    }`}
                  >
                    <p>{message.message}</p>
                    <p
                      className={`text-xs mt-1 ${isUser ? "text-primary-foreground/70" : "text-muted-foreground"}`}
                    >
                      {new Date(message.createdAt).toLocaleTimeString([], {
                        hour: "2-digit",
                        minute: "2-digit",
                      })}
                    </p>
                  </div>
                </motion.div>
              );
            })}
            <div ref={messagesEndRef} />
          </motion.div>
        ) : (
          <div className="flex flex-col items-center justify-center h-full">
            <p className="text-muted-foreground">No messages yet</p>
            <p className="text-sm text-muted-foreground">
              Start the conversation by sending a message
            </p>
          </div>
        )}
      </div>

      <div className="border-t p-4">
        <div className="flex items-end gap-2">
          <Button
            variant="outline"
            size="icon"
            className="rounded-full h-10 w-10 shrink-0"
          >
            <Paperclip className="h-5 w-5" />
          </Button>
          <Textarea
            placeholder="Type a message..."
            className="min-h-10 resize-none"
            value={newMessage}
            onChange={(e) => setNewMessage(e.target.value)}
            onKeyDown={handleKeyDown}
          />
          <Button
            onClick={handleSendMessage}
            disabled={!newMessage.trim() || isSending}
            className="rounded-full h-10 w-10 p-0"
          >
            <Send className="h-5 w-5" />
          </Button>
        </div>
      </div>
    </div>
  );
}
