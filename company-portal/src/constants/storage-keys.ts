/**
 * Constants for localStorage keys used throughout the app
 *
 * This file centralizes all localStorage key definitions to:
 * - Prevent typos when accessing localStorage
 * - Make it easier to track what data we're storing
 * - Provide proper TypeScript typing
 */

/**
 * Storage keys used throughout the app
 */
const STORAGE_KEYS = {
  // Authentication related keys
  AUTH_TOKEN: 'auth_token',
  AUTH_REFRESH_TOKEN: 'auth_refresh_token',
  USER_DATA: 'user_data',
  
  // Theme related keys
  THEME_PREFERENCE: 'theme_preference',
  COLOR_SCHEME: 'color_scheme',
  DARK_MODE_ENABLED: 'dark_mode_enabled',
} as const;

export default STORAGE_KEYS;

export type StorageKeys = typeof STORAGE_KEYS;
