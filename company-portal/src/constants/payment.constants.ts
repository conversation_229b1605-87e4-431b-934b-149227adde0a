/**
 * Payment Configuration Constants
 * 
 * This file centralizes all payment-related constants to avoid hard-coded values
 * and provide a single place to manage payment configuration.
 */

export const PAYMENT_CONFIG = {
  // Company branding
  COMPANY_NAME: process.env.NEXT_PUBLIC_COMPANY_NAME || 'JobMatch',
  COMPANY_LOGO: process.env.NEXT_PUBLIC_COMPANY_LOGO || '/logo.png',
  
  // Razorpay configuration
  RAZORPAY: {
    KEY_ID: process.env.NEXT_PUBLIC_RAZORPAY_KEY_ID || 'rzp_test_your_key_id',
    SCRIPT_URL: 'https://checkout.razorpay.com/v1/checkout.js',
    THEME_COLOR: process.env.NEXT_PUBLIC_RAZORPAY_THEME_COLOR || '#0ea5e9', // Using primary color from design tokens
  },
  
  // Default currency
  DEFAULT_CURRENCY: process.env.NEXT_PUBLIC_DEFAULT_CURRENCY || 'INR',
  
  // Payment timeouts
  TIMEOUT: {
    SCRIPT_LOAD: 10000, // 10 seconds
    PAYMENT_PROCESS: 300000, // 5 minutes
  },
} as const;

export type PaymentConfig = typeof PAYMENT_CONFIG;
