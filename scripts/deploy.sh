#!/bin/bash

# Production Deployment Script for Job Platform
# This script handles the complete deployment process

set -e  # Exit on any error

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
ENVIRONMENT=${1:-production}
BACKUP_DIR="./backups/$(date +%Y%m%d_%H%M%S)"
DOCKER_COMPOSE_FILE="docker-compose.prod.yml"
ENV_FILE=".env.production"

# Functions
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

check_prerequisites() {
    log_info "Checking prerequisites..."
    
    # Check if Docker is installed and running
    if ! command -v docker &> /dev/null; then
        log_error "Docker is not installed"
        exit 1
    fi
    
    if ! docker info &> /dev/null; then
        log_error "Docker is not running"
        exit 1
    fi
    
    # Check if Docker Compose is installed
    if ! command -v docker-compose &> /dev/null; then
        log_error "Docker Compose is not installed"
        exit 1
    fi
    
    # Check if environment file exists
    if [ ! -f "$ENV_FILE" ]; then
        log_error "Environment file $ENV_FILE not found"
        log_info "Please create $ENV_FILE based on .env.production.example"
        exit 1
    fi
    
    # Check if SSL certificates exist (for production)
    if [ "$ENVIRONMENT" = "production" ]; then
        if [ ! -d "./nginx/ssl" ]; then
            log_warning "SSL certificates not found in ./nginx/ssl/"
            log_info "Please ensure SSL certificates are properly configured"
        fi
    fi
    
    log_success "Prerequisites check passed"
}

backup_database() {
    log_info "Creating database backup..."
    
    # Create backup directory
    mkdir -p "$BACKUP_DIR"
    
    # Backup database if container is running
    if docker-compose -f "$DOCKER_COMPOSE_FILE" ps postgres | grep -q "Up"; then
        docker-compose -f "$DOCKER_COMPOSE_FILE" exec -T postgres pg_dump \
            -U "${POSTGRES_USER}" \
            -d "${POSTGRES_DB}" \
            > "$BACKUP_DIR/database_backup.sql"
        
        log_success "Database backup created: $BACKUP_DIR/database_backup.sql"
    else
        log_warning "Database container not running, skipping backup"
    fi
}

build_images() {
    log_info "Building Docker images..."
    
    # Build all images
    docker-compose -f "$DOCKER_COMPOSE_FILE" build --no-cache
    
    log_success "Docker images built successfully"
}

run_tests() {
    log_info "Running tests..."
    
    # Run backend tests
    cd backend
    npm run test:ci
    cd ..
    
    # Run E2E tests
    cd backend
    npm run test:e2e
    cd ..
    
    log_success "All tests passed"
}

deploy_services() {
    log_info "Deploying services..."
    
    # Copy environment file
    cp "$ENV_FILE" .env
    
    # Stop existing services
    docker-compose -f "$DOCKER_COMPOSE_FILE" down
    
    # Start services
    docker-compose -f "$DOCKER_COMPOSE_FILE" up -d
    
    log_success "Services deployed successfully"
}

wait_for_services() {
    log_info "Waiting for services to be ready..."
    
    # Wait for database
    log_info "Waiting for database..."
    timeout 60 bash -c 'until docker-compose -f '"$DOCKER_COMPOSE_FILE"' exec postgres pg_isready -U ${POSTGRES_USER}; do sleep 2; done'
    
    # Wait for backend
    log_info "Waiting for backend..."
    timeout 60 bash -c 'until curl -f http://localhost:3000/api/v1/health/live; do sleep 2; done'
    
    # Wait for frontend services
    log_info "Waiting for company portal..."
    timeout 60 bash -c 'until curl -f http://localhost:3001; do sleep 2; done'
    
    log_info "Waiting for admin portal..."
    timeout 60 bash -c 'until curl -f http://localhost:3002; do sleep 2; done'
    
    log_success "All services are ready"
}

run_migrations() {
    log_info "Running database migrations..."
    
    # Run migrations
    docker-compose -f "$DOCKER_COMPOSE_FILE" exec backend npm run migration:run
    
    log_success "Database migrations completed"
}

health_check() {
    log_info "Performing health checks..."
    
    # Check backend health
    if curl -f http://localhost:3000/api/v1/health; then
        log_success "Backend health check passed"
    else
        log_error "Backend health check failed"
        return 1
    fi
    
    # Check database connectivity
    if docker-compose -f "$DOCKER_COMPOSE_FILE" exec backend npm run migration:status; then
        log_success "Database connectivity check passed"
    else
        log_error "Database connectivity check failed"
        return 1
    fi
    
    log_success "All health checks passed"
}

cleanup() {
    log_info "Cleaning up..."
    
    # Remove unused Docker images
    docker image prune -f
    
    # Remove old backups (keep last 5)
    if [ -d "./backups" ]; then
        ls -t ./backups | tail -n +6 | xargs -r rm -rf
    fi
    
    log_success "Cleanup completed"
}

rollback() {
    log_error "Deployment failed, initiating rollback..."
    
    # Stop current services
    docker-compose -f "$DOCKER_COMPOSE_FILE" down
    
    # Restore database if backup exists
    if [ -f "$BACKUP_DIR/database_backup.sql" ]; then
        log_info "Restoring database from backup..."
        docker-compose -f "$DOCKER_COMPOSE_FILE" up -d postgres
        sleep 10
        docker-compose -f "$DOCKER_COMPOSE_FILE" exec -T postgres psql \
            -U "${POSTGRES_USER}" \
            -d "${POSTGRES_DB}" \
            < "$BACKUP_DIR/database_backup.sql"
    fi
    
    log_error "Rollback completed. Please check the logs and fix issues before retrying."
    exit 1
}

# Main deployment process
main() {
    log_info "Starting deployment for environment: $ENVIRONMENT"
    
    # Set trap for cleanup on error
    trap rollback ERR
    
    check_prerequisites
    backup_database
    build_images
    
    if [ "$ENVIRONMENT" = "production" ]; then
        run_tests
    fi
    
    deploy_services
    wait_for_services
    run_migrations
    health_check
    cleanup
    
    log_success "Deployment completed successfully!"
    log_info "Services are running at:"
    log_info "  - Backend API: http://localhost:3000"
    log_info "  - Company Portal: http://localhost:3001"
    log_info "  - Admin Portal: http://localhost:3002"
    log_info "  - Health Check: http://localhost:3000/api/v1/health"
}

# Show usage if no arguments
if [ $# -eq 0 ]; then
    echo "Usage: $0 [environment]"
    echo "  environment: production (default) | staging | development"
    echo ""
    echo "Example: $0 production"
    exit 1
fi

# Load environment variables
if [ -f "$ENV_FILE" ]; then
    source "$ENV_FILE"
fi

# Run main function
main
