#!/bin/bash

# Production Monitoring Script for Job Platform
# This script monitors the health and performance of all services

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
DOCKER_COMPOSE_FILE="docker-compose.prod.yml"
LOG_DIR="./logs/monitoring"
ALERT_EMAIL=${ALERT_EMAIL:-"<EMAIL>"}
CHECK_INTERVAL=${CHECK_INTERVAL:-60}  # seconds

# Create log directory
mkdir -p "$LOG_DIR"

# Functions
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
    echo "$(date '+%Y-%m-%d %H:%M:%S') [INFO] $1" >> "$LOG_DIR/monitor.log"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
    echo "$(date '+%Y-%m-%d %H:%M:%S') [SUCCESS] $1" >> "$LOG_DIR/monitor.log"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
    echo "$(date '+%Y-%m-%d %H:%M:%S') [WARNING] $1" >> "$LOG_DIR/monitor.log"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
    echo "$(date '+%Y-%m-%d %H:%M:%S') [ERROR] $1" >> "$LOG_DIR/monitor.log"
}

check_docker_services() {
    log_info "Checking Docker services..."
    
    local services=("postgres" "redis" "backend" "company-portal" "admin-portal" "nginx")
    local failed_services=()
    
    for service in "${services[@]}"; do
        if docker-compose -f "$DOCKER_COMPOSE_FILE" ps "$service" | grep -q "Up"; then
            log_success "$service is running"
        else
            log_error "$service is not running"
            failed_services+=("$service")
        fi
    done
    
    if [ ${#failed_services[@]} -gt 0 ]; then
        log_error "Failed services: ${failed_services[*]}"
        return 1
    fi
    
    return 0
}

check_health_endpoints() {
    log_info "Checking health endpoints..."
    
    # Backend health check
    if curl -f -s http://localhost:3000/api/v1/health/live > /dev/null; then
        log_success "Backend health endpoint is responding"
    else
        log_error "Backend health endpoint is not responding"
        return 1
    fi
    
    # Database health check
    if curl -f -s http://localhost:3000/api/v1/health/ready > /dev/null; then
        log_success "Database health check passed"
    else
        log_error "Database health check failed"
        return 1
    fi
    
    # Company portal check
    if curl -f -s http://localhost:3001 > /dev/null; then
        log_success "Company portal is responding"
    else
        log_error "Company portal is not responding"
        return 1
    fi
    
    # Admin portal check
    if curl -f -s http://localhost:3002 > /dev/null; then
        log_success "Admin portal is responding"
    else
        log_error "Admin portal is not responding"
        return 1
    fi
    
    return 0
}

check_database_connectivity() {
    log_info "Checking database connectivity..."
    
    if docker-compose -f "$DOCKER_COMPOSE_FILE" exec -T postgres pg_isready -U "${POSTGRES_USER}" > /dev/null; then
        log_success "Database is accepting connections"
    else
        log_error "Database is not accepting connections"
        return 1
    fi
    
    return 0
}

check_redis_connectivity() {
    log_info "Checking Redis connectivity..."
    
    if docker-compose -f "$DOCKER_COMPOSE_FILE" exec -T redis redis-cli ping | grep -q "PONG"; then
        log_success "Redis is responding"
    else
        log_error "Redis is not responding"
        return 1
    fi
    
    return 0
}

check_disk_space() {
    log_info "Checking disk space..."
    
    local usage=$(df / | awk 'NR==2 {print $5}' | sed 's/%//')
    
    if [ "$usage" -gt 90 ]; then
        log_error "Disk usage is critical: ${usage}%"
        return 1
    elif [ "$usage" -gt 80 ]; then
        log_warning "Disk usage is high: ${usage}%"
    else
        log_success "Disk usage is normal: ${usage}%"
    fi
    
    return 0
}

check_memory_usage() {
    log_info "Checking memory usage..."
    
    local usage=$(free | awk 'NR==2{printf "%.0f", $3*100/$2}')
    
    if [ "$usage" -gt 90 ]; then
        log_error "Memory usage is critical: ${usage}%"
        return 1
    elif [ "$usage" -gt 80 ]; then
        log_warning "Memory usage is high: ${usage}%"
    else
        log_success "Memory usage is normal: ${usage}%"
    fi
    
    return 0
}

check_container_resources() {
    log_info "Checking container resource usage..."
    
    # Get container stats
    docker stats --no-stream --format "table {{.Container}}\t{{.CPUPerc}}\t{{.MemUsage}}" > "$LOG_DIR/container_stats.log"
    
    log_success "Container stats saved to $LOG_DIR/container_stats.log"
    return 0
}

check_log_errors() {
    log_info "Checking for errors in logs..."
    
    local error_count=0
    
    # Check backend logs for errors
    if docker-compose -f "$DOCKER_COMPOSE_FILE" logs --tail=100 backend | grep -i "error" > "$LOG_DIR/backend_errors.log"; then
        error_count=$(wc -l < "$LOG_DIR/backend_errors.log")
        if [ "$error_count" -gt 10 ]; then
            log_warning "Found $error_count errors in backend logs"
        fi
    fi
    
    return 0
}

send_alert() {
    local message="$1"
    local severity="$2"
    
    log_error "ALERT [$severity]: $message"
    
    # Send email alert (requires mail command to be configured)
    if command -v mail &> /dev/null; then
        echo "$message" | mail -s "Job Platform Alert [$severity]" "$ALERT_EMAIL"
    fi
    
    # Log to alert file
    echo "$(date '+%Y-%m-%d %H:%M:%S') [$severity] $message" >> "$LOG_DIR/alerts.log"
}

restart_service() {
    local service="$1"
    
    log_warning "Attempting to restart $service..."
    
    if docker-compose -f "$DOCKER_COMPOSE_FILE" restart "$service"; then
        log_success "$service restarted successfully"
        sleep 30  # Wait for service to stabilize
        return 0
    else
        log_error "Failed to restart $service"
        return 1
    fi
}

perform_health_check() {
    local failed_checks=()
    
    # Run all health checks
    check_docker_services || failed_checks+=("docker_services")
    check_health_endpoints || failed_checks+=("health_endpoints")
    check_database_connectivity || failed_checks+=("database")
    check_redis_connectivity || failed_checks+=("redis")
    check_disk_space || failed_checks+=("disk_space")
    check_memory_usage || failed_checks+=("memory")
    check_container_resources || failed_checks+=("container_resources")
    check_log_errors || failed_checks+=("log_errors")
    
    if [ ${#failed_checks[@]} -eq 0 ]; then
        log_success "All health checks passed"
        return 0
    else
        log_error "Failed health checks: ${failed_checks[*]}"
        
        # Send alert for critical failures
        for check in "${failed_checks[@]}"; do
            case $check in
                "docker_services"|"health_endpoints"|"database"|"redis")
                    send_alert "Critical service failure: $check" "CRITICAL"
                    ;;
                "disk_space"|"memory")
                    send_alert "Resource usage warning: $check" "WARNING"
                    ;;
            esac
        done
        
        return 1
    fi
}

continuous_monitoring() {
    log_info "Starting continuous monitoring (interval: ${CHECK_INTERVAL}s)"
    
    while true; do
        log_info "Running health check cycle..."
        
        if perform_health_check; then
            log_success "Health check cycle completed successfully"
        else
            log_error "Health check cycle failed"
        fi
        
        sleep "$CHECK_INTERVAL"
    done
}

generate_report() {
    log_info "Generating monitoring report..."
    
    local report_file="$LOG_DIR/report_$(date +%Y%m%d_%H%M%S).txt"
    
    {
        echo "Job Platform Monitoring Report"
        echo "Generated: $(date)"
        echo "================================"
        echo ""
        
        echo "Service Status:"
        docker-compose -f "$DOCKER_COMPOSE_FILE" ps
        echo ""
        
        echo "Container Resource Usage:"
        docker stats --no-stream
        echo ""
        
        echo "System Resources:"
        echo "Disk Usage: $(df -h /)"
        echo "Memory Usage: $(free -h)"
        echo "CPU Load: $(uptime)"
        echo ""
        
        echo "Recent Alerts:"
        if [ -f "$LOG_DIR/alerts.log" ]; then
            tail -20 "$LOG_DIR/alerts.log"
        else
            echo "No alerts found"
        fi
        
    } > "$report_file"
    
    log_success "Report generated: $report_file"
}

# Main function
main() {
    case "${1:-check}" in
        "check")
            perform_health_check
            ;;
        "monitor")
            continuous_monitoring
            ;;
        "report")
            generate_report
            ;;
        "restart")
            if [ -z "$2" ]; then
                echo "Usage: $0 restart <service_name>"
                exit 1
            fi
            restart_service "$2"
            ;;
        *)
            echo "Usage: $0 {check|monitor|report|restart <service>}"
            echo ""
            echo "Commands:"
            echo "  check   - Run a single health check"
            echo "  monitor - Start continuous monitoring"
            echo "  report  - Generate a monitoring report"
            echo "  restart - Restart a specific service"
            exit 1
            ;;
    esac
}

# Load environment variables if available
if [ -f ".env.production" ]; then
    source .env.production
fi

# Run main function
main "$@"
