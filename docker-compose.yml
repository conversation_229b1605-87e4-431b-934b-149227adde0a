version: '3.8'

services:
  postgres:
    image: postgres:15-alpine
    container_name: job-platform-postgres
    restart: unless-stopped
    environment:
      POSTGRES_USER: ${POSTGRES_USER:-postgres}
      POSTGRES_PASSWORD: ${POSTGRES_PASSWORD:-postgres}
      POSTGRES_DB: ${POSTGRES_DB:-job_platform}
    ports:
      - "5432:5432"
    volumes:
      - postgres-data:/var/lib/postgresql/data
    networks:
      - job-platform-network

  backend:
    build:
      context: ./backend
      dockerfile: Dockerfile
    container_name: job-platform-backend
    restart: unless-stopped
    depends_on:
      - postgres
    environment:
      - NODE_ENV=development
      - PORT=3000
      - DATABASE_URL=postgresql://${POSTGRES_USER:-postgres}:${POSTGRES_PASSWORD:-postgres}@postgres:5432/${POSTGRES_DB:-job_platform}
      - JWT_SECRET=${JWT_SECRET:-your_jwt_secret}
      - JWT_EXPIRES_IN=${JWT_EXPIRES_IN:-1d}
    ports:
      - "3000:3000"
    volumes:
      - ./backend:/app
      - /app/node_modules
    networks:
      - job-platform-network

  company-portal:
    build:
      context: ./company-portal
      dockerfile: Dockerfile
    container_name: job-platform-company-portal
    restart: unless-stopped
    depends_on:
      - backend
    environment:
      - NEXT_PUBLIC_API_URL=http://localhost:3000
    ports:
      - "3001:3000"
    volumes:
      - ./company-portal:/app
      - /app/node_modules
      - /app/.next
    networks:
      - job-platform-network

  admin-portal:
    build:
      context: ./admin-portal
      dockerfile: Dockerfile
    container_name: job-platform-admin-portal
    restart: unless-stopped
    depends_on:
      - backend
    environment:
      - NEXT_PUBLIC_API_URL=http://localhost:3000
    ports:
      - "3002:3000"
    volumes:
      - ./admin-portal:/app
      - /app/node_modules
      - /app/.next
    networks:
      - job-platform-network

networks:
  job-platform-network:
    driver: bridge

volumes:
  postgres-data:
