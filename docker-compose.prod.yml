version: '3.8'

services:
  postgres:
    image: postgres:15-alpine
    container_name: job-platform-postgres-prod
    restart: unless-stopped
    environment:
      POSTGRES_USER: ${POSTGRES_USER}
      POSTGRES_PASSWORD: ${POSTGRES_PASSWORD}
      POSTGRES_DB: ${POSTGRES_DB}
    ports:
      - "5432:5432"
    volumes:
      - postgres-data:/var/lib/postgresql/data
      - ./backend/init.sql:/docker-entrypoint-initdb.d/init.sql:ro
    networks:
      - job-platform-network
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U ${POSTGRES_USER} -d ${POSTGRES_DB}"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 30s

  redis:
    image: redis:7-alpine
    container_name: job-platform-redis-prod
    restart: unless-stopped
    ports:
      - "6379:6379"
    volumes:
      - redis-data:/data
    networks:
      - job-platform-network
    healthcheck:
      test: ["<PERSON><PERSON>", "redis-cli", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3

  backend:
    build:
      context: ./backend
      dockerfile: Dockerfile
      target: production
    container_name: job-platform-backend-prod
    restart: unless-stopped
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
    environment:
      - NODE_ENV=production
      - PORT=3000
      - DATABASE_URL=postgresql://${POSTGRES_USER}:${POSTGRES_PASSWORD}@postgres:5432/${POSTGRES_DB}
      - REDIS_URL=redis://redis:6379
      - JWT_SECRET=${JWT_SECRET}
      - JWT_EXPIRES_IN=${JWT_EXPIRES_IN}
      - CORS_ORIGIN=${CORS_ORIGIN}
      - EMAIL_PROVIDER=${EMAIL_PROVIDER}
      - EMAIL_FROM=${EMAIL_FROM}
      - SENDGRID_API_KEY=${SENDGRID_API_KEY}
      - STRIPE_SECRET_KEY=${STRIPE_SECRET_KEY}
      - THROTTLE_TTL=${THROTTLE_TTL}
      - THROTTLE_LIMIT=${THROTTLE_LIMIT}
    ports:
      - "3000:3000"
    networks:
      - job-platform-network
    healthcheck:
      test: ["CMD", "node", "health-check.js"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 30s

  company-portal:
    build:
      context: ./company-portal
      dockerfile: Dockerfile
      target: production
    container_name: job-platform-company-portal-prod
    restart: unless-stopped
    depends_on:
      backend:
        condition: service_healthy
    environment:
      - NODE_ENV=production
      - NEXT_PUBLIC_API_URL=${NEXT_PUBLIC_API_URL}
      - NEXTAUTH_SECRET=${NEXTAUTH_SECRET}
      - NEXTAUTH_URL=${COMPANY_PORTAL_URL}
    ports:
      - "3001:3000"
    networks:
      - job-platform-network

  admin-portal:
    build:
      context: ./admin-portal
      dockerfile: Dockerfile
      target: production
    container_name: job-platform-admin-portal-prod
    restart: unless-stopped
    depends_on:
      backend:
        condition: service_healthy
    environment:
      - NODE_ENV=production
      - NEXT_PUBLIC_API_URL=${NEXT_PUBLIC_API_URL}
      - NEXTAUTH_SECRET=${NEXTAUTH_SECRET}
      - NEXTAUTH_URL=${ADMIN_PORTAL_URL}
    ports:
      - "3002:3000"
    networks:
      - job-platform-network

  nginx:
    image: nginx:alpine
    container_name: job-platform-nginx-prod
    restart: unless-stopped
    depends_on:
      - backend
      - company-portal
      - admin-portal
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf:ro
      - ./nginx/ssl:/etc/nginx/ssl:ro
    networks:
      - job-platform-network

networks:
  job-platform-network:
    driver: bridge

volumes:
  postgres-data:
  redis-data:
