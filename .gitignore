# Dependencies
node_modules/
.pnp/
.pnp.js

# Testing
coverage/

# Production
build/
dist/
out/
.next/
.nuxt/
.output/

# Environment variables
.env
.env.local
.env.development.local
.env.test.local
.env.production.local
.env*.local

# Logs
logs
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*
pnpm-debug.log*
lerna-debug.log*

# Editor directories and files
.idea/
.vscode/*
!.vscode/extensions.json
!.vscode/settings.json
!.vscode/tasks.json
!.vscode/launch.json
*.suo
*.ntvs*
*.njsproj
*.sln
*.sw?
.DS_Store
*.pem

# Mobile app specific
mobile-app/.expo/
mobile-app/dist/
mobile-app/npm-debug.*
mobile-app/*.jks
mobile-app/*.p8
mobile-app/*.p12
mobile-app/*.key
mobile-app/*.mobileprovision
mobile-app/*.orig.*
mobile-app/web-build/

# Backend specific
backend/dist/
backend/node_modules/

# Web portals specific
company-portal/.next/
company-portal/out/
company-portal/node_modules/
admin-portal/.next/
admin-portal/out/
admin-portal/node_modules/

# Misc
.github-account
.cache/
.parcel-cache
.turbo
.vercel
.netlify
