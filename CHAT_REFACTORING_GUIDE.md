# Chat System Refactoring Guide

## Overview

The chat system has been refactored from a narrow worker-company focused implementation to a flexible, generic chat system that can handle various types of conversations while maintaining backward compatibility.

## Key Changes

### 1. New Generic Architecture

#### Entities
- **`GenericChat`**: Main chat entity supporting multiple chat types
- **`ChatParticipant`**: Flexible participant management with roles
- **Enhanced `ChatMessage`**: Support for rich content, replies, and metadata

#### Enums
- **`ChatType`**: `WORKER_COMPANY`, `SUPPORT`, `GROUP`, `ADMIN_USER`, `GENERAL`
- **`ParticipantRole`**: `WORKER`, `COMPANY`, `ADMIN`, `<PERSON>UPPORT`, `MODERATOR`, `MEMBER`
- **`MessageType`**: `TEXT`, `IMAGE`, `FILE`, `SYSTEM`, `NOTIFICATION`
- **`MessageStatus`**: `SENT`, `DELIVERED`, `READ`, `FAILED`

### 2. Backward Compatibility

The existing chat API endpoints continue to work unchanged:
- `POST /chats` - Creates worker-company chats using the new system
- `GET /chats` - Returns chats in the legacy format
- All existing DTOs and interfaces remain functional

### 3. New Generic API Endpoints

#### Generic Chat Management
```typescript
// Create a generic chat
POST /generic-chats
{
  "type": "SUPPORT",
  "title": "Help with Payment Issue",
  "participants": [
    { "userId": "user-id", "role": "MEMBER" },
    { "userId": "support-id", "role": "SUPPORT" }
  ]
}

// Get user's chats
GET /generic-chats?page=1&limit=10

// Get specific chat
GET /generic-chats/:id

// Send message
POST /generic-chats/:id/messages
{
  "message": "Hello, I need help",
  "messageType": "TEXT"
}

// Mark as read
PATCH /generic-chats/:id/read
```

## Usage Examples

### 1. Worker-Company Chat (Backward Compatible)
```typescript
// Existing code continues to work
const chat = await chatService.create(userId, {
  workerId: "worker-id",
  companyId: "company-id",
  jobId: "job-id"
});
```

### 2. Support Chat
```typescript
const supportChat = await genericChatService.createGenericChat(userId, {
  type: ChatType.SUPPORT,
  title: "Payment Issue",
  participants: [
    { userId: userId, role: ParticipantRole.MEMBER },
    { userId: supportAgentId, role: ParticipantRole.SUPPORT }
  ],
  contextType: "support_ticket",
  contextId: ticketId
});
```

### 3. Group Chat
```typescript
const groupChat = await genericChatService.createGenericChat(userId, {
  type: ChatType.GROUP,
  title: "Project Team Discussion",
  description: "Discussion for the new project",
  participants: [
    { userId: managerId, role: ParticipantRole.MODERATOR },
    { userId: worker1Id, role: ParticipantRole.MEMBER },
    { userId: worker2Id, role: ParticipantRole.MEMBER }
  ]
});
```

### 4. Rich Messages
```typescript
// Text message with reply
await genericChatService.addGenericMessage(chatId, senderId, {
  message: "Thanks for the clarification!",
  messageType: MessageType.TEXT,
  replyToId: originalMessageId
});

// File message
await genericChatService.addGenericMessage(chatId, senderId, {
  message: "Here's the document you requested",
  messageType: MessageType.FILE,
  fileUrl: "https://example.com/document.pdf",
  fileName: "contract.pdf",
  fileSize: 1024000,
  fileMimeType: "application/pdf"
});
```

## Migration Strategy

### Phase 1: Database Migration
Run the migration script to add new tables and columns:
```bash
npm run migration:run
```

### Phase 2: Gradual Adoption
1. Existing worker-company chats continue using legacy endpoints
2. New features (support, group chats) use generic endpoints
3. Frontend can gradually adopt new chat types

### Phase 3: Full Migration (Optional)
1. Migrate existing chats to generic format
2. Update frontend to use generic APIs exclusively
3. Deprecate legacy endpoints

## Frontend Integration

### Mobile App (React Native)
```typescript
// Import new types
import { 
  ChatType, 
  ParticipantRole, 
  MessageType, 
  GenericChat 
} from '../shared-types';

// Use in RTK Query
const { data: chats } = useGetGenericChatsQuery({ page: 1, limit: 10 });
```

### Web Portals
```typescript
// Company portal - create support chat
const createSupportChat = async (issue: string) => {
  return await genericChatApi.createChat({
    type: ChatType.SUPPORT,
    title: `Support: ${issue}`,
    participants: [
      { userId: currentUser.id, role: ParticipantRole.COMPANY },
      { userId: supportAgentId, role: ParticipantRole.SUPPORT }
    ]
  });
};
```

## Benefits

### 1. Flexibility
- Support for multiple chat types
- Extensible participant roles
- Rich message content support

### 2. Scalability
- Efficient participant management
- Optimized unread count tracking
- Support for large group chats

### 3. Future-Proof
- Easy to add new chat types
- Extensible message formats
- Metadata support for custom features

### 4. Backward Compatibility
- Existing code continues to work
- Gradual migration path
- No breaking changes

## Database Schema

### New Tables
- `generic_chats`: Main chat entities
- `chat_participants`: Participant management

### Enhanced Tables
- `chat_messages`: Added support for rich content, replies, and metadata

### Indexes
- Optimized for participant queries
- Efficient chat type filtering
- Fast message retrieval

## Testing

### Unit Tests
- Test generic chat creation
- Test participant management
- Test message handling
- Test backward compatibility

### Integration Tests
- Test API endpoints
- Test WebSocket integration
- Test migration scripts

### E2E Tests
- Test chat flows in mobile app
- Test chat flows in web portals
- Test cross-platform compatibility

## Performance Considerations

### Optimizations
- Efficient participant queries using indexes
- Lazy loading of chat participants
- Optimized unread count updates

### Monitoring
- Track chat creation patterns
- Monitor message volume
- Analyze participant engagement

## Security

### Access Control
- Participant-based access control
- Role-based permissions
- Context-aware authorization

### Data Protection
- Encrypted message content
- Secure file uploads
- Privacy-compliant metadata handling

## Future Enhancements

### Planned Features
1. Message reactions and emoji support
2. Thread-based conversations
3. Voice and video message support
4. Advanced moderation tools
5. Chat analytics and insights

### Extension Points
- Custom message types via metadata
- Plugin architecture for chat features
- Webhook integration for external systems
- AI-powered chat assistance

## Troubleshooting

### Common Issues
1. **Migration fails**: Check database permissions and constraints
2. **Type errors**: Ensure shared types are properly imported
3. **Backward compatibility**: Verify legacy endpoints still work

### Debug Tools
- Chat service logging
- Database query analysis
- WebSocket connection monitoring

## Conclusion

The refactored chat system provides a solid foundation for current and future chat requirements while maintaining full backward compatibility. The generic architecture allows for easy extension and customization based on evolving business needs.
