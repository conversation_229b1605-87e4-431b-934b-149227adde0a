# Production Deployment Guide

This guide covers the complete production deployment process for the Job Platform application.

## 🚀 Quick Start

1. **Clone and Setup**
   ```bash
   git clone <repository-url>
   cd job-platform
   cp .env.production.example .env.production
   # Edit .env.production with your production values
   ```

2. **Deploy**
   ```bash
   ./scripts/deploy.sh production
   ```

3. **Monitor**
   ```bash
   ./scripts/monitor.sh monitor
   ```

## 📋 Prerequisites

### System Requirements
- **OS**: Ubuntu 20.04+ / CentOS 8+ / RHEL 8+
- **RAM**: Minimum 4GB, Recommended 8GB+
- **Storage**: Minimum 50GB SSD
- **CPU**: 2+ cores recommended

### Software Dependencies
- Docker 20.10+
- Docker Compose 2.0+
- Git
- curl
- Node.js 18+ (for local development)

### Installation Commands
```bash
# Ubuntu/Debian
sudo apt update
sudo apt install -y docker.io docker-compose git curl

# CentOS/RHEL
sudo yum install -y docker docker-compose git curl

# Start Docker
sudo systemctl start docker
sudo systemctl enable docker
sudo usermod -aG docker $USER
```

## 🔧 Configuration

### Environment Variables
Copy `.env.production.example` to `.env.production` and configure:

```bash
# Database
POSTGRES_USER=jobplatform_user
POSTGRES_PASSWORD=your_secure_password
POSTGRES_DB=job_platform_prod

# Security
JWT_SECRET=your_super_secure_jwt_secret_32_chars_min
NEXTAUTH_SECRET=your_nextauth_secret_32_chars_min

# URLs
NEXT_PUBLIC_API_URL=https://api.jobplatform.com
COMPANY_PORTAL_URL=https://company.jobplatform.com
ADMIN_PORTAL_URL=https://admin.jobplatform.com

# Email
EMAIL_PROVIDER=sendgrid
SENDGRID_API_KEY=your_sendgrid_api_key
EMAIL_FROM=<EMAIL>

# Payment
STRIPE_SECRET_KEY=sk_live_your_stripe_secret_key

# Monitoring
SENTRY_DSN=your_sentry_dsn
```

### SSL Certificates
Place SSL certificates in `nginx/ssl/`:
```
nginx/ssl/
├── api.jobplatform.com.crt
├── api.jobplatform.com.key
├── company.jobplatform.com.crt
├── company.jobplatform.com.key
├── admin.jobplatform.com.crt
└── admin.jobplatform.com.key
```

## 🚢 Deployment Process

### Automated Deployment
```bash
# Full production deployment
./scripts/deploy.sh production

# Staging deployment
./scripts/deploy.sh staging
```

### Manual Deployment Steps
```bash
# 1. Build images
docker-compose -f docker-compose.prod.yml build

# 2. Start services
docker-compose -f docker-compose.prod.yml up -d

# 3. Run migrations
docker-compose -f docker-compose.prod.yml exec backend npm run migration:run

# 4. Health check
curl http://localhost:3000/api/v1/health
```

## 📊 Monitoring

### Health Checks
```bash
# Single health check
./scripts/monitor.sh check

# Continuous monitoring
./scripts/monitor.sh monitor

# Generate report
./scripts/monitor.sh report
```

### Service URLs
- **Backend API**: http://localhost:3000
- **Company Portal**: http://localhost:3001
- **Admin Portal**: http://localhost:3002
- **Health Check**: http://localhost:3000/api/v1/health

### Key Metrics to Monitor
- **Response Time**: < 500ms for API endpoints
- **Error Rate**: < 1% for critical endpoints
- **CPU Usage**: < 80% sustained
- **Memory Usage**: < 80% sustained
- **Disk Usage**: < 90%
- **Database Connections**: Monitor pool usage

## 🔒 Security Checklist

### Pre-Deployment
- [ ] Update all dependencies to latest secure versions
- [ ] Configure strong passwords and secrets
- [ ] Set up SSL certificates
- [ ] Configure firewall rules
- [ ] Enable rate limiting
- [ ] Set up monitoring and alerting

### Post-Deployment
- [ ] Verify SSL configuration
- [ ] Test authentication flows
- [ ] Verify rate limiting is working
- [ ] Check security headers
- [ ] Test file upload restrictions
- [ ] Verify database access controls

## 🔄 Backup & Recovery

### Database Backup
```bash
# Manual backup
docker-compose -f docker-compose.prod.yml exec postgres pg_dump \
  -U $POSTGRES_USER -d $POSTGRES_DB > backup_$(date +%Y%m%d).sql

# Automated backup (add to crontab)
0 2 * * * /path/to/backup-script.sh
```

### File Backup
```bash
# Backup uploaded files
tar -czf uploads_backup_$(date +%Y%m%d).tar.gz ./uploads/
```

### Recovery Process
```bash
# Restore database
docker-compose -f docker-compose.prod.yml exec -T postgres psql \
  -U $POSTGRES_USER -d $POSTGRES_DB < backup_file.sql

# Restore files
tar -xzf uploads_backup.tar.gz
```

## 🚨 Troubleshooting

### Common Issues

#### Services Not Starting
```bash
# Check logs
docker-compose -f docker-compose.prod.yml logs

# Check specific service
docker-compose -f docker-compose.prod.yml logs backend

# Restart service
./scripts/monitor.sh restart backend
```

#### Database Connection Issues
```bash
# Check database status
docker-compose -f docker-compose.prod.yml exec postgres pg_isready

# Check connection from backend
docker-compose -f docker-compose.prod.yml exec backend npm run migration:status
```

#### High Memory Usage
```bash
# Check container stats
docker stats

# Restart services if needed
docker-compose -f docker-compose.prod.yml restart
```

### Log Locations
- **Application Logs**: `docker-compose logs <service>`
- **Monitoring Logs**: `./logs/monitoring/`
- **Nginx Logs**: Inside nginx container at `/var/log/nginx/`

## 📈 Performance Optimization

### Database Optimization
- Enable connection pooling
- Add database indexes for frequently queried fields
- Regular VACUUM and ANALYZE operations
- Monitor slow queries

### Application Optimization
- Enable Redis caching
- Optimize API response sizes
- Use CDN for static assets
- Enable gzip compression

### Infrastructure Optimization
- Use SSD storage
- Adequate RAM allocation
- Load balancing for high traffic
- Database read replicas

## 🔄 Updates & Maintenance

### Regular Maintenance Tasks
- **Daily**: Monitor health checks and alerts
- **Weekly**: Review logs and performance metrics
- **Monthly**: Update dependencies and security patches
- **Quarterly**: Full backup and disaster recovery testing

### Update Process
```bash
# 1. Backup current state
./scripts/monitor.sh report

# 2. Deploy new version
git pull origin main
./scripts/deploy.sh production

# 3. Verify deployment
./scripts/monitor.sh check
```

## 📞 Support

### Emergency Contacts
- **DevOps Team**: <EMAIL>
- **On-Call Engineer**: +1-XXX-XXX-XXXX

### Monitoring Alerts
- **Sentry**: Application error monitoring
- **Email Alerts**: Critical service failures
- **Slack Integration**: Real-time notifications

### Documentation
- **API Documentation**: https://api.jobplatform.com/docs
- **Architecture Docs**: `./docs/architecture.md`
- **Runbooks**: `./docs/runbooks/`
