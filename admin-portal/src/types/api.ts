import { User<PERSON>ole, ApplicationStatus, JobStatus } from "@shared/types";
import {
  ApiResponse,
  PaginatedResponse,
  PaginationMeta,
  ApiErrorResponse,
  PaginationParams,
} from "@shared/types/api-response";

/**
 * User interface
 */
export interface User {
  id: string;
  email?: string;
  phone?: string;
  fullName: string;
  role: UserRole;
  trustScore: number;
  profilePic?: string;
  isVerified: boolean;
  isKycVerified: boolean;
  isActive: boolean;
  isBanned: boolean;
  banReason?: string;
  address?: string;
  city?: string;
  state?: string;
  country?: string;
  postalCode?: string;
  bio?: string;
  companyName?: string;
  companyRegistrationNumber?: string;
  companyTaxId?: string;
  companyWebsite?: string;
  companySize?: string;
  companyIndustry?: string;
  createdAt: string;
  updatedAt: string;
}

/**
 * Auth response interface
 */
export interface AuthResponse {
  token: string;
  refreshToken: string;
  user: User;
}

/**
 * Login request interface
 */
export interface LoginRequest {
  email: string;
  password: string;
}

/**
 * Job interface
 */
export interface Job {
  id: string;
  title: string;
  description: string;
  companyId: string;
  company: {
    id: string;
    companyName: string;
    profilePic?: string;
    isVerified: boolean;
    rating?: number;
  };
  location: string;
  city?: string;
  state?: string;
  country?: string;
  postalCode?: string;
  latitude?: number;
  longitude?: number;
  startDateTime: string;
  endDateTime: string;
  paymentAmount: number;
  duration: number;
  trustScoreRequired: number;
  requiredWorkers: number;
  hiredWorkers: number;
  requiresLaptop: boolean;
  requiresSmartphone: boolean;
  skillsRequired?: string;
  isEmergencyJob: boolean;
  status: JobStatus;
  createdAt: string;
  updatedAt: string;
}

/**
 * Application interface
 */
export interface Application {
  id: string;
  workerId: string;
  worker: User;
  jobId: string;
  job: Job;
  status: ApplicationStatus;
  coverLetter?: string;
  rejectionReason?: string;
  cancellationReason?: string;
  createdAt: string;
  updatedAt: string;
}

/**
 * Document interface
 */
export interface Document {
  id: string;
  userId: string;
  user: User;
  documentType: string;
  documentUrl: string;
  documentNumber?: string;
  isVerified: boolean;
  rejectionReason?: string;
  verifiedAt?: string;
  createdAt: string;
  updatedAt: string;
}

/**
 * Payout interface
 */
export interface Payout {
  id: string;
  jobId: string;
  job: Job;
  workerId: string;
  worker: User;
  amount: number;
  status: string;
  paymentMethod?: string;
  transactionId?: string;
  createdAt: string;
  updatedAt: string;
}

/**
 * Dispute interface
 */
export interface Dispute {
  id: string;
  title: string;
  description: string;
  status: "open" | "in_progress" | "resolved" | "cancelled";
  type: "payment" | "job" | "application" | "other";
  raisedById: string;
  raisedBy: User;
  againstId: string;
  against: User;
  jobId?: string;
  job?: Job;
  applicationId?: string;
  application?: Application;
  resolution?: string;
  createdAt: string;
  updatedAt: string;
  closedAt?: string;
}

/**
 * Activity log interface
 */
export interface ActivityLog {
  id: string;
  userId: string;
  user?: User;
  action: string;
  details: Record<string, any>;
  ipAddress?: string;
  userAgent?: string;
  createdAt: string;
}

/**
 * Setting interface
 */
export interface Setting {
  key: string;
  value: any;
  description: string;
  type: "string" | "number" | "boolean" | "json";
  isPublic: boolean;
  createdAt: string;
  updatedAt: string;
}

/**
 * Report interface
 */
export interface Report {
  id: string;
  type: "user" | "job" | "application" | "other";
  title: string;
  description: string;
  reportedById: string;
  reportedBy: User;
  reportedId: string;
  status: "pending" | "investigating" | "resolved" | "dismissed";
  resolution?: string;
  createdAt: string;
  updatedAt: string;
}

/**
 * Dashboard stats interface
 */
export interface DashboardStats {
  totalUsers: number;
  totalJobs: number;
  totalApplications: number;
  totalDisputes: number;
  activeUsers: number;
  activeJobs: number;
  pendingKyc: number;
  pendingDisputes: number;
  recentUsers: User[];
  recentJobs: Job[];
  recentDisputes: Dispute[];
}

/**
 * User stats interface
 */
export interface UserStats {
  totalUsers: number;
  newUsers: number;
  activeUsers: number;
  verifiedUsers: number;
  kycVerifiedUsers: number;
  bannedUsers: number;
  usersByRole: Record<UserRole, number>;
  usersByCountry: Record<string, number>;
  registrationTrend: Array<{ date: string; count: number }>;
}

/**
 * Job stats interface
 */
export interface JobStats {
  totalJobs: number;
  activeJobs: number;
  completedJobs: number;
  cancelledJobs: number;
  emergencyJobs: number;
  averagePayment: number;
  jobsByStatus: Record<JobStatus, number>;
  jobsByLocation: Record<string, number>;
  jobTrend: Array<{ date: string; count: number }>;
}

/**
 * Revenue stats interface
 */
export interface RevenueStats {
  totalRevenue: number;
  monthlyRevenue: number;
  averageJobValue: number;
  revenueByMonth: Array<{ month: string; revenue: number }>;
  revenueByCategory: Record<string, number>;
  topEarningCompanies: Array<{ company: User; revenue: number }>;
}

/**
 * Trust score stats interface
 */
export interface TrustScoreStats {
  averageTrustScore: number;
  trustScoreDistribution: Record<string, number>;
  topTrustedUsers: Array<{ user: User; score: number }>;
  lowestTrustedUsers: Array<{ user: User; score: number }>;
  trustScoreTrend: Array<{ date: string; averageScore: number }>;
}
