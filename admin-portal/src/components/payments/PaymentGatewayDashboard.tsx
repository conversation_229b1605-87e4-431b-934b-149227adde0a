"use client";

import React, { useState } from "react";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, TabsTrigger } from "@/components/ui/tabs";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  CreditCard,
  TrendingUp,
  DollarSign,
  Activity,
  AlertTriangle,
  CheckCircle,
  XCircle,
  RefreshCw,
  Globe,
  Zap,
} from "lucide-react";
import {
  useGetPaymentAnalyticsQuery,
  useGetGatewayConfigurationsQuery,
  useGetGatewayHealthQuery,
  useTestGatewayConnectionMutation,
  useGetPaymentMethodStatsQuery,
  useGetCurrencyStatsQuery,
} from "@/store/api/paymentsApi";

interface PaymentGatewayDashboardProps {
  className?: string;
}

export const PaymentGatewayDashboard: React.FC<PaymentGatewayDashboardProps> = ({
  className = "",
}) => {
  const [dateRange, setDateRange] = useState("7d");
  const [selectedGateway, setSelectedGateway] = useState<string>("all");

  // API queries
  const { data: analytics, isLoading: analyticsLoading } = useGetPaymentAnalyticsQuery({
    startDate: getStartDate(dateRange),
    endDate: new Date().toISOString(),
    gateway: selectedGateway === "all" ? undefined : selectedGateway,
  });

  const { data: gatewayConfigs, isLoading: configsLoading } = useGetGatewayConfigurationsQuery();
  const { data: gatewayHealth, isLoading: healthLoading } = useGetGatewayHealthQuery();
  const { data: methodStats } = useGetPaymentMethodStatsQuery({
    startDate: getStartDate(dateRange),
    endDate: new Date().toISOString(),
    gateway: selectedGateway === "all" ? undefined : selectedGateway,
  });
  const { data: currencyStats } = useGetCurrencyStatsQuery({
    startDate: getStartDate(dateRange),
    endDate: new Date().toISOString(),
  });

  const [testConnection] = useTestGatewayConnectionMutation();

  const handleTestConnection = async (gateway: string) => {
    try {
      await testConnection({ gateway }).unwrap();
    } catch (error) {
      console.error("Failed to test gateway connection:", error);
    }
  };

  const formatCurrency = (amount: number, currency = "USD") => {
    return new Intl.NumberFormat("en-US", {
      style: "currency",
      currency,
    }).format(amount);
  };

  const formatPercentage = (value: number) => {
    return `${(value * 100).toFixed(1)}%`;
  };

  function getStartDate(range: string): string {
    const now = new Date();
    switch (range) {
      case "1d":
        return new Date(now.getTime() - 24 * 60 * 60 * 1000).toISOString();
      case "7d":
        return new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000).toISOString();
      case "30d":
        return new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000).toISOString();
      case "90d":
        return new Date(now.getTime() - 90 * 24 * 60 * 60 * 1000).toISOString();
      default:
        return new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000).toISOString();
    }
  }

  const getGatewayIcon = (gateway: string) => {
    switch (gateway.toLowerCase()) {
      case "stripe":
        return <Globe className="h-5 w-5" />;
      case "razorpay":
        return <Zap className="h-5 w-5" />;
      default:
        return <CreditCard className="h-5 w-5" />;
    }
  };

  const getHealthStatusColor = (status: string) => {
    switch (status) {
      case "healthy":
        return "text-green-600";
      case "degraded":
        return "text-yellow-600";
      case "down":
        return "text-red-600";
      default:
        return "text-gray-600";
    }
  };

  const getHealthStatusIcon = (status: string) => {
    switch (status) {
      case "healthy":
        return <CheckCircle className="h-4 w-4 text-green-600" />;
      case "degraded":
        return <AlertTriangle className="h-4 w-4 text-yellow-600" />;
      case "down":
        return <XCircle className="h-4 w-4 text-red-600" />;
      default:
        return <Activity className="h-4 w-4 text-gray-600" />;
    }
  };

  if (analyticsLoading || configsLoading || healthLoading) {
    return (
      <div className={`space-y-6 ${className}`}>
        <div className="animate-pulse">
          <div className="h-8 bg-gray-200 rounded w-1/4 mb-4"></div>
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
            {[...Array(4)].map((_, i) => (
              <div key={i} className="h-32 bg-gray-200 rounded"></div>
            ))}
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Header */}
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
        <div>
          <h1 className="text-3xl font-bold">Payment Gateway Dashboard</h1>
          <p className="text-gray-600">Monitor and manage payment gateways</p>
        </div>
        <div className="flex gap-2">
          <Select value={selectedGateway} onValueChange={setSelectedGateway}>
            <SelectTrigger className="w-40">
              <SelectValue placeholder="All Gateways" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All Gateways</SelectItem>
              <SelectItem value="stripe">Stripe</SelectItem>
              <SelectItem value="razorpay">Razorpay</SelectItem>
            </SelectContent>
          </Select>
          <Select value={dateRange} onValueChange={setDateRange}>
            <SelectTrigger className="w-32">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="1d">Last 24h</SelectItem>
              <SelectItem value="7d">Last 7 days</SelectItem>
              <SelectItem value="30d">Last 30 days</SelectItem>
              <SelectItem value="90d">Last 90 days</SelectItem>
            </SelectContent>
          </Select>
        </div>
      </div>

      {/* Overview Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Revenue</CardTitle>
            <DollarSign className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {formatCurrency(analytics?.totalRevenue || 0)}
            </div>
            <p className="text-xs text-muted-foreground">
              Across all gateways
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Transactions</CardTitle>
            <TrendingUp className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {analytics?.totalTransactions?.toLocaleString() || 0}
            </div>
            <p className="text-xs text-muted-foreground">
              Total processed
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Success Rate</CardTitle>
            <CheckCircle className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {formatPercentage(analytics?.successRate || 0)}
            </div>
            <p className="text-xs text-muted-foreground">
              Payment success rate
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Avg. Transaction</CardTitle>
            <Activity className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {formatCurrency(analytics?.averageTransactionValue || 0)}
            </div>
            <p className="text-xs text-muted-foreground">
              Average value
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Detailed Analytics */}
      <Tabs defaultValue="gateways" className="space-y-4">
        <TabsList>
          <TabsTrigger value="gateways">Gateway Breakdown</TabsTrigger>
          <TabsTrigger value="methods">Payment Methods</TabsTrigger>
          <TabsTrigger value="currencies">Currencies</TabsTrigger>
          <TabsTrigger value="health">Gateway Health</TabsTrigger>
        </TabsList>

        <TabsContent value="gateways" className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {analytics?.gatewayBreakdown?.map((gateway) => (
              <Card key={gateway.gateway}>
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium flex items-center gap-2">
                    {getGatewayIcon(gateway.gateway)}
                    {gateway.gateway.charAt(0).toUpperCase() + gateway.gateway.slice(1)}
                  </CardTitle>
                  <Badge variant={gateway.gateway === "stripe" ? "default" : "secondary"}>
                    {formatPercentage(gateway.successRate)}
                  </Badge>
                </CardHeader>
                <CardContent>
                  <div className="space-y-2">
                    <div className="flex justify-between">
                      <span className="text-sm text-gray-600">Revenue:</span>
                      <span className="font-medium">{formatCurrency(gateway.revenue)}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-sm text-gray-600">Transactions:</span>
                      <span className="font-medium">{gateway.transactions.toLocaleString()}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-sm text-gray-600">Avg. Value:</span>
                      <span className="font-medium">{formatCurrency(gateway.averageValue)}</span>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </TabsContent>

        <TabsContent value="methods" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Payment Method Performance</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {methodStats?.map((method) => (
                  <div key={method.method} className="flex items-center justify-between p-3 border rounded">
                    <div className="flex items-center gap-3">
                      <CreditCard className="h-5 w-5 text-gray-600" />
                      <div>
                        <p className="font-medium">{method.method.toUpperCase()}</p>
                        <p className="text-sm text-gray-600">
                          {method.transactions} transactions
                        </p>
                      </div>
                    </div>
                    <div className="text-right">
                      <p className="font-medium">{formatCurrency(method.revenue)}</p>
                      <p className="text-sm text-gray-600">
                        {formatPercentage(method.successRate)} success
                      </p>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="currencies" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Currency Breakdown</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {currencyStats?.map((currency) => (
                  <div key={currency.currency} className="flex items-center justify-between p-3 border rounded">
                    <div>
                      <p className="font-medium">{currency.currency}</p>
                      <p className="text-sm text-gray-600">
                        Primary gateway: {currency.topGateway}
                      </p>
                    </div>
                    <div className="text-right">
                      <p className="font-medium">{formatCurrency(currency.revenue, currency.currency)}</p>
                      <p className="text-sm text-gray-600">
                        {currency.transactions} transactions
                      </p>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="health" className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {gatewayHealth?.map((health) => (
              <Card key={health.gateway}>
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium flex items-center gap-2">
                    {getGatewayIcon(health.gateway)}
                    {health.gateway.charAt(0).toUpperCase() + health.gateway.slice(1)}
                  </CardTitle>
                  <div className="flex items-center gap-2">
                    {getHealthStatusIcon(health.status)}
                    <Button
                      size="sm"
                      variant="outline"
                      onClick={() => handleTestConnection(health.gateway)}
                    >
                      <RefreshCw className="h-3 w-3" />
                    </Button>
                  </div>
                </CardHeader>
                <CardContent>
                  <div className="space-y-2">
                    <div className="flex justify-between">
                      <span className="text-sm text-gray-600">Status:</span>
                      <span className={`font-medium ${getHealthStatusColor(health.status)}`}>
                        {health.status.charAt(0).toUpperCase() + health.status.slice(1)}
                      </span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-sm text-gray-600">Response Time:</span>
                      <span className="font-medium">{health.responseTime}ms</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-sm text-gray-600">Uptime:</span>
                      <span className="font-medium">{formatPercentage(health.uptime / 100)}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-sm text-gray-600">Last Checked:</span>
                      <span className="font-medium text-xs">
                        {new Date(health.lastChecked).toLocaleTimeString()}
                      </span>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </TabsContent>
      </Tabs>
    </div>
  );
};

export default PaymentGatewayDashboard;
