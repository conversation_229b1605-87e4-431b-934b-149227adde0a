import React, { useState } from 'react';
import {
  <PERSON><PERSON>,
  <PERSON>alog<PERSON>ontent,
  <PERSON><PERSON>Header,
  <PERSON><PERSON>Title,
  DialogFooter,
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Textarea } from '@/components/ui/textarea';
import { Label } from '@/components/ui/label';
import { Card, CardContent } from '@/components/ui/card';
import { Separator } from '@/components/ui/separator';
import {
  CheckCircle,
  XCircle,
  FileText,
  User,
  Building,
  Calendar,
  Eye,
  Download,
  AlertTriangle,
} from 'lucide-react';
import { useGetDocumentForReviewQuery, useVerifyDocumentMutation } from '@/store/api/adminApi';
import { formatDate, formatTimeAgo } from '@/utils/format';
import { toast } from '@/hooks/use-toast';

interface DocumentReviewModalProps {
  documentId: string | null;
  isOpen: boolean;
  onClose: () => void;
  onVerified?: () => void;
}

export const DocumentReviewModal: React.FC<DocumentReviewModalProps> = ({
  documentId,
  isOpen,
  onClose,
  onVerified,
}) => {
  const [rejectionReason, setRejectionReason] = useState('');
  const [isSubmitting, setIsSubmitting] = useState(false);

  const { data: document, isLoading } = useGetDocumentForReviewQuery(documentId!, {
    skip: !documentId,
  });

  const [verifyDocument] = useVerifyDocumentMutation();

  const handleVerify = async (status: 'VERIFIED' | 'REJECTED') => {
    if (!documentId) return;

    if (status === 'REJECTED' && !rejectionReason.trim()) {
      toast({
        title: 'Rejection reason required',
        description: 'Please provide a reason for rejecting this document.',
        variant: 'destructive',
      });
      return;
    }

    setIsSubmitting(true);
    try {
      await verifyDocument({
        documentId,
        status,
        rejectionReason: status === 'REJECTED' ? rejectionReason : undefined,
      }).unwrap();

      toast({
        title: `Document ${status.toLowerCase()}`,
        description: `The document has been successfully ${status.toLowerCase()}.`,
      });

      onVerified?.();
      onClose();
      setRejectionReason('');
    } catch (error) {
      toast({
        title: 'Error',
        description: 'Failed to update document status. Please try again.',
        variant: 'destructive',
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  const getDocumentTypeLabel = (type: string) => {
    const labels: Record<string, string> = {
      'ID_PROOF': 'ID Proof',
      'ADDRESS_PROOF': 'Address Proof',
      'COMPANY_REGISTRATION': 'Company Registration',
      'TAX_DOCUMENT': 'Tax Document',
      'BANK_STATEMENT': 'Bank Statement',
    };
    return labels[type] || type;
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'VERIFIED': return 'bg-green-100 text-green-800 border-green-200';
      case 'REJECTED': return 'bg-red-100 text-red-800 border-red-200';
      case 'PENDING': return 'bg-yellow-100 text-yellow-800 border-yellow-200';
      default: return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  if (!isOpen) return null;

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <FileText className="h-5 w-5" />
            Document Review
          </DialogTitle>
        </DialogHeader>

        {isLoading ? (
          <div className="flex items-center justify-center h-64">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
          </div>
        ) : document ? (
          <div className="space-y-6">
            {/* Document Info */}
            <Card>
              <CardContent className="p-6">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div className="space-y-4">
                    <div>
                      <Label className="text-sm font-medium text-gray-600">Document Type</Label>
                      <p className="text-lg font-semibold text-gray-900">
                        {getDocumentTypeLabel(document.documentType)}
                      </p>
                    </div>
                    
                    <div>
                      <Label className="text-sm font-medium text-gray-600">Document Number</Label>
                      <p className="text-gray-900">
                        {document.documentNumber || 'Not provided'}
                      </p>
                    </div>

                    <div>
                      <Label className="text-sm font-medium text-gray-600">Status</Label>
                      <div className="mt-1">
                        <Badge className={getStatusColor(document.verificationStatus)}>
                          {document.verificationStatus}
                        </Badge>
                      </div>
                    </div>
                  </div>

                  <div className="space-y-4">
                    <div>
                      <Label className="text-sm font-medium text-gray-600">Submitted</Label>
                      <p className="text-gray-900">
                        {formatDate(document.submittedAt)} ({formatTimeAgo(document.submittedAt)})
                      </p>
                    </div>

                    {document.verifiedAt && (
                      <div>
                        <Label className="text-sm font-medium text-gray-600">Verified</Label>
                        <p className="text-gray-900">
                          {formatDate(document.verifiedAt)} by {document.verifiedBy?.fullName}
                        </p>
                      </div>
                    )}

                    {document.rejectionReason && (
                      <div>
                        <Label className="text-sm font-medium text-gray-600">Rejection Reason</Label>
                        <p className="text-red-600">{document.rejectionReason}</p>
                      </div>
                    )}
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* User/Company Info */}
            <Card>
              <CardContent className="p-6">
                <div className="flex items-start gap-4">
                  <div className="h-12 w-12 bg-gray-100 rounded-lg flex items-center justify-center">
                    {document.user ? (
                      <User className="h-6 w-6 text-gray-600" />
                    ) : (
                      <Building className="h-6 w-6 text-gray-600" />
                    )}
                  </div>
                  <div className="flex-1">
                    <h3 className="text-lg font-semibold text-gray-900">
                      {document.user?.fullName || document.company?.name}
                    </h3>
                    <p className="text-gray-600">
                      {document.user?.email || document.company?.user?.email}
                    </p>
                    <div className="mt-2">
                      <Badge variant="outline">
                        {document.user?.role || 'Company'}
                      </Badge>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Document Preview */}
            <Card>
              <CardContent className="p-6">
                <div className="flex items-center justify-between mb-4">
                  <h3 className="text-lg font-semibold text-gray-900">Document Preview</h3>
                  <div className="flex gap-2">
                    <Button variant="outline" size="sm" className="flex items-center gap-2">
                      <Eye className="h-4 w-4" />
                      View Full Size
                    </Button>
                    <Button variant="outline" size="sm" className="flex items-center gap-2">
                      <Download className="h-4 w-4" />
                      Download
                    </Button>
                  </div>
                </div>
                
                <div className="border rounded-lg overflow-hidden bg-gray-50">
                  <img
                    src={document.documentUrl}
                    alt="Document"
                    className="w-full h-96 object-contain"
                    onError={(e) => {
                      e.currentTarget.style.display = 'none';
                      e.currentTarget.nextElementSibling?.classList.remove('hidden');
                    }}
                  />
                  <div className="hidden flex items-center justify-center h-96 text-gray-500">
                    <div className="text-center">
                      <FileText className="h-12 w-12 mx-auto mb-2" />
                      <p>Unable to preview document</p>
                      <Button variant="outline" size="sm" className="mt-2">
                        Download to view
                      </Button>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Verification Actions */}
            {document.verificationStatus === 'PENDING' && (
              <Card>
                <CardContent className="p-6">
                  <h3 className="text-lg font-semibold text-gray-900 mb-4">Verification Decision</h3>
                  
                  <div className="space-y-4">
                    <div>
                      <Label htmlFor="rejection-reason" className="text-sm font-medium text-gray-600">
                        Rejection Reason (required if rejecting)
                      </Label>
                      <Textarea
                        id="rejection-reason"
                        placeholder="Provide a clear reason for rejection..."
                        value={rejectionReason}
                        onChange={(e) => setRejectionReason(e.target.value)}
                        className="mt-1"
                        rows={3}
                      />
                    </div>

                    <div className="flex gap-3">
                      <Button
                        onClick={() => handleVerify('VERIFIED')}
                        disabled={isSubmitting}
                        className="flex items-center gap-2 bg-green-600 hover:bg-green-700"
                      >
                        <CheckCircle className="h-4 w-4" />
                        Approve Document
                      </Button>
                      
                      <Button
                        onClick={() => handleVerify('REJECTED')}
                        disabled={isSubmitting}
                        variant="destructive"
                        className="flex items-center gap-2"
                      >
                        <XCircle className="h-4 w-4" />
                        Reject Document
                      </Button>
                    </div>

                    {rejectionReason.trim() === '' && (
                      <div className="flex items-center gap-2 text-amber-600 text-sm">
                        <AlertTriangle className="h-4 w-4" />
                        <span>Rejection reason is required when rejecting a document</span>
                      </div>
                    )}
                  </div>
                </CardContent>
              </Card>
            )}
          </div>
        ) : (
          <div className="text-center py-8">
            <FileText className="h-12 w-12 text-gray-400 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">Document not found</h3>
            <p className="text-gray-600">The requested document could not be loaded.</p>
          </div>
        )}

        <DialogFooter>
          <Button variant="outline" onClick={onClose} disabled={isSubmitting}>
            Close
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};
