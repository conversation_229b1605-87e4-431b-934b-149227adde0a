"use client"

import { useState, useEffect } from "react"
import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query"
import { motion, AnimatePresence } from "framer-motion"
import { Bell, Check, AlertTriangle, Info, MessageSquare, User, Calendar } from "lucide-react"
import { Button } from "@/components/ui/button"
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover"
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Badge } from "@/components/ui/badge"
import { ScrollArea } from "@/components/ui/scroll-area"
import { api } from "@/lib/api"
import { staggerContainer, staggerItem } from "@/utils/animations"

export function NotificationCenter() {
  const queryClient = useQueryClient()
  const [open, setOpen] = useState(false)
  const [unreadCount, setUnreadCount] = useState(0)

  const { data: notifications } = useQuery({
    queryKey: ["notifications"],
    queryFn: async () => {
      const response = await api.get("/admin/notifications")
      return response.data
    },
    refetchInterval: 30000, // Refetch every 30 seconds
  })

  const markAsReadMutation = useMutation({
    mutationFn: async (notificationId: string) => {
      return api.patch(`/admin/notifications/${notificationId}/read`)
    },
    onSuccess: () => {
      queryClient.invalidateQueries(["notifications"])
    },
  })

  const markAllAsReadMutation = useMutation({
    mutationFn: async () => {
      return api.patch("/admin/notifications/read-all")
    },
    onSuccess: () => {
      queryClient.invalidateQueries(["notifications"])
    },
  })

  useEffect(() => {
    if (notifications) {
      const count = notifications.filter((notification) => !notification.isRead).length
      setUnreadCount(count)
    }
  }, [notifications])

  const getNotificationIcon = (type) => {
    switch (type) {
      case "alert":
        return <AlertTriangle className="h-5 w-5 text-red-500" />
      case "info":
        return <Info className="h-5 w-5 text-blue-500" />
      case "message":
        return <MessageSquare className="h-5 w-5 text-indigo-500" />
      case "user":
        return <User className="h-5 w-5 text-green-500" />
      case "event":
        return <Calendar className="h-5 w-5 text-yellow-500" />
      default:
        return <Info className="h-5 w-5 text-gray-500" />
    }
  }

  const handleMarkAsRead = (notificationId: string) => {
    markAsReadMutation.mutate(notificationId)
  }

  const handleMarkAllAsRead = () => {
    markAllAsReadMutation.mutate()
  }

  const unreadNotifications = notifications?.filter((notification) => !notification.isRead) || []
  const allNotifications = notifications || []

  return (
    <Popover open={open} onOpenChange={setOpen}>
      <PopoverTrigger asChild>
        <Button variant="outline" size="icon" className="relative">
          <Bell className="h-5 w-5" />
          {unreadCount > 0 && (
            <Badge
              className="absolute -right-1 -top-1 flex h-5 w-5 items-center justify-center rounded-full p-0"
              variant="destructive"
            >
              {unreadCount > 9 ? "9+" : unreadCount}
            </Badge>
          )}
          <span className="sr-only">Toggle notifications</span>
        </Button>
      </PopoverTrigger>
      <PopoverContent className="w-[380px] p-0" align="end">
        <Tabs defaultValue="unread">
          <div className="flex items-center justify-between border-b px-4 py-2">
            <h4 className="font-medium">Notifications</h4>
            <div className="flex items-center gap-2">
              <TabsList className="grid w-[120px] grid-cols-2">
                <TabsTrigger value="unread">Unread</TabsTrigger>
                <TabsTrigger value="all">All</TabsTrigger>
              </TabsList>
            </div>
          </div>

          <TabsContent value="unread" className="p-0">
            {unreadNotifications.length > 0 ? (
              <>
                <div className="flex items-center justify-between border-b px-4 py-2">
                  <p className="text-sm text-muted-foreground">
                    You have {unreadNotifications.length} unread notifications
                  </p>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={handleMarkAllAsRead}
                    disabled={markAllAsReadMutation.isLoading}
                  >
                    <Check className="mr-2 h-4 w-4" />
                    Mark all as read
                  </Button>
                </div>
                <ScrollArea className="h-[300px]">
                  <motion.div variants={staggerContainer} initial="hidden" animate="visible" className="flex flex-col">
                    <AnimatePresence>
                      {unreadNotifications.map((notification) => (
                        <motion.div
                          key={notification.id}
                          variants={staggerItem}
                          exit={{ opacity: 0, height: 0 }}
                          className="border-b last:border-0"
                        >
                          <div className="flex items-start gap-3 p-4">
                            <div className="mt-1">{getNotificationIcon(notification.type)}</div>
                            <div className="flex-1 space-y-1">
                              <p className="text-sm font-medium">{notification.title}</p>
                              <p className="text-sm text-muted-foreground">{notification.message}</p>
                              <p className="text-xs text-muted-foreground">
                                {new Date(notification.createdAt).toLocaleString()}
                              </p>
                            </div>
                            <Button
                              variant="ghost"
                              size="icon"
                              className="h-8 w-8"
                              onClick={() => handleMarkAsRead(notification.id)}
                            >
                              <Check className="h-4 w-4" />
                              <span className="sr-only">Mark as read</span>
                            </Button>
                          </div>
                        </motion.div>
                      ))}
                    </AnimatePresence>
                  </motion.div>
                </ScrollArea>
              </>
            ) : (
              <div className="flex flex-col items-center justify-center p-8">
                <Bell className="h-12 w-12 text-muted-foreground mb-4" />
                <h3 className="text-lg font-medium">All caught up!</h3>
                <p className="text-sm text-muted-foreground text-center mt-2">You have no unread notifications.</p>
              </div>
            )}
          </TabsContent>

          <TabsContent value="all" className="p-0">
            {allNotifications.length > 0 ? (
              <ScrollArea className="h-[300px]">
                <motion.div variants={staggerContainer} initial="hidden" animate="visible" className="flex flex-col">
                  {allNotifications.map((notification) => (
                    <motion.div key={notification.id} variants={staggerItem} className="border-b last:border-0">
                      <div className="flex items-start gap-3 p-4">
                        <div className="mt-1">{getNotificationIcon(notification.type)}</div>
                        <div className="flex-1 space-y-1">
                          <div className="flex items-center justify-between">
                            <p className="text-sm font-medium">{notification.title}</p>
                            {!notification.isRead && (
                              <Badge variant="secondary" className="ml-2">
                                New
                              </Badge>
                            )}
                          </div>
                          <p className="text-sm text-muted-foreground">{notification.message}</p>
                          <p className="text-xs text-muted-foreground">
                            {new Date(notification.createdAt).toLocaleString()}
                          </p>
                        </div>
                        {!notification.isRead && (
                          <Button
                            variant="ghost"
                            size="icon"
                            className="h-8 w-8"
                            onClick={() => handleMarkAsRead(notification.id)}
                          >
                            <Check className="h-4 w-4" />
                            <span className="sr-only">Mark as read</span>
                          </Button>
                        )}
                      </div>
                    </motion.div>
                  ))}
                </motion.div>
              </ScrollArea>
            ) : (
              <div className="flex flex-col items-center justify-center p-8">
                <Bell className="h-12 w-12 text-muted-foreground mb-4" />
                <h3 className="text-lg font-medium">No notifications</h3>
                <p className="text-sm text-muted-foreground text-center mt-2">You don't have any notifications yet.</p>
              </div>
            )}
          </TabsContent>
        </Tabs>
      </PopoverContent>
    </Popover>
  )
}
