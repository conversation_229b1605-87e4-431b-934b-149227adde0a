/**
 * Utility functions for formatting data in the admin portal
 */

/**
 * Format currency values
 */
export const formatCurrency = (
  amount: number,
  currency: string = 'INR',
  locale: string = 'en-IN'
): string => {
  return new Intl.NumberFormat(locale, {
    style: 'currency',
    currency,
    minimumFractionDigits: 0,
    maximumFractionDigits: 2,
  }).format(amount);
};

/**
 * Format numbers with appropriate suffixes (K, M, B)
 */
export const formatNumber = (num: number): string => {
  if (num >= 1000000000) {
    return (num / 1000000000).toFixed(1) + 'B';
  }
  if (num >= 1000000) {
    return (num / 1000000).toFixed(1) + 'M';
  }
  if (num >= 1000) {
    return (num / 1000).toFixed(1) + 'K';
  }
  return num.toString();
};

/**
 * Format percentage values
 */
export const formatPercentage = (value: number, decimals: number = 1): string => {
  return `${value.toFixed(decimals)}%`;
};

/**
 * Format date strings
 */
export const formatDate = (
  date: string | Date,
  options: Intl.DateTimeFormatOptions = {
    year: 'numeric',
    month: 'short',
    day: 'numeric',
  }
): string => {
  const dateObj = typeof date === 'string' ? new Date(date) : date;
  return dateObj.toLocaleDateString('en-US', options);
};

/**
 * Format date and time strings
 */
export const formatDateTime = (
  date: string | Date,
  options: Intl.DateTimeFormatOptions = {
    year: 'numeric',
    month: 'short',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit',
  }
): string => {
  const dateObj = typeof date === 'string' ? new Date(date) : date;
  return dateObj.toLocaleDateString('en-US', options);
};

/**
 * Format relative time (time ago)
 */
export const formatTimeAgo = (date: string | Date): string => {
  const dateObj = typeof date === 'string' ? new Date(date) : date;
  const now = new Date();
  const diffInSeconds = Math.floor((now.getTime() - dateObj.getTime()) / 1000);

  if (diffInSeconds < 60) {
    return 'just now';
  }

  const diffInMinutes = Math.floor(diffInSeconds / 60);
  if (diffInMinutes < 60) {
    return `${diffInMinutes} minute${diffInMinutes > 1 ? 's' : ''} ago`;
  }

  const diffInHours = Math.floor(diffInMinutes / 60);
  if (diffInHours < 24) {
    return `${diffInHours} hour${diffInHours > 1 ? 's' : ''} ago`;
  }

  const diffInDays = Math.floor(diffInHours / 24);
  if (diffInDays < 7) {
    return `${diffInDays} day${diffInDays > 1 ? 's' : ''} ago`;
  }

  const diffInWeeks = Math.floor(diffInDays / 7);
  if (diffInWeeks < 4) {
    return `${diffInWeeks} week${diffInWeeks > 1 ? 's' : ''} ago`;
  }

  const diffInMonths = Math.floor(diffInDays / 30);
  if (diffInMonths < 12) {
    return `${diffInMonths} month${diffInMonths > 1 ? 's' : ''} ago`;
  }

  const diffInYears = Math.floor(diffInDays / 365);
  return `${diffInYears} year${diffInYears > 1 ? 's' : ''} ago`;
};

/**
 * Format duration in hours and minutes
 */
export const formatDuration = (hours: number): string => {
  const wholeHours = Math.floor(hours);
  const minutes = Math.round((hours - wholeHours) * 60);

  if (wholeHours === 0) {
    return `${minutes} min`;
  }

  if (minutes === 0) {
    return `${wholeHours}h`;
  }

  return `${wholeHours}h ${minutes}m`;
};

/**
 * Format file size
 */
export const formatFileSize = (bytes: number): string => {
  if (bytes === 0) return '0 Bytes';

  const k = 1024;
  const sizes = ['Bytes', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));

  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
};

/**
 * Format phone numbers
 */
export const formatPhoneNumber = (phone: string): string => {
  // Remove all non-digit characters
  const cleaned = phone.replace(/\D/g, '');

  // Format Indian phone numbers
  if (cleaned.length === 10) {
    return `+91 ${cleaned.slice(0, 5)} ${cleaned.slice(5)}`;
  }

  if (cleaned.length === 12 && cleaned.startsWith('91')) {
    return `+91 ${cleaned.slice(2, 7)} ${cleaned.slice(7)}`;
  }

  // Return original if format is not recognized
  return phone;
};

/**
 * Truncate text with ellipsis
 */
export const truncateText = (text: string, maxLength: number): string => {
  if (text.length <= maxLength) return text;
  return text.slice(0, maxLength) + '...';
};

/**
 * Format trust score with color coding
 */
export const formatTrustScore = (score: number): { value: string; color: string } => {
  const value = score.toFixed(0);
  
  let color = 'text-gray-600';
  if (score >= 80) color = 'text-green-600';
  else if (score >= 60) color = 'text-yellow-600';
  else if (score >= 40) color = 'text-orange-600';
  else color = 'text-red-600';

  return { value, color };
};

/**
 * Format status badges
 */
export const formatStatusBadge = (status: string): { label: string; variant: string } => {
  const statusMap: Record<string, { label: string; variant: string }> = {
    // Job statuses
    'DRAFT': { label: 'Draft', variant: 'secondary' },
    'OPEN': { label: 'Open', variant: 'default' },
    'IN_PROGRESS': { label: 'In Progress', variant: 'warning' },
    'COMPLETED': { label: 'Completed', variant: 'success' },
    'CANCELLED': { label: 'Cancelled', variant: 'destructive' },
    
    // Application statuses
    'PENDING': { label: 'Pending', variant: 'warning' },
    'ACCEPTED': { label: 'Accepted', variant: 'success' },
    'REJECTED': { label: 'Rejected', variant: 'destructive' },
    
    // Verification statuses
    'VERIFIED': { label: 'Verified', variant: 'success' },
    'REJECTED': { label: 'Rejected', variant: 'destructive' },
    
    // Payment statuses
    'PROCESSING': { label: 'Processing', variant: 'warning' },
    'FAILED': { label: 'Failed', variant: 'destructive' },
    'REFUNDED': { label: 'Refunded', variant: 'secondary' },
  };

  return statusMap[status] || { label: status, variant: 'default' };
};

/**
 * Format address
 */
export const formatAddress = (address: {
  street?: string;
  city?: string;
  state?: string;
  country?: string;
  zipCode?: string;
}): string => {
  const parts = [
    address.street,
    address.city,
    address.state,
    address.country,
    address.zipCode,
  ].filter(Boolean);

  return parts.join(', ');
};

/**
 * Format rating stars
 */
export const formatRating = (rating: number): string => {
  const stars = '★'.repeat(Math.floor(rating)) + '☆'.repeat(5 - Math.floor(rating));
  return `${stars} (${rating.toFixed(1)})`;
};

/**
 * Format growth percentage with appropriate styling
 */
export const formatGrowth = (growth: number): { value: string; color: string; icon: string } => {
  const value = `${growth > 0 ? '+' : ''}${growth.toFixed(1)}%`;
  const color = growth > 0 ? 'text-green-600' : growth < 0 ? 'text-red-600' : 'text-gray-600';
  const icon = growth > 0 ? '↗' : growth < 0 ? '↘' : '→';

  return { value, color, icon };
};
