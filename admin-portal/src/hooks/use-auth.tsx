"use client";

import { createContext, useContext, ReactNode } from "react";
import { useRouter } from "next/navigation";
import { useAuthRTK } from "@/providers/AuthRTKProvider";
import type { User } from "@/types/api";

interface AuthContextType {
  user: User | null;
  login: (email: string, password: string) => Promise<void>;
  logout: () => void;
  updateUser: (userData: Partial<User>) => void;
  isLoading: boolean;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

/**
 * Legacy Auth Provider that wraps the RTK Auth Provider
 * This provides backward compatibility while migrating to RTK Query
 */
export function AuthProvider({ children }: { children: ReactNode }) {
  const router = useRouter();
  const { 
    user, 
    isLoading, 
    login: rtkLogin, 
    logout: rtkLogout,
    updateUser: rtkUpdateUser
  } = useAuthRTK();

  // Legacy login function that adapts to RTK Query
  const login = async (email: string, password: string) => {
    await rtkLogin({ email, password });
  };

  // Legacy logout function that adapts to RTK Query
  const logout = () => {
    rtkLogout();
    router.push('/login');
  };

  // Legacy updateUser function that adapts to RTK Query
  const updateUser = (userData: Partial<User>) => {
    rtkUpdateUser(userData);
  };

  return (
    <AuthContext.Provider value={{ user, login, logout, updateUser, isLoading }}>
      {children}
    </AuthContext.Provider>
  );
}

/**
 * Custom hook to use the auth context
 */
export function useAuth() {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error("useAuth must be used within an AuthProvider");
  }
  return context;
}
