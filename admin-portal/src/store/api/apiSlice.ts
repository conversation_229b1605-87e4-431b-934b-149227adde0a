"use client";

import { createApi, fetchBaseQuery } from "@reduxjs/toolkit/query/react";
import type { RootState } from "../index";
import STORAGE_KEYS from "../../constants/storage-keys";

/**
 * Tag types for cache invalidation
 */
export type ApiTagTypes =
  | "User"
  | "Users"
  | "Admin"
  | "Company"
  | "Companies"
  | "Job"
  | "Jobs"
  | "Application"
  | "Applications"
  | "Dispute"
  | "Disputes"
  | "Payout"
  | "Payouts"
  | "ActivityLog"
  | "Report"
  | "Reports"
  | "Notification"
  | "Notifications"
  | "Analytics";

/**
 * Base API slice using RTK Query
 * This handles all API requests with proper error handling and caching
 */
export const api = createApi({
  reducerPath: "api",
  baseQuery: fetchBaseQuery({
    baseUrl: process.env.NEXT_PUBLIC_API_URL || "http://localhost:3040/api/v1",
    prepareHeaders: (headers, { getState }) => {
      // Try fetching token from state, otherwise localStorage
      const state = getState() as RootState;
      const token =
        state.auth.token ||
        (typeof window !== "undefined"
          ? localStorage.getItem(STORAGE_KEYS.AUTH_TOKEN)
          : null);

      if (token) {
        headers.set("Authorization", `Bearer ${token}`);
      }

      headers.set("Content-Type", "application/json");
      return headers;
    },
  }),
  tagTypes: [
    "User",
    "Users",
    "Admin",
    "Company",
    "Companies",
    "Job",
    "Jobs",
    "Application",
    "Applications",
    "Dispute",
    "Disputes",
    "Payout",
    "Payouts",
    "ActivityLog",
    "Report",
    "Reports",
    "Notification",
    "Notifications",
    "Analytics",
  ],
  endpoints: () => ({}),
});

/**
 * Custom error handler for API requests
 */
export const handleApiError = (error: unknown): ApiError => {
  if (error && typeof error === "object" && "data" in error) {
    // RTK Query error format
    const errorData = error.data as any;

    // Check if the error is already in the expected format
    if (
      errorData &&
      typeof errorData === "object" &&
      "success" in errorData &&
      errorData.success === false
    ) {
      return errorData as ApiError;
    }

    // Convert to expected format
    return {
      success: false,
      statusCode: errorData.statusCode || 500,
      message: errorData.message || "An unexpected error occurred",
      error: errorData.error || "Internal Server Error",
      path: errorData.path,
      timestamp: errorData.timestamp || new Date().toISOString(),
    };
  }

  // Default error format
  return {
    success: false,
    statusCode: 500,
    message:
      error instanceof Error ? error.message : "An unexpected error occurred",
    error: "Internal Server Error",
    timestamp: new Date().toISOString(),
  };
};

/**
 * API Error interface
 */
export interface ApiError {
  success: false;
  statusCode: number;
  message: string;
  error?: string;
  path?: string;
  timestamp?: string;
}
