"use client";

import { api } from './apiSlice';
import type { 
  DashboardStats, 
  UserStats, 
  JobStats, 
  RevenueStats, 
  TrustScoreStats 
} from '../../types/api';

/**
 * Analytics API endpoints using RTK Query
 */
export const analyticsApi = api.injectEndpoints({
  endpoints: (builder) => ({
    getDashboardStats: builder.query<DashboardStats, void>({
      query: () => '/admin/analytics/dashboard',
      providesTags: ['Analytics'],
    }),

    getUserStats: builder.query<UserStats, { period?: 'day' | 'week' | 'month' | 'year' }>({
      query: (params) => ({
        url: '/admin/analytics/users',
        params,
      }),
      providesTags: ['Analytics'],
    }),

    getJobStats: builder.query<JobStats, { period?: 'day' | 'week' | 'month' | 'year' }>({
      query: (params) => ({
        url: '/admin/analytics/jobs',
        params,
      }),
      providesTags: ['Analytics'],
    }),

    getRevenueStats: builder.query<RevenueStats, { period?: 'day' | 'week' | 'month' | 'year' }>({
      query: (params) => ({
        url: '/admin/analytics/revenue',
        params,
      }),
      providesTags: ['Analytics'],
    }),

    getTrustScoreStats: builder.query<TrustScoreStats, void>({
      query: () => '/admin/analytics/trust-score',
      providesTags: ['Analytics'],
    }),
  }),
  overrideExisting: false,
});

export const {
  useGetDashboardStatsQuery,
  useGetUserStatsQuery,
  useGetJobStatsQuery,
  useGetRevenueStatsQuery,
  useGetTrustScoreStatsQuery,
} = analyticsApi;
