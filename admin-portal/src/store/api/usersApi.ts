"use client";

import { api } from './apiSlice';
import type { User, Document } from '../../types/api';

/**
 * Users API endpoints using RTK Query
 */
export const usersApi = api.injectEndpoints({
  endpoints: (builder) => ({
    getUsers: builder.query<
      { data: User[]; total: number; page: number; limit: number },
      { page?: number; limit?: number; search?: string; role?: string; isVerified?: boolean } | void
    >({
      query: (params) => ({
        url: '/admin/users',
        params,
      }),
      providesTags: (result) =>
        result
          ? [
              ...result.data.map(({ id }) => ({ type: 'User' as const, id })),
              { type: 'Users' as const, id: 'LIST' },
            ]
          : [{ type: 'Users' as const, id: 'LIST' }],
    }),

    getUserById: builder.query<User, string>({
      query: (id) => `/admin/users/${id}`,
      providesTags: (result, error, id) => [{ type: 'User', id }],
    }),

    updateUser: builder.mutation<User, { id: string; data: Partial<User> }>({
      query: ({ id, data }) => ({
        url: `/admin/users/${id}`,
        method: 'PATCH',
        body: data,
      }),
      invalidatesTags: (result, error, { id }) => [
        { type: 'User', id },
        { type: 'Users', id: 'LIST' },
      ],
    }),

    verifyUser: builder.mutation<User, string>({
      query: (id) => ({
        url: `/admin/users/${id}/verify`,
        method: 'PATCH',
      }),
      invalidatesTags: (result, error, id) => [
        { type: 'User', id },
        { type: 'Users', id: 'LIST' },
      ],
    }),

    banUser: builder.mutation<User, { id: string; reason: string }>({
      query: ({ id, reason }) => ({
        url: `/admin/users/${id}/ban`,
        method: 'PATCH',
        body: { reason },
      }),
      invalidatesTags: (result, error, { id }) => [
        { type: 'User', id },
        { type: 'Users', id: 'LIST' },
      ],
    }),

    unbanUser: builder.mutation<User, string>({
      query: (id) => ({
        url: `/admin/users/${id}/unban`,
        method: 'PATCH',
      }),
      invalidatesTags: (result, error, id) => [
        { type: 'User', id },
        { type: 'Users', id: 'LIST' },
      ],
    }),

    getUserDocuments: builder.query<Document[], string>({
      query: (userId) => `/admin/users/${userId}/documents`,
      providesTags: (result, error, userId) => [{ type: 'User', id: userId }],
    }),

    verifyDocument: builder.mutation<Document, string>({
      query: (documentId) => ({
        url: `/admin/documents/${documentId}/verify`,
        method: 'PATCH',
      }),
      invalidatesTags: ['User', 'Users'],
    }),

    rejectDocument: builder.mutation<Document, { id: string; reason: string }>({
      query: ({ id, reason }) => ({
        url: `/admin/documents/${id}/reject`,
        method: 'PATCH',
        body: { reason },
      }),
      invalidatesTags: ['User', 'Users'],
    }),

    updateTrustScore: builder.mutation<
      User,
      { userId: string; score: number; reason: string }
    >({
      query: ({ userId, score, reason }) => ({
        url: `/admin/users/${userId}/trust-score`,
        method: 'PATCH',
        body: { score, reason },
      }),
      invalidatesTags: (result, error, { userId }) => [
        { type: 'User', id: userId },
        { type: 'Users', id: 'LIST' },
      ],
    }),
  }),
  overrideExisting: false,
});

export const {
  useGetUsersQuery,
  useGetUserByIdQuery,
  useUpdateUserMutation,
  useVerifyUserMutation,
  useBanUserMutation,
  useUnbanUserMutation,
  useGetUserDocumentsQuery,
  useVerifyDocumentMutation,
  useRejectDocumentMutation,
  useUpdateTrustScoreMutation,
} = usersApi;
