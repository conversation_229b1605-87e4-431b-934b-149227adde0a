"use client";

import { api } from "./apiSlice";

/**
 * Payment analytics types
 */
export interface PaymentAnalytics {
  totalRevenue: number;
  totalTransactions: number;
  averageTransactionValue: number;
  successRate: number;
  gatewayBreakdown: {
    gateway: string;
    revenue: number;
    transactions: number;
    successRate: number;
    averageValue: number;
  }[];
  currencyBreakdown: {
    currency: string;
    revenue: number;
    transactions: number;
  }[];
  timeSeriesData: {
    date: string;
    revenue: number;
    transactions: number;
    stripeRevenue: number;
    razorpayRevenue: number;
  }[];
}

export interface PaymentGatewayConfig {
  gateway: string;
  enabled: boolean;
  supportedCurrencies: string[];
  supportedMethods: string[];
  defaultCurrency: string;
}

export interface PaymentTransaction {
  id: string;
  amount: number;
  currency: string;
  status: string;
  gateway: string;
  paymentMethod: string;
  transactionId?: string;
  orderId?: string;
  createdAt: string;
  updatedAt: string;
  metadata?: Record<string, unknown>;
}

export interface PaymentRefund {
  id: string;
  transactionId: string;
  amount: number;
  currency: string;
  status: string;
  gateway: string;
  reason?: string;
  createdAt: string;
  processedAt?: string;
}

export interface PaymentDispute {
  id: string;
  transactionId: string;
  amount: number;
  currency: string;
  status: string;
  gateway: string;
  reason?: string;
  createdAt: string;
  resolvedAt?: string;
}

/**
 * Payments API endpoints for admin portal using RTK Query
 */
export const paymentsApi = api.injectEndpoints({
  endpoints: (builder) => ({
    getPaymentAnalytics: builder.query<
      PaymentAnalytics,
      {
        startDate?: string;
        endDate?: string;
        gateway?: string;
        currency?: string;
      }
    >({
      query: (params) => ({
        url: "/admin/payments/analytics",
        params,
      }),
      providesTags: ["Analytics"],
    }),

    getGatewayConfigurations: builder.query<PaymentGatewayConfig[], void>({
      query: () => "/payments/gateways",
      providesTags: ["PaymentGateways"],
    }),

    updateGatewayConfiguration: builder.mutation<
      PaymentGatewayConfig,
      {
        gateway: string;
        enabled: boolean;
        supportedCurrencies?: string[];
        supportedMethods?: string[];
      }
    >({
      query: ({ gateway, ...data }) => ({
        url: `/admin/payments/gateways/${gateway}`,
        method: "PATCH",
        body: data,
      }),
      invalidatesTags: ["PaymentGateways"],
    }),

    getPaymentTransactions: builder.query<
      {
        transactions: PaymentTransaction[];
        total: number;
        page: number;
        limit: number;
      },
      {
        page?: number;
        limit?: number;
        status?: string;
        gateway?: string;
        currency?: string;
        startDate?: string;
        endDate?: string;
      }
    >({
      query: (params) => ({
        url: "/admin/payments/transactions",
        params,
      }),
      providesTags: (result) =>
        result
          ? [
              ...result.transactions.map(({ id }) => ({
                type: "PaymentTransaction" as const,
                id,
              })),
              { type: "PaymentTransactions" as const, id: "LIST" },
            ]
          : [{ type: "PaymentTransactions" as const, id: "LIST" }],
    }),

    getPaymentTransaction: builder.query<PaymentTransaction, string>({
      query: (id) => `/admin/payments/transactions/${id}`,
      providesTags: (result, error, id) => [{ type: "PaymentTransaction", id }],
    }),

    getPaymentRefunds: builder.query<
      {
        refunds: PaymentRefund[];
        total: number;
        page: number;
        limit: number;
      },
      {
        page?: number;
        limit?: number;
        status?: string;
        gateway?: string;
        startDate?: string;
        endDate?: string;
      }
    >({
      query: (params) => ({
        url: "/admin/payments/refunds",
        params,
      }),
      providesTags: ["PaymentRefunds"],
    }),

    processRefund: builder.mutation<
      PaymentRefund,
      {
        transactionId: string;
        amount?: number;
        reason?: string;
      }
    >({
      query: (data) => ({
        url: "/admin/payments/refunds",
        method: "POST",
        body: data,
      }),
      invalidatesTags: ["PaymentRefunds", "PaymentTransactions", "Analytics"],
    }),

    getPaymentDisputes: builder.query<
      {
        disputes: PaymentDispute[];
        total: number;
        page: number;
        limit: number;
      },
      {
        page?: number;
        limit?: number;
        status?: string;
        gateway?: string;
        startDate?: string;
        endDate?: string;
      }
    >({
      query: (params) => ({
        url: "/admin/payments/disputes",
        params,
      }),
      providesTags: ["PaymentDisputes"],
    }),

    resolveDispute: builder.mutation<
      PaymentDispute,
      {
        disputeId: string;
        resolution: "accept" | "reject";
        notes?: string;
      }
    >({
      query: ({ disputeId, ...data }) => ({
        url: `/admin/payments/disputes/${disputeId}/resolve`,
        method: "PATCH",
        body: data,
      }),
      invalidatesTags: ["PaymentDisputes", "Analytics"],
    }),

    getGatewayHealth: builder.query<
      {
        gateway: string;
        status: "healthy" | "degraded" | "down";
        responseTime: number;
        lastChecked: string;
        uptime: number;
      }[],
      void
    >({
      query: () => "/admin/payments/gateway-health",
      providesTags: ["GatewayHealth"],
    }),

    testGatewayConnection: builder.mutation<
      {
        gateway: string;
        success: boolean;
        responseTime: number;
        error?: string;
      },
      { gateway: string }
    >({
      query: ({ gateway }) => ({
        url: `/admin/payments/gateways/${gateway}/test`,
        method: "POST",
      }),
      invalidatesTags: ["GatewayHealth"],
    }),

    getPaymentMethodStats: builder.query<
      {
        method: string;
        transactions: number;
        revenue: number;
        successRate: number;
        averageValue: number;
      }[],
      {
        startDate?: string;
        endDate?: string;
        gateway?: string;
      }
    >({
      query: (params) => ({
        url: "/admin/payments/method-stats",
        params,
      }),
      providesTags: ["Analytics"],
    }),

    getCurrencyStats: builder.query<
      {
        currency: string;
        transactions: number;
        revenue: number;
        topGateway: string;
      }[],
      {
        startDate?: string;
        endDate?: string;
      }
    >({
      query: (params) => ({
        url: "/admin/payments/currency-stats",
        params,
      }),
      providesTags: ["Analytics"],
    }),
  }),
  overrideExisting: false,
});

export const {
  useGetPaymentAnalyticsQuery,
  useGetGatewayConfigurationsQuery,
  useUpdateGatewayConfigurationMutation,
  useGetPaymentTransactionsQuery,
  useGetPaymentTransactionQuery,
  useGetPaymentRefundsQuery,
  useProcessRefundMutation,
  useGetPaymentDisputesQuery,
  useResolveDisputeMutation,
  useGetGatewayHealthQuery,
  useTestGatewayConnectionMutation,
  useGetPaymentMethodStatsQuery,
  useGetCurrencyStatsQuery,
} = paymentsApi;
