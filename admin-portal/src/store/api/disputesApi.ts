"use client";

import { api } from './apiSlice';
import type { Dispute } from '../../types/api';

/**
 * Disputes API endpoints using RTK Query
 */
export const disputesApi = api.injectEndpoints({
  endpoints: (builder) => ({
    getDisputes: builder.query<
      { data: Dispute[]; total: number; page: number; limit: number },
      { page?: number; limit?: number; search?: string; status?: string; type?: string } | void
    >({
      query: (params) => ({
        url: '/admin/disputes',
        params,
      }),
      providesTags: (result) =>
        result
          ? [
              ...result.data.map(({ id }) => ({ type: 'Dispute' as const, id })),
              { type: 'Disputes' as const, id: 'LIST' },
            ]
          : [{ type: 'Disputes' as const, id: 'LIST' }],
    }),

    getDisputeById: builder.query<Dispute, string>({
      query: (id) => `/admin/disputes/${id}`,
      providesTags: (result, error, id) => [{ type: 'Dispute', id }],
    }),

    updateDisputeStatus: builder.mutation<
      Dispute,
      { id: string; status: string; resolution?: string }
    >({
      query: ({ id, status, resolution }) => ({
        url: `/admin/disputes/${id}/status`,
        method: 'PATCH',
        body: { status, resolution },
      }),
      invalidatesTags: (result, error, { id }) => [
        { type: 'Dispute', id },
        { type: 'Disputes', id: 'LIST' },
      ],
    }),

    assignDispute: builder.mutation<Dispute, { id: string; adminId: string }>({
      query: ({ id, adminId }) => ({
        url: `/admin/disputes/${id}/assign`,
        method: 'PATCH',
        body: { adminId },
      }),
      invalidatesTags: (result, error, { id }) => [
        { type: 'Dispute', id },
        { type: 'Disputes', id: 'LIST' },
      ],
    }),

    addDisputeComment: builder.mutation<
      Dispute,
      { id: string; comment: string; isInternal: boolean }
    >({
      query: ({ id, comment, isInternal }) => ({
        url: `/admin/disputes/${id}/comments`,
        method: 'POST',
        body: { comment, isInternal },
      }),
      invalidatesTags: (result, error, { id }) => [{ type: 'Dispute', id }],
    }),
  }),
  overrideExisting: false,
});

export const {
  useGetDisputesQuery,
  useGetDisputeByIdQuery,
  useUpdateDisputeStatusMutation,
  useAssignDisputeMutation,
  useAddDisputeCommentMutation,
} = disputesApi;
