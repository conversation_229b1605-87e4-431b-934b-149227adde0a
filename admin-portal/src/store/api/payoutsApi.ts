"use client";

import { api } from './apiSlice';
import type { Payout } from '../../types/api';

/**
 * Payouts API endpoints using RTK Query
 */
export const payoutsApi = api.injectEndpoints({
  endpoints: (builder) => ({
    getPayouts: builder.query<
      { data: Payout[]; total: number; page: number; limit: number },
      { page?: number; limit?: number; search?: string; status?: string } | void
    >({
      query: (params) => ({
        url: '/admin/payouts',
        params,
      }),
      providesTags: (result) =>
        result
          ? [
              ...result.data.map(({ id }) => ({ type: 'Payout' as const, id })),
              { type: 'Payouts' as const, id: 'LIST' },
            ]
          : [{ type: 'Payouts' as const, id: 'LIST' }],
    }),

    getPayoutById: builder.query<Payout, string>({
      query: (id) => `/admin/payouts/${id}`,
      providesTags: (result, error, id) => [{ type: 'Payout', id }],
    }),

    updatePayoutStatus: builder.mutation<
      Payout,
      { id: string; status: string; transactionId?: string }
    >({
      query: ({ id, status, transactionId }) => ({
        url: `/admin/payouts/${id}/status`,
        method: 'PATCH',
        body: { status, transactionId },
      }),
      invalidatesTags: (result, error, { id }) => [
        { type: 'Payout', id },
        { type: 'Payouts', id: 'LIST' },
      ],
    }),

    processPayouts: builder.mutation<
      { success: boolean; processed: number },
      { payoutIds: string[] }
    >({
      query: (data) => ({
        url: '/admin/payouts/process',
        method: 'POST',
        body: data,
      }),
      invalidatesTags: ['Payouts'],
    }),
  }),
  overrideExisting: false,
});

export const {
  useGetPayoutsQuery,
  useGetPayoutByIdQuery,
  useUpdatePayoutStatusMutation,
  useProcessPayoutsMutation,
} = payoutsApi;
