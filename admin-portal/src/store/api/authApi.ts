"use client";

import { api } from './apiSlice';
import type { AuthResponse, LoginRequest, User } from '../../types/api';

/**
 * Auth API endpoints using RTK Query
 */
export const authApi = api.injectEndpoints({
  endpoints: (builder) => ({
    login: builder.mutation<AuthResponse, LoginRequest>({
      query: (credentials) => ({
        url: '/auth/admin/login',
        method: 'POST',
        body: credentials,
      }),
      invalidatesTags: ['Admin'],
    }),

    getProfile: builder.query<User, void>({
      query: () => '/auth/admin/profile',
      providesTags: ['Admin'],
    }),

    refreshToken: builder.mutation<
      { token: string; refreshToken: string },
      { refreshToken: string }
    >({
      query: (data) => ({
        url: '/auth/admin/refresh',
        method: 'POST',
        body: data,
      }),
    }),

    logout: builder.mutation<{ success: boolean }, void>({
      query: () => ({
        url: '/auth/admin/logout',
        method: 'POST',
      }),
    }),

    changePassword: builder.mutation<
      { success: boolean; message: string },
      { currentPassword: string; newPassword: string }
    >({
      query: (data) => ({
        url: '/auth/admin/change-password',
        method: 'POST',
        body: data,
      }),
    }),
  }),
  overrideExisting: false,
});

export const {
  useLoginMutation,
  useGetProfileQuery,
  useRefreshTokenMutation,
  useLogoutMutation,
  useChangePasswordMutation,
} = authApi;
