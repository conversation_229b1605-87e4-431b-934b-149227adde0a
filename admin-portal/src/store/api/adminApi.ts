import { api } from './apiSlice';

export interface KycDashboardStats {
  totalPendingDocuments: number;
  totalVerifiedDocuments: number;
  totalRejectedDocuments: number;
  pendingWorkerKyc: number;
  pendingCompanyKyc: number;
  verifiedWorkers: number;
  verifiedCompanies: number;
  recentSubmissions: number;
  averageVerificationTime: number;
}

export interface PendingKycItem {
  id: string;
  userId: string;
  userType: 'worker' | 'company';
  userName: string;
  userEmail: string;
  documentType: string;
  documentUrl: string;
  documentNumber?: string;
  submittedAt: string;
  priority: 'high' | 'medium' | 'low';
}

export interface PendingKycResponse {
  items: PendingKycItem[];
  total: number;
  page: number;
  limit: number;
}

export interface KycVerificationHistory {
  id: string;
  documentId: string;
  userId: string;
  userName: string;
  documentType: string;
  verificationStatus: string;
  verifiedBy: string;
  verifiedByName: string;
  verifiedAt: string;
  rejectionReason?: string;
}

export interface VerificationHistoryResponse {
  items: KycVerificationHistory[];
  total: number;
  page: number;
  limit: number;
}

export interface DocumentDetails {
  id: string;
  userId: string;
  companyId?: string;
  documentType: string;
  documentUrl: string;
  documentNumber?: string;
  verificationStatus: string;
  submittedAt: string;
  verifiedAt?: string;
  verifiedBy?: {
    id: string;
    fullName: string;
  };
  rejectionReason?: string;
  user?: {
    id: string;
    fullName: string;
    email: string;
    role: string;
  };
  company?: {
    id: string;
    name: string;
    user: {
      email: string;
    };
  };
}

export interface BulkVerificationRequest {
  documentIds: string[];
  status: 'VERIFIED' | 'REJECTED';
  rejectionReason?: string;
}

export interface BulkVerificationResponse {
  verified: number;
  failed: string[];
}

export interface UserKycStatus {
  user: {
    id: string;
    fullName: string;
    email: string;
    role: string;
    isKycVerified: boolean;
  };
  documents: DocumentDetails[];
  kycComplete: boolean;
  missingDocuments: string[];
}

export interface KycRemindersResponse {
  sent: number;
  failed: number;
}

export const adminApi = api.injectEndpoints({
  endpoints: (builder) => ({
    // KYC Dashboard Stats
    getKycDashboardStats: builder.query<KycDashboardStats, void>({
      query: () => '/admin/kyc/dashboard',
      providesTags: ['KycStats'],
    }),

    // Get Pending KYC Documents
    getPendingKycDocuments: builder.query<
      PendingKycResponse,
      {
        page?: number;
        limit?: number;
        priority?: 'high' | 'medium' | 'low';
        documentType?: string;
      }
    >({
      query: ({ page = 1, limit = 20, priority, documentType }) => ({
        url: '/admin/kyc/pending',
        params: {
          page,
          limit,
          ...(priority && { priority }),
          ...(documentType && { documentType }),
        },
      }),
      providesTags: ['PendingKyc'],
    }),

    // Get Verification History
    getVerificationHistory: builder.query<
      VerificationHistoryResponse,
      {
        page?: number;
        limit?: number;
        status?: string;
        startDate?: string;
        endDate?: string;
      }
    >({
      query: ({ page = 1, limit = 20, status, startDate, endDate }) => ({
        url: '/admin/kyc/history',
        params: {
          page,
          limit,
          ...(status && { status }),
          ...(startDate && { startDate }),
          ...(endDate && { endDate }),
        },
      }),
      providesTags: ['VerificationHistory'],
    }),

    // Get Document for Review
    getDocumentForReview: builder.query<DocumentDetails, string>({
      query: (documentId) => `/admin/kyc/document/${documentId}`,
      providesTags: (result, error, documentId) => [
        { type: 'Document', id: documentId },
      ],
    }),

    // Verify Document
    verifyDocument: builder.mutation<
      DocumentDetails,
      {
        documentId: string;
        status: 'VERIFIED' | 'REJECTED';
        rejectionReason?: string;
      }
    >({
      query: ({ documentId, status, rejectionReason }) => ({
        url: `/admin/kyc/document/${documentId}/verify`,
        method: 'PATCH',
        body: { status, rejectionReason },
      }),
      invalidatesTags: (result, error, { documentId }) => [
        'KycStats',
        'PendingKyc',
        'VerificationHistory',
        { type: 'Document', id: documentId },
      ],
    }),

    // Bulk Verify Documents
    bulkVerifyDocuments: builder.mutation<
      BulkVerificationResponse,
      BulkVerificationRequest
    >({
      query: (data) => ({
        url: '/admin/kyc/bulk-verify',
        method: 'POST',
        body: data,
      }),
      invalidatesTags: [
        'KycStats',
        'PendingKyc',
        'VerificationHistory',
      ],
    }),

    // Get User KYC Status
    getUserKycStatus: builder.query<UserKycStatus, string>({
      query: (userId) => `/admin/kyc/user/${userId}/status`,
      providesTags: (result, error, userId) => [
        { type: 'UserKyc', id: userId },
      ],
    }),

    // Send KYC Reminders
    sendKycReminders: builder.mutation<KycRemindersResponse, void>({
      query: () => ({
        url: '/admin/kyc/send-reminders',
        method: 'POST',
      }),
    }),

    // Analytics endpoints
    getAdminDashboardStats: builder.query<any, void>({
      query: () => '/analytics/admin/dashboard',
      providesTags: ['AdminStats'],
    }),

    getCompanyPerformanceAnalytics: builder.query<
      any,
      { companyId: string; timeRange: string }
    >({
      query: ({ companyId, timeRange }) => ({
        url: `/analytics/company/${companyId}/performance`,
        params: { timeRange },
      }),
      providesTags: (result, error, { companyId }) => [
        { type: 'CompanyAnalytics', id: companyId },
      ],
    }),

    // User Management
    getAllUsers: builder.query<
      {
        users: any[];
        total: number;
        page: number;
        limit: number;
      },
      {
        page?: number;
        limit?: number;
        role?: string;
        search?: string;
        isActive?: boolean;
        isKycVerified?: boolean;
      }
    >({
      query: ({ page = 1, limit = 20, role, search, isActive, isKycVerified }) => ({
        url: '/admin/users',
        params: {
          page,
          limit,
          ...(role && { role }),
          ...(search && { search }),
          ...(isActive !== undefined && { isActive }),
          ...(isKycVerified !== undefined && { isKycVerified }),
        },
      }),
      providesTags: ['AdminUsers'],
    }),

    // Update User Status
    updateUserStatus: builder.mutation<
      any,
      {
        userId: string;
        isActive?: boolean;
        isBanned?: boolean;
        trustScore?: number;
      }
    >({
      query: ({ userId, ...data }) => ({
        url: `/admin/users/${userId}/status`,
        method: 'PATCH',
        body: data,
      }),
      invalidatesTags: ['AdminUsers'],
    }),

    // Job Management
    getAllJobs: builder.query<
      {
        jobs: any[];
        total: number;
        page: number;
        limit: number;
      },
      {
        page?: number;
        limit?: number;
        status?: string;
        search?: string;
        companyId?: string;
      }
    >({
      query: ({ page = 1, limit = 20, status, search, companyId }) => ({
        url: '/admin/jobs',
        params: {
          page,
          limit,
          ...(status && { status }),
          ...(search && { search }),
          ...(companyId && { companyId }),
        },
      }),
      providesTags: ['AdminJobs'],
    }),

    // Platform Settings
    getPlatformSettings: builder.query<any, void>({
      query: () => '/admin/settings',
      providesTags: ['PlatformSettings'],
    }),

    updatePlatformSettings: builder.mutation<any, any>({
      query: (settings) => ({
        url: '/admin/settings',
        method: 'PATCH',
        body: settings,
      }),
      invalidatesTags: ['PlatformSettings'],
    }),
  }),
});

export const {
  useGetKycDashboardStatsQuery,
  useGetPendingKycDocumentsQuery,
  useGetVerificationHistoryQuery,
  useGetDocumentForReviewQuery,
  useVerifyDocumentMutation,
  useBulkVerifyDocumentsMutation,
  useGetUserKycStatusQuery,
  useSendKycRemindersMutation,
  useGetAdminDashboardStatsQuery,
  useGetCompanyPerformanceAnalyticsQuery,
  useGetAllUsersQuery,
  useUpdateUserStatusMutation,
  useGetAllJobsQuery,
  useGetPlatformSettingsQuery,
  useUpdatePlatformSettingsMutation,
} = adminApi;
