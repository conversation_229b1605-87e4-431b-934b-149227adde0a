"use client";

import { api } from "./apiSlice";
import type {
  Job,
  Application,
  PaginatedResponse,
  ApiResponse,
} from "../../types/api";

/**
 * Jobs API endpoints using RTK Query
 */
export const jobsApi = api.injectEndpoints({
  endpoints: (builder) => ({
    getJobs: builder.query<
      PaginatedResponse<Job>,
      { page?: number; limit?: number; search?: string; status?: string } | void
    >({
      query: (params) => ({
        url: "/admin/jobs",
        params,
      }),
      providesTags: (result) =>
        result && result.success && result.data
          ? [
              ...result.data.map(({ id }) => ({ type: "Job" as const, id })),
              { type: "Jobs" as const, id: "LIST" },
            ]
          : [{ type: "Jobs" as const, id: "LIST" }],
    }),

    getJobById: builder.query<ApiResponse<Job>, string>({
      query: (id) => `/admin/jobs/${id}`,
      providesTags: (result, error, id) => [{ type: "Job", id }],
      transformResponse: (response: ApiResponse<Job>) => response,
    }),

    updateJob: builder.mutation<
      ApiResponse<Job>,
      { id: string; data: Partial<Job> }
    >({
      query: ({ id, data }) => ({
        url: `/admin/jobs/${id}`,
        method: "PATCH",
        body: data,
      }),
      invalidatesTags: (result, error, { id }) => [
        { type: "Job", id },
        { type: "Jobs", id: "LIST" },
      ],
    }),

    approveJob: builder.mutation<ApiResponse<Job>, string>({
      query: (id) => ({
        url: `/admin/jobs/${id}/approve`,
        method: "PATCH",
      }),
      invalidatesTags: (result, error, id) => [
        { type: "Job", id },
        { type: "Jobs", id: "LIST" },
      ],
    }),

    rejectJob: builder.mutation<
      ApiResponse<Job>,
      { id: string; reason: string }
    >({
      query: ({ id, reason }) => ({
        url: `/admin/jobs/${id}/reject`,
        method: "PATCH",
        body: { reason },
      }),
      invalidatesTags: (result, error, { id }) => [
        { type: "Job", id },
        { type: "Jobs", id: "LIST" },
      ],
    }),

    getJobApplications: builder.query<ApiResponse<Application[]>, string>({
      query: (jobId) => `/admin/jobs/${jobId}/applications`,
      providesTags: (result) =>
        result && result.success && result.data
          ? [
              ...result.data.map(({ id }) => ({
                type: "Application" as const,
                id,
              })),
              { type: "Applications" as const, id: "LIST" },
            ]
          : [{ type: "Applications" as const, id: "LIST" }],
    }),

    getApplicationById: builder.query<ApiResponse<Application>, string>({
      query: (id) => `/admin/applications/${id}`,
      providesTags: (result, error, id) => [{ type: "Application", id }],
    }),

    updateApplicationStatus: builder.mutation<
      ApiResponse<Application>,
      { id: string; status: string; reason?: string }
    >({
      query: ({ id, status, reason }) => ({
        url: `/admin/applications/${id}/status`,
        method: "PATCH",
        body: { status, reason },
      }),
      invalidatesTags: (result, error, { id }) => [
        { type: "Application", id },
        { type: "Applications", id: "LIST" },
        { type: "Job", id: result?.data?.jobId || "" },
        { type: "Jobs", id: "LIST" },
      ],
    }),
  }),
  overrideExisting: false,
});

export const {
  useGetJobsQuery,
  useGetJobByIdQuery,
  useUpdateJobMutation,
  useApproveJobMutation,
  useRejectJobMutation,
  useGetJobApplicationsQuery,
  useGetApplicationByIdQuery,
  useUpdateApplicationStatusMutation,
} = jobsApi;
