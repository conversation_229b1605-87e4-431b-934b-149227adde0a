"use client";

import React, { createContext, useContext, useCallback } from 'react';
import { useRouter } from 'next/navigation';
import { useAppSelector, useAppDispatch } from '../store';
import { setCredentials, updateUser as updateUserAction, logout as logoutAction } from '../store/slices/authSlice';
import { 
  useLoginMutation, 
  useLogoutMutation,
  useGetProfileQuery
} from '../store/api/authApi';
import type { LoginRequest, User } from '../types/api';

// Define the auth context type
interface AuthContextType {
  user: User | null;
  isAuthenticated: boolean;
  isLoading: boolean;
  login: (credentials: LoginRequest) => Promise<void>;
  logout: () => Promise<void>;
  updateUser: (userData: Partial<User>) => void;
}

// Create the auth context
const AuthContext = createContext<AuthContextType | undefined>(undefined);

// Auth provider component
export const AuthRTKProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const router = useRouter();
  const dispatch = useAppDispatch();
  const { user, isAuthenticated, isLoading } = useAppSelector((state) => state.auth);

  // RTK Query hooks
  const [loginMutation] = useLoginMutation();
  const [logoutMutation] = useLogoutMutation();
  
  // Skip profile query if not authenticated
  const { refetch: refetchProfile } = useGetProfileQuery(undefined, {
    skip: !isAuthenticated,
  });

  // Login function
  const login = useCallback(
    async (credentials: LoginRequest) => {
      try {
        const result = await loginMutation(credentials).unwrap();
        dispatch(
          setCredentials({
            user: result.user,
            token: result.token,
            refreshToken: result.refreshToken,
          })
        );
        router.push('/dashboard');
      } catch (error) {
        console.error('Login failed:', error);
        throw error;
      }
    },
    [loginMutation, dispatch, router]
  );

  // Logout function
  const logout = useCallback(async () => {
    try {
      if (isAuthenticated) {
        await logoutMutation().unwrap();
      }
    } catch (error) {
      console.error('Logout API call failed:', error);
    } finally {
      dispatch(logoutAction());
      router.push('/login');
    }
  }, [logoutMutation, dispatch, router, isAuthenticated]);

  // Update user function
  const updateUser = useCallback(
    (userData: Partial<User>) => {
      dispatch(updateUserAction(userData));
      
      // Refetch profile to ensure data consistency
      if (isAuthenticated) {
        refetchProfile();
      }
    },
    [dispatch, isAuthenticated, refetchProfile]
  );

  // Create the context value
  const contextValue: AuthContextType = {
    user,
    isAuthenticated,
    isLoading,
    login,
    logout,
    updateUser,
  };

  return <AuthContext.Provider value={contextValue}>{children}</AuthContext.Provider>;
};

// Custom hook to use the auth context
export const useAuthRTK = () => {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuthRTK must be used within an AuthRTKProvider');
  }
  return context;
};

export default AuthRTKProvider;
