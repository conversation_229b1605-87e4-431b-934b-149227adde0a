import axios from "axios";
import type {
  User,
  AuthResponse,
  LoginRequest,
  Job,
  Application,
  Document,
  Payout,
  Dispute,
  ActivityLog,
  Setting,
  Report,
  DashboardStats,
  UserStats,
  JobStats,
  RevenueStats,
  TrustScoreStats,
} from "../types/api";

// Get the API URL from environment variables or use a default
const API_URL = process.env.NEXT_PUBLIC_API_URL || "http://localhost:3000/api";

// Create axios instance
const api = axios.create({
  baseURL: API_URL,
  timeout: 10000,
  headers: {
    "Content-Type": "application/json",
  },
});

// Add request interceptor to add auth token from localStorage (client-side only)
if (typeof window !== "undefined") {
  api.interceptors.request.use(
    (config) => {
      const token = localStorage.getItem("token");
      if (token) {
        config.headers.Authorization = `Bearer ${token}`;
      }
      return config;
    },
    (error) => Promise.reject(error)
  );
}

// Add response interceptor to handle errors
api.interceptors.response.use(
  (response) => response,
  async (error) => {
    const originalRequest = error.config;

    // If error is 401 and not already retrying
    if (
      error.response?.status === 401 &&
      !originalRequest._retry &&
      typeof window !== "undefined"
    ) {
      originalRequest._retry = true;

      try {
        // Try to refresh token (if you have refresh token functionality)
        // const refreshToken = localStorage.getItem('refreshToken')
        // const response = await api.post('/auth/refresh', { refreshToken })
        // const { token } = response.data
        // localStorage.setItem('token', token)
        // originalRequest.headers.Authorization = `Bearer ${token}`
        // return api(originalRequest)

        // If no refresh token functionality, just logout
        localStorage.removeItem("token");
        localStorage.removeItem("user");
        window.location.href = "/login";
      } catch (refreshError) {
        console.error("Error refreshing token:", refreshError);
        localStorage.removeItem("token");
        localStorage.removeItem("user");
        window.location.href = "/login";
      }
    }

    return Promise.reject(error);
  }
);

// Auth API
export const authAPI = {
  login: (data: LoginRequest) => api.post<AuthResponse>("/auth/login", data),
};

// Users API
export const usersAPI = {
  getAllUsers: (params?: {
    role?: string;
    search?: string;
    isVerified?: boolean;
    isKycVerified?: boolean;
    isActive?: boolean;
    isBanned?: boolean;
    page?: number;
    limit?: number;
    sortBy?: string;
    sortOrder?: "asc" | "desc";
  }) =>
    api.get<{ data: User[]; total: number; page: number; limit: number }>(
      "/users",
      { params }
    ),

  getUserById: (id: string) => api.get<{ data: User }>(`/users/${id}`),

  updateUser: (id: string, data: Partial<User>) =>
    api.patch<{ data: User }>(`/users/${id}`, data),

  verifyKyc: (
    id: string,
    status: "approved" | "rejected",
    rejectionReason?: string
  ) =>
    api.patch<{ data: User }>(`/users/${id}/verify-kyc`, {
      status,
      rejectionReason,
    }),

  updateTrustScore: (id: string, score: number, reason: string) =>
    api.patch<{ data: User }>(`/users/${id}/trust-score`, { score, reason }),

  banUser: (id: string, reason: string) =>
    api.patch<{ data: User }>(`/users/${id}/ban`, { reason }),

  unbanUser: (id: string) => api.patch<{ data: User }>(`/users/${id}/unban`),

  getUserDocuments: (userId: string) =>
    api.get<{ data: Document[] }>(`/documents/user/${userId}`),

  verifyDocument: (id: string, isApproved: boolean, rejectionReason?: string) =>
    api.post<{ data: Document }>(`/documents/verify/${id}`, {
      isApproved,
      rejectionReason,
    }),
};

// Jobs API
export const jobsAPI = {
  getAllJobs: (params?: {
    status?: string;
    companyId?: string;
    search?: string;
    startDate?: string;
    endDate?: string;
    isEmergencyJob?: boolean;
    page?: number;
    limit?: number;
    sortBy?: string;
    sortOrder?: "asc" | "desc";
  }) =>
    api.get<{ data: Job[]; total: number; page: number; limit: number }>(
      "/jobs",
      { params }
    ),

  getJobById: (id: string) => api.get<{ data: Job }>(`/jobs/${id}`),

  updateJobStatus: (id: string, status: JobStatus) =>
    api.patch<{ data: Job }>(`/jobs/${id}/status`, { status }),
};

// Applications API
export const applicationsAPI = {
  getAllApplications: (params?: {
    jobId?: string;
    workerId?: string;
    status?: ApplicationStatus;
    page?: number;
    limit?: number;
    sortBy?: string;
    sortOrder?: "asc" | "desc";
  }) =>
    api.get<{
      data: Application[];
      total: number;
      page: number;
      limit: number;
    }>("/applications", { params }),

  getApplicationById: (id: string) =>
    api.get<{ data: Application }>(`/applications/${id}`),
};

// Payouts API
export const payoutsAPI = {
  getAllPayouts: (params?: {
    jobId?: string;
    workerId?: string;
    status?: string;
    page?: number;
    limit?: number;
    sortBy?: string;
    sortOrder?: "asc" | "desc";
  }) =>
    api.get<{ data: Payout[]; total: number; page: number; limit: number }>(
      "/payouts",
      { params }
    ),

  getPayoutById: (id: string) => api.get<{ data: Payout }>(`/payouts/${id}`),

  createPayout: (data: { jobId: string; workerId: string; amount: number }) =>
    api.post<{ data: Payout }>("/payouts", data),

  updatePayout: (
    id: string,
    data: {
      status?: string;
      amount?: number;
    }
  ) => api.patch<{ data: Payout }>(`/payouts/${id}`, data),

  processPayout: (id: string, paymentMethod: string) =>
    api.patch<{ data: Payout }>(`/payouts/${id}/process`, { paymentMethod }),
};

// Payments API
export const paymentsAPI = {
  processStripePayment: (data: {
    jobId: string;
    amount: number;
    currency: string;
    description: string;
  }) =>
    api.post<{
      success: boolean;
      clientSecret: string;
      paymentIntentId: string;
    }>("/payments/stripe", data),

  processUpiPayment: (data: {
    jobId: string;
    amount: number;
    currency: string;
    description: string;
    upiId: string;
  }) =>
    api.post<{
      success: boolean;
      paymentId: string;
      upiLink: string;
    }>("/payments/upi", data),

  processRazorpayPayment: (data: {
    jobId: string;
    amount: number;
    currency: string;
    description: string;
    customerEmail?: string;
    customerPhone?: string;
  }) =>
    api.post<{
      success: boolean;
      transactionId: string;
      gatewayResponse: any;
    }>("/payments/razorpay", data),

  createRazorpayOrder: (data: {
    jobId: string;
    amount: number;
    currency: string;
    description: string;
    customerEmail?: string;
    customerPhone?: string;
  }) =>
    api.post<{
      success: boolean;
      transactionId: string;
      gatewayResponse: any;
    }>("/payments/razorpay/order", data),

  verifyRazorpaySignature: (data: {
    orderId: string;
    paymentId: string;
    signature: string;
  }) =>
    api.post<{
      verified: boolean;
    }>("/payments/razorpay/verify", data),

  getPaymentMethods: () =>
    api.get<{
      data: Array<{
        id: string;
        name: string;
        description: string;
        enabled: boolean;
      }>;
    }>("/payments/methods"),
};

// Disputes API
export const disputesAPI = {
  getAllDisputes: (params?: {
    status?: "open" | "in_progress" | "resolved" | "cancelled";
    type?: "payment" | "job" | "application" | "other";
    raisedById?: string;
    againstId?: string;
    jobId?: string;
    applicationId?: string;
    page?: number;
    limit?: number;
    sortBy?: string;
    sortOrder?: "asc" | "desc";
  }) =>
    api.get<{ data: Dispute[]; total: number; page: number; limit: number }>(
      "/disputes",
      { params }
    ),

  getDisputeById: (id: string) => api.get<{ data: Dispute }>(`/disputes/${id}`),

  updateDispute: (
    id: string,
    data: {
      status?: "open" | "in_progress" | "resolved" | "cancelled";
      resolution?: string;
    }
  ) => api.patch<{ data: Dispute }>(`/disputes/${id}`, data),
};

// Activity Logs API
export const activityLogsAPI = {
  getAllLogs: (params?: {
    userId?: string;
    action?: string;
    startDate?: string;
    endDate?: string;
    page?: number;
    limit?: number;
    sortBy?: string;
    sortOrder?: "asc" | "desc";
  }) =>
    api.get<{
      data: ActivityLog[];
      total: number;
      page: number;
      limit: number;
    }>("/activity-logs", { params }),
};

// Settings API
export const settingsAPI = {
  getAllSettings: () => api.get<{ data: Setting[] }>("/settings"),

  updateSetting: (key: string, value: string | number | boolean | object) =>
    api.patch<{ data: Setting }>(`/settings/${key}`, { value }),
};

// Reports API
export const reportsAPI = {
  getAllReports: (params?: {
    type?: "user" | "job" | "application" | "other";
    status?: "pending" | "investigating" | "resolved" | "dismissed";
    reportedById?: string;
    reportedId?: string;
    page?: number;
    limit?: number;
    sortBy?: string;
    sortOrder?: "asc" | "desc";
  }) =>
    api.get<{ data: Report[]; total: number; page: number; limit: number }>(
      "/reports",
      { params }
    ),

  getReportById: (id: string) => api.get<{ data: Report }>(`/reports/${id}`),

  createReport: (data: {
    type: "user" | "job" | "application" | "other";
    title: string;
    description: string;
    reportedId: string;
  }) => api.post<{ data: Report }>("/reports", data),
};

// Analytics API
export const analyticsAPI = {
  getDashboardStats: () =>
    api.get<{ data: DashboardStats }>("/analytics/dashboard"),

  getUserStats: (period?: "day" | "week" | "month" | "year") =>
    api.get<{ data: UserStats }>("/analytics/users", { params: { period } }),

  getJobStats: (period?: "day" | "week" | "month" | "year") =>
    api.get<{ data: JobStats }>("/analytics/jobs", { params: { period } }),

  getRevenueStats: (period?: "day" | "week" | "month" | "year") =>
    api.get<{ data: RevenueStats }>("/analytics/revenue", {
      params: { period },
    }),

  getTrustScoreStats: () =>
    api.get<{ data: TrustScoreStats }>("/analytics/trust-scores"),
};

export default api;
