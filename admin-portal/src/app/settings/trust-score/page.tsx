"use client"

import { useState } from "react"
import { useQ<PERSON>y, useMutation, useQueryClient } from "@tanstack/react-query"
import { motion } from "framer-motion"
import { Shield, Info, Save, RefreshCw } from "lucide-react"
import { Button } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Separator } from "@/components/ui/separator"
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip"
import { useToast } from "@/components/ui/use-toast"
import { api } from "@/lib/api"
import { pageTransition, fadeIn, slideIn } from "@/utils/animations"

export default function TrustScoreSettingsPage() {
  const queryClient = useQueryClient()
  const { toast } = useToast()
  const [isEditing, setIsEditing] = useState(false)
  const [settings, setSettings] = useState<Record<string, string>>({})

  const { data, isLoading } = useQuery({
    queryKey: ["trust-score-settings"],
    queryFn: async () => {
      const response = await api.get("/admin/settings")
      return response.data.filter((setting) => 
        setting.key.startsWith("TRUST_SCORE_") || 
        setting.key === "PLATFORM_COMMISSION_RATE" || 
        setting.key === "PLATFORM_COMMISSION_FIXED"
      )
    },
    onSuccess: (data) => {
      const settingsObj = data.reduce((acc, setting) => {
        acc[setting.key] = setting.value
        return acc
      }, {})
      setSettings(settingsObj)
    },
  })

  const updateSettingMutation = useMutation({
    mutationFn: async ({ key, value }: { key: string; value: string }) => {
      return api.patch(`/admin/settings/${key}`, { value })
    },
    onSuccess: () => {
      queryClient.invalidateQueries(["trust-score-settings"])
      toast({
        title: "Settings updated",
        description: "Trust score settings have been updated successfully.",
      })
      setIsEditing(false)
    },
    onError: () => {
      toast({
        variant: "destructive",
        title: "Error",
        description: "Failed to update settings. Please try again.",
      })
    },
  })

  const handleInputChange = (key: string, value: string) => {
    setSettings((prev) => ({
      ...prev,
      [key]: value,
    }))
  }

  const handleSaveSettings = () => {
    if (!data) return

    const promises = data.map((setting) => {
      if (settings[setting.key] !== setting.value) {
        return updateSettingMutation.mutateAsync({
          key: setting.key,
          value: settings[setting.key],
        })
      }
      return Promise.resolve()
    })

    Promise.all(promises)
  }

  const handleResetDefaults = () => {
    const defaultSettings = {
      TRUST_SCORE_INITIAL: "50",
      TRUST_SCORE_EMERGENCY_THRESHOLD: "40",
      TRUST_SCORE_EMERGENCY_JOB_COMPLETION: "10",
      TRUST_SCORE_STANDARD_JOB_COMPLETION: "5",
      TRUST_SCORE_LAST_MINUTE_CANCELLATION: "-15",
      TRUST_SCORE_NO_SHOW: "-25",
      TRUST_SCORE_LOW_RATING: "-10",
      PLATFORM_COMMISSION_RATE: "0.10",
      PLATFORM_COMMISSION_FIXED: "200",
    }

    setSettings(defaultSettings)
  }

  if (isLoading) {
    return (
      <div className="flex h-full items-center justify-center">
        <div className="h-8 w-8 animate-spin rounded-full border-4 border-primary border-t-transparent"></div>
      </div>
    )
  }

  return (
    <motion.div variants={pageTransition} initial="hidden" animate="visible" exit="exit" className="space-y-4 p-8 pt-6">
      <div className="flex items-center justify-between">
        <h2 className="text-3xl font-bold tracking-tight">Trust Score Settings</h2>
        <div className="flex space-x-2">
          {isEditing ? (
            <>
              <Button variant="outline" onClick={() => setIsEditing(false)}>
                Cancel
              </Button>
              <Button onClick={handleSaveSettings} disabled={updateSettingMutation.isLoading}>
                {updateSettingMutation.isLoading ? (
                  <>
                    <RefreshCw className="mr-2 h-4 w-4 animate-spin" />
                    Saving...
                  </>
                ) : (
                  <>
                    <Save className="mr-2 h-4 w-4" />
                    Save Changes
                  </>
                )}
              </Button>
            </>
          ) : (
            <Button onClick={() => setIsEditing(true)}>
              Edit Settings
            </Button>
          )}
        </div>
      </div>

      <Tabs defaultValue="trust-score" className="space-y-4">
        <TabsList>
          <TabsTrigger value="trust-score">Trust Score</TabsTrigger>
          <TabsTrigger value="commission">Commission</TabsTrigger>
        </TabsList>

        <TabsContent value="trust-score" className="space-y-4">
          <motion.div variants={fadeIn}>
            <Card>
              <CardHeader>
                <CardTitle>Trust Score Rules</CardTitle>
                <CardDescription>
                  Configure the rules for trust score calculation. These settings affect how trust scores are calculated for workers.
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-6">
                <div className="space-y-2">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-2">
                      <Label htmlFor="initial-score">Initial Trust Score</Label>
                      <TooltipProvider>
                        <Tooltip>
                          <TooltipTrigger asChild>
                            <Info className="h-4 w-4 text-muted-foreground" />
                          </TooltipTrigger>
                          <TooltipContent>
                            <p>The initial trust score assigned to new workers</p>
                          </TooltipContent>
                        </Tooltip>
                      </TooltipProvider>
                    </div>
                    <Input
                      id="initial-score"
                      className="w-20 text-right"
                      value={settings.TRUST_SCORE_INITIAL || "50"}
                      onChange={(e) => handleInputChange("TRUST_SCORE_INITIAL", e.target.value)}
                      disabled={!isEditing}
                    />
                  </div>
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-2">
                      <Label htmlFor="emergency-threshold">Emergency Job Threshold</Label>
                      <TooltipProvider>
                        <Tooltip>
                          <TooltipTrigger asChild>
                            <Info className="h-4 w-4 text-muted-foreground" />
                          </TooltipTrigger>
                          <TooltipContent>
                            <p>Workers below this threshold can access emergency jobs</p>
                          </TooltipContent>
                        </Tooltip>
                      </TooltipProvider>
                    </div>
                    <Input
                      id="emergency-threshold"
                      className="w-20 text-right"
                      value={settings.TRUST_SCORE_EMERGENCY_THRESHOLD || "40"}
                      onChange={(e) => handleInputChange("TRUST_SCORE_EMERGENCY_THRESHOLD", e.target.value)}
                      disabled={!isEditing}
                    />
                  </div>
                </div>

                <Separator />

                <div className="space-y-2">
                  <h3 className="text-lg font-medium">Score Adjustments</h3>
                  <div className="grid gap-4 md:grid-cols-2">
                    <div className="space-y-2">
                      <h4 className="font-medium text-green-600">Positive Adjustments</h4>
                      <div className="space-y-2">
                        <div className="flex items-center justify-between">
                          <Label htmlFor="emergency-completion">Emergency Job Completion</Label>
                          <div className="flex items-center">
                            <span className="mr-2 text-green-600">+</span>
                            <Input
                              id="emergency-completion"
                              className="w-16 text-right"
                              value={settings.TRUST_SCORE_EMERGENCY_JOB_COMPLETION?.replace(/^\+/, "") || "10"}
                              onChange={(e) => handleInputChange("TRUST_SCORE_EMERGENCY_JOB_COMPLETION", e.target.value)}
                              disabled={!isEditing}
                            />
                          </div>
                        </div>
                        <div className="flex items-center justify-between">
                          <Label htmlFor="standard-completion">Standard Job Completion</Label>
                          <div className="flex items-center">
                            <span className="mr-2 text-green-600">+</span>
                            <Input
                              id="standard-completion"
                              className="w-16 text-right"
                              value={settings.TRUST_SCORE_STANDARD_JOB_COMPLETION?.replace(/^\+/, "") || "5"}
                              onChange={(e) => handleInputChange("TRUST_SCORE_STANDARD_JOB_COMPLETION", e.target.value)}
                              disabled={!isEditing}
                            />
                          </div>
                        </div>
                      </div>
                    </div>

                    <div className="space-y-2">
                      <h4 className="font-medium text-red-600">Negative Adjustments</h4>
                      <div className="space-y-2">
                        <div className="flex items-center justify-between">
                          <Label htmlFor="last-minute-cancellation">Last-Minute Cancellation</Label>
                          <div className="flex items-center">
                            <span className="mr-2 text-red-600">-</span>
                            <Input
                              id="last-minute-cancellation"
                              className="w-16 text-right"
                              value={settings.TRUST_SCORE_LAST_MINUTE_CANCELLATION?.replace(/^-/, "") || "15"}
                              onChange={(e) => handleInputChange("TRUST_SCORE_LAST_MINUTE_CANCELLATION", `-${e.target.value}`)}
                              disabled={!isEditing}
                            />
                          </div>
                        </div>
                        <div className="flex items-center justify-between">
                          <Label htmlFor="no-show">No-Show</Label>
                          <div className="flex items-center">
                            <span className="mr-2 text-red-600">-</span>
                            <Input
                              id="no-show"
                              className="w-16 text-right"
                              value={settings.TRUST_SCORE_NO_SHOW?.replace(/^-/, "") || "25"}
                              onChange={(e) => handleInputChange("TRUST_SCORE_NO_SHOW", `-${e.target.value}`)}
                              disabled={!isEditing}
                            />
                          </div>
                        </div>
                        <div className="flex items-center justify-between">
                          <Label htmlFor="low-rating\">Low Rating (< 3 stars)</Label>
                          <div className="flex items-center">
                            <span className="mr-2 text-red-600">-</span>
                            <Input
                              id="low-rating"
                              className="w-16 text-right"
                              value={settings.TRUST_SCORE_LOW_RATING?.replace(/^-/, "") || "10"}
                              onChange={(e) => handleInputChange("TRUST_SCORE_LOW_RATING", `-${e.target.value}`)}
                              disabled={!isEditing}
                            />
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </CardContent>
              <CardFooter className="flex justify-between">
                <Button variant="outline" onClick={handleResetDefaults} disabled={!isEditing}>
                  Reset to Defaults
                </Button>
                {isEditing && (
                  <Button onClick={handleSaveSettings} disabled={updateSettingMutation.isLoading}>
                    {updateSettingMutation.isLoading ? "Saving..." : "Save Changes"}
                  </Button>
                )}
              </CardFooter>
            </Card>
          </motion.div>

          <motion.div variants={slideIn("up")}>
            <Card>
              <CardHeader>
                <CardTitle>Trust Score Explanation</CardTitle>
                <CardDescription>
                  How trust scores work in the platform
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex items-start space-x-4">
                  <div className="mt-0.5 rounded-full bg-primary/10 p-1">
                    <Shield className="h-4 w-4 text-primary" />
                  </div>
                  <div>
                    <h4 className="font-medium">Trust Score Range</h4>
                    <p className="text-sm text-muted-foreground">
                      Trust scores range from 0 to 100, with higher scores indicating more reliable workers.
                    </p>
                  </div>
                </div>

                <div className="flex items-start space-x-4">
                  <div className="mt-0.5 rounded-full bg-primary/10 p-1">
                    <Shield className="h-4 w-4 text-primary" />
                  </div>
                  <div>
                    <h4 className="font-medium">Initial Score</h4>
                    <p className="text-sm text-muted-foreground">
                      New workers start with a trust score of {settings.TRUST_SCORE_INITIAL || "50"}.
                    </p>
                  </div>
                </div>

                <div className="flex items-start space-x-4">
                  <div className="mt-0.5 rounded-full bg-primary/10 p-1">
                    <Shield className="h-4 w-4 text-primary" />
                  </div>
                  <div>
                    <h4 className="font-medium">Score Adjustments</h4>
                    <p className="text-sm text-muted-foreground">
                      Trust scores increase when workers complete jobs successfully and decrease when they cancel jobs, don't show up, or receive low ratings.
                    </p>
                  </div>
                </div>

                <div className="flex items-start space-x-4">
                  <div className="mt-0.5 rounded-full bg-primary/10 p-1">
                    <Shield className="h-4 w-4 text-primary" />
                  </div>
                  <div>
                    <h4 className="font-medium">Emergency Jobs</h4>
                    <p className="text-sm text-muted-foreground">
                      Workers with trust scores below {settings.TRUST_SCORE_EMERGENCY_THRESHOLD || "40"} can access emergency jobs to rebuild their trust score.
                    </p>
                  </div>
                </div>
              </CardContent>
            </Card>
          </motion.div>
        </TabsContent>

        <TabsContent value="commission" className="space-y-4">
          <motion.div variants={fadeIn}>
            <Card>
              <CardHeader>
                <CardTitle>Commission Settings</CardTitle>
                <CardDescription>
                  Configure the commission rates for the platform
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-6">
                <div className="space-y-4">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-2">
                      <Label htmlFor="commission-rate">Commission Rate</Label>
                      <TooltipProvider>
                        <Tooltip>
                          <TooltipTrigger asChild>
                            <Info className="h-4 w-4 text-muted-foreground" />
                          </TooltipTrigger>
                          <TooltipContent>
                            <p>Percentage of job payment taken as commission</p>
                          </TooltipContent>
                        </Tooltip>
                      </TooltipProvider>
                    </div>
                    <div className="flex items-center">
                      <Input
                        id="commission-rate"
                        className="w-20 text-right"
                        value={(Number.parseFloat(settings.PLATFORM_COMMISSION_RATE || "0.10") * 100).toString()}
                        onChange={(e) => handleInputChange("PLATFORM_COMMISSION_RATE", (Number.parseFloat(e.target.value) / 100).toString())}
                        disabled={!isEditing}
                      />
                      <span className="ml-2">%</span>
                    </div>
                  </div>

                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-2">
                      <Label htmlFor="fixed-commission">Fixed Commission</Label>
                      <TooltipProvider>
                        <Tooltip>
                          <TooltipTrigger asChild>
                            <Info className="h-4 w-4 text-muted-foreground" />
                          </TooltipTrigger>
                          <TooltipContent>
                            <p>Fixed amount taken as commission per job</p>
                          </TooltipContent>
                        </Tooltip>
                      </TooltipProvider>
                    </div>
                    <div className="flex items-center">
                      <span className="mr-2">₹</span>
                      <Input
                        id="fixed-commission"
                        className="w-20 text-right"
                        value={settings.PLATFORM_COMMISSION_FIXED || "200"}
                        onChange={(e) => handleInputChange("PLATFORM_COMMISSION_FIXED", e.target.value)}
                        disabled={!isEditing}
                      />
                    </div>
                  </div>
                </div>

                <div className="rounded-lg border p-4">
                  <h4 className="font-medium">Commission Example</h4>
                  <p className="text-sm text-muted-foreground mt-2">
                    For a job with payment of ₹1,000:
                  </p>
                  <div className="mt-4 space-y-2">
                    <div className="flex justify-between text-sm">
                      <span>Percentage Commission ({(Number.parseFloat(settings.PLATFORM_COMMISSION_RATE || "0.10") * 100).toFixed(0)}%):</span>
                      <span>₹{(1000 * Number.parseFloat(settings.PLATFORM_COMMISSION_RATE || "0.10")).toFixed(2)}</span>
                    </div>
                    <div className="flex justify-between text-sm">
                      <span>Fixed Commission:</span>
                      <span>₹{settings.PLATFORM_COMMISSION_FIXED || "200"}</span>
                    </div>
                    <div className="flex justify-between text-sm font-medium">
                      <span>Total Commission:</span>
                      <span>₹{(1000 * Number.parseFloat(settings.PLATFORM_COMMISSION_RATE || "0.10") + Number.parseFloat(settings.PLATFORM_COMMISSION_FIXED || "200")).toFixed(2)}</span>
                    </div>
                    <div className="flex justify-between text-sm font-medium">
                      <span>Worker Receives:</span>
                      <span>₹{(1000 - (1000 * Number.parseFloat(settings.PLATFORM_COMMISSION_RATE || "0.10") + Number.parseFloat(settings.PLATFORM_COMMISSION_FIXED || "200"))).toFixed(2)}</span>
                    </div>
                  </div>
                </div>
              </CardContent>
              <CardFooter className="flex justify-between">
                <Button variant="outline" onClick={handleResetDefaults} disabled={!isEditing}>
                  Reset to Defaults
                </Button>
                {isEditing && (
                  <Button onClick={handleSaveSettings} disabled={updateSettingMutation.isLoading}>
                    {updateSettingMutation.isLoading ? "Saving..." : "Save Changes"}
                  </Button>
                )}
              </CardFooter>
            </Card>
          </motion.div>
        </TabsContent>
      </Tabs>
    </motion.div>
  )
}
