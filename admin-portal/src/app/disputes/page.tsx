"use client"

import { useState } from "react"
import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query"
import { motion } from "framer-motion"
import { AlertTriangle, Search, CheckCircle, XCircle, Clock, MoreHorizontal } from "lucide-react"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Badge } from "@/components/ui/badge"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog"
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from "@/components/ui/dropdown-menu"
import { Textarea } from "@/components/ui/textarea"
import { useToast } from "@/components/ui/use-toast"
import { api } from "@/lib/api"
import { pageTransition, staggerContainer, staggerItem } from "@/utils/animations"

export default function DisputesPage() {
  const queryClient = useQueryClient()
  const { toast } = useToast()
  const [searchQuery, setSearchQuery] = useState("")
  const [statusFilter, setStatusFilter] = useState("all")
  const [page, setPage] = useState(1)
  const [selectedDispute, setSelectedDispute] = useState(null)
  const [isResolveDialogOpen, setIsResolveDialogOpen] = useState(false)
  const [isRejectDialogOpen, setIsRejectDialogOpen] = useState(false)
  const [resolution, setResolution] = useState("")

  const { data, isLoading } = useQuery({
    queryKey: ["disputes", statusFilter, searchQuery, page],
    queryFn: async () => {
      const params = {
        ...(statusFilter !== "all" && { status: statusFilter }),
        ...(searchQuery && { search: searchQuery }),
        page,
        limit: 10,
      }
      const response = await api.get("/admin/disputes", { params })
      return response.data
    },
  })

  const resolveDisputeMutation = useMutation({
    mutationFn: async ({ id, resolution }) => {
      return api.patch(`/admin/disputes/${id}/resolve`, { resolution })
    },
    onSuccess: () => {
      queryClient.invalidateQueries(["disputes"])
      toast({
        title: "Dispute resolved",
        description: "The dispute has been resolved successfully.",
      })
      setIsResolveDialogOpen(false)
      setResolution("")
    },
    onError: () => {
      toast({
        variant: "destructive",
        title: "Error",
        description: "Failed to resolve dispute. Please try again.",
      })
    },
  })

  const rejectDisputeMutation = useMutation({
    mutationFn: async ({ id, resolution }) => {
      return api.patch(`/admin/disputes/${id}/reject`, { resolution })
    },
    onSuccess: () => {
      queryClient.invalidateQueries(["disputes"])
      toast({
        title: "Dispute rejected",
        description: "The dispute has been rejected.",
      })
      setIsRejectDialogOpen(false)
      setResolution("")
    },
    onError: () => {
      toast({
        variant: "destructive",
        title: "Error",
        description: "Failed to reject dispute. Please try again.",
      })
    },
  })

  const updateDisputeStatusMutation = useMutation({
    mutationFn: async ({ id, status }) => {
      return api.patch(`/admin/disputes/${id}/status`, { status })
    },
    onSuccess: () => {
      queryClient.invalidateQueries(["disputes"])
      toast({
        title: "Status updated",
        description: "The dispute status has been updated successfully.",
      })
    },
    onError: () => {
      toast({
        variant: "destructive",
        title: "Error",
        description: "Failed to update dispute status. Please try again.",
      })
    },
  })

  const handleResolveDispute = () => {
    if (!selectedDispute || !resolution.trim()) return
    resolveDisputeMutation.mutate({ id: selectedDispute.id, resolution })
  }

  const handleRejectDispute = () => {
    if (!selectedDispute || !resolution.trim()) return
    rejectDisputeMutation.mutate({ id: selectedDispute.id, resolution })
  }

  const handleUpdateStatus = (id, status) => {
    updateDisputeStatusMutation.mutate({ id, status })
  }

  const disputes = data?.disputes || []
  const totalPages = data?.totalPages || 1

  const getStatusBadge = (status) => {
    switch (status) {
      case "open":
        return (
          <Badge variant="outline" className="bg-red-50 text-red-700 border-red-200">
            <AlertTriangle className="mr-1 h-3 w-3" />
            Open
          </Badge>
        )
      case "under_review":
        return (
          <Badge variant="outline" className="bg-yellow-50 text-yellow-700 border-yellow-200">
            <Clock className="mr-1 h-3 w-3" />
            Under Review
          </Badge>
        )
      case "resolved":
        return (
          <Badge variant="outline" className="bg-green-50 text-green-700 border-green-200">
            <CheckCircle className="mr-1 h-3 w-3" />
            Resolved
          </Badge>
        )
      case "rejected":
        return (
          <Badge variant="outline" className="bg-gray-50 text-gray-700 border-gray-200">
            <XCircle className="mr-1 h-3 w-3" />
            Rejected
          </Badge>
        )
      default:
        return <Badge variant="outline">{status}</Badge>
    }
  }

  return (
    <motion.div variants={pageTransition} initial="hidden" animate="visible" exit="exit" className="space-y-4 p-8 pt-6">
      <div className="flex items-center justify-between">
        <h2 className="text-3xl font-bold tracking-tight">Dispute Management</h2>
      </div>

      <div className="flex flex-col space-y-4 md:flex-row md:items-center md:space-x-4 md:space-y-0">
        <div className="relative flex-1">
          <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
          <Input
            placeholder="Search disputes..."
            className="pl-8"
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
          />
        </div>

        <Select value={statusFilter} onValueChange={setStatusFilter}>
          <SelectTrigger className="w-[180px]">
            <SelectValue placeholder="Filter by status" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">All Disputes</SelectItem>
            <SelectItem value="open">Open</SelectItem>
            <SelectItem value="under_review">Under Review</SelectItem>
            <SelectItem value="resolved">Resolved</SelectItem>
            <SelectItem value="rejected">Rejected</SelectItem>
          </SelectContent>
        </Select>
      </div>

      <div className="rounded-md border">
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>ID</TableHead>
              <TableHead>Raised By</TableHead>
              <TableHead>Job</TableHead>
              <TableHead>Description</TableHead>
              <TableHead>Status</TableHead>
              <TableHead>Date</TableHead>
              <TableHead className="text-right">Actions</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {isLoading ? (
              <TableRow>
                <TableCell colSpan={7} className="h-24 text-center">
                  <div className="flex justify-center">
                    <div className="h-8 w-8 animate-spin rounded-full border-4 border-primary border-t-transparent"></div>
                  </div>
                </TableCell>
              </TableRow>
            ) : disputes.length === 0 ? (
              <TableRow>
                <TableCell colSpan={7} className="h-24 text-center">
                  No disputes found.
                </TableCell>
              </TableRow>
            ) : (
              <motion.tbody variants={staggerContainer}>
                {disputes.map((dispute) => (
                  <motion.tr key={dispute.id} variants={staggerItem}>
                    <TableCell className="font-medium">{dispute.id.substring(0, 8)}</TableCell>
                    <TableCell>{dispute.raisedBy.name}</TableCell>
                    <TableCell>{dispute.job.title}</TableCell>
                    <TableCell className="max-w-[200px] truncate">{dispute.description}</TableCell>
                    <TableCell>{getStatusBadge(dispute.status)}</TableCell>
                    <TableCell>{new Date(dispute.createdAt).toLocaleDateString()}</TableCell>
                    <TableCell className="text-right">
                      <DropdownMenu>
                        <DropdownMenuTrigger asChild>
                          <Button variant="ghost" size="sm">
                            <MoreHorizontal className="h-4 w-4" />
                            <span className="sr-only">Open menu</span>
                          </Button>
                        </DropdownMenuTrigger>
                        <DropdownMenuContent align="end">
                          <DropdownMenuItem onClick={() => window.open(`/disputes/${dispute.id}`, "_blank")}>
                            View Details
                          </DropdownMenuItem>
                          {dispute.status === "open" && (
                            <DropdownMenuItem onClick={() => handleUpdateStatus(dispute.id, "under_review")}>
                              Mark as Under Review
                            </DropdownMenuItem>
                          )}
                          {(dispute.status === "open" || dispute.status === "under_review") && (
                            <>
                              <DropdownMenuItem
                                onClick={() => {
                                  setSelectedDispute(dispute)
                                  setIsResolveDialogOpen(true)
                                }}
                              >
                                Resolve Dispute
                              </DropdownMenuItem>
                              <DropdownMenuItem
                                onClick={() => {
                                  setSelectedDispute(dispute)
                                  setIsRejectDialogOpen(true)
                                }}
                              >
                                Reject Dispute
                              </DropdownMenuItem>
                            </>
                          )}
                        </DropdownMenuContent>
                      </DropdownMenu>
                    </TableCell>
                  </motion.tr>
                ))}
              </motion.tbody>
            )}
          </TableBody>
        </Table>
      </div>

      {totalPages > 1 && (
        <div className="flex items-center justify-end space-x-2 py-4">
          <Button variant="outline" size="sm" onClick={() => setPage((p) => Math.max(p - 1, 1))} disabled={page === 1}>
            Previous
          </Button>
          <div className="text-sm">
            Page {page} of {totalPages}
          </div>
          <Button
            variant="outline"
            size="sm"
            onClick={() => setPage((p) => Math.min(p + 1, totalPages))}
            disabled={page === totalPages}
          >
            Next
          </Button>
        </div>
      )}

      {/* Resolve Dispute Dialog */}
      <Dialog open={isResolveDialogOpen} onOpenChange={setIsResolveDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Resolve Dispute</DialogTitle>
            <DialogDescription>
              Provide a resolution for this dispute. This will be visible to the user who raised the dispute.
            </DialogDescription>
          </DialogHeader>
          <div className="space-y-4 py-4">
            <div className="space-y-2">
              <h4 className="font-medium">Dispute Description</h4>
              <p className="text-sm text-muted-foreground">{selectedDispute?.description}</p>
            </div>
            <div className="space-y-2">
              <label htmlFor="resolution" className="text-sm font-medium">
                Resolution
              </label>
              <Textarea
                id="resolution"
                placeholder="Enter your resolution..."
                value={resolution}
                onChange={(e) => setResolution(e.target.value)}
                rows={4}
              />
            </div>
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setIsResolveDialogOpen(false)}>
              Cancel
            </Button>
            <Button onClick={handleResolveDispute} disabled={!resolution.trim() || resolveDisputeMutation.isLoading}>
              {resolveDisputeMutation.isLoading ? "Resolving..." : "Resolve Dispute"}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Reject Dispute Dialog */}
      <Dialog open={isRejectDialogOpen} onOpenChange={setIsRejectDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Reject Dispute</DialogTitle>
            <DialogDescription>
              Provide a reason for rejecting this dispute. This will be visible to the user who raised the dispute.
            </DialogDescription>
          </DialogHeader>
          <div className="space-y-4 py-4">
            <div className="space-y-2">
              <h4 className="font-medium">Dispute Description</h4>
              <p className="text-sm text-muted-foreground">{selectedDispute?.description}</p>
            </div>
            <div className="space-y-2">
              <label htmlFor="rejection-reason" className="text-sm font-medium">
                Rejection Reason
              </label>
              <Textarea
                id="rejection-reason"
                placeholder="Enter your reason for rejection..."
                value={resolution}
                onChange={(e) => setResolution(e.target.value)}
                rows={4}
              />
            </div>
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setIsRejectDialogOpen(false)}>
              Cancel
            </Button>
            <Button
              variant="destructive"
              onClick={handleRejectDispute}
              disabled={!resolution.trim() || rejectDisputeMutation.isLoading}
            >
              {rejectDisputeMutation.isLoading ? "Rejecting..." : "Reject Dispute"}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </motion.div>
  )
}
