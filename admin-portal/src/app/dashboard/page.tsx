"use client";

import { useState } from "react";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { <PERSON><PERSON>, <PERSON><PERSON>Content, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { Overview } from "@/components/dashboard/overview";
import { RecentActivity } from "@/components/dashboard/recent-activity";
import { DashboardHeader } from "@/components/dashboard-header";
import { DashboardShell } from "@/components/dashboard-shell";
import {
  Loader2,
  Users,
  Briefcase,
  AlertTriangle,
  DollarSign,
} from "lucide-react";
import { useGetDashboardStatsQuery } from "@/store/api/analyticsApi";

export default function DashboardPage() {
  const [period, setPeriod] = useState("7d");

  // Use RTK Query to fetch dashboard stats
  const { data: stats, isLoading } = useGetDashboardStatsQuery();

  return (
    <DashboardShell>
      <DashboardHeader
        heading="Dashboard"
        text="Overview of your platform's performance"
      />

      <Tabs defaultValue="overview" className="space-y-4">
        <TabsList>
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="analytics">Analytics</TabsTrigger>
          <TabsTrigger value="reports">Reports</TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-4">
          <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
            {isLoading ? (
              Array(4)
                .fill(0)
                .map((_, i) => (
                  <Card
                    key={i}
                    className="h-[120px] flex items-center justify-center"
                  >
                    <Loader2 className="h-8 w-8 animate-spin text-primary" />
                  </Card>
                ))
            ) : (
              <>
                <Card>
                  <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                    <CardTitle className="text-sm font-medium">
                      Total Users
                    </CardTitle>
                    <Users className="h-4 w-4 text-muted-foreground" />
                  </CardHeader>
                  <CardContent>
                    <div className="text-2xl font-bold">
                      {stats?.totalUsers}
                    </div>
                    <p className="text-xs text-muted-foreground">
                      {stats?.userGrowth > 0 ? "+" : ""}
                      {stats?.userGrowth}% from last period
                    </p>
                  </CardContent>
                </Card>

                <Card>
                  <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                    <CardTitle className="text-sm font-medium">
                      Active Jobs
                    </CardTitle>
                    <Briefcase className="h-4 w-4 text-muted-foreground" />
                  </CardHeader>
                  <CardContent>
                    <div className="text-2xl font-bold">
                      {stats?.activeJobs}
                    </div>
                    <p className="text-xs text-muted-foreground">
                      {stats?.jobGrowth > 0 ? "+" : ""}
                      {stats?.jobGrowth}% from last period
                    </p>
                  </CardContent>
                </Card>

                <Card>
                  <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                    <CardTitle className="text-sm font-medium">
                      Open Disputes
                    </CardTitle>
                    <AlertTriangle className="h-4 w-4 text-muted-foreground" />
                  </CardHeader>
                  <CardContent>
                    <div className="text-2xl font-bold">
                      {stats?.openDisputes}
                    </div>
                    <p className="text-xs text-muted-foreground">
                      {stats?.disputeGrowth > 0 ? "+" : ""}
                      {stats?.disputeGrowth}% from last period
                    </p>
                  </CardContent>
                </Card>

                <Card>
                  <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                    <CardTitle className="text-sm font-medium">
                      Revenue
                    </CardTitle>
                    <DollarSign className="h-4 w-4 text-muted-foreground" />
                  </CardHeader>
                  <CardContent>
                    <div className="text-2xl font-bold">
                      ${stats?.revenue.toFixed(2)}
                    </div>
                    <p className="text-xs text-muted-foreground">
                      {stats?.revenueGrowth > 0 ? "+" : ""}
                      {stats?.revenueGrowth}% from last period
                    </p>
                  </CardContent>
                </Card>
              </>
            )}
          </div>

          <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-7">
            <Card className="col-span-4">
              <CardHeader>
                <CardTitle>Overview</CardTitle>
                <CardDescription>
                  Platform activity for the{" "}
                  <select
                    value={period}
                    onChange={(e) => setPeriod(e.target.value)}
                    className="border-none bg-transparent font-medium underline"
                  >
                    <option value="7d">last 7 days</option>
                    <option value="30d">last 30 days</option>
                    <option value="90d">last 90 days</option>
                  </select>
                </CardDescription>
              </CardHeader>
              <CardContent className="pl-2">
                {isLoading ? (
                  <div className="flex justify-center items-center h-80">
                    <Loader2 className="h-8 w-8 animate-spin text-primary" />
                  </div>
                ) : (
                  <Overview data={stats?.chartData} />
                )}
              </CardContent>
            </Card>

            <Card className="col-span-3">
              <CardHeader>
                <CardTitle>Recent Activity</CardTitle>
                <CardDescription>
                  Latest actions across the platform
                </CardDescription>
              </CardHeader>
              <CardContent>
                {isLoading ? (
                  <div className="flex justify-center items-center h-80">
                    <Loader2 className="h-8 w-8 animate-spin text-primary" />
                  </div>
                ) : (
                  <RecentActivity activities={stats?.recentActivities || []} />
                )}
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="analytics" className="space-y-4">
          {/* Analytics content */}
        </TabsContent>

        <TabsContent value="reports" className="space-y-4">
          {/* Reports content */}
        </TabsContent>
      </Tabs>
    </DashboardShell>
  );
}
