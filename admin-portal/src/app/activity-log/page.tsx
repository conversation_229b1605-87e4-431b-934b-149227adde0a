"use client"

import { useState } from "react"
import { useQuery } from "@tanstack/react-query"
import { motion } from "framer-motion"
import { Search, Download, Clock, User, Activity } from "lucide-react"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from "@/components/ui/dropdown-menu"
import { Badge } from "@/components/ui/badge"
import api from "@/lib/api"
import { format } from "date-fns"
import { pageTransition, staggerContainer, staggerItem } from "@/utils/animations"

export default function ActivityLogPage() {
  const [searchQuery, setSearchQuery] = useState("")
  const [activityType, setActivityType] = useState("all")
  const [userRole, setUserRole] = useState("all")
  const [page, setPage] = useState(1)

  const { data, isLoading } = useQuery({
    queryKey: ["activity-log", activityType, userRole, searchQuery, page],
    queryFn: async () => {
      const params = {
        ...(activityType !== "all" && { type: activityType }),
        ...(userRole !== "all" && { role: userRole }),
        ...(searchQuery && { search: searchQuery }),
        page,
        limit: 20,
      }
      const response = await api.get("/admin/activity-log", { params })
      return response.data
    },
  })

  const activities = data?.activities || []
  const totalPages = data?.totalPages || 1

  const getActivityBadgeColor = (type) => {
    switch (type) {
      case "login":
        return "bg-blue-500"
      case "job_created":
        return "bg-green-500"
      case "job_updated":
        return "bg-yellow-500"
      case "job_deleted":
        return "bg-red-500"
      case "application_created":
        return "bg-purple-500"
      case "application_updated":
        return "bg-indigo-500"
      case "user_created":
        return "bg-teal-500"
      case "user_updated":
        return "bg-orange-500"
      case "payment_processed":
        return "bg-emerald-500"
      default:
        return "bg-gray-500"
    }
  }

  return (
    <motion.div variants={pageTransition} initial="hidden" animate="visible" exit="exit" className="space-y-4 p-8 pt-6">
      <div className="flex items-center justify-between">
        <h2 className="text-3xl font-bold tracking-tight">Activity Log</h2>
        <Button variant="outline">
          <Download className="mr-2 h-4 w-4" />
          Export
        </Button>
      </div>

      <div className="flex flex-col space-y-4 md:flex-row md:items-center md:space-x-4 md:space-y-0">
        <div className="relative flex-1">
          <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
          <Input
            placeholder="Search activities..."
            className="pl-8"
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
          />
        </div>

        <Select value={activityType} onValueChange={setActivityType}>
          <SelectTrigger className="w-[180px]">
            <SelectValue placeholder="Activity Type" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">All Activities</SelectItem>
            <SelectItem value="login">Login</SelectItem>
            <SelectItem value="job_created">Job Created</SelectItem>
            <SelectItem value="job_updated">Job Updated</SelectItem>
            <SelectItem value="job_deleted">Job Deleted</SelectItem>
            <SelectItem value="application_created">Application Created</SelectItem>
            <SelectItem value="application_updated">Application Updated</SelectItem>
            <SelectItem value="user_created">User Created</SelectItem>
            <SelectItem value="user_updated">User Updated</SelectItem>
            <SelectItem value="payment_processed">Payment Processed</SelectItem>
          </SelectContent>
        </Select>

        <Select value={userRole} onValueChange={setUserRole}>
          <SelectTrigger className="w-[180px]">
            <SelectValue placeholder="User Role" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">All Roles</SelectItem>
            <SelectItem value="admin">Admin</SelectItem>
            <SelectItem value="company">Company</SelectItem>
            <SelectItem value="worker">Worker</SelectItem>
            <SelectItem value="system">System</SelectItem>
          </SelectContent>
        </Select>
      </div>

      <div className="rounded-md border">
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead className="w-[180px]">Timestamp</TableHead>
              <TableHead>Activity Type</TableHead>
              <TableHead>User</TableHead>
              <TableHead>Role</TableHead>
              <TableHead className="w-[300px]">Description</TableHead>
              <TableHead>IP Address</TableHead>
              <TableHead className="text-right">Details</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {isLoading ? (
              <TableRow>
                <TableCell colSpan={7} className="h-24 text-center">
                  <div className="flex justify-center">
                    <div className="h-8 w-8 animate-spin rounded-full border-4 border-primary border-t-transparent"></div>
                  </div>
                </TableCell>
              </TableRow>
            ) : activities.length === 0 ? (
              <TableRow>
                <TableCell colSpan={7} className="h-24 text-center">
                  No activities found.
                </TableCell>
              </TableRow>
            ) : (
              <motion.tbody variants={staggerContainer}>
                {activities.map((activity) => (
                  <motion.tr key={activity.id} variants={staggerItem}>
                    <TableCell className="font-medium">
                      <div className="flex items-center">
                        <Clock className="mr-2 h-4 w-4 text-muted-foreground" />
                        {format(new Date(activity.timestamp), "MMM dd, yyyy HH:mm:ss")}
                      </div>
                    </TableCell>
                    <TableCell>
                      <Badge className={getActivityBadgeColor(activity.type)} variant="secondary">
                        {activity.type.replace("_", " ")}
                      </Badge>
                    </TableCell>
                    <TableCell>
                      <div className="flex items-center">
                        <User className="mr-2 h-4 w-4 text-muted-foreground" />
                        {activity.userName || "System"}
                      </div>
                    </TableCell>
                    <TableCell>
                      <Badge variant="outline">{activity.userRole}</Badge>
                    </TableCell>
                    <TableCell className="max-w-[300px] truncate">{activity.description}</TableCell>
                    <TableCell>{activity.ipAddress}</TableCell>
                    <TableCell className="text-right">
                      <DropdownMenu>
                        <DropdownMenuTrigger asChild>
                          <Button variant="ghost" size="sm">
                            <Activity className="h-4 w-4" />
                            <span className="sr-only">View details</span>
                          </Button>
                        </DropdownMenuTrigger>
                        <DropdownMenuContent align="end">
                          <DropdownMenuItem>View Full Details</DropdownMenuItem>
                          <DropdownMenuItem>View Related User</DropdownMenuItem>
                          <DropdownMenuItem>View Related Entity</DropdownMenuItem>
                        </DropdownMenuContent>
                      </DropdownMenu>
                    </TableCell>
                  </motion.tr>
                ))}
              </motion.tbody>
            )}
          </TableBody>
        </Table>
      </div>

      {totalPages > 1 && (
        <div className="flex items-center justify-end space-x-2 py-4">
          <Button variant="outline" size="sm" onClick={() => setPage((p) => Math.max(p - 1, 1))} disabled={page === 1}>
            Previous
          </Button>
          <div className="text-sm">
            Page {page} of {totalPages}
          </div>
          <Button
            variant="outline"
            size="sm"
            onClick={() => setPage((p) => Math.min(p + 1, totalPages))}
            disabled={page === totalPages}
          >
            Next
          </Button>
        </div>
      )}
    </motion.div>
  )
}
