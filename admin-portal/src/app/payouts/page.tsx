"use client";

import { useState } from "react";
import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { motion } from "framer-motion";
import {
  Search,
  DollarSign,
  CheckCircle,
  XCircle,
  Clock,
  Download,
  MoreHorizontal,
} from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Badge } from "@/components/ui/badge";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { <PERSON>, CardContent, <PERSON><PERSON>eader, CardTitle } from "@/components/ui/card";
import { useToast } from "@/components/ui/use-toast";
import { api } from "@/lib/api";
import {
  pageTransition,
  staggerContainer,
  staggerItem,
} from "@/utils/animations";

export default function PayoutsPage() {
  const queryClient = useQueryClient();
  const { toast } = useToast();
  const [searchQuery, setSearchQuery] = useState("");
  const [statusFilter, setStatusFilter] = useState("all");
  const [page, setPage] = useState(1);
  const [selectedPayout, setSelectedPayout] = useState(null);
  const [isProcessDialogOpen, setIsProcessDialogOpen] = useState(false);
  const [selectedPaymentMethod, setSelectedPaymentMethod] = useState("stripe");

  const { data, isLoading } = useQuery({
    queryKey: ["payouts", statusFilter, searchQuery, page],
    queryFn: async () => {
      const params = {
        ...(statusFilter !== "all" && { status: statusFilter }),
        ...(searchQuery && { search: searchQuery }),
        page,
        limit: 10,
      };
      const response = await api.get("/admin/payouts", { params });
      return response.data;
    },
  });

  const { data: stats } = useQuery({
    queryKey: ["payout-stats"],
    queryFn: async () => {
      const response = await api.get("/admin/payouts/stats");
      return response.data;
    },
  });

  const processPayoutMutation = useMutation({
    mutationFn: async ({ id, paymentMethod }) => {
      return api.patch(`/payouts/${id}/process`, { paymentMethod });
    },
    onSuccess: () => {
      queryClient.invalidateQueries(["payouts"]);
      queryClient.invalidateQueries(["payout-stats"]);
      toast({
        title: "Payout processed",
        description: "The payout has been processed successfully.",
      });
      setIsProcessDialogOpen(false);
    },
    onError: (error) => {
      console.error("Error processing payout:", error);
      toast({
        variant: "destructive",
        title: "Error",
        description:
          error.response?.data?.message ||
          "Failed to process payout. Please try again.",
      });
    },
  });

  const handleProcessPayout = () => {
    if (!selectedPayout) return;
    processPayoutMutation.mutate({
      id: selectedPayout.id,
      paymentMethod: selectedPaymentMethod,
    });
  };

  const payouts = data?.payouts || [];
  const totalPages = data?.totalPages || 1;

  const getStatusBadge = (status) => {
    switch (status) {
      case "pending":
        return (
          <Badge
            variant="outline"
            className="bg-yellow-50 text-yellow-700 border-yellow-200"
          >
            <Clock className="mr-1 h-3 w-3" />
            Pending
          </Badge>
        );
      case "completed":
        return (
          <Badge
            variant="outline"
            className="bg-green-50 text-green-700 border-green-200"
          >
            <CheckCircle className="mr-1 h-3 w-3" />
            Completed
          </Badge>
        );
      case "failed":
        return (
          <Badge
            variant="outline"
            className="bg-red-50 text-red-700 border-red-200"
          >
            <XCircle className="mr-1 h-3 w-3" />
            Failed
          </Badge>
        );
      default:
        return <Badge variant="outline">{status}</Badge>;
    }
  };

  return (
    <motion.div
      variants={pageTransition}
      initial="hidden"
      animate="visible"
      exit="exit"
      className="space-y-4 p-8 pt-6"
    >
      <div className="flex items-center justify-between">
        <h2 className="text-3xl font-bold tracking-tight">Payout Management</h2>
      </div>

      {stats && (
        <div className="grid gap-4 md:grid-cols-4">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">
                Pending Payouts
              </CardTitle>
              <Clock className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{stats.totalPending}</div>
            </CardContent>
          </Card>
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">
                Completed Payouts
              </CardTitle>
              <CheckCircle className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{stats.totalCompleted}</div>
            </CardContent>
          </Card>
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">
                Total Amount Paid
              </CardTitle>
              <DollarSign className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">
                ₹{stats.totalAmount.toFixed(2)}
              </div>
            </CardContent>
          </Card>
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">
                Total Commission
              </CardTitle>
              <DollarSign className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">
                ₹{stats.totalCommission.toFixed(2)}
              </div>
            </CardContent>
          </Card>
        </div>
      )}

      <div className="flex flex-col space-y-4 md:flex-row md:items-center md:space-x-4 md:space-y-0">
        <div className="relative flex-1">
          <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
          <Input
            placeholder="Search payouts..."
            className="pl-8"
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
          />
        </div>

        <Select value={statusFilter} onValueChange={setStatusFilter}>
          <SelectTrigger className="w-[180px]">
            <SelectValue placeholder="Filter by status" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">All Payouts</SelectItem>
            <SelectItem value="pending">Pending</SelectItem>
            <SelectItem value="completed">Completed</SelectItem>
            <SelectItem value="failed">Failed</SelectItem>
          </SelectContent>
        </Select>
      </div>

      <div className="rounded-md border">
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>ID</TableHead>
              <TableHead>Worker</TableHead>
              <TableHead>Job</TableHead>
              <TableHead>Gross Amount</TableHead>
              <TableHead>Commission</TableHead>
              <TableHead>Net Amount</TableHead>
              <TableHead>Status</TableHead>
              <TableHead>Date</TableHead>
              <TableHead className="text-right">Actions</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {isLoading ? (
              <TableRow>
                <TableCell colSpan={9} className="h-24 text-center">
                  <div className="flex justify-center">
                    <div className="h-8 w-8 animate-spin rounded-full border-4 border-primary border-t-transparent"></div>
                  </div>
                </TableCell>
              </TableRow>
            ) : payouts.length === 0 ? (
              <TableRow>
                <TableCell colSpan={9} className="h-24 text-center">
                  No payouts found.
                </TableCell>
              </TableRow>
            ) : (
              <motion.tbody variants={staggerContainer}>
                {payouts.map((payout) => (
                  <motion.tr key={payout.id} variants={staggerItem}>
                    <TableCell className="font-medium">
                      {payout.id.substring(0, 8)}
                    </TableCell>
                    <TableCell>{payout.user.name}</TableCell>
                    <TableCell>{payout.job.title}</TableCell>
                    <TableCell>₹{payout.grossPay.toFixed(2)}</TableCell>
                    <TableCell>₹{payout.commission.toFixed(2)}</TableCell>
                    <TableCell className="font-medium">
                      ₹{payout.netPay.toFixed(2)}
                    </TableCell>
                    <TableCell>{getStatusBadge(payout.status)}</TableCell>
                    <TableCell>
                      {new Date(payout.createdAt).toLocaleDateString()}
                    </TableCell>
                    <TableCell className="text-right">
                      <DropdownMenu>
                        <DropdownMenuTrigger asChild>
                          <Button variant="ghost" size="sm">
                            <MoreHorizontal className="h-4 w-4" />
                            <span className="sr-only">Open menu</span>
                          </Button>
                        </DropdownMenuTrigger>
                        <DropdownMenuContent align="end">
                          <DropdownMenuItem
                            onClick={() =>
                              window.open(`/payouts/${payout.id}`, "_blank")
                            }
                          >
                            View Details
                          </DropdownMenuItem>
                          {payout.status === "pending" && (
                            <DropdownMenuItem
                              onClick={() => {
                                setSelectedPayout(payout);
                                setIsProcessDialogOpen(true);
                              }}
                            >
                              Process Payout
                            </DropdownMenuItem>
                          )}
                          {payout.status === "completed" && (
                            <DropdownMenuItem>
                              <Download className="mr-2 h-4 w-4" />
                              Download Receipt
                            </DropdownMenuItem>
                          )}
                        </DropdownMenuContent>
                      </DropdownMenu>
                    </TableCell>
                  </motion.tr>
                ))}
              </motion.tbody>
            )}
          </TableBody>
        </Table>
      </div>

      {totalPages > 1 && (
        <div className="flex items-center justify-end space-x-2 py-4">
          <Button
            variant="outline"
            size="sm"
            onClick={() => setPage((p) => Math.max(p - 1, 1))}
            disabled={page === 1}
          >
            Previous
          </Button>
          <div className="text-sm">
            Page {page} of {totalPages}
          </div>
          <Button
            variant="outline"
            size="sm"
            onClick={() => setPage((p) => Math.min(p + 1, totalPages))}
            disabled={page === totalPages}
          >
            Next
          </Button>
        </div>
      )}

      {/* Process Payout Dialog */}
      <Dialog open={isProcessDialogOpen} onOpenChange={setIsProcessDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Process Payout</DialogTitle>
            <DialogDescription>
              Are you sure you want to process this payout? This action cannot
              be undone.
            </DialogDescription>
          </DialogHeader>
          <div className="space-y-4 py-4">
            <div className="space-y-2">
              <h4 className="font-medium">Payout Details</h4>
              <div className="rounded-md border p-4">
                <div className="grid grid-cols-2 gap-2 text-sm">
                  <div className="text-muted-foreground">Worker:</div>
                  <div>{selectedPayout?.user.name}</div>
                  <div className="text-muted-foreground">Job:</div>
                  <div>{selectedPayout?.job.title}</div>
                  <div className="text-muted-foreground">Gross Amount:</div>
                  <div>₹{selectedPayout?.grossPay.toFixed(2)}</div>
                  <div className="text-muted-foreground">Commission:</div>
                  <div>₹{selectedPayout?.commission.toFixed(2)}</div>
                  <div className="text-muted-foreground">Net Amount:</div>
                  <div className="font-medium">
                    ₹{selectedPayout?.netPay.toFixed(2)}
                  </div>
                </div>
              </div>
            </div>

            <div className="space-y-2">
              <h4 className="font-medium">Payment Method</h4>
              <Select
                value={selectedPaymentMethod}
                onValueChange={setSelectedPaymentMethod}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Select payment method" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="stripe">Stripe</SelectItem>
                  <SelectItem value="upi">UPI</SelectItem>
                  <SelectItem value="bank_transfer">Bank Transfer</SelectItem>
                </SelectContent>
              </Select>
              <p className="text-sm text-muted-foreground">
                {selectedPaymentMethod === "stripe" &&
                  "Process payment via Stripe (International)"}
                {selectedPaymentMethod === "upi" &&
                  "Process payment via UPI (India)"}
                {selectedPaymentMethod === "bank_transfer" &&
                  "Process payment via Bank Transfer"}
              </p>
            </div>
          </div>
          <DialogFooter>
            <Button
              variant="outline"
              onClick={() => setIsProcessDialogOpen(false)}
            >
              Cancel
            </Button>
            <Button
              onClick={handleProcessPayout}
              disabled={processPayoutMutation.isLoading}
            >
              {processPayoutMutation.isLoading
                ? "Processing..."
                : "Process Payout"}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </motion.div>
  );
}
