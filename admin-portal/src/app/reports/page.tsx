"use client"

import { <PERSON><PERSON> } from "@/components/ui/badge"

import { useState } from "react"
import { useQ<PERSON>y, useMutation } from "@tanstack/react-query"
import { motion } from "framer-motion"
import { useRouter } from "next/navigation"
import { Plus, FileText, Download, Bar<PERSON>hart, Calendar, RefreshCw } from "lucide-react"
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Form, FormControl, FormDescription, FormField, FormItem, FormLabel, FormMessage } from "@/components/ui/form"
import { Input } from "@/components/ui/input"
import { Checkbox } from "@/components/ui/checkbox"
import { DatePicker } from "@/components/ui/date-picker"
import { api } from "@/lib/api"
import { useToast } from "@/components/ui/use-toast"
import { useForm } from "react-hook-form"
import { zodResolver } from "@hookform/resolvers/zod"
import * as z from "zod"
import { pageTransition, staggerContainer, staggerItem } from "@/utils/animations"

const reportFormSchema = z.object({
  name: z.string().min(3, {
    message: "Report name must be at least 3 characters",
  }),
  type: z.string({
    required_error: "Please select a report type",
  }),
  dateRange: z.object({
    from: z.date(),
    to: z.date(),
  }),
  filters: z.array(z.string()).optional(),
  includeCharts: z.boolean().default(true),
  scheduledDelivery: z.boolean().default(false),
  emailRecipients: z.string().optional(),
})

export default function ReportsPage() {
  const router = useRouter()
  const { toast } = useToast()
  const [isCreateDialogOpen, setIsCreateDialogOpen] = useState(false)

  const {
    data: reports,
    isLoading,
    refetch,
  } = useQuery({
    queryKey: ["reports"],
    queryFn: async () => {
      const response = await api.get("/admin/reports")
      return response.data
    },
  })

  const form = useForm<z.infer<typeof reportFormSchema>>({
    resolver: zodResolver(reportFormSchema),
    defaultValues: {
      name: "",
      type: "",
      dateRange: {
        from: new Date(),
        to: new Date(),
      },
      filters: [],
      includeCharts: true,
      scheduledDelivery: false,
      emailRecipients: "",
    },
  })

  const createReportMutation = useMutation({
    mutationFn: async (values: z.infer<typeof reportFormSchema>) => {
      return api.post("/admin/reports", values)
    },
    onSuccess: () => {
      toast({
        title: "Report created",
        description: "Your report has been created successfully.",
      })
      setIsCreateDialogOpen(false)
      form.reset()
      refetch()
    },
    onError: () => {
      toast({
        variant: "destructive",
        title: "Error",
        description: "Failed to create report. Please try again.",
      })
    },
  })

  const generateReportMutation = useMutation({
    mutationFn: async (reportId: string) => {
      return api.post(`/admin/reports/${reportId}/generate`)
    },
    onSuccess: (data) => {
      toast({
        title: "Report generated",
        description: "Your report has been generated successfully.",
      })
      // In a real app, you would handle the download here
      // window.open(data.data.downloadUrl, '_blank')
    },
    onError: () => {
      toast({
        variant: "destructive",
        title: "Error",
        description: "Failed to generate report. Please try again.",
      })
    },
  })

  function onSubmit(values: z.infer<typeof reportFormSchema>) {
    createReportMutation.mutate(values)
  }

  const handleGenerateReport = (reportId: string) => {
    generateReportMutation.mutate(reportId)
  }

  return (
    <motion.div variants={pageTransition} initial="hidden" animate="visible" exit="exit" className="space-y-4 p-8 pt-6">
      <div className="flex items-center justify-between">
        <h2 className="text-3xl font-bold tracking-tight">Reports</h2>
        <Dialog open={isCreateDialogOpen} onOpenChange={setIsCreateDialogOpen}>
          <DialogTrigger asChild>
            <Button>
              <Plus className="mr-2 h-4 w-4" />
              Create Report
            </Button>
          </DialogTrigger>
          <DialogContent className="sm:max-w-[600px]">
            <DialogHeader>
              <DialogTitle>Create New Report</DialogTitle>
              <DialogDescription>Configure your custom report parameters and filters.</DialogDescription>
            </DialogHeader>
            <Form {...form}>
              <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
                <FormField
                  control={form.control}
                  name="name"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Report Name</FormLabel>
                      <FormControl>
                        <Input placeholder="Monthly Worker Performance" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="type"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Report Type</FormLabel>
                      <Select onValueChange={field.onChange} defaultValue={field.value}>
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue placeholder="Select report type" />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          <SelectItem value="worker_performance">Worker Performance</SelectItem>
                          <SelectItem value="job_completion">Job Completion</SelectItem>
                          <SelectItem value="trust_score_analysis">Trust Score Analysis</SelectItem>
                          <SelectItem value="financial">Financial Report</SelectItem>
                          <SelectItem value="user_activity">User Activity</SelectItem>
                        </SelectContent>
                      </Select>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <div className="grid grid-cols-2 gap-4">
                  <FormField
                    control={form.control}
                    name="dateRange.from"
                    render={({ field }) => (
                      <FormItem className="flex flex-col">
                        <FormLabel>Start Date</FormLabel>
                        <DatePicker date={field.value} setDate={field.onChange} />
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="dateRange.to"
                    render={({ field }) => (
                      <FormItem className="flex flex-col">
                        <FormLabel>End Date</FormLabel>
                        <DatePicker date={field.value} setDate={field.onChange} />
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>

                <FormField
                  control={form.control}
                  name="includeCharts"
                  render={({ field }) => (
                    <FormItem className="flex flex-row items-start space-x-3 space-y-0">
                      <FormControl>
                        <Checkbox checked={field.value} onCheckedChange={field.onChange} />
                      </FormControl>
                      <div className="space-y-1 leading-none">
                        <FormLabel>Include Charts and Visualizations</FormLabel>
                        <FormDescription>Add visual representations of data in the report</FormDescription>
                      </div>
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="scheduledDelivery"
                  render={({ field }) => (
                    <FormItem className="flex flex-row items-start space-x-3 space-y-0">
                      <FormControl>
                        <Checkbox checked={field.value} onCheckedChange={field.onChange} />
                      </FormControl>
                      <div className="space-y-1 leading-none">
                        <FormLabel>Schedule Regular Delivery</FormLabel>
                        <FormDescription>Automatically generate and send this report on a schedule</FormDescription>
                      </div>
                    </FormItem>
                  )}
                />

                {form.watch("scheduledDelivery") && (
                  <FormField
                    control={form.control}
                    name="emailRecipients"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Email Recipients</FormLabel>
                        <FormControl>
                          <Input placeholder="<EMAIL>, <EMAIL>" {...field} />
                        </FormControl>
                        <FormDescription>Comma-separated list of email addresses</FormDescription>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                )}

                <DialogFooter>
                  <Button type="submit" disabled={createReportMutation.isLoading}>
                    {createReportMutation.isLoading ? "Creating..." : "Create Report"}
                  </Button>
                </DialogFooter>
              </form>
            </Form>
          </DialogContent>
        </Dialog>
      </div>

      {isLoading ? (
        <div className="flex justify-center p-8">
          <div className="h-8 w-8 animate-spin rounded-full border-4 border-primary border-t-transparent"></div>
        </div>
      ) : reports?.length > 0 ? (
        <motion.div variants={staggerContainer} className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
          {reports.map((report) => (
            <motion.div key={report.id} variants={staggerItem}>
              <Card>
                <CardHeader className="pb-2">
                  <div className="flex items-start justify-between">
                    <div>
                      <CardTitle>{report.name}</CardTitle>
                      <CardDescription>{report.type.replace("_", " ")}</CardDescription>
                    </div>
                    <div className="flex items-center space-x-2">
                      {report.scheduledDelivery && (
                        <Badge variant="outline" className="ml-2">
                          <Calendar className="mr-1 h-3 w-3" />
                          Scheduled
                        </Badge>
                      )}
                    </div>
                  </div>
                </CardHeader>
                <CardContent>
                  <div className="space-y-2">
                    <div className="flex items-center justify-between text-sm">
                      <span className="text-muted-foreground">Date Range:</span>
                      <span>
                        {new Date(report.dateRange.from).toLocaleDateString()} -{" "}
                        {new Date(report.dateRange.to).toLocaleDateString()}
                      </span>
                    </div>
                    <div className="flex items-center justify-between text-sm">
                      <span className="text-muted-foreground">Last Generated:</span>
                      <span>
                        {report.lastGenerated ? new Date(report.lastGenerated).toLocaleDateString() : "Never"}
                      </span>
                    </div>
                    {report.filters && report.filters.length > 0 && (
                      <div className="flex items-center justify-between text-sm">
                        <span className="text-muted-foreground">Filters:</span>
                        <span>{report.filters.length} applied</span>
                      </div>
                    )}
                  </div>
                </CardContent>
                <CardFooter className="flex justify-between">
                  <Button variant="outline" size="sm" onClick={() => router.push(`/reports/${report.id}`)}>
                    <FileText className="mr-2 h-4 w-4" />
                    View
                  </Button>
                  <div className="flex space-x-2">
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => handleGenerateReport(report.id)}
                      disabled={generateReportMutation.isLoading}
                    >
                      <RefreshCw className="mr-2 h-4 w-4" />
                      Generate
                    </Button>
                    <Button variant="outline" size="sm" disabled={!report.lastGenerated}>
                      <Download className="mr-2 h-4 w-4" />
                      Download
                    </Button>
                  </div>
                </CardFooter>
              </Card>
            </motion.div>
          ))}
        </motion.div>
      ) : (
        <Card>
          <CardHeader>
            <CardTitle>No Reports Found</CardTitle>
            <CardDescription>You haven't created any custom reports yet.</CardDescription>
          </CardHeader>
          <CardContent className="flex flex-col items-center justify-center p-6">
            <BarChart className="h-16 w-16 text-muted-foreground mb-4" />
            <p className="text-center text-muted-foreground mb-4">
              Custom reports help you analyze platform data and track key metrics.
            </p>
            <Dialog open={isCreateDialogOpen} onOpenChange={setIsCreateDialogOpen}>
              <DialogTrigger asChild>
                <Button>
                  <Plus className="mr-2 h-4 w-4" />
                  Create Your First Report
                </Button>
              </DialogTrigger>
            </Dialog>
          </CardContent>
        </Card>
      )}
    </motion.div>
  )
}
