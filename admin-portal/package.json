{"name": "inventory-audit-admin-portal", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev -p 3002", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@hookform/resolvers": "^3.1.1", "@radix-ui/react-avatar": "^1.0.3", "@radix-ui/react-dialog": "^1.0.4", "@radix-ui/react-dropdown-menu": "^2.0.5", "@radix-ui/react-label": "^2.0.2", "@radix-ui/react-select": "^1.2.2", "@radix-ui/react-separator": "^1.0.3", "@radix-ui/react-slot": "^1.0.2", "@radix-ui/react-tabs": "^1.0.4", "@radix-ui/react-toast": "^1.1.4", "@reduxjs/toolkit": "^2.0.1", "@tanstack/react-table": "^8.9.3", "class-variance-authority": "^0.6.1", "clsx": "^1.2.1", "date-fns": "^2.30.0", "lucide-react": "^0.259.0", "next": "13.4.9", "next-auth": "^4.22.1", "react": "18.2.0", "react-dom": "18.2.0", "react-hook-form": "^7.45.1", "react-redux": "^9.0.4", "recharts": "^2.7.2", "tailwind-merge": "^1.13.2", "tailwindcss-animate": "^1.0.6", "zod": "^3.21.4"}, "devDependencies": {"@types/node": "20.4.1", "@types/react": "18.2.14", "@types/react-dom": "18.2.6", "autoprefixer": "10.4.14", "eslint": "8.44.0", "eslint-config-next": "13.4.9", "postcss": "8.4.25", "tailwindcss": "3.3.2", "typescript": "5.1.6"}}