// Initialize global objects for <PERSON><PERSON> compatibility
if (typeof global !== 'undefined') {
  // Ensure process exists
  if (!global.process) {
    global.process = {};
  }

  // Ensure process.env exists
  if (!global.process.env) {
    global.process.env = {};
  }
}

// Import and register the app
import { registerRootComponent } from 'expo';
import App from './App';

// registerRootComponent calls AppRegistry.registerComponent('main', () => App);
// It also ensures that whether you load the app in Expo Go or in a native build,
// the environment is set up appropriately
registerRootComponent(App);
