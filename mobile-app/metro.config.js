const { getDefaultConfig } = require('expo/metro-config');
const { withNativeWind } = require('nativewind/metro');
const path = require('path');

const config = getDefaultConfig(__dirname);

// Add the shared directory to the watchFolders so Metro can watch for changes
config.watchFolders = [path.resolve(__dirname, '../shared')];

// Keep the resolver configuration minimal for Hermes compatibility
config.resolver = {
  ...config.resolver,
  // Only add essential configurations
  platforms: ['ios', 'android', 'native', 'web'],
  sourceExts: [...(config.resolver?.sourceExts || []), 'ts', 'tsx'],
};

module.exports = withNativeWind(config, {
  input: './src/global.css',
});
