{"compilerOptions": {"strict": true, "jsx": "react-native", "jsxImportSource": "nativewind", "baseUrl": ".", "paths": {"@/*": ["./src/*"], "@shared/*": ["../shared/src/*"], "@shared-types": ["../shared/src/types"]}, "types": ["nativewind/types", "react-native", "jest"], "allowSyntheticDefaultImports": true, "esModuleInterop": true, "isolatedModules": true, "lib": ["es2017"], "moduleResolution": "node", "noEmit": true, "skipLibCheck": true, "resolveJsonModule": true, "target": "esnext"}, "include": ["src", "App.tsx", "nativewind-env.d.ts"], "extends": "expo/tsconfig.base"}