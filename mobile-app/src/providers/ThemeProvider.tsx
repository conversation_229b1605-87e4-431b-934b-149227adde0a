import React, { createContext, useContext, ReactNode } from 'react';
import { StyleSheet, View } from 'react-native';
import { useTheme, Theme } from '../hooks/use-theme';

// Define the theme context type
interface ThemeContextType {
  theme: 'light' | 'dark';
  setTheme: (theme: Theme) => void;
  isDark: boolean;
}

// Create the context
const ThemeContext = createContext<ThemeContextType | undefined>(undefined);

/**
 * Theme provider component
 * Provides theme context to the app and applies the appropriate theme class
 */
export const ThemeProvider: React.FC<{ children: ReactNode }> = ({ children }) => {
  // Use the theme hook to get theme state and utilities
  const { theme, setTheme, isDark } = useTheme();

  // Force StyleSheet registration to ensure NativeWind processing
  StyleSheet.create({});

  // Create the context value
  const contextValue: ThemeContextType = {
    theme,
    setTheme,
    isDark,
  };

  // Apply the appropriate theme class
  const themeClass = isDark ? 'dark' : '';

  return (
    <ThemeContext.Provider value={contextValue}>
      <View className={`flex-1 ${themeClass}`}>{children}</View>
    </ThemeContext.Provider>
  );
};

/**
 * Custom hook to use the theme context
 */
export function useThemeContext() {
  const context = useContext(ThemeContext);
  if (context === undefined) {
    throw new Error('useThemeContext must be used within a ThemeProvider');
  }
  return context;
}

export default ThemeProvider;
