/**
 * Authentication Provider using RTK Query
 * This component provides authentication context using Redux and RTK Query
 */
import React, { createContext, useContext, useEffect } from 'react';
import { useAuthRTK } from '../hooks/use-auth-rtk';
import type { User, LoginRequest, RegisterRequest } from '../types/api';

// Define the auth context type
interface AuthContextType {
  user: User | null;
  isAuthenticated: boolean;
  isLoading: boolean;
  hasCompletedOnboarding: boolean;
  signIn: (credentials: LoginRequest) => Promise<any>;
  signInWithOtp: (phone: string, otp: string) => Promise<any>;
  requestOtp: (phone: string) => Promise<any>;
  signUp: (userData: RegisterRequest) => Promise<any>;
  signOut: () => Promise<void>;
  updateUser: (data: Partial<User>) => Promise<boolean>;
  completeOnboarding: () => Promise<void>;
}

// Create the auth context
const AuthRTKContext = createContext<AuthContextType | undefined>(undefined);

/**
 * Auth Provider component
 * Provides authentication context to the app using RTK Query
 */
export const AuthRTKProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const auth = useAuthRTK();

  return <AuthRTKContext.Provider value={auth}>{children}</AuthRTKContext.Provider>;
};

/**
 * Custom hook to use the auth context
 * @returns Authentication context
 */
export function useAuth() {
  const context = useContext(AuthRTKContext);
  if (!context) {
    throw new Error('useAuth must be used within an AuthRTKProvider');
  }
  return context;
}

export default AuthRTKProvider;
