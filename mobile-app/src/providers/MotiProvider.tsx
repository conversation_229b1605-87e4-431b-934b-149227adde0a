import React, { ReactNode } from 'react';

interface MotiProviderProps {
  children: ReactNode;
}

/**
 * MotiProvider wrapper component to ensure proper initialization
 * and context for Moti animations throughout the app.
 *
 * Note: We're not using the actual MotiProvider from the moti package
 * because it's causing hook-related errors. Instead, we're just passing
 * children through directly, which works fine for our use case.
 */
const MotiProvider = ({ children }: MotiProviderProps) => {
  // Simply pass children without any additional wrapping
  return <>{children}</>;
};

export default MotiProvider;
