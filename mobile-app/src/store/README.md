# Redux Toolkit and RTK Query Implementation

This directory contains the Redux Toolkit and RTK Query implementation for the mobile app. It provides a centralized state management system and API integration.

## Directory Structure

```
store/
├── api/                  # RTK Query API endpoints
│   ├── apiSlice.ts       # Base API configuration
│   ├── authApi.ts        # Authentication endpoints
│   ├── userApi.ts        # User profile endpoints
│   ├── jobsApi.ts        # Jobs endpoints
│   ├── applicationsApi.ts # Applications endpoints
│   └── notificationsApi.ts # Notifications endpoints
├── slices/               # Redux slices for local state
│   └── authSlice.ts      # Authentication state
├── index.ts              # Store configuration
└── ReduxProvider.tsx     # Provider component
```

## Key Features

1. **Centralized State Management**
   - Redux store with proper TypeScript typing
   - Typed hooks for accessing state and dispatching actions
   - Proper middleware configuration

2. **API Integration with RTK Query**
   - Automatic caching and invalidation
   - Loading and error states
   - Optimistic updates
   - Offline support with request queueing

3. **Authentication**
   - Token-based authentication
   - Automatic token refresh
   - Persistent authentication state

4. **TypeScript Support**
   - Full TypeScript interfaces for all API requests and responses
   - Type-safe Redux state and actions

## Usage Examples

### Accessing Redux State

```tsx
import { useAppSelector } from '../store';

const MyComponent = () => {
  const { user, isAuthenticated } = useAppSelector((state) => state.auth);
  
  return (
    <View>
      {isAuthenticated ? (
        <Text>Welcome, {user.fullName}</Text>
      ) : (
        <Text>Please sign in</Text>
      )}
    </View>
  );
};
```

### Using RTK Query Hooks

```tsx
import { useGetJobsQuery } from '../store/api/jobsApi';

const JobsList = () => {
  const { data, error, isLoading, refetch } = useGetJobsQuery({
    limit: 10,
    offset: 0,
    sortBy: 'createdAt',
    sortOrder: 'desc',
  });
  
  if (isLoading) {
    return <Loading />;
  }
  
  if (error) {
    return (
      <View>
        <Text>Error loading jobs</Text>
        <Button title="Retry" onPress={refetch} />
      </View>
    );
  }
  
  return (
    <FlatList
      data={data.jobs}
      renderItem={({ item }) => <JobCard job={item} />}
      keyExtractor={(item) => item.id}
    />
  );
};
```

### Mutations with RTK Query

```tsx
import { useSaveJobMutation } from '../store/api/jobsApi';

const SaveButton = ({ jobId }) => {
  const [saveJob, { isLoading }] = useSaveJobMutation();
  
  const handleSave = async () => {
    try {
      await saveJob(jobId).unwrap();
      Alert.alert('Success', 'Job saved successfully');
    } catch (error) {
      Alert.alert('Error', 'Failed to save job');
    }
  };
  
  return (
    <Button
      title={isLoading ? 'Saving...' : 'Save Job'}
      onPress={handleSave}
      disabled={isLoading}
    />
  );
};
```

## Authentication Flow

1. User signs in using `useLoginMutation` or `useVerifyOtpMutation`
2. The auth slice stores the user data and tokens
3. The tokens are persisted in AsyncStorage
4. The API slice automatically includes the token in all requests
5. If a token expires, the API slice handles refreshing it

## Offline Support

1. The API slice checks if the device is online before making requests
2. For GET requests, it returns cached data if available
3. For mutation requests, it queues them for later execution
4. When the device comes back online, queued requests are executed

## Error Handling

1. All API errors are properly typed and handled
2. Components can access error details through the RTK Query hooks
3. The API slice provides a `handleApiError` utility for consistent error handling
