import React, { useEffect } from 'react';
import { Provider } from 'react-redux';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { store } from './index';
import { loadStoredAuth } from './slices/authSlice';
import STORAGE_KEYS from '../constants/storage-keys';
import AuthRTKProvider from '../providers/AuthRTKProvider';

interface ReduxProviderProps {
  children: React.ReactNode;
}

/**
 * Redux Provider component that wraps the app and loads stored auth data
 */
export const ReduxProvider: React.FC<ReduxProviderProps> = ({ children }) => {
  useEffect(() => {
    // Load stored auth data on app start
    const loadAuthData = async () => {
      try {
        const [userString, token, refreshToken, onboardingCompleted] = await AsyncStorage.multiGet([
          STORAGE_KEYS.USER_DATA,
          STORAGE_KEYS.AUTH_TOKEN,
          STORAGE_KEYS.AUTH_REFRESH_TOKEN,
          STORAGE_KEYS.ONBOARDING_COMPLETED,
        ]);

        store.dispatch(
          loadStoredAuth({
            user: userString[1] ? JSON.parse(userString[1]) : null,
            token: token[1],
            refreshToken: refreshToken[1],
            hasCompletedOnboarding: onboardingCompleted[1] === 'true',
          })
        );
      } catch (error) {
        console.error('Error loading stored auth data:', error);
        store.dispatch(
          loadStoredAuth({
            user: null,
            token: null,
            refreshToken: null,
            hasCompletedOnboarding: false,
          })
        );
      }
    };

    loadAuthData();
  }, []);

  return (
    <Provider store={store}>
      <AuthRTKProvider>{children}</AuthRTKProvider>
    </Provider>
  );
};

export default ReduxProvider;
