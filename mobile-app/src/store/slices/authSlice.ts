import { createSlice, PayloadAction } from '@reduxjs/toolkit';
import AsyncStorage from '@react-native-async-storage/async-storage';
import type { User } from '../../types/api';
import STORAGE_KEYS from '../../constants/storage-keys';

interface AuthState {
  user: User | null;
  token: string | null;
  refreshToken: string | null;
  isAuthenticated: boolean;
  isLoading: boolean;
  hasCompletedOnboarding: boolean;
}

const initialState: AuthState = {
  user: null,
  token: null,
  refreshToken: null,
  isAuthenticated: false,
  isLoading: true,
  hasCompletedOnboarding: false,
};

/**
 * Auth slice for managing authentication state
 */
const authSlice = createSlice({
  name: 'auth',
  initialState,
  reducers: {
    setCredentials: (
      state,
      action: PayloadAction<{
        user: User;
        token: string;
        refreshToken: string;
      }>
    ) => {
      const { user, token, refreshToken } = action.payload;
      state.user = user;
      state.token = token;
      state.refreshToken = refreshToken;
      state.isAuthenticated = true;

      // Save to AsyncStorage
      AsyncStorage.multiSet([
        [STORAGE_KEYS.USER_DATA, JSON.stringify(user)],
        [STORAGE_KEYS.AUTH_TOKEN, token],
        [STORAGE_KEYS.AUTH_REFRESH_TOKEN, refreshToken],
      ]);
    },
    updateUser: (state, action: PayloadAction<Partial<User>>) => {
      if (state.user) {
        state.user = { ...state.user, ...action.payload };

        // Update user in AsyncStorage
        AsyncStorage.setItem(STORAGE_KEYS.USER_DATA, JSON.stringify(state.user));
      }
    },
    setOnboardingCompleted: (state, action: PayloadAction<boolean>) => {
      state.hasCompletedOnboarding = action.payload;

      // Save to AsyncStorage
      AsyncStorage.setItem(STORAGE_KEYS.ONBOARDING_COMPLETED, action.payload ? 'true' : 'false');
    },
    setLoading: (state, action: PayloadAction<boolean>) => {
      state.isLoading = action.payload;
    },
    loadStoredAuth: (
      state,
      action: PayloadAction<{
        user: User | null;
        token: string | null;
        refreshToken: string | null;
        hasCompletedOnboarding: boolean;
      }>
    ) => {
      const { user, token, refreshToken, hasCompletedOnboarding } = action.payload;
      state.user = user;
      state.token = token;
      state.refreshToken = refreshToken;
      state.isAuthenticated = !!token && !!user;
      state.hasCompletedOnboarding = hasCompletedOnboarding;
      state.isLoading = false;
    },
    logout: (state) => {
      // Clear state
      state.user = null;
      state.token = null;
      state.refreshToken = null;
      state.isAuthenticated = false;

      // Clear AsyncStorage
      AsyncStorage.multiRemove([
        STORAGE_KEYS.USER_DATA,
        STORAGE_KEYS.AUTH_TOKEN,
        STORAGE_KEYS.AUTH_REFRESH_TOKEN,
      ]);
    },
  },
});

export const {
  setCredentials,
  updateUser,
  setOnboardingCompleted,
  setLoading,
  loadStoredAuth,
  logout,
} = authSlice.actions;

export default authSlice.reducer;
