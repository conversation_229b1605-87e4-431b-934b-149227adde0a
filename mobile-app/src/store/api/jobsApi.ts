import { api } from './apiSlice';
import type {
  Job,
  JobsResponse,
  JobsResponseData,
  JobsQueryParams,
  ApiResponse,
  JobStatus,
} from '../../types/api';
import LocationService from '../../services/LocationService';

/**
 * Extended job query parameters with location options
 */
export interface ExtendedJobsQueryParams extends JobsQueryParams {
  useLocation?: boolean;
  maxDistance?: number;
}

/**
 * Job report request interface
 */
export interface JobReportRequest {
  jobId: string;
  reason: string;
  description: string;
}

/**
 * Job application status counts interface
 */
export interface JobApplicationStats {
  total: number;
  pending: number;
  shortlisted: number;
  rejected: number;
  hired: number;
  withdrawn: number;
}

/**
 * Jobs API endpoints using RTK Query
 */
export const jobsApi = api.injectEndpoints({
  endpoints: (builder) => ({
    /**
     * Get jobs with optional filtering and location-based search
     */
    getJobs: builder.query<JobsResponse, ExtendedJobsQueryParams | void>({
      query: (params = {}) => ({
        url: '/jobs',
        params: {
          ...params,
        },
      }),

      // providesTags: (result) =>
      //   result?.data
      //     ? [
      //         ...result.data.jobs.map(({ id }) => ({ type: 'Job' as const, id })),
      //         { type: 'Jobs' as const, id: 'LIST' },
      //       ]
      //     : [{ type: 'Jobs' as const, id: 'LIST' }],
      // async onQueryStarted(params = {}, { queryFulfilled, dispatch }) {
      //   try {
      //     // If useLocation is true, add location parameters
      //     if (params.useLocation) {
      //       try {
      //         const location = await LocationService.getCurrentLocation();

      //         if (location) {
      //           const { latitude, longitude } = location.coords;

      //           // Refetch with location parameters
      //           dispatch(
      //             jobsApi.endpoints.getJobs.initiate({
      //               ...params,
      //               latitude,
      //               longitude,
      //               maxDistance: params.maxDistance || 10,
      //               useLocation: undefined, // Remove this flag as it's not a backend parameter
      //             })
      //           );

      //           // Don't await the original query since we're replacing it
      //           return;
      //         }
      //       } catch (locationError) {
      //         console.error('Error getting location for jobs query:', locationError);
      //         // Continue with the original query if location fails
      //       }
      //     }

      //     // Wait for the original query to complete if we didn't replace it
      //     await queryFulfilled;
      //   } catch (error) {
      //     console.error('Error in jobs query:', error);
      //   }
      // },
    }),

    getJobById: builder.query<ApiResponse<Job>, string>({
      query: (id) => `/jobs/${id}`,
      providesTags: (_result, _error, id) => [{ type: 'Job', id }],
    }),

    /**
     * Get recommended jobs based on user profile and preferences
     */
    getRecommendedJobs: builder.query<JobsResponse, ExtendedJobsQueryParams | void>({
      query: (params = {}) => ({
        url: '/jobs/recommended',
        params: {
          ...params,
          useLocation: undefined, // Remove this flag as it's not a backend parameter
        },
      }),

      providesTags: [{ type: 'Jobs', id: 'RECOMMENDED' }],
      async onQueryStarted(params, { queryFulfilled, dispatch }) {
        try {
          // If useLocation is true, add location parameters
          if (params && 'useLocation' in params && params.useLocation) {
            try {
              const location = await LocationService.getCurrentLocation();

              if (location) {
                const { latitude, longitude } = location.coords;

                // Refetch with location parameters
                dispatch(
                  jobsApi.endpoints.getRecommendedJobs.initiate({
                    ...params,
                    latitude,
                    longitude,
                    maxDistance: params.maxDistance || 10,
                    useLocation: undefined, // Remove this flag as it's not a backend parameter
                  } as ExtendedJobsQueryParams)
                );

                // Don't await the original query since we're replacing it
                return;
              }
            } catch (locationError) {
              console.error('Error getting location for recommended jobs query:', locationError);
              // Continue with the original query if location fails
            }
          }

          // Wait for the original query to complete if we didn't replace it
          await queryFulfilled;
        } catch (error) {
          console.error('Error in recommended jobs query:', error);
        }
      },
    }),

    /**
     * Get nearby jobs based on user's current location
     */
    getNearbyJobs: builder.query<
      JobsResponse,
      {
        limit?: number;
        offset?: number;
        maxDistance?: number;
        sortBy?: string;
        sortOrder?: 'asc' | 'desc';
      }
    >({
      query: (params) => ({
        url: '/jobs/nearby',
        params: {
          ...params,
          // Default parameters will be added in the component before calling this endpoint
        },
      }),
      providesTags: [{ type: 'Jobs', id: 'NEARBY' }],
      // Cache for a shorter time since location-based data should be refreshed frequently
      keepUnusedDataFor: 300, // 5 minutes in seconds
    }),

    /**
     * Get all job categories
     */
    getJobCategories: builder.query<ApiResponse<{ id: string; name: string }[]>, void>({
      query: () => '/jobs/categories',

      providesTags: [{ type: 'Jobs', id: 'CATEGORIES' }],
      // Cache for a longer time as categories don't change often
      keepUnusedDataFor: 3600, // 1 hour in seconds
    }),

    /**
     * Get emergency jobs that need immediate attention
     */
    getEmergencyJobs: builder.query<JobsResponse, { limit?: number; offset?: number }>({
      query: (params) => ({
        url: '/jobs/emergency',
        params,
      }),

      providesTags: [{ type: 'Jobs', id: 'EMERGENCY' }],
    }),

    /**
     * Save a job to user's favorites
     */
    saveJob: builder.mutation<ApiResponse<{ success: boolean }>, string>({
      query: (jobId) => ({
        url: `/jobs/${jobId}/save`,
        method: 'POST',
      }),
      invalidatesTags: (_result, _error, jobId) => [
        { type: 'SavedJobs', id: 'LIST' },
        { type: 'SavedJobs', id: jobId },
      ],
      // Optimistic update
      async onQueryStarted(jobId, { dispatch, queryFulfilled }) {
        // Update isJobSaved cache optimistically
        const patchResult = dispatch(
          jobsApi.util.updateQueryData('isJobSaved', jobId, (draft) => {
            if (draft && draft.data) {
              draft.data.saved = true;
            }
          })
        );

        try {
          await queryFulfilled;
        } catch {
          // If the mutation fails, undo the optimistic update
          patchResult.undo();
        }
      },
    }),

    /**
     * Remove a job from user's favorites
     */
    unsaveJob: builder.mutation<ApiResponse<{ success: boolean }>, string>({
      query: (jobId) => ({
        url: `/jobs/${jobId}/save`,
        method: 'DELETE',
      }),
      invalidatesTags: (_result, _error, jobId) => [
        { type: 'SavedJobs', id: 'LIST' },
        { type: 'SavedJobs', id: jobId },
      ],
      // Optimistic update
      async onQueryStarted(jobId, { dispatch, queryFulfilled }) {
        // Update isJobSaved cache optimistically
        const patchResult = dispatch(
          jobsApi.util.updateQueryData('isJobSaved', jobId, (draft) => {
            if (draft && draft.data) {
              draft.data.saved = false;
            }
          })
        );

        try {
          await queryFulfilled;
        } catch {
          // If the mutation fails, undo the optimistic update
          patchResult.undo();
        }
      },
    }),

    /**
     * Get user's saved/favorite jobs
     */
    getSavedJobs: builder.query<
      JobsResponse,
      { limit?: number; offset?: number; sortBy?: string; sortOrder?: 'asc' | 'desc' } | void
    >({
      query: (params) => ({
        url: '/jobs/favorites',
        params: { ...params },
      }),

      providesTags: [{ type: 'SavedJobs', id: 'LIST' }],
    }),

    /**
     * Check if a job is saved by the user
     */
    isJobSaved: builder.query<ApiResponse<{ saved: boolean }>, string>({
      query: (jobId) => `/jobs/${jobId}/is-saved`,

      providesTags: (_result, _error, jobId) => [{ type: 'SavedJobs', id: jobId }],
    }),

    /**
     * Report a job for inappropriate content or other issues
     */
    reportJob: builder.mutation<ApiResponse<{ success: boolean }>, JobReportRequest>({
      query: ({ jobId, ...data }) => ({
        url: `/jobs/${jobId}/report`,
        method: 'POST',
        body: data,
      }),
    }),

    /**
     * Get application statistics for a job
     */
    getJobApplicationStats: builder.query<ApiResponse<JobApplicationStats>, string>({
      query: (jobId) => `/jobs/${jobId}/application-stats`,

      providesTags: (_result, _error, jobId) => [
        { type: 'Job', id: jobId },
        { type: 'Applications', id: 'LIST' },
      ],
    }),
  }),
  overrideExisting: false,
});

export const {
  useGetJobsQuery,
  useGetJobByIdQuery,
  useGetRecommendedJobsQuery,
  useGetNearbyJobsQuery,
  useGetJobCategoriesQuery,
  useGetEmergencyJobsQuery,
  useSaveJobMutation,
  useUnsaveJobMutation,
  useGetSavedJobsQuery,
  useIsJobSavedQuery,
  useReportJobMutation,
  useGetJobApplicationStatsQuery,
} = jobsApi;
