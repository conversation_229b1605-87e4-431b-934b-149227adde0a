import { api } from './apiSlice';
import { Badge, Level, LevelProgress, Achievement } from '../../types/api';

/**
 * Gamification API endpoints using RTK Query
 */
export const gamificationApi = api.injectEndpoints({
  endpoints: (builder) => ({
    getMyBadges: builder.query<{ badges: Badge[] }, void>({
      query: () => '/gamification/badges',
      providesTags: ['User'],
    }),

    getAllBadges: builder.query<{ badges: Badge[] }, void>({
      query: () => '/gamification/badges/all',
      providesTags: ['User'],
    }),

    getBadgeById: builder.query<Badge, string>({
      query: (id) => `/gamification/badges/${id}`,
      providesTags: (result, error, id) => [{ type: 'User', id }],
    }),

    getMyLevel: builder.query<Level, void>({
      query: () => '/gamification/level',
      providesTags: ['User'],
    }),

    getAllLevels: builder.query<{ levels: Level[] }, void>({
      query: () => '/gamification/levels',
      providesTags: ['User'],
    }),

    getLevelProgress: builder.query<LevelProgress, void>({
      query: () => '/gamification/level/progress',
      providesTags: ['User'],
    }),

    getMyAchievements: builder.query<{ achievements: Achievement[] }, void>({
      query: () => '/gamification/achievements',
      providesTags: ['User'],
    }),
  }),
  overrideExisting: false,
});

export const {
  useGetMyBadgesQuery,
  useGetAllBadgesQuery,
  useGetBadgeByIdQuery,
  useGetMyLevelQuery,
  useGetAllLevelsQuery,
  useGetLevelProgressQuery,
  useGetMyAchievementsQuery,
} = gamificationApi;
