import { api } from './apiSlice';
import type { Company, CompaniesResponse, CompaniesQueryParams } from '../../types/api';

/**
 * Companies API endpoints using RTK Query
 */
export const companiesApi = api.injectEndpoints({
  endpoints: (builder) => ({
    /**
     * Get companies with optional filtering
     */
    getCompanies: builder.query<CompaniesResponse, CompaniesQueryParams | void>({
      query: (params = {}) => ({
        url: '/companies',
        params,
      }),
      providesTags: (result) =>
        result
          ? [
              ...result.companies.map(({ id }) => ({ type: 'Company' as const, id })),
              { type: 'Companies' as const, id: 'LIST' },
            ]
          : [{ type: 'Companies' as const, id: 'LIST' }],
    }),

    /**
     * Get company by ID
     */
    getCompanyById: builder.query<Company, string>({
      query: (id) => `/companies/${id}`,
      providesTags: (result, error, id) => [{ type: 'Company', id }],
    }),

    /**
     * Get company profile for the authenticated user
     */
    getCompanyProfile: builder.query<Company, void>({
      query: () => '/companies/profile',
      providesTags: ['CompanyProfile'],
    }),

    /**
     * Update company profile
     */
    updateCompanyProfile: builder.mutation<
      Company,
      {
        name?: string;
        registrationNumber?: string;
        taxId?: string;
        website?: string;
        size?: string;
        industry?: string;
        description?: string;
        address?: string;
        city?: string;
        state?: string;
        country?: string;
        postalCode?: string;
      }
    >({
      query: (data) => ({
        url: '/companies/profile',
        method: 'PATCH',
        body: data,
      }),
      invalidatesTags: ['CompanyProfile'],
    }),

    /**
     * Update company KYC information
     */
    updateCompanyKyc: builder.mutation<
      Company,
      {
        registrationNumber: string;
        taxId: string;
        notes?: string;
      }
    >({
      query: (data) => ({
        url: '/companies/kyc',
        method: 'PATCH',
        body: data,
      }),
      invalidatesTags: ['CompanyProfile'],
    }),

    /**
     * Upload company document
     */
    uploadCompanyDocument: builder.mutation<
      { id: string; documentType: string; documentUrl: string; verificationStatus: string },
      FormData
    >({
      query: (data) => ({
        url: '/companies/documents',
        method: 'POST',
        body: data,
        formData: true,
      }),
      invalidatesTags: ['CompanyDocuments'],
    }),

    /**
     * Get company documents
     */
    getCompanyDocuments: builder.query<
      Array<{
        id: string;
        documentType: string;
        documentUrl: string;
        documentNumber?: string;
        verificationStatus: string;
        rejectionReason?: string;
        verifiedAt?: string;
      }>,
      void
    >({
      query: () => '/companies/documents',
      providesTags: ['CompanyDocuments'],
    }),

    /**
     * Get company jobs
     */
    getCompanyJobs: builder.query<
      {
        data: Array<{
          id: string;
          title: string;
          status: string;
          applicationsCount: number;
          createdAt: string;
        }>;
        meta: {
          total: number;
          page: number;
          limit: number;
          totalPages: number;
        };
      },
      {
        status?: string;
        page?: number;
        limit?: number;
        search?: string;
      }
    >({
      query: (params) => ({
        url: '/jobs/company',
        params,
      }),
      providesTags: ['CompanyJobs'],
    }),

    /**
     * Get company analytics
     */
    getCompanyAnalytics: builder.query<
      {
        jobsPosted: number;
        activeJobs: number;
        completedJobs: number;
        totalApplications: number;
        totalHired: number;
        totalSpent: number;
        averageRating: number;
        reviewsCount: number;
        jobsPerMonth: Array<{ month: string; count: number }>;
        applicationsPerMonth: Array<{ month: string; count: number }>;
      },
      void
    >({
      query: () => '/analytics/company',
      providesTags: ['CompanyAnalytics'],
    }),
  }),
  overrideExisting: false,
});

export const {
  useGetCompaniesQuery,
  useGetCompanyByIdQuery,
  useGetCompanyProfileQuery,
  useUpdateCompanyProfileMutation,
  useUpdateCompanyKycMutation,
  useUploadCompanyDocumentMutation,
  useGetCompanyDocumentsQuery,
  useGetCompanyJobsQuery,
  useGetCompanyAnalyticsQuery,
} = companiesApi;
