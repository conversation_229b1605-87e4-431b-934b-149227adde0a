import { api } from './apiSlice';
import {
  User,
  Job,
  Application,
  Dispute,
  DisputesResponse,
  DisputeStatus,
  DisputeType,
} from '../../types/api';

interface CreateDisputeRequest {
  title: string;
  description: string;
  type: DisputeType;
  jobId?: string;
  applicationId?: string;
  attachments?: FormData;
}

interface DisputeComment {
  id: string;
  disputeId: string;
  user: User;
  message: string;
  attachments?: string[];
  createdAt: string;
}

interface DisputeCommentsResponse {
  comments: DisputeComment[];
  total: number;
}

/**
 * Disputes API endpoints using RTK Query
 */
export const disputesApi = api.injectEndpoints({
  endpoints: (builder) => ({
    getMyDisputes: builder.query<
      DisputesResponse,
      { status?: DisputeStatus; limit?: number; offset?: number }
    >({
      query: (params) => ({
        url: '/disputes',
        params,
      }),
      providesTags: (result) =>
        result
          ? [
              ...result.disputes.map(({ id }) => ({ type: 'Dispute' as const, id })),
              { type: 'Disputes' as const, id: 'LIST' },
            ]
          : [{ type: 'Disputes' as const, id: 'LIST' }],
    }),

    getDisputeById: builder.query<Dispute, string>({
      query: (id) => `/disputes/${id}`,
      providesTags: (result, error, id) => [{ type: 'Dispute', id }],
    }),

    createDispute: builder.mutation<Dispute, CreateDisputeRequest>({
      query: (data) => {
        const { attachments, ...rest } = data;

        if (attachments) {
          return {
            url: '/disputes',
            method: 'POST',
            body: attachments,
            formData: true,
            params: rest,
          };
        }

        return {
          url: '/disputes',
          method: 'POST',
          body: rest,
        };
      },
      invalidatesTags: [{ type: 'Disputes', id: 'LIST' }],
    }),

    updateDispute: builder.mutation<
      Dispute,
      { id: string; status?: DisputeStatus; resolution?: string }
    >({
      query: ({ id, ...data }) => ({
        url: `/disputes/${id}`,
        method: 'PATCH',
        body: data,
      }),
      invalidatesTags: (result, error, { id }) => [
        { type: 'Dispute', id },
        { type: 'Disputes', id: 'LIST' },
      ],
    }),

    getDisputeComments: builder.query<DisputeCommentsResponse, string>({
      query: (disputeId) => `/disputes/${disputeId}/comments`,
      providesTags: (result, error, disputeId) => [{ type: 'DisputeComments', id: disputeId }],
    }),

    addDisputeComment: builder.mutation<
      DisputeComment,
      { disputeId: string; message: string; attachments?: FormData }
    >({
      query: ({ disputeId, message, attachments }) => {
        if (attachments) {
          // Add message to form data
          attachments.append('message', message);

          return {
            url: `/disputes/${disputeId}/comments`,
            method: 'POST',
            body: attachments,
            formData: true,
          };
        }

        return {
          url: `/disputes/${disputeId}/comments`,
          method: 'POST',
          body: { message },
        };
      },
      invalidatesTags: (result, error, { disputeId }) => [
        { type: 'DisputeComments', id: disputeId },
      ],
    }),
  }),
  overrideExisting: false,
});

export const {
  useGetMyDisputesQuery,
  useGetDisputeByIdQuery,
  useCreateDisputeMutation,
  useUpdateDisputeMutation,
  useGetDisputeCommentsQuery,
  useAddDisputeCommentMutation,
} = disputesApi;
