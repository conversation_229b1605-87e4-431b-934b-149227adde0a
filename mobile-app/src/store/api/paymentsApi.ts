import { api } from './apiSlice';
import type { User, Job } from '../../types/api';

/**
 * Time range type for earnings
 */
export type TimeRange = 'week' | 'month' | 'year';

/**
 * Earnings interface
 */
export interface Earnings {
  summary: {
    totalEarnings: number;
    jobsCompleted: number;
    platformFee: number;
    platformCommission: number;
    fixedFee: number;
    netEarnings: number;
  };
  payouts: Array<{
    id: string;
    job: {
      id: string;
      title: string;
    };
    grossPay: number;
    commission: number;
    netPay: number;
    status: 'pending' | 'processing' | 'completed';
    transactionId?: string;
    createdAt: string;
  }>;
}

/**
 * Payment status type
 */
export type PaymentStatus = 'pending' | 'processing' | 'completed' | 'failed';

/**
 * Payment method type
 */
export type PaymentMethod = 'bank_transfer' | 'upi' | 'wallet' | 'card' | 'razorpay';

/**
 * Payment interface
 */
export interface Payment {
  id: string;
  jobId: string;
  job?: Job;
  workerId: string;
  worker?: User;
  companyId: string;
  company?: User;
  amount: number;
  commission: number;
  netAmount: number;
  status: PaymentStatus;
  paymentMethod?: PaymentMethod;
  transactionId?: string;
  paymentDate?: string;
  createdAt: string;
  updatedAt: string;
}

/**
 * Payments response interface
 */
export interface PaymentsResponse {
  payments: Payment[];
  total: number;
  limit: number;
  offset: number;
}

/**
 * Payment summary interface
 */
export interface PaymentSummary {
  totalEarnings: number;
  pendingPayments: number;
  completedPayments: number;
  thisMonthEarnings: number;
  lastMonthEarnings: number;
}

/**
 * Payment method interface
 */
export interface PaymentMethodDetails {
  id: string;
  type: PaymentMethod;
  isDefault: boolean;
  details: {
    accountNumber?: string;
    bankName?: string;
    ifscCode?: string;
    upiId?: string;
    cardNumber?: string;
    cardExpiry?: string;
    walletId?: string;
  };
  createdAt: string;
}

/**
 * Add payment method request interface
 */
export interface AddPaymentMethodRequest {
  type: PaymentMethod;
  isDefault?: boolean;
  details: {
    accountNumber?: string;
    bankName?: string;
    ifscCode?: string;
    upiId?: string;
    cardNumber?: string;
    cardExpiry?: string;
    walletId?: string;
  };
}

/**
 * New unified payment types
 */
export interface UnifiedPaymentRequest {
  amount: number;
  currency: string;
  description?: string;
  preferredGateway?: 'stripe' | 'razorpay';
  paymentMethodId?: string;
  customerId?: string;
  customerEmail?: string;
  customerPhone?: string;
  orderId?: string;
  metadata?: Record<string, unknown>;
}

export interface PaymentResult {
  success: boolean;
  transactionId?: string;
  orderId?: string;
  status: string;
  message?: string;
  gateway: string;
  paymentMethod: string;
  clientSecret?: string;
  redirectUrl?: string;
  gatewayResponse?: Record<string, unknown>;
}

export interface RefundRequest {
  transactionId: string;
  amount?: number;
  reason?: string;
  metadata?: Record<string, unknown>;
}

export interface RefundResult {
  success: boolean;
  refundId: string;
  amount: number;
  status: 'pending' | 'succeeded' | 'failed';
  gateway: string;
  gatewayResponse?: Record<string, unknown>;
}

export interface PaymentGatewayConfig {
  gateway: string;
  enabled: boolean;
  supportedCurrencies: string[];
  supportedMethods: string[];
  defaultCurrency: string;
}

export interface AvailablePaymentMethods {
  currency: string;
  methods: string[];
}

/**
 * Payments API endpoints using RTK Query
 */
export const paymentsApi = api.injectEndpoints({
  endpoints: (builder) => ({
    // New unified payment endpoints
    processUnifiedPayment: builder.mutation<PaymentResult, UnifiedPaymentRequest>({
      query: (data) => ({
        url: '/payments/process',
        method: 'POST',
        body: data,
      }),
      invalidatesTags: ['Payments'],
    }),

    createUnifiedPaymentIntent: builder.mutation<PaymentResult, UnifiedPaymentRequest>({
      query: (data) => ({
        url: '/payments/intent',
        method: 'POST',
        body: data,
      }),
    }),

    refundPayment: builder.mutation<RefundResult, RefundRequest>({
      query: (data) => ({
        url: '/payments/refund',
        method: 'POST',
        body: data,
      }),
      invalidatesTags: ['Payments'],
    }),

    getGatewayConfigurations: builder.query<PaymentGatewayConfig[], void>({
      query: () => '/payments/gateways',
    }),

    getAvailablePaymentMethods: builder.query<AvailablePaymentMethods, string>({
      query: (currency) => ({
        url: '/payments/methods/available',
        params: { currency },
      }),
    }),

    // Existing endpoints
    getPayments: builder.query<
      PaymentsResponse,
      { status?: PaymentStatus; limit?: number; offset?: number }
    >({
      query: (params) => ({
        url: '/payments',
        params,
      }),
      providesTags: (result) =>
        result
          ? [
              ...result.payments.map(({ id }) => ({ type: 'Payment' as const, id })),
              { type: 'Payments' as const, id: 'LIST' },
            ]
          : [{ type: 'Payments' as const, id: 'LIST' }],
    }),

    getPaymentById: builder.query<Payment, string>({
      query: (id) => `/payments/${id}`,
      providesTags: (result, error, id) => [{ type: 'Payment', id }],
    }),

    getPaymentSummary: builder.query<PaymentSummary, void>({
      query: () => '/payments/summary',
      providesTags: ['PaymentSummary'],
    }),

    getPaymentMethods: builder.query<PaymentMethodDetails[], void>({
      query: () => '/payments/methods',
      providesTags: ['PaymentMethods'],
    }),

    addPaymentMethod: builder.mutation<PaymentMethodDetails, AddPaymentMethodRequest>({
      query: (data) => ({
        url: '/payments/methods',
        method: 'POST',
        body: data,
      }),
      invalidatesTags: ['PaymentMethods'],
    }),

    updatePaymentMethod: builder.mutation<
      PaymentMethodDetails,
      { id: string; data: Partial<AddPaymentMethodRequest> }
    >({
      query: ({ id, data }) => ({
        url: `/payments/methods/${id}`,
        method: 'PATCH',
        body: data,
      }),
      invalidatesTags: ['PaymentMethods'],
    }),

    deletePaymentMethod: builder.mutation<{ success: boolean }, string>({
      query: (id) => ({
        url: `/payments/methods/${id}`,
        method: 'DELETE',
      }),
      invalidatesTags: ['PaymentMethods'],
    }),

    setDefaultPaymentMethod: builder.mutation<{ success: boolean }, string>({
      query: (id) => ({
        url: `/payments/methods/${id}/default`,
        method: 'POST',
      }),
      invalidatesTags: ['PaymentMethods'],
    }),

    getEarnings: builder.query<Earnings, TimeRange>({
      query: (timeRange) => `/payouts?timeRange=${timeRange}`,
      providesTags: ['Earnings'],
    }),

    // Razorpay specific endpoints
    createRazorpayOrder: builder.mutation<
      { success: boolean; transactionId: string; gatewayResponse: Record<string, unknown> },
      {
        amount: number;
        currency: string;
        description?: string;
        customerEmail?: string;
        customerPhone?: string;
        metadata?: Record<string, unknown>;
      }
    >({
      query: (data) => ({
        url: '/payments/razorpay/order',
        method: 'POST',
        body: data,
      }),
    }),

    processRazorpayPayment: builder.mutation<
      { success: boolean; transactionId: string; gatewayResponse: Record<string, unknown> },
      {
        amount: number;
        currency: string;
        description?: string;
        customerEmail?: string;
        customerPhone?: string;
        metadata?: Record<string, unknown>;
        orderId?: string;
      }
    >({
      query: (data) => ({
        url: '/payments/razorpay',
        method: 'POST',
        body: data,
      }),
      invalidatesTags: ['Payments'],
    }),

    verifyRazorpaySignature: builder.mutation<
      { verified: boolean },
      {
        orderId: string;
        paymentId: string;
        signature: string;
      }
    >({
      query: (data) => ({
        url: '/payments/razorpay/verify',
        method: 'POST',
        body: data,
      }),
    }),
  }),
  overrideExisting: false,
});

export const {
  // New unified hooks
  useProcessUnifiedPaymentMutation,
  useCreateUnifiedPaymentIntentMutation,
  useRefundPaymentMutation,
  useGetGatewayConfigurationsQuery,
  useGetAvailablePaymentMethodsQuery,

  // Existing hooks
  useGetPaymentsQuery,
  useGetPaymentByIdQuery,
  useGetPaymentSummaryQuery,
  useGetPaymentMethodsQuery,
  useAddPaymentMethodMutation,
  useUpdatePaymentMethodMutation,
  useDeletePaymentMethodMutation,
  useSetDefaultPaymentMethodMutation,
  useGetEarningsQuery,
  useCreateRazorpayOrderMutation,
  useProcessRazorpayPaymentMutation,
  useVerifyRazorpaySignatureMutation,
} = paymentsApi;
