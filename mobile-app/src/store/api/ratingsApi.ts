import { api } from './apiSlice';
import { Rating, RatingsResponse } from '../../types/api';

interface RatingSummary {
  averageRating: number;
  totalRatings: number;
  ratingCounts: {
    1: number;
    2: number;
    3: number;
    4: number;
    5: number;
  };
  categories?: {
    [key: string]: {
      averageRating: number;
      totalRatings: number;
    };
  };
}

interface CreateRatingRequest {
  jobId: string;
  userId: string;
  rating: number;
  comment?: string;
  categories?: {
    [key: string]: number;
  };
}

/**
 * Ratings API endpoints using RTK Query
 */
export const ratingsApi = api.injectEndpoints({
  endpoints: (builder) => ({
    getMyRatings: builder.query<RatingsResponse, { limit?: number; offset?: number }>({
      query: (params) => ({
        url: '/ratings/received',
        params,
      }),
      providesTags: [{ type: 'Rating', id: 'RECEIVED' }],
    }),

    getGivenRatings: builder.query<RatingsResponse, { limit?: number; offset?: number }>({
      query: (params) => ({
        url: '/ratings/given',
        params,
      }),
      providesTags: [{ type: 'Rating', id: 'GIVEN' }],
    }),

    getRatingsSummary: builder.query<RatingSummary, void>({
      query: () => '/ratings/summary',
      providesTags: [{ type: 'Rating', id: 'SUMMARY' }],
    }),

    createRating: builder.mutation<Rating, CreateRatingRequest>({
      query: (data) => ({
        url: '/ratings',
        method: 'POST',
        body: data,
      }),
      invalidatesTags: [
        { type: 'Rating', id: 'RECEIVED' },
        { type: 'Rating', id: 'GIVEN' },
        { type: 'Rating', id: 'SUMMARY' },
      ],
    }),

    getUserRatings: builder.query<
      RatingsResponse,
      { userId: string; limit?: number; offset?: number }
    >({
      query: ({ userId, ...params }) => ({
        url: `/ratings/user/${userId}`,
        params,
      }),
      providesTags: (_result, _error, { userId }) => [{ type: 'User', id: userId }],
    }),

    getJobRatings: builder.query<
      RatingsResponse,
      { jobId: string; limit?: number; offset?: number }
    >({
      query: ({ jobId, ...params }) => ({
        url: `/ratings/job/${jobId}`,
        params,
      }),
      providesTags: (_result, _error, { jobId }) => [{ type: 'Job', id: jobId }],
    }),
  }),
  overrideExisting: false,
});

export const {
  useGetMyRatingsQuery,
  useGetGivenRatingsQuery,
  useGetRatingsSummaryQuery,
  useCreateRatingMutation,
  useGetUserRatingsQuery,
  useGetJobRatingsQuery,
} = ratingsApi;
