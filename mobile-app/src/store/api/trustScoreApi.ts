import { api } from './apiSlice';
import {
  TrustScore,
  TrustScoreLogsResponse,
  TrustScoreBreakdown,
  TrustScoreSuggestions,
} from '../../types/api';

/**
 * Trust Score API endpoints using RTK Query
 */
export const trustScoreApi = api.injectEndpoints({
  endpoints: (builder) => ({
    getTrustScore: builder.query<TrustScore, void>({
      query: () => '/users/trust-score',
      providesTags: ['TrustScore'],
    }),

    getTrustScoreLogs: builder.query<TrustScoreLogsResponse, { limit?: number; offset?: number }>({
      query: (params) => ({
        url: '/users/trust-score/logs',
        params,
      }),
      providesTags: ['TrustScore'],
    }),

    getTrustScoreBreakdown: builder.query<TrustScoreBreakdown, void>({
      query: () => '/users/trust-score/breakdown',
      providesTags: ['TrustScore'],
    }),

    getTrustScoreImprovementSuggestions: builder.query<TrustScoreSuggestions, void>({
      query: () => '/users/trust-score/suggestions',
      providesTags: ['TrustScore'],
    }),
  }),
  overrideExisting: false,
});

export const {
  useGetTrustScoreQuery,
  useGetTrustScoreLogsQuery,
  useGetTrustScoreBreakdownQuery,
  useGetTrustScoreImprovementSuggestionsQuery,
} = trustScoreApi;
