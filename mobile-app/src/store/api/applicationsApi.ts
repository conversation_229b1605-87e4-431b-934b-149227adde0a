import { api } from './apiSlice';
import type {
  Application,
  ApplicationsResponse,
  ApplicationStatus,
  ApiResponse,
} from '../../types/api';

/**
 * Application filter parameters interface
 */
export interface ApplicationsQueryParams {
  limit?: number;
  offset?: number;
  status?: ApplicationStatus;
  sortBy?: 'createdAt' | 'updatedAt' | 'startDate';
  sortOrder?: 'asc' | 'desc';
  search?: string;
}

/**
 * Application creation request interface
 */
export interface ApplicationCreateRequest {
  jobId: string;
  coverLetter?: string;
  expectedSalary?: number;
  availability?: string;
  attachments?: File[];
}

/**
 * Application update request interface
 */
export interface ApplicationUpdateRequest {
  id: string;
  coverLetter?: string;
  expectedSalary?: number;
  availability?: string;
}

/**
 * Application withdrawal request interface
 */
export interface ApplicationWithdrawRequest {
  id: string;
  reason?: string;
}

/**
 * Application status response interface
 */
export interface ApplicationStatusResponse {
  hasApplied: boolean;
  application?: Application;
  status?: ApplicationStatus;
}

/**
 * Applications API endpoints using RTK Query
 */
export const applicationsApi = api.injectEndpoints({
  endpoints: (builder) => ({
    /**
     * Get all applications with optional filtering
     */
    getApplications: builder.query<ApplicationsResponse, ApplicationsQueryParams | void>({
      query: (params = {}) => ({
        url: '/applications',
        params,
      }),
      providesTags: (result) =>
        result
          ? [
              ...result.applications.map(({ id }) => ({
                type: 'Application' as const,
                id,
              })),
              { type: 'Applications' as const, id: 'LIST' },
            ]
          : [{ type: 'Applications' as const, id: 'LIST' }],
    }),

    /**
     * Get a specific application by ID
     */
    getApplicationById: builder.query<Application, string>({
      query: (id) => `/applications/${id}`,
      providesTags: (result, error, id) => [{ type: 'Application', id }],
    }),

    /**
     * Get applications for a specific job
     */
    getApplicationsByJobId: builder.query<
      ApplicationsResponse,
      { jobId: string } & Omit<ApplicationsQueryParams, 'search'>
    >({
      query: ({ jobId, ...params }) => ({
        url: `/jobs/${jobId}/applications`,
        params,
      }),
      providesTags: (result, error, { jobId }) =>
        result
          ? [
              ...result.applications.map(({ id }) => ({
                type: 'Application' as const,
                id,
              })),
              { type: 'Job' as const, id: jobId },
              { type: 'Applications' as const, id: 'LIST' },
            ]
          : [
              { type: 'Job' as const, id: jobId },
              { type: 'Applications' as const, id: 'LIST' },
            ],
    }),

    /**
     * Apply for a job
     */
    applyForJob: builder.mutation<Application, { jobId: string; formData: FormData }>({
      query: ({ jobId, formData }) => ({
        url: `/jobs/${jobId}/apply`,
        method: 'POST',
        body: formData,
        formData: true,
      }),
      invalidatesTags: (result, error, { jobId }) => [
        { type: 'Applications', id: 'LIST' },
        { type: 'Job', id: jobId },
        { type: 'Jobs', id: 'LIST' },
      ],
      // Optimistic update to show application immediately
      async onQueryStarted({ jobId }, { dispatch, queryFulfilled }) {
        try {
          const { data: newApplication } = await queryFulfilled;

          // Update the job applications count
          dispatch(
            api.util.updateQueryData('getJobById', jobId, (draft) => {
              if (draft) {
                draft.applicationsCount = (draft.applicationsCount || 0) + 1;
              }
            })
          );
        } catch {
          // If the mutation fails, the cache will be automatically restored
        }
      },
    }),

    /**
     * Withdraw an application
     */
    withdrawApplication: builder.mutation<
      { success: boolean; message: string },
      string | ApplicationWithdrawRequest
    >({
      query: (arg) => {
        const id = typeof arg === 'string' ? arg : arg.id;
        const body = typeof arg === 'string' ? {} : { reason: arg.reason };

        return {
          url: `/applications/${id}/withdraw`,
          method: 'POST',
          body,
        };
      },
      invalidatesTags: (result, error, arg) => {
        const id = typeof arg === 'string' ? arg : arg.id;
        return [
          { type: 'Application', id },
          { type: 'Applications', id: 'LIST' },
        ];
      },
      // Optimistic update to show withdrawal immediately
      async onQueryStarted(arg, { dispatch, queryFulfilled }) {
        const id = typeof arg === 'string' ? arg : arg.id;

        // Get the application to find the jobId
        const patchResult = dispatch(
          api.util.updateQueryData('getApplicationById', id, (draft) => {
            if (draft) {
              draft.status = 'withdrawn';
            }
          })
        );

        try {
          await queryFulfilled;
        } catch {
          // If the mutation fails, undo the optimistic update
          patchResult.undo();
        }
      },
    }),

    /**
     * Update an application
     */
    updateApplication: builder.mutation<Application, ApplicationUpdateRequest>({
      query: ({ id, ...data }) => ({
        url: `/applications/${id}`,
        method: 'PUT',
        body: data,
      }),
      invalidatesTags: (result, error, { id }) => [
        { type: 'Application', id },
        { type: 'Applications', id: 'LIST' },
      ],
    }),

    /**
     * Get application timeline
     */
    getApplicationTimeline: builder.query<
      { id: string; status: string; date: string; note: string }[],
      string
    >({
      query: (id) => `/applications/${id}/timeline`,
      providesTags: (result, error, id) => [{ type: 'Application', id }],
    }),

    /**
     * Check if the current user has applied to a specific job
     */
    getApplicationStatus: builder.query<ApiResponse<ApplicationStatusResponse>, string>({
      query: (jobId) => ({
        url: '/applications',
        params: { jobId, limit: 1 },
      }),
      transformResponse: (response: ApplicationsResponse) => {
        const applications = response.data?.applications || [];
        const hasApplied = applications.length > 0;
        const application = hasApplied ? applications[0] : undefined;

        return {
          success: true,
          data: {
            hasApplied,
            application,
            status: application?.status,
          },
          message: hasApplied ? 'Application found' : 'No application found',
        };
      },
      providesTags: (result, error, jobId) => [{ type: 'Application', id: `STATUS_${jobId}` }],
    }),
  }),
  overrideExisting: false,
});

export const {
  useGetApplicationsQuery,
  useGetApplicationByIdQuery,
  useGetApplicationsByJobIdQuery,
  useApplyForJobMutation,
  useWithdrawApplicationMutation,
  useUpdateApplicationMutation,
  useGetApplicationTimelineQuery,
  useGetApplicationStatusQuery,
} = applicationsApi;
