import { createApi, fetchBaseQuery } from '@reduxjs/toolkit/query/react';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { API_URL, API_TIMEOUT } from '../../config';
import type { RootState } from '../index';
import type { ApiError } from '../../types/api';
import STORAGE_KEYS from '../../constants/storage-keys';

/**
 * Tag types for cache invalidation
 */
export type ApiTagTypes =
  | 'User'
  | 'Job'
  | 'Jobs'
  | 'Application'
  | 'Applications'
  | 'Notification'
  | 'Notifications'
  | 'TrustScore'
  | 'SavedJobs'
  | 'Favorite'
  | 'Favorites'
  | 'Document'
  | 'Documents'
  | 'Dispute'
  | 'Disputes'
  | 'DisputeComments'
  | 'Rating'
  | 'Ratings'
  | 'Badge'
  | 'Badges'
  | 'Level'
  | 'Achievement'
  | 'Company'
  | 'Companies'
  | 'CompanyProfile'
  | 'CompanyDocuments'
  | 'CompanyJobs'
  | 'CompanyAnalytics';

/**
 * Base API slice using RTK Query
 * This handles all API requests with proper error handling and caching
 */
export const api = createApi({
  reducerPath: 'api',
  baseQuery: fetchBaseQuery({
    baseUrl: API_URL,
    timeout: API_TIMEOUT,
    prepareHeaders: async (headers, { getState }) => {
      // Try fetching token from state, otherwise AsyncStorage
      const state = getState() as RootState;
      const token =
        state.auth.token ||
        (await AsyncStorage.getItem(STORAGE_KEYS.AUTH_TOKEN)) ||
        (await AsyncStorage.getItem(STORAGE_KEYS.LEGACY_AUTH_TOKEN));

      if (token) {
        headers.set('Authorization', `Bearer ${token}`);
      }

      headers.set('Content-Type', 'application/json');
      return headers;
    },
  }),
  tagTypes: [
    'User',
    'Job',
    'Jobs',
    'Application',
    'Applications',
    'Notification',
    'Notifications',
    'TrustScore',
    'SavedJobs',
    'Favorite',
    'Favorites',
    'Document',
    'Documents',
    'Dispute',
    'Disputes',
    'DisputeComments',
    'Rating',
    'Ratings',
    'Badge',
    'Badges',
    'Level',
    'Achievement',
    'Company',
    'Companies',
    'CompanyProfile',
    'CompanyDocuments',
    'CompanyJobs',
    'CompanyAnalytics',
  ],
  endpoints: () => ({}),
});

/**
 * Custom error handler for API requests
 */
export const handleApiError = (error: unknown): ApiError => {
  if (error && typeof error === 'object' && 'data' in error) {
    // RTK Query error format
    return error.data as ApiError;
  }

  // Default error format
  return {
    statusCode: 500,
    message: error instanceof Error ? error.message : 'An unexpected error occurred',
    error: 'Internal Server Error',
  };
};
