import { api } from './apiSlice';
import type { User, UserRole } from '../../types/api';

/**
 * User profile update request interface
 */
export interface ProfileUpdateRequest {
  fullName?: string;
  email?: string;
  phone?: string;
  address?: string;
  city?: string;
  state?: string;
  country?: string;
  postalCode?: string;
  bio?: string;
  companyName?: string;
  companyRegistrationNumber?: string;
  companyTaxId?: string;
  companyWebsite?: string;
  companySize?: string;
  companyIndustry?: string;
}

/**
 * KYC verification status
 */
export type KycStatus = 'pending' | 'verified' | 'rejected' | 'not_submitted';

/**
 * KYC verification response interface
 */
export interface KycStatusResponse {
  status: KycStatus;
  message?: string;
  submittedAt?: string;
  verifiedAt?: string;
  rejectedAt?: string;
  rejectionReason?: string;
}

/**
 * Password change request interface
 */
export interface PasswordChangeRequest {
  currentPassword: string;
  newPassword: string;
}

/**
 * Public user profile interface
 */
export interface PublicUserProfile {
  id: string;
  fullName: string;
  profilePic?: string;
  bio?: string;
  location?: string;
  trustScore: number;
  role: UserRole;
  skills?: string[];
  joinedAt: string;
  completedJobs: number;
  ratings: {
    average: number;
    count: number;
  };
  badges?: Array<{
    id: string;
    name: string;
    icon: string;
  }>;
}

/**
 * User API endpoints using RTK Query
 */
export const userApi = api.injectEndpoints({
  endpoints: (builder) => ({
    /**
     * Get the current user's profile
     */
    getProfile: builder.query<{ success: boolean; message: string; data: User }, void>({
      query: () => '/users/profile',
      providesTags: ['User'],
    }),

    /**
     * Update the current user's profile
     */
    updateProfile: builder.mutation<User, ProfileUpdateRequest>({
      query: (data) => ({
        url: '/users/profile',
        method: 'PUT',
        body: data,
      }),
      invalidatesTags: ['User'],
      // Optimistic update
      async onQueryStarted(data, { dispatch, queryFulfilled }) {
        // Update the profile in the cache optimistically
        const patchResult = dispatch(
          userApi.util.updateQueryData('getProfile', undefined, (draft) => {
            Object.assign(draft, data);
          })
        );

        try {
          await queryFulfilled;
        } catch {
          // If the mutation fails, undo the optimistic update
          patchResult.undo();
        }
      },
    }),

    /**
     * Upload a profile picture
     */
    uploadProfilePicture: builder.mutation<{ profilePic: string }, FormData>({
      query: (formData) => ({
        url: '/users/profile/picture',
        method: 'POST',
        body: formData,
        formData: true,
      }),
      invalidatesTags: ['User'],
    }),

    /**
     * Submit KYC verification documents
     */
    verifyKyc: builder.mutation<{ success: boolean; message: string }, FormData>({
      query: (formData) => ({
        url: '/users/kyc/verify',
        method: 'POST',
        body: formData,
        formData: true,
      }),
      invalidatesTags: ['User'],
    }),

    /**
     * Get KYC verification status
     */
    getKycStatus: builder.query<KycStatusResponse, void>({
      query: () => '/users/kyc/status',
      providesTags: ['User'],
    }),

    /**
     * Delete user account
     */
    deleteAccount: builder.mutation<{ success: boolean; message: string }, void>({
      query: () => ({
        url: '/users/account',
        method: 'DELETE',
      }),
    }),

    /**
     * Change user password
     */
    changePassword: builder.mutation<{ success: boolean; message: string }, PasswordChangeRequest>({
      query: (data) => ({
        url: '/users/password',
        method: 'PUT',
        body: data,
      }),
    }),

    /**
     * Get user by ID (for admin or internal use)
     */
    getUserById: builder.query<User, string>({
      query: (id) => `/users/${id}`,
      providesTags: (_result, _error, id) => [{ type: 'User', id }],
    }),

    /**
     * Get public profile of a user
     */
    getPublicProfile: builder.query<PublicUserProfile, string>({
      query: (id) => `/users/${id}/public`,
      providesTags: (_result, _error, id) => [{ type: 'User', id }],
    }),

    /**
     * Get user skills
     */
    getUserSkills: builder.query<string[], void>({
      query: () => '/users/skills',
      providesTags: ['User'],
    }),

    /**
     * Update user skills
     */
    updateUserSkills: builder.mutation<{ success: boolean }, string[]>({
      query: (skills) => ({
        url: '/users/skills',
        method: 'PUT',
        body: { skills },
      }),
      invalidatesTags: ['User'],
    }),

    /**
     * Get user statistics (completed jobs, earnings, etc.)
     */
    getUserStats: builder.query<
      {
        completedJobs: number;
        totalEarnings: number;
        averageRating: number;
        totalRatings: number;
        activeApplications: number;
        savedJobs: number;
      },
      void
    >({
      query: () => '/users/stats',
      providesTags: ['User'],
    }),
  }),
  overrideExisting: false,
});

export const {
  useGetProfileQuery,
  useUpdateProfileMutation,
  useUploadProfilePictureMutation,
  useVerifyKycMutation,
  useGetKycStatusQuery,
  useDeleteAccountMutation,
  useChangePasswordMutation,
  useGetUserByIdQuery,
  useGetPublicProfileQuery,
  useGetUserSkillsQuery,
  useUpdateUserSkillsMutation,
  useGetUserStatsQuery,
} = userApi;
