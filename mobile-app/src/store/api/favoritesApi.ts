import { api } from './apiSlice';
import type { Job, ApiResponse } from '../../types/api';

/**
 * Favorite interface
 */
export interface Favorite {
  id: string;
  workerId: string;
  jobId: string;
  job: Job;
  createdAt: string;
}

/**
 * Favorites response interface
 */
export interface FavoritesResponseData {
  favorites: Favorite[];
  total: number;
  limit: number;
  offset: number;
}

export type FavoritesResponse = ApiResponse<FavoritesResponseData>;

/**
 * Favorites API endpoints using RTK Query
 */
export const favoritesApi = api.injectEndpoints({
  endpoints: (builder) => ({
    /**
     * Get user's favorite jobs
     */
    getFavorites: builder.query<FavoritesResponse, { limit?: number; offset?: number } | void>({
      query: (params = {}) => ({
        url: '/favorites',
        params,
      }),
      providesTags: (result) =>
        result
          ? [
              ...result.data.favorites.map(({ id }) => ({ type: 'Favorite' as const, id })),
              { type: 'Favorites' as const, id: 'LIST' },
            ]
          : [{ type: 'Favorites' as const, id: 'LIST' }],
    }),

    /**
     * Add job to favorites
     */
    addFavorite: builder.mutation<ApiResponse<Favorite>, string>({
      query: (jobId) => ({
        url: '/favorites',
        method: 'POST',
        body: { jobId },
      }),
      invalidatesTags: [
        { type: 'Favorites', id: 'LIST' },
        { type: 'SavedJobs', id: 'LIST' },
      ],
      // Optimistic update
      async onQueryStarted(jobId, { dispatch, queryFulfilled }) {
        try {
          await queryFulfilled;
          // Invalidate the isJobSaved query for this specific job
          dispatch(
            api.util.invalidateTags([{ type: 'SavedJobs', id: jobId }])
          );
        } catch {
          // Error handling is done by RTK Query
        }
      },
    }),

    /**
     * Remove job from favorites
     */
    removeFavorite: builder.mutation<ApiResponse<{ success: boolean }>, string>({
      query: (jobId) => ({
        url: `/favorites/${jobId}`,
        method: 'DELETE',
      }),
      invalidatesTags: [
        { type: 'Favorites', id: 'LIST' },
        { type: 'SavedJobs', id: 'LIST' },
      ],
      // Optimistic update
      async onQueryStarted(jobId, { dispatch, queryFulfilled }) {
        // Optimistically remove from favorites list
        const patchResult = dispatch(
          favoritesApi.util.updateQueryData('getFavorites', undefined, (draft) => {
            if (draft && draft.data) {
              draft.data.favorites = draft.data.favorites.filter(
                (favorite) => favorite.jobId !== jobId
              );
              draft.data.total = Math.max(0, draft.data.total - 1);
            }
          })
        );

        try {
          await queryFulfilled;
          // Invalidate the isJobSaved query for this specific job
          dispatch(
            api.util.invalidateTags([{ type: 'SavedJobs', id: jobId }])
          );
        } catch {
          // If the mutation fails, undo the optimistic update
          patchResult.undo();
        }
      },
    }),

    /**
     * Check if a job is in favorites
     */
    isFavorite: builder.query<ApiResponse<{ isFavorite: boolean }>, string>({
      query: (jobId) => `/favorites/check/${jobId}`,
      providesTags: (_result, _error, jobId) => [{ type: 'Favorite', id: jobId }],
    }),
  }),
  overrideExisting: false,
});

export const {
  useGetFavoritesQuery,
  useAddFavoriteMutation,
  useRemoveFavoriteMutation,
  useIsFavoriteQuery,
} = favoritesApi;
