import { api } from './apiSlice';
import type { 
  Document, 
  DocumentsResponse, 
  UploadDocumentRequest,
  ApiResponse,
  DocumentType,
  VerificationStatus 
} from '../../types/api';

/**
 * Documents query parameters
 */
export interface DocumentsQueryParams {
  limit?: number;
  offset?: number;
  documentType?: DocumentType;
  verificationStatus?: VerificationStatus;
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
}

/**
 * Document download response
 */
export interface DocumentDownloadResponse {
  downloadUrl: string;
  fileName: string;
  fileSize: number;
  mimeType: string;
}

/**
 * Documents API endpoints using RTK Query
 */
export const documentsApi = api.injectEndpoints({
  endpoints: (builder) => ({
    /**
     * Get user's documents with optional filtering
     */
    getDocuments: builder.query<DocumentsResponse, DocumentsQueryParams | void>({
      query: (params = {}) => ({
        url: '/documents',
        params,
      }),
      providesTags: (result) =>
        result
          ? [
              ...result.data.documents.map(({ id }) => ({ type: 'Document' as const, id })),
              { type: 'Documents' as const, id: 'LIST' },
            ]
          : [{ type: 'Documents' as const, id: 'LIST' }],
    }),

    /**
     * Get a specific document by ID
     */
    getDocumentById: builder.query<ApiResponse<Document>, string>({
      query: (id) => `/documents/${id}`,
      providesTags: (_result, _error, id) => [{ type: 'Document', id }],
    }),

    /**
     * Upload a new document
     */
    uploadDocument: builder.mutation<ApiResponse<Document>, UploadDocumentRequest>({
      query: ({ file, documentType, documentNumber }) => {
        const formData = new FormData();
        formData.append('file', {
          uri: file.uri,
          name: file.name,
          type: file.type,
        } as any);
        formData.append('type', documentType);
        if (documentNumber) {
          formData.append('documentNumber', documentNumber);
        }

        return {
          url: '/users/upload-document',
          method: 'POST',
          body: formData,
          headers: {
            'Content-Type': 'multipart/form-data',
          },
        };
      },
      invalidatesTags: [
        { type: 'Documents', id: 'LIST' },
        { type: 'User', id: 'PROFILE' },
      ],
    }),

    /**
     * Delete a document
     */
    deleteDocument: builder.mutation<ApiResponse<{ success: boolean }>, string>({
      query: (id) => ({
        url: `/documents/${id}`,
        method: 'DELETE',
      }),
      invalidatesTags: (_result, _error, id) => [
        { type: 'Document', id },
        { type: 'Documents', id: 'LIST' },
        { type: 'User', id: 'PROFILE' },
      ],
    }),

    /**
     * Get document download URL
     */
    getDocumentDownloadUrl: builder.query<ApiResponse<DocumentDownloadResponse>, string>({
      query: (id) => `/documents/${id}/download`,
      // Don't cache download URLs as they may expire
      keepUnusedDataFor: 0,
    }),

    /**
     * Update document metadata
     */
    updateDocument: builder.mutation<
      ApiResponse<Document>, 
      { id: string; documentNumber?: string; documentType?: DocumentType }
    >({
      query: ({ id, ...data }) => ({
        url: `/documents/${id}`,
        method: 'PATCH',
        body: data,
      }),
      invalidatesTags: (_result, _error, { id }) => [
        { type: 'Document', id },
        { type: 'Documents', id: 'LIST' },
      ],
    }),

    /**
     * Get documents by type
     */
    getDocumentsByType: builder.query<DocumentsResponse, DocumentType>({
      query: (documentType) => ({
        url: '/documents',
        params: { documentType },
      }),
      providesTags: (_result, _error, documentType) => [
        { type: 'Documents', id: documentType },
      ],
    }),

    /**
     * Get pending documents (for verification status)
     */
    getPendingDocuments: builder.query<DocumentsResponse, void>({
      query: () => ({
        url: '/documents',
        params: { verificationStatus: VerificationStatus.PENDING },
      }),
      providesTags: [{ type: 'Documents', id: 'PENDING' }],
    }),
  }),
  overrideExisting: false,
});

export const {
  useGetDocumentsQuery,
  useGetDocumentByIdQuery,
  useUploadDocumentMutation,
  useDeleteDocumentMutation,
  useGetDocumentDownloadUrlQuery,
  useUpdateDocumentMutation,
  useGetDocumentsByTypeQuery,
  useGetPendingDocumentsQuery,
  useLazyGetDocumentDownloadUrlQuery,
} = documentsApi;
