import { useCallback, useEffect } from 'react';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { useAppDispatch, useAppSelector } from '../store';
import STORAGE_KEYS from '../constants/storage-keys';
import {
  setCredentials,
  updateUser as updateUserAction,
  setOnboardingCompleted,
  setLoading,
  loadStoredAuth,
  logout as logoutAction,
} from '../store/slices/authSlice';
import {
  useLoginMutation,
  useRegisterMutation,
  useVerifyOtpMutation,
  useRequestOtpMutation,
  useLogoutMutation,
  useForgotPasswordMutation,
  useResetPasswordMutation,
  useVerifyEmailMutation,
  useResendVerificationEmailMutation,
  useRefreshTokenMutation,
} from '../store/api/authApi';
import { useUpdateProfileMutation, useChangePasswordMutation } from '../store/api/userApi';
import { PushNotificationService } from '../services/push-notification.service';
import { handleApiError } from '../store/api/apiSlice';
import type {
  User,
  RegisterRequest,
  AuthResponse,
  LoginRequest,
  OtpLoginRequest,
  ApiError,
} from '../types/api';

/**
 * Custom hook for authentication
 * Provides comprehensive authentication methods and state management
 */
export const useAuth = () => {
  const dispatch = useAppDispatch();
  const { user, token, refreshToken, isAuthenticated, isLoading, hasCompletedOnboarding } =
    useAppSelector((state) => state.auth);

  // RTK Query mutations
  const [loginMutation, { isLoading: isLoginLoading }] = useLoginMutation();
  const [registerMutation, { isLoading: isRegisterLoading }] = useRegisterMutation();
  const [verifyOtpMutation, { isLoading: isVerifyOtpLoading }] = useVerifyOtpMutation();
  const [requestOtpMutation, { isLoading: isRequestOtpLoading }] = useRequestOtpMutation();
  const [logoutMutation, { isLoading: isLogoutLoading }] = useLogoutMutation();
  const [forgotPasswordMutation, { isLoading: isForgotPasswordLoading }] =
    useForgotPasswordMutation();
  const [resetPasswordMutation, { isLoading: isResetPasswordLoading }] = useResetPasswordMutation();
  const [verifyEmailMutation, { isLoading: isVerifyEmailLoading }] = useVerifyEmailMutation();
  const [resendVerificationEmailMutation, { isLoading: isResendVerificationEmailLoading }] =
    useResendVerificationEmailMutation();
  const [refreshTokenMutation, { isLoading: isRefreshTokenLoading }] = useRefreshTokenMutation();
  const [updateProfileMutation, { isLoading: isUpdateProfileLoading }] = useUpdateProfileMutation();
  const [changePasswordMutation, { isLoading: isChangePasswordLoading }] =
    useChangePasswordMutation();

  /**
   * Initialize auth state from AsyncStorage on app startup
   */
  const initializeAuthState = useCallback(async () => {
    try {
      dispatch(setLoading(true));

      const [userString, token, refreshToken, onboardingCompleted] = await AsyncStorage.multiGet([
        STORAGE_KEYS.USER_DATA,
        STORAGE_KEYS.AUTH_TOKEN,
        STORAGE_KEYS.AUTH_REFRESH_TOKEN,
        STORAGE_KEYS.ONBOARDING_COMPLETED,
      ]);

      dispatch(
        loadStoredAuth({
          user: userString[1] ? JSON.parse(userString[1]) : null,
          token: token[1],
          refreshToken: refreshToken[1],
          hasCompletedOnboarding: onboardingCompleted[1] === 'true',
        })
      );

      // Initialize push notifications if user is authenticated
      if (token[1] && userString[1]) {
        await initializePushNotifications();
      }
    } catch (error) {
      console.error('Failed to initialize auth state:', error);
      dispatch(setLoading(false));
    }
  }, [dispatch]);

  /**
   * Sign in with email/phone and password
   * @param credentials - Login credentials (email/phone and password)
   * @returns Authentication response with user data and tokens
   */
  const login = useCallback(
    async (credentials: LoginRequest): Promise<AuthResponse> => {
      try {
        const result = await loginMutation(credentials).unwrap();
        const resultData = result.data;
        console.log('result', result);
        dispatch(
          setCredentials({
            user: resultData.user,
            token: resultData.token || resultData.access_token || '',
            refreshToken: resultData.refreshToken || '',
          })
        );

        // Initialize push notifications
        // await initializePushNotifications();

        return result;
      } catch (error) {
        console.error('Login error:', error);
        throw handleApiError(error);
      }
    },
    [dispatch, loginMutation]
  );

  /**
   * Register a new user
   * @param userData - User registration data
   * @returns Authentication response with user data and tokens
   */
  const register = useCallback(
    async (userData: RegisterRequest): Promise<AuthResponse> => {
      try {
        const result = await registerMutation(userData).unwrap();
        const resultData = result.data;
        dispatch(
          setCredentials({
            user: resultData.user,
            token: resultData.token || resultData.access_token || '',
            refreshToken: resultData.refreshToken || '',
          })
        );

        // Initialize push notifications
        await initializePushNotifications();

        return result;
      } catch (error) {
        console.error('Register error:', error);
        throw handleApiError(error);
      }
    },
    [dispatch, registerMutation]
  );

  /**
   * Request OTP for phone number
   * @param phone - Phone number to send OTP to
   * @returns Success status and message
   */
  const requestOtp = useCallback(
    async (phone: string): Promise<{ success: boolean; message: string }> => {
      try {
        return await requestOtpMutation({ phone }).unwrap();
      } catch (error) {
        console.error('Request OTP error:', error);
        throw handleApiError(error);
      }
    },
    [requestOtpMutation]
  );

  /**
   * Verify OTP and authenticate user
   * @param data - OTP verification data (phone and code)
   * @returns Authentication response with user data and tokens
   */
  const verifyOtp = useCallback(
    async (data: OtpLoginRequest): Promise<AuthResponse> => {
      try {
        const result = await verifyOtpMutation(data).unwrap();
        const resultData = result.data;
        dispatch(
          setCredentials({
            user: resultData.user,
            token: resultData.token || resultData.access_token || '',
            refreshToken: resultData.refreshToken || '',
          })
        );

        // Initialize push notifications
        await initializePushNotifications();

        return result;
      } catch (error) {
        console.error('OTP verification error:', error);
        throw handleApiError(error);
      }
    },
    [dispatch, verifyOtpMutation]
  );

  /**
   * Logout user
   * @returns Promise that resolves when logout is complete
   */
  const logout = useCallback(async (): Promise<void> => {
    try {
      // Unregister device token before logging out
      await unregisterPushNotifications();

      // Call logout API
      await logoutMutation().unwrap();

      // Clear local state
      dispatch(logoutAction());
    } catch (error) {
      console.error('Logout error:', error);
      // Still clear local state even if API call fails
      dispatch(logoutAction());
      throw handleApiError(error);
    }
  }, [dispatch, logoutMutation]);

  /**
   * Request password reset
   * @param email - Email address to send reset instructions to
   * @returns Success status and message
   */
  const forgotPassword = useCallback(
    async (email: string): Promise<{ success: boolean; message: string }> => {
      try {
        return await forgotPasswordMutation({ email }).unwrap();
      } catch (error) {
        console.error('Forgot password error:', error);
        throw handleApiError(error);
      }
    },
    [forgotPasswordMutation]
  );

  /**
   * Reset password with token
   * @param token - Reset token from email
   * @param password - New password
   * @returns Success status and message
   */
  const resetPassword = useCallback(
    async (token: string, password: string): Promise<{ success: boolean; message: string }> => {
      try {
        return await resetPasswordMutation({ token, password }).unwrap();
      } catch (error) {
        console.error('Reset password error:', error);
        throw handleApiError(error);
      }
    },
    [resetPasswordMutation]
  );

  /**
   * Verify email with token
   * @param token - Verification token from email
   * @returns Success status and message
   */
  const verifyEmail = useCallback(
    async (token: string): Promise<{ success: boolean; message: string }> => {
      try {
        return await verifyEmailMutation({ token }).unwrap();
      } catch (error) {
        console.error('Verify email error:', error);
        throw handleApiError(error);
      }
    },
    [verifyEmailMutation]
  );

  /**
   * Resend verification email
   * @returns Success status and message
   */
  const resendVerificationEmail = useCallback(async (): Promise<{
    success: boolean;
    message: string;
  }> => {
    try {
      return await resendVerificationEmailMutation().unwrap();
    } catch (error) {
      console.error('Resend verification email error:', error);
      throw handleApiError(error);
    }
  }, [resendVerificationEmailMutation]);

  /**
   * Refresh authentication token
   * @returns New token and refresh token
   */
  const refreshAuthToken = useCallback(async (): Promise<{
    token: string;
    refreshToken: string;
  }> => {
    if (!refreshToken) {
      throw new Error('No refresh token available');
    }

    try {
      const result = await refreshTokenMutation({ refreshToken }).unwrap();

      // Update tokens in Redux store
      dispatch(
        setCredentials({
          user: user!,
          token: result.token,
          refreshToken: result.refreshToken,
        })
      );

      return result;
    } catch (error) {
      console.error('Refresh token error:', error);
      // If refresh fails, log the user out
      dispatch(logoutAction());
      throw handleApiError(error);
    }
  }, [dispatch, refreshToken, refreshTokenMutation, user]);

  /**
   * Update user profile
   * @param data - Partial user data to update
   * @returns Updated user object
   */
  const updateUser = useCallback(
    async (data: Partial<User>): Promise<User> => {
      try {
        const updatedUser = await updateProfileMutation(data).unwrap();
        dispatch(updateUserAction(updatedUser));
        return updatedUser;
      } catch (error) {
        console.error('Update user error:', error);
        throw handleApiError(error);
      }
    },
    [dispatch, updateProfileMutation]
  );

  /**
   * Change user password
   * @param currentPassword - Current password
   * @param newPassword - New password
   * @returns Success status and message
   */
  const changePassword = useCallback(
    async (
      currentPassword: string,
      newPassword: string
    ): Promise<{ success: boolean; message: string }> => {
      try {
        return await changePasswordMutation({
          currentPassword,
          newPassword,
        }).unwrap();
      } catch (error) {
        console.error('Change password error:', error);
        throw handleApiError(error);
      }
    },
    [changePasswordMutation]
  );

  /**
   * Complete onboarding
   */
  const completeOnboarding = useCallback((): void => {
    dispatch(setOnboardingCompleted(true));
  }, [dispatch]);

  /**
   * Initialize push notifications
   */
  const initializePushNotifications = async (): Promise<void> => {
    try {
      const pushNotificationService = PushNotificationService.getInstance();
      await pushNotificationService.initialize();
    } catch (error) {
      console.error('Failed to initialize push notifications:', error);
    }
  };

  /**
   * Unregister push notifications
   */
  const unregisterPushNotifications = async (): Promise<void> => {
    try {
      const pushNotificationService = PushNotificationService.getInstance();
      await pushNotificationService.unregisterDeviceToken();
      pushNotificationService.cleanup();
    } catch (error) {
      console.error('Failed to unregister push notifications:', error);
    }
  };

  return {
    // Auth state
    user,
    token,
    refreshToken,
    isAuthenticated,
    isLoading,
    hasCompletedOnboarding,

    // Loading states
    isLoginLoading,
    isRegisterLoading,
    isVerifyOtpLoading,
    isRequestOtpLoading,
    isLogoutLoading,
    isForgotPasswordLoading,
    isResetPasswordLoading,
    isVerifyEmailLoading,
    isResendVerificationEmailLoading,
    isRefreshTokenLoading,
    isUpdateProfileLoading,
    isChangePasswordLoading,

    // Auth methods
    login,
    register,
    requestOtp,
    verifyOtp,
    logout,
    forgotPassword,
    resetPassword,
    verifyEmail,
    resendVerificationEmail,
    refreshAuthToken,
    updateUser,
    changePassword,
    completeOnboarding,

    // For backward compatibility
    signIn: login,
    signUp: register,
    signInWithOtp: (phone: string, code: string) => verifyOtp({ phone, code }),
    signOut: logout,

    // Initialization method (for manual initialization if needed)
    initializeAuthState,
  };
};

export default useAuth;
