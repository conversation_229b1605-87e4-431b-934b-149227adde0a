import { renderHook, act } from '@testing-library/react-hooks';
import { Provider } from 'react-redux';
import { configureStore } from '@reduxjs/toolkit';
import React from 'react';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { useAuth } from '../use-auth';
import authReducer from '../../store/slices/authSlice';
import { api } from '../../store/api/apiSlice';

// Mock AsyncStorage
jest.mock('@react-native-async-storage/async-storage', () => ({
  multiGet: jest.fn(),
  multiSet: jest.fn(),
  multiRemove: jest.fn(),
  setItem: jest.fn(),
}));

// Mock PushNotificationService
jest.mock('../../services/push-notification.service', () => ({
  PushNotificationService: {
    getInstance: jest.fn().mockReturnValue({
      initialize: jest.fn().mockResolvedValue(undefined),
      unregisterDeviceToken: jest.fn().mockResolvedValue(undefined),
      cleanup: jest.fn(),
    }),
  },
}));

// Create a test wrapper with Redux store
const createWrapper = () => {
  const store = configureStore({
    reducer: {
      auth: authReducer,
      [api.reducerPath]: api.reducer,
    },
    middleware: (getDefaultMiddleware) =>
      getDefaultMiddleware({
        serializableCheck: false,
      }).concat(api.middleware),
  });

  return ({ children }: { children: React.ReactNode }) => (
    <Provider store={store}>{children}</Provider>
  );
};

describe('useAuth hook', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    (AsyncStorage.multiGet as jest.Mock).mockResolvedValue([
      ['@JobApp:user', null],
      ['@JobApp:token', null],
      ['@JobApp:refreshToken', null],
      ['@JobApp:onboardingCompleted', null],
    ]);
  });

  it('should initialize auth state on mount', async () => {
    const { result, waitForNextUpdate } = renderHook(() => useAuth(), {
      wrapper: createWrapper(),
    });

    // Wait for the initialization effect to complete
    await waitForNextUpdate();

    // Verify that AsyncStorage.multiGet was called
    expect(AsyncStorage.multiGet).toHaveBeenCalledWith([
      '@JobApp:user',
      '@JobApp:token',
      '@JobApp:refreshToken',
      '@JobApp:onboardingCompleted',
    ]);

    // Verify initial state
    expect(result.current.isAuthenticated).toBe(false);
    expect(result.current.user).toBeNull();
    expect(result.current.token).toBeNull();
    expect(result.current.refreshToken).toBeNull();
    expect(result.current.hasCompletedOnboarding).toBe(false);
  });

  it('should provide all required authentication methods', () => {
    const { result } = renderHook(() => useAuth(), {
      wrapper: createWrapper(),
    });

    // Verify that all methods are defined
    expect(result.current.login).toBeDefined();
    expect(result.current.register).toBeDefined();
    expect(result.current.requestOtp).toBeDefined();
    expect(result.current.verifyOtp).toBeDefined();
    expect(result.current.logout).toBeDefined();
    expect(result.current.forgotPassword).toBeDefined();
    expect(result.current.resetPassword).toBeDefined();
    expect(result.current.verifyEmail).toBeDefined();
    expect(result.current.resendVerificationEmail).toBeDefined();
    expect(result.current.refreshAuthToken).toBeDefined();
    expect(result.current.updateUser).toBeDefined();
    expect(result.current.changePassword).toBeDefined();
    expect(result.current.completeOnboarding).toBeDefined();
    
    // Verify backward compatibility methods
    expect(result.current.signIn).toBeDefined();
    expect(result.current.signUp).toBeDefined();
    expect(result.current.signInWithOtp).toBeDefined();
    expect(result.current.signOut).toBeDefined();
  });

  // Additional tests would be added for each method
  // but are omitted for brevity
});
