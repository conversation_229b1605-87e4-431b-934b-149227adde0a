import { useState, useEffect } from 'react';
import { useColorScheme, StyleSheet } from 'react-native';
import AsyncStorage from '@react-native-async-storage/async-storage';
import STORAGE_KEYS from '../constants/storage-keys';

export type Theme = 'light' | 'dark' | 'system';

// Default colors for light and dark themes
const lightColors = {
  primary: '#0ea5e9', // primary-500
  background: '#ffffff',
  card: '#ffffff',
  text: '#111827',
  border: '#e5e7eb',
  notification: '#ef4444',
  error: '#ef4444',
  success: '#22c55e',
  warning: '#f59e0b',
  info: '#0ea5e9',
  gray: {
    50: '#f9fafb',
    100: '#f3f4f6',
    200: '#e5e7eb',
    300: '#d1d5db',
    400: '#9ca3af',
    500: '#6b7280',
    600: '#4b5563',
    700: '#374151',
    800: '#1f2937',
    900: '#111827',
    950: '#030712',
  },
};

const darkColors = {
  primary: '#0ea5e9', // primary-500
  background: '#111827',
  card: '#1f2937',
  text: '#f9fafb',
  border: '#374151',
  notification: '#b91c1c',
  error: '#b91c1c',
  success: '#15803d',
  warning: '#b45309',
  info: '#0369a1',
  gray: {
    50: '#f9fafb',
    100: '#f3f4f6',
    200: '#e5e7eb',
    300: '#d1d5db',
    400: '#9ca3af',
    500: '#6b7280',
    600: '#4b5563',
    700: '#374151',
    800: '#1f2937',
    900: '#111827',
    950: '#030712',
  },
};

/**
 * Custom hook for theme management
 * Provides theme state and utilities for the app
 */
export function useTheme() {
  const systemTheme = useColorScheme() as 'light' | 'dark';
  const [themePreference, setThemePreference] = useState<Theme>('system');
  const [loading, setLoading] = useState(true);

  // Determine the actual theme to use
  const actualTheme = themePreference === 'system' ? systemTheme : themePreference;
  const isDark = actualTheme === 'dark';

  // Get the appropriate colors based on the theme
  const colors = isDark ? darkColors : lightColors;

  // Load saved theme preference
  useEffect(() => {
    const loadTheme = async () => {
      try {
        const savedTheme = await AsyncStorage.getItem(STORAGE_KEYS.THEME_PREFERENCE);
        if (savedTheme) {
          setThemePreference(savedTheme as Theme);
        }
      } catch (error) {
        console.error('Error loading theme:', error);
      } finally {
        setLoading(false);
      }
    };

    loadTheme();
  }, []);

  // Save theme preference when it changes
  const setTheme = async (newTheme: Theme) => {
    setThemePreference(newTheme);
    try {
      await AsyncStorage.setItem(STORAGE_KEYS.THEME_PREFERENCE, newTheme);
    } catch (error) {
      console.error('Error saving theme:', error);
    }
  };

  // Helper function to get Tailwind color classes
  const getColorClass = (colorName: string, variant: string = 'DEFAULT'): string => {
    if (variant === 'DEFAULT') {
      return `${colorName}`;
    }
    return `${colorName}-${variant}`;
  };

  // Force StyleSheet registration to ensure NativeWind processing
  useEffect(() => {
    StyleSheet.create({});
  }, []);

  return {
    theme: actualTheme,
    setTheme,
    systemTheme,
    themePreference,
    isDark,
    loading,
    colors,
    getColorClass,
  };
}
