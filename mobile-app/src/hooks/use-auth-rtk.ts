/**
 * Custom hook for authentication using RTK Query
 * This hook provides a consistent interface for authentication operations
 * and manages authentication state using Redux
 */
import { useCallback } from 'react';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { useAppDispatch, useAppSelector } from '../store';
import {
  useLoginMutation,
  useRegisterMutation,
  useRequestOtpMutation,
  useVerifyOtpMutation,
  useLogoutMutation,
} from '../store/api/authApi';
import { setCredentials, updateUser, logout, setOnboardingCompleted } from '../store/slices/authSlice';
import STORAGE_KEYS from '../constants/storage-keys';
import type { LoginRequest, RegisterRequest, User } from '../types/api';
import { PushNotificationService } from '../services/push-notification.service';

/**
 * Custom hook for authentication using RTK Query
 * @returns Authentication methods and state
 */
export function useAuthRTK() {
  const dispatch = useAppDispatch();
  const { user, isAuthenticated, isLoading, hasCompletedOnboarding } = useAppSelector(
    (state) => state.auth
  );

  // RTK Query hooks
  const [loginMutation, { isLoading: isLoginLoading }] = useLoginMutation();
  const [registerMutation, { isLoading: isRegisterLoading }] = useRegisterMutation();
  const [requestOtpMutation, { isLoading: isRequestingOtp }] = useRequestOtpMutation();
  const [verifyOtpMutation, { isLoading: isVerifyingOtp }] = useVerifyOtpMutation();
  const [logoutMutation, { isLoading: isLoggingOut }] = useLogoutMutation();

  /**
   * Sign in with email/phone and password
   * @param credentials Login credentials
   */
  const signIn = useCallback(
    async (credentials: LoginRequest) => {
      try {
        const response = await loginMutation(credentials).unwrap();
        const { user, token, refreshToken } = response.data;

        // Store credentials in Redux
        dispatch(setCredentials({ user, token, refreshToken }));

        // Initialize push notifications
        await initializePushNotifications();

        return response;
      } catch (error) {
        console.error('Sign in error:', error);
        throw error;
      }
    },
    [dispatch, loginMutation]
  );

  /**
   * Request OTP for phone number
   * @param phone Phone number
   */
  const requestOtp = useCallback(
    async (phone: string) => {
      try {
        return await requestOtpMutation({ phone }).unwrap();
      } catch (error) {
        console.error('Request OTP error:', error);
        throw error;
      }
    },
    [requestOtpMutation]
  );

  /**
   * Sign in with OTP
   * @param phone Phone number
   * @param otp OTP code
   */
  const signInWithOtp = useCallback(
    async (phone: string, otp: string) => {
      try {
        const response = await verifyOtpMutation({ phone, otp, purpose: 'login' }).unwrap();
        const { user, token, refreshToken } = response.data;

        // Store credentials in Redux
        dispatch(setCredentials({ user, token, refreshToken }));

        // Initialize push notifications
        await initializePushNotifications();

        return response;
      } catch (error) {
        console.error('OTP sign in error:', error);
        throw error;
      }
    },
    [dispatch, verifyOtpMutation]
  );

  /**
   * Register a new user
   * @param userData User registration data
   */
  const signUp = useCallback(
    async (userData: RegisterRequest) => {
      try {
        const response = await registerMutation(userData).unwrap();
        const { user, token, refreshToken } = response.data;

        // Store credentials in Redux
        dispatch(setCredentials({ user, token, refreshToken }));

        // Initialize push notifications
        await initializePushNotifications();

        return response;
      } catch (error) {
        console.error('Sign up error:', error);
        throw error;
      }
    },
    [dispatch, registerMutation]
  );

  /**
   * Sign out the current user
   */
  const signOut = useCallback(async () => {
    try {
      // Unregister device token before signing out
      await unregisterPushNotifications();

      // Call logout API
      await logoutMutation().unwrap();

      // Clear Redux state
      dispatch(logout());
    } catch (error) {
      console.error('Sign out error:', error);
      // Still clear Redux state even if API call fails
      dispatch(logout());
      throw error;
    }
  }, [dispatch, logoutMutation]);

  /**
   * Update user profile
   * @param data Updated user data
   */
  const updateUserProfile = useCallback(
    async (data: Partial<User>) => {
      try {
        dispatch(updateUser(data));
        return true;
      } catch (error) {
        console.error('Update user error:', error);
        throw error;
      }
    },
    [dispatch]
  );

  /**
   * Mark onboarding as completed
   */
  const completeOnboarding = useCallback(async () => {
    await AsyncStorage.setItem(STORAGE_KEYS.ONBOARDING_COMPLETED, 'true');
    dispatch(setOnboardingCompleted(true));
  }, [dispatch]);

  /**
   * Initialize push notifications
   */
  const initializePushNotifications = async () => {
    try {
      const pushNotificationService = PushNotificationService.getInstance();
      await pushNotificationService.initialize();
    } catch (error) {
      console.error('Failed to initialize push notifications:', error);
    }
  };

  /**
   * Unregister push notifications
   */
  const unregisterPushNotifications = async () => {
    try {
      const pushNotificationService = PushNotificationService.getInstance();
      await pushNotificationService.unregisterDeviceToken();
      pushNotificationService.cleanup();
    } catch (error) {
      console.error('Failed to unregister push notifications:', error);
    }
  };

  return {
    user,
    isAuthenticated,
    isLoading: isLoading || isLoginLoading || isRegisterLoading || isRequestingOtp || isVerifyingOtp || isLoggingOut,
    hasCompletedOnboarding,
    signIn,
    signInWithOtp,
    requestOtp,
    signUp,
    signOut,
    updateUser: updateUserProfile,
    completeOnboarding,
  };
}
