import * as Notifications from 'expo-notifications';
import * as Device from 'expo-device';
import { Platform } from 'react-native';
import AsyncStorage from '@react-native-async-storage/async-storage';
import STORAGE_KEYS from '../constants/storage-keys';

// Configure notification behavior
Notifications.setNotificationHandler({
  handleNotification: async () => ({
    shouldShowAlert: true,
    shouldPlaySound: true,
    shouldSetBadge: true,
    shouldShowBanner: true,
    shouldShowList: true,
  }),
});

class NotificationService {
  private static instance: NotificationService;
  private notificationListener: any;
  private responseListener: any;
  private pushToken: string | null = null;

  private constructor() {
    // Private constructor to enforce singleton pattern
  }

  public static getInstance(): NotificationService {
    if (!NotificationService.instance) {
      NotificationService.instance = new NotificationService();
    }
    return NotificationService.instance;
  }

  /**
   * Initialize notification service
   */
  public async initialize(): Promise<void> {
    try {
      // Check if we already have permission
      const { status: existingStatus } = await Notifications.getPermissionsAsync();
      let finalStatus = existingStatus;

      // If we don't have permission, ask for it
      if (existingStatus !== 'granted') {
        const { status } = await Notifications.requestPermissionsAsync();
        finalStatus = status;
      }

      // If we still don't have permission, we can't proceed
      if (finalStatus !== 'granted') {
        console.log('Failed to get push token for push notification!');
        return;
      }

      // Get the token
      await this.registerForPushNotifications();

      // Set up notification listeners
      this.setupNotificationListeners();
    } catch (error) {
      console.error('Error initializing notification service:', error);
    }
  }

  /**
   * Register for push notifications
   */
  public async registerForPushNotifications(): Promise<string | null> {
    try {
      // Check if device is physical (not simulator/emulator)
      if (!Device.isDevice) {
        console.log('Must use physical device for push notifications');
        return null;
      }

      // Get the token that uniquely identifies this device
      let token;
      try {
        // Skip the Expo push token for now to avoid the projectId error
        // We'll use a dummy token for development purposes
        const dummyToken = `ExpoToken-${Date.now()}`;
        token = dummyToken;
        this.pushToken = token;

        // Save token to AsyncStorage for future reference
        await AsyncStorage.setItem(STORAGE_KEYS.PUSH_TOKEN, token);

        console.log('Using development dummy token:', token);
      } catch (error) {
        console.error('Error getting push token:', error);
        return null;
      }

      // Platform-specific setup
      if (Platform.OS === 'android') {
        Notifications.setNotificationChannelAsync('default', {
          name: 'default',
          importance: Notifications.AndroidImportance.MAX,
          vibrationPattern: [0, 250, 250, 250],
          lightColor: '#FF231F7C',
        });
      }

      console.log('Push token registered:', token);
      return token;
    } catch (error) {
      console.error('Error registering for push notifications:', error);
      return null;
    }
  }

  /**
   * Set up notification listeners
   */
  private setupNotificationListeners(): void {
    try {
      // This listener is fired whenever a notification is received while the app is foregrounded
      this.notificationListener = Notifications.addNotificationReceivedListener((notification) => {
        const data = notification.request.content.data;
        console.log('Notification received in foreground:', data);
      });

      // This listener is fired whenever a user taps on or interacts with a notification
      this.responseListener = Notifications.addNotificationResponseReceivedListener((response) => {
        const data = response.notification.request.content.data;
        console.log('Notification response received:', data);
        this.handleNotificationResponse(data);
      });
    } catch (error) {
      console.error('Error setting up notification listeners:', error);
    }
  }

  /**
   * Handle notification response (when user taps on notification)
   */
  private handleNotificationResponse(data: any): void {
    try {
      // Handle different notification types
      if (data.type === 'job') {
        console.log('Navigate to job:', data.jobId);
      } else if (data.type === 'chat') {
        console.log('Navigate to chat:', data.chatId);
      } else if (data.type === 'application') {
        console.log('Navigate to application:', data.applicationId);
      } else if (data.type === 'payment') {
        console.log('Navigate to payment:', data.paymentId);
      }
    } catch (error) {
      console.error('Error handling notification response:', error);
    }
  }

  /**
   * Clean up listeners
   */
  public cleanup(): void {
    try {
      if (this.notificationListener) {
        this.notificationListener.remove();
      }
      if (this.responseListener) {
        this.responseListener.remove();
      }
    } catch (error) {
      console.error('Error cleaning up notification listeners:', error);
    }
  }
}

export default NotificationService.getInstance();
