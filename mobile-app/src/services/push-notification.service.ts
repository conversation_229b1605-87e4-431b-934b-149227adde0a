import { Platform } from 'react-native';
import * as Device from 'expo-device';
import * as Notifications from 'expo-notifications';
import api from './api';

// Configure notification behavior
Notifications.setNotificationHandler({
  handleNotification: async () => ({
    shouldShowAlert: true,
    shouldPlaySound: true,
    shouldSetBadge: true,
    shouldShowBanner: true,
    shouldShowList: true,
  }),
});

export class PushNotificationService {
  private static instance: PushNotificationService;
  private expoPushToken: string | null = null;
  private notificationListener: any;
  private responseListener: any;

  private constructor() {
    // Private constructor for singleton pattern
  }

  /**
   * Get the singleton instance
   */
  public static getInstance(): PushNotificationService {
    if (!PushNotificationService.instance) {
      PushNotificationService.instance = new PushNotificationService();
    }
    return PushNotificationService.instance;
  }

  /**
   * Initialize push notifications
   */
  public async initialize(): Promise<void> {
    if (!Device.isDevice) {
      console.log('Push Notifications are not available on emulator');
      return;
    }

    try {
      // Request permission
      const { status: existingStatus } = await Notifications.getPermissionsAsync();
      let finalStatus = existingStatus;

      if (existingStatus !== 'granted') {
        const { status } = await Notifications.requestPermissionsAsync();
        finalStatus = status;
      }

      if (finalStatus !== 'granted') {
        console.log('Failed to get push token for push notification!');
        return;
      }

      // Get Expo push token
      try {
        const tokenData = await Notifications.getExpoPushTokenAsync();
        this.expoPushToken = tokenData.data;
        console.log('Expo Push Token:', this.expoPushToken);
      } catch (error) {
        console.error('Error getting Expo push token:', error);
        return;
      }

      // Register device token with backend
      await this.registerDeviceToken();

      // Required for Android
      if (Platform.OS === 'android') {
        Notifications.setNotificationChannelAsync('default', {
          name: 'default',
          importance: Notifications.AndroidImportance.MAX,
          vibrationPattern: [0, 250, 250, 250],
          lightColor: '#FF231F7C',
        });
      }

      // Set up notification listeners
      this.setupNotificationListeners();
    } catch (error) {
      console.error('Error initializing push notifications:', error);
    }
  }

  /**
   * Register device token with backend
   */
  private async registerDeviceToken(): Promise<void> {
    if (!this.expoPushToken) return;

    try {
      await api.post('/device-tokens/register', { token: this.expoPushToken });
      console.log('Device token registered successfully');
    } catch (error) {
      console.error('Error registering device token:', error);
    }
  }

  /**
   * Unregister device token from backend
   */
  public async unregisterDeviceToken(): Promise<void> {
    if (!this.expoPushToken) return;

    try {
      await api.delete('/device-tokens/unregister', {
        data: { token: this.expoPushToken },
      });
      console.log('Device token unregistered successfully');
    } catch (error) {
      console.error('Error unregistering device token:', error);
    }
  }

  /**
   * Set up notification listeners
   */
  private setupNotificationListeners(): void {
    try {
      // This listener is fired whenever a notification is received while the app is foregrounded
      this.notificationListener = Notifications.addNotificationReceivedListener((notification) => {
        console.log('Notification received:', notification);
      });

      // This listener is fired whenever a user taps on or interacts with a notification
      this.responseListener = Notifications.addNotificationResponseReceivedListener((response) => {
        console.log('Notification response received:', response);

        // Handle notification tap
        const data = response.notification.request.content.data;
        this.handleNotificationTap(data);
      });
    } catch (error) {
      console.error('Error setting up notification listeners:', error);
    }
  }

  /**
   * Handle notification tap
   */
  private handleNotificationTap(data: any): void {
    // TODO: Implement navigation to specific screens based on notification data
    console.log('Handling notification tap with data:', data);
  }

  /**
   * Clean up listeners
   */
  public cleanup(): void {
    try {
      if (this.notificationListener) {
        this.notificationListener.remove();
      }

      if (this.responseListener) {
        this.responseListener.remove();
      }
    } catch (error) {
      console.error('Error cleaning up notification listeners:', error);
    }
  }
}
