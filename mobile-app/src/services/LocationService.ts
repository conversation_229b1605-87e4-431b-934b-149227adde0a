import * as Location from 'expo-location';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { Platform } from 'react-native';
import STORAGE_KEYS from '../constants/storage-keys';

// Default location cache expiry (30 minutes)
const DEFAULT_CACHE_EXPIRY_MS = 30 * 60 * 1000;

class LocationService {
  private static instance: LocationService;
  private hasPermission: boolean = false;
  private cachedLocation: Location.LocationObject | null = null;
  private cachedLocationTimestamp: number = 0;
  private locationSubscription: Location.LocationSubscription | null = null;
  private locationListeners: Array<(location: Location.LocationObject) => void> = [];

  private constructor() {
    // Private constructor to enforce singleton pattern
  }

  public static getInstance(): LocationService {
    if (!LocationService.instance) {
      LocationService.instance = new LocationService();
    }
    return LocationService.instance;
  }

  /**
   * Initialize location service
   */
  public async initialize(): Promise<void> {
    try {
      // Check if we have cached permission
      const cachedPermission = await AsyncStorage.getItem(STORAGE_KEYS.LOCATION_PERMISSION);
      this.hasPermission = cachedPermission === 'granted';

      // Load cached location
      await this.loadCachedLocation();

      // Request permission if we don't have it
      if (!this.hasPermission) {
        await this.requestLocationPermission();
      }
    } catch (error) {
      console.error('Error initializing location service:', error);
    }
  }

  /**
   * Request location permission
   */
  public async requestLocationPermission(): Promise<boolean> {
    try {
      const { status } = await Location.requestForegroundPermissionsAsync();
      this.hasPermission = status === 'granted';

      // Cache permission status
      await AsyncStorage.setItem(STORAGE_KEYS.LOCATION_PERMISSION, status);

      return this.hasPermission;
    } catch (error) {
      console.error('Error requesting location permission:', error);
      return false;
    }
  }

  /**
   * Check if we have location permission
   */
  public async hasLocationPermission(): Promise<boolean> {
    try {
      const { status } = await Location.getForegroundPermissionsAsync();
      this.hasPermission = status === 'granted';
      return this.hasPermission;
    } catch (error) {
      console.error('Error checking location permission:', error);
      return this.hasPermission;
    }
  }

  /**
   * Get current location
   * @param options Location options
   * @param forceRefresh Force refresh even if we have a cached location
   */
  public async getCurrentLocation(
    options: Location.LocationOptions = {
      accuracy: Location.Accuracy.Balanced,
    },
    forceRefresh: boolean = false
  ): Promise<Location.LocationObject | null> {
    try {
      // Check if we have permission
      if (!this.hasPermission) {
        const granted = await this.requestLocationPermission();
        if (!granted) {
          return null;
        }
      }

      // Check if we have a cached location that's still valid
      const now = Date.now();
      if (
        !forceRefresh &&
        this.cachedLocation &&
        now - this.cachedLocationTimestamp < DEFAULT_CACHE_EXPIRY_MS
      ) {
        return this.cachedLocation;
      }

      // Get current location
      const location = await Location.getCurrentPositionAsync(options);

      // Cache location
      this.cachedLocation = location;
      this.cachedLocationTimestamp = now;
      await this.cacheLocation(location);

      // Notify listeners
      this.notifyListeners(location);

      return location;
    } catch (error) {
      console.error('Error getting current location:', error);

      // Return cached location if available
      if (this.cachedLocation) {
        return this.cachedLocation;
      }

      return null;
    }
  }

  /**
   * Start watching location
   */
  public async startWatchingLocation(
    options: Location.LocationOptions = {
      accuracy: Location.Accuracy.Balanced,
      distanceInterval: 100, // Minimum distance (in meters) between updates
      timeInterval: 5000, // Minimum time (in ms) between updates
    }
  ): Promise<boolean> {
    try {
      // Check if we have permission
      if (!this.hasPermission) {
        const granted = await this.requestLocationPermission();
        if (!granted) {
          return false;
        }
      }

      // Stop any existing subscription
      this.stopWatchingLocation();

      // Start watching location
      this.locationSubscription = await Location.watchPositionAsync(options, (location) => {
        // Update cached location
        this.cachedLocation = location;
        this.cachedLocationTimestamp = Date.now();
        this.cacheLocation(location);

        // Notify listeners
        this.notifyListeners(location);
      });

      return true;
    } catch (error) {
      console.error('Error watching location:', error);
      return false;
    }
  }

  /**
   * Stop watching location
   */
  public stopWatchingLocation(): void {
    if (this.locationSubscription) {
      this.locationSubscription.remove();
      this.locationSubscription = null;
    }
  }

  /**
   * Add location listener
   */
  public addLocationListener(listener: (location: Location.LocationObject) => void): void {
    this.locationListeners.push(listener);

    // Notify with current location if available
    if (this.cachedLocation) {
      listener(this.cachedLocation);
    }
  }

  /**
   * Remove location listener
   */
  public removeLocationListener(listener: (location: Location.LocationObject) => void): void {
    this.locationListeners = this.locationListeners.filter((l) => l !== listener);
  }

  /**
   * Notify all location listeners
   */
  private notifyListeners(location: Location.LocationObject): void {
    this.locationListeners.forEach((listener) => {
      listener(location);
    });
  }

  /**
   * Cache location
   */
  private async cacheLocation(location: Location.LocationObject): Promise<void> {
    try {
      await AsyncStorage.setItem(STORAGE_KEYS.LOCATION_CACHE, JSON.stringify(location));
      await AsyncStorage.setItem(STORAGE_KEYS.LOCATION_TIMESTAMP, Date.now().toString());
    } catch (error) {
      console.error('Error caching location:', error);
    }
  }

  /**
   * Load cached location
   */
  private async loadCachedLocation(): Promise<void> {
    try {
      const cachedLocationJson = await AsyncStorage.getItem(STORAGE_KEYS.LOCATION_CACHE);
      const cachedTimestampStr = await AsyncStorage.getItem(STORAGE_KEYS.LOCATION_TIMESTAMP);

      if (cachedLocationJson && cachedTimestampStr) {
        const cachedLocation = JSON.parse(cachedLocationJson);
        const cachedTimestamp = parseInt(cachedTimestampStr, 10);

        // Check if cache is still valid
        if (Date.now() - cachedTimestamp < DEFAULT_CACHE_EXPIRY_MS) {
          this.cachedLocation = cachedLocation;
          this.cachedLocationTimestamp = cachedTimestamp;
        }
      }
    } catch (error) {
      console.error('Error loading cached location:', error);
    }
  }

  /**
   * Calculate distance between two coordinates in kilometers
   */
  public calculateDistance(lat1: number, lon1: number, lat2: number, lon2: number): number {
    const R = 6371; // Radius of the earth in km
    const dLat = this.deg2rad(lat2 - lat1);
    const dLon = this.deg2rad(lon2 - lon1);
    const a =
      Math.sin(dLat / 2) * Math.sin(dLat / 2) +
      Math.cos(this.deg2rad(lat1)) *
        Math.cos(this.deg2rad(lat2)) *
        Math.sin(dLon / 2) *
        Math.sin(dLon / 2);
    const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));
    const distance = R * c; // Distance in km
    return distance;
  }

  /**
   * Convert degrees to radians
   */
  private deg2rad(deg: number): number {
    return deg * (Math.PI / 180);
  }

  /**
   * Clean up resources
   */
  public cleanup(): void {
    this.stopWatchingLocation();
    this.locationListeners = [];
  }
}

export default LocationService.getInstance();
