import AsyncStorage from '@react-native-async-storage/async-storage';
import STORAG<PERSON>_KEYS from '../constants/storage-keys';

// Interface for cached data
interface CachedData<T> {
  data: T;
  timestamp: number;
  expiresAt: number;
}

class CacheService {
  private static instance: CacheService;
  private readonly CACHE_PREFIX = STORAGE_KEYS.CACHE_PREFIX;
  private readonly DEFAULT_EXPIRY_MS = 24 * 60 * 60 * 1000; // 24 hours

  private constructor() {
    // Private constructor to enforce singleton pattern
  }

  public static getInstance(): CacheService {
    if (!CacheService.instance) {
      CacheService.instance = new CacheService();
    }
    return CacheService.instance;
  }

  /**
   * Set data in cache
   * @param key Cache key
   * @param data Data to cache
   * @param expiryMs Time in milliseconds until the cache expires (default: 24 hours)
   */
  public async setData<T>(
    key: string,
    data: T,
    expiryMs: number = this.DEFAULT_EXPIRY_MS
  ): Promise<void> {
    try {
      const now = Date.now();
      const cacheKey = this.getCacheKey(key);
      const cacheData: CachedData<T> = {
        data,
        timestamp: now,
        expiresAt: now + expiryMs,
      };

      await AsyncStorage.setItem(cacheKey, JSON.stringify(cacheData));
    } catch (error) {
      console.error(`Error caching data for key ${key}:`, error);
    }
  }

  /**
   * Get data from cache
   * @param key Cache key
   * @returns Cached data or null if not found or expired
   */
  public async getData<T>(key: string): Promise<T | null> {
    try {
      const cacheKey = this.getCacheKey(key);
      const cachedJson = await AsyncStorage.getItem(cacheKey);

      if (!cachedJson) {
        return null;
      }

      const cachedData: CachedData<T> = JSON.parse(cachedJson);
      const now = Date.now();

      // Check if cache has expired
      if (cachedData.expiresAt < now) {
        // Cache expired, remove it
        await this.removeData(key);
        return null;
      }

      return cachedData.data;
    } catch (error) {
      console.error(`Error retrieving cached data for key ${key}:`, error);
      return null;
    }
  }

  /**
   * Remove data from cache
   * @param key Cache key
   */
  public async removeData(key: string): Promise<void> {
    try {
      const cacheKey = this.getCacheKey(key);
      await AsyncStorage.removeItem(cacheKey);
    } catch (error) {
      console.error(`Error removing cached data for key ${key}:`, error);
    }
  }

  /**
   * Check if data exists in cache and is not expired
   * @param key Cache key
   * @returns True if valid data exists in cache
   */
  public async hasValidData(key: string): Promise<boolean> {
    try {
      const cacheKey = this.getCacheKey(key);
      const cachedJson = await AsyncStorage.getItem(cacheKey);

      if (!cachedJson) {
        return false;
      }

      const cachedData: CachedData<any> = JSON.parse(cachedJson);
      const now = Date.now();

      return cachedData.expiresAt >= now;
    } catch (error) {
      console.error(`Error checking cache validity for key ${key}:`, error);
      return false;
    }
  }

  /**
   * Get cache expiry time
   * @param key Cache key
   * @returns Expiry timestamp in milliseconds or null if not found
   */
  public async getExpiryTime(key: string): Promise<number | null> {
    try {
      const cacheKey = this.getCacheKey(key);
      const cachedJson = await AsyncStorage.getItem(cacheKey);

      if (!cachedJson) {
        return null;
      }

      const cachedData: CachedData<any> = JSON.parse(cachedJson);
      return cachedData.expiresAt;
    } catch (error) {
      console.error(`Error getting cache expiry for key ${key}:`, error);
      return null;
    }
  }

  /**
   * Clear all cached data
   */
  public async clearAllCache(): Promise<void> {
    try {
      const keys = await AsyncStorage.getAllKeys();
      const cacheKeys = keys.filter((key) => key.startsWith(this.CACHE_PREFIX));

      if (cacheKeys.length > 0) {
        await AsyncStorage.multiRemove(cacheKeys);
      }
    } catch (error) {
      console.error('Error clearing cache:', error);
    }
  }

  /**
   * Clear expired cache entries
   */
  public async clearExpiredCache(): Promise<void> {
    try {
      const keys = await AsyncStorage.getAllKeys();
      const cacheKeys = keys.filter((key) => key.startsWith(this.CACHE_PREFIX));
      const now = Date.now();
      const keysToRemove: string[] = [];

      // Check each cache entry
      for (const cacheKey of cacheKeys) {
        const cachedJson = await AsyncStorage.getItem(cacheKey);
        if (cachedJson) {
          const cachedData: CachedData<any> = JSON.parse(cachedJson);
          if (cachedData.expiresAt < now) {
            keysToRemove.push(cacheKey);
          }
        }
      }

      // Remove expired entries
      if (keysToRemove.length > 0) {
        await AsyncStorage.multiRemove(keysToRemove);
      }
    } catch (error) {
      console.error('Error clearing expired cache:', error);
    }
  }

  /**
   * Get the full cache key with prefix
   */
  private getCacheKey(key: string): string {
    return `${this.CACHE_PREFIX}${key}`;
  }
}

export default CacheService.getInstance();
