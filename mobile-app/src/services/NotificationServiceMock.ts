/**
 * Mock implementation of NotificationService for development in Expo Go
 * 
 * This mock service is used when running the app in Expo Go with SDK 53+,
 * where expo-notifications is no longer supported.
 * 
 * For production or development builds, use the real NotificationService.
 */

import AsyncStorage from '@react-native-async-storage/async-storage';
import STORAGE_KEYS from '../constants/storage-keys';

class NotificationServiceMock {
  private static instance: NotificationServiceMock;
  private pushToken: string | null = null;

  /**
   * Get the singleton instance
   */
  public static getInstance(): NotificationServiceMock {
    if (!NotificationServiceMock.instance) {
      NotificationServiceMock.instance = new NotificationServiceMock();
    }
    return NotificationServiceMock.instance;
  }

  /**
   * Initialize notification service (mock implementation)
   */
  public async initialize(): Promise<void> {
    console.log('⚠️ Using mock notification service in Expo Go');
    console.log('⚠️ For full notification support, create a development build');
    
    // Generate a mock push token
    const mockToken = `mock-expo-token-${Date.now()}`;
    this.pushToken = mockToken;
    
    // Save token to AsyncStorage for future reference
    await AsyncStorage.setItem(STORAGE_KEYS.PUSH_TOKEN, mockToken);
    
    console.log('Mock push token:', mockToken);
  }

  /**
   * Register for push notifications (mock implementation)
   */
  public async registerForPushNotifications(): Promise<string | null> {
    console.log('⚠️ Using mock push notification registration in Expo Go');
    return this.pushToken;
  }

  /**
   * Send a local notification (mock implementation)
   */
  public async sendLocalNotification(
    title: string,
    body: string,
    data: Record<string, any> = {}
  ): Promise<void> {
    console.log('⚠️ Mock local notification:', { title, body, data });
  }

  /**
   * Schedule a notification (mock implementation)
   */
  public async scheduleNotification(
    title: string,
    body: string,
    trigger: any,
    data: Record<string, any> = {}
  ): Promise<string> {
    console.log('⚠️ Mock scheduled notification:', { title, body, trigger, data });
    return `mock-notification-${Date.now()}`;
  }

  /**
   * Cancel all scheduled notifications (mock implementation)
   */
  public async cancelAllScheduledNotifications(): Promise<void> {
    console.log('⚠️ Mock: Cancelled all scheduled notifications');
  }

  /**
   * Get all scheduled notifications (mock implementation)
   */
  public async getAllScheduledNotifications(): Promise<any[]> {
    console.log('⚠️ Mock: Getting all scheduled notifications');
    return [];
  }

  /**
   * Clean up notification listeners (mock implementation)
   */
  public cleanup(): void {
    console.log('⚠️ Mock: Cleaned up notification listeners');
  }
}

export default NotificationServiceMock.getInstance();
