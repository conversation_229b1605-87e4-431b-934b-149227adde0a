import NetInfo, { NetInfoState, NetInfoSubscription } from '@react-native-community/netinfo';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { Platform } from 'react-native';
import STORAGE_KEYS from '../constants/storage-keys';

// Define the types of requests that can be queued
export type QueuedRequestType = 'POST' | 'PUT' | 'PATCH' | 'DELETE';

// Interface for a queued request
interface QueuedRequest {
  id: string;
  url: string;
  method: QueuedRequestType;
  data: any;
  headers?: Record<string, string>;
  createdAt: number;
}

class NetworkService {
  private static instance: NetworkService;
  private isConnected: boolean = true;
  private netInfoSubscription: NetInfoSubscription | null = null;
  private connectionListeners: Array<(isConnected: boolean) => void> = [];
  private requestQueue: QueuedRequest[] = [];
  private isProcessingQueue: boolean = false;
  private readonly QUEUE_STORAGE_KEY = STORAGE_KEYS.NETWORK_REQUEST_QUEUE;

  private constructor() {
    // Private constructor to enforce singleton pattern
  }

  public static getInstance(): NetworkService {
    if (!NetworkService.instance) {
      NetworkService.instance = new NetworkService();
    }
    return NetworkService.instance;
  }

  /**
   * Initialize network monitoring
   */
  public initialize(): void {
    // Start listening for network changes
    this.netInfoSubscription = NetInfo.addEventListener(this.handleNetworkChange);

    // Load any queued requests from storage
    this.loadQueueFromStorage();
  }

  /**
   * Handle network state changes
   */
  private handleNetworkChange = (state: NetInfoState): void => {
    const previouslyConnected = this.isConnected;
    this.isConnected = state.isConnected !== null ? state.isConnected : false;

    // If we just got connected and we weren't before, process the queue
    if (this.isConnected && !previouslyConnected) {
      this.processQueue();
    }

    // Notify listeners of connection change
    if (previouslyConnected !== this.isConnected) {
      this.notifyListeners();
    }
  };

  /**
   * Notify all connection listeners
   */
  private notifyListeners(): void {
    this.connectionListeners.forEach((listener) => {
      listener(this.isConnected);
    });
  }

  /**
   * Add a connection listener
   */
  public addConnectionListener(listener: (isConnected: boolean) => void): void {
    this.connectionListeners.push(listener);
    // Immediately notify the new listener of the current state
    listener(this.isConnected);
  }

  /**
   * Remove a connection listener
   */
  public removeConnectionListener(listener: (isConnected: boolean) => void): void {
    this.connectionListeners = this.connectionListeners.filter((l) => l !== listener);
  }

  /**
   * Check if the device is currently connected to the internet
   */
  public isNetworkConnected(): boolean {
    return this.isConnected;
  }

  /**
   * Queue a request to be sent when the network is available
   */
  public queueRequest(
    url: string,
    method: QueuedRequestType,
    data: any,
    headers?: Record<string, string>
  ): string {
    const id = Date.now().toString();
    const request: QueuedRequest = {
      id,
      url,
      method,
      data,
      headers,
      createdAt: Date.now(),
    };

    this.requestQueue.push(request);
    this.saveQueueToStorage();

    // If we're connected, try to process the queue immediately
    if (this.isConnected) {
      this.processQueue();
    }

    return id;
  }

  /**
   * Process the request queue
   */
  private async processQueue(): Promise<void> {
    // If we're already processing or there's nothing to process, return
    if (this.isProcessingQueue || this.requestQueue.length === 0 || !this.isConnected) {
      return;
    }

    this.isProcessingQueue = true;

    try {
      // Process each request in the queue
      const requestsToProcess = [...this.requestQueue];
      const successfulRequests: string[] = [];

      for (const request of requestsToProcess) {
        try {
          await this.sendRequest(request);
          successfulRequests.push(request.id);
        } catch (error) {
          console.error(`Failed to process queued request ${request.id}:`, error);
          // If the request failed, we'll leave it in the queue to try again later
        }
      }

      // Remove successful requests from the queue
      if (successfulRequests.length > 0) {
        this.requestQueue = this.requestQueue.filter(
          (request) => !successfulRequests.includes(request.id)
        );
        this.saveQueueToStorage();
      }
    } finally {
      this.isProcessingQueue = false;
    }
  }

  /**
   * Send a single request
   */
  private async sendRequest(request: QueuedRequest): Promise<any> {
    const response = await fetch(request.url, {
      method: request.method,
      headers: {
        'Content-Type': 'application/json',
        ...request.headers,
      },
      body: JSON.stringify(request.data),
    });

    if (!response.ok) {
      throw new Error(`Request failed with status ${response.status}`);
    }

    return response.json();
  }

  /**
   * Save the request queue to storage
   */
  private async saveQueueToStorage(): Promise<void> {
    try {
      await AsyncStorage.setItem(this.QUEUE_STORAGE_KEY, JSON.stringify(this.requestQueue));
    } catch (error) {
      console.error('Failed to save request queue to storage:', error);
    }
  }

  /**
   * Load the request queue from storage
   */
  private async loadQueueFromStorage(): Promise<void> {
    try {
      const queueJson = await AsyncStorage.getItem(this.QUEUE_STORAGE_KEY);
      if (queueJson) {
        this.requestQueue = JSON.parse(queueJson);

        // If we're connected, try to process the queue
        if (this.isConnected) {
          this.processQueue();
        }
      }
    } catch (error) {
      console.error('Failed to load request queue from storage:', error);
    }
  }

  /**
   * Clean up resources
   */
  public cleanup(): void {
    if (this.netInfoSubscription) {
      this.netInfoSubscription();
      this.netInfoSubscription = null;
    }
    this.connectionListeners = [];
  }
}

export default NetworkService.getInstance();
