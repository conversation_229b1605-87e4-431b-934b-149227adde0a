import api from './index';

/**
 * API service for dispute related operations
 */
const disputesAPI = {
  /**
   * Get all disputes for the current user
   */
  getMyDisputes: (params?: {
    limit?: number;
    offset?: number;
    status?: string;
    startDate?: string;
    endDate?: string;
  }) => {
    return api.get('/disputes', { params });
  },

  /**
   * Get dispute by ID
   */
  getDisputeById: (id: string) => {
    return api.get(`/disputes/${id}`);
  },

  /**
   * Create a new dispute
   */
  createDispute: (data: {
    jobId: string;
    applicationId: string;
    reason: string;
    description: string;
    attachments?: File[];
  }) => {
    const formData = new FormData();
    formData.append('jobId', data.jobId);
    formData.append('applicationId', data.applicationId);
    formData.append('reason', data.reason);
    formData.append('description', data.description);
    
    if (data.attachments && data.attachments.length > 0) {
      data.attachments.forEach((file, index) => {
        formData.append(`attachments`, file);
      });
    }
    
    return api.post('/disputes', formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    });
  },

  /**
   * Add a comment to a dispute
   */
  addComment: (disputeId: string, comment: string, attachments?: File[]) => {
    const formData = new FormData();
    formData.append('comment', comment);
    
    if (attachments && attachments.length > 0) {
      attachments.forEach((file, index) => {
        formData.append(`attachments`, file);
      });
    }
    
    return api.post(`/disputes/${disputeId}/comments`, formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    });
  },

  /**
   * Cancel a dispute
   */
  cancelDispute: (disputeId: string, reason: string) => {
    return api.patch(`/disputes/${disputeId}/cancel`, { reason });
  },

  /**
   * Get dispute reasons (for dropdown)
   */
  getDisputeReasons: () => {
    return api.get('/disputes/reasons');
  },
};

export default disputesAPI;
