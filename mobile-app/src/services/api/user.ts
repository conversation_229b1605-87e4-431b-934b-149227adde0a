import api from './index';

const userAPI = {
  /**
   * Get current user profile
   */
  getProfile: () => {
    return api.get('/users/profile');
  },

  /**
   * Update user profile
   */
  updateProfile: (data: FormData) => {
    return api.patch('/users/profile', data, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    });
  },

  /**
   * Upload document
   */
  uploadDocument: (type: string, file: any) => {
    const formData = new FormData();
    formData.append('type', type);
    formData.append('document', file);
    
    return api.post('/users/documents', formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    });
  },

  /**
   * Get user documents
   */
  getDocuments: () => {
    return api.get('/users/documents');
  },

  /**
   * Get user badges
   */
  getBadges: () => {
    return api.get('/users/badges');
  },

  /**
   * Get user trust score
   */
  getTrustScore: () => {
    return api.get('/users/trust-score');
  },

  /**
   * Get user earnings
   */
  getEarnings: () => {
    return api.get('/users/earnings');
  },
};

export default userAPI;