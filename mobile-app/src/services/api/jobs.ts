import api from './index';
import LocationService from '../LocationService';
import type { Job, JobCategory } from '../../types/api';

interface JobsResponse {
  data: Job[];
  total: number;
  page: number;
  limit: number;
}

interface JobResponse {
  data: Job;
  success: boolean;
  message: string;
}

interface JobCategoriesResponse {
  data: JobCategory[];
}

interface SavedJobResponse {
  success: boolean;
  message: string;
}

interface IsJobSavedResponse {
  data: {
    isSaved: boolean;
  };
}

interface ReportJobResponse {
  success: boolean;
  message: string;
}

/**
 * API service for job related operations
 */
const jobsAPI = {
  /**
   * Get all jobs with optional filters
   */
  getJobs: async (params?: {
    limit?: number;
    page?: number;
    search?: string;
    category?: string;
    status?: string;
    minSalary?: number;
    maxSalary?: number;
    startDate?: string;
    endDate?: string;
    sortBy?: string;
    sortOrder?: 'asc' | 'desc';
    useLocation?: boolean;
    maxDistance?: number;
  }): Promise<JobsResponse> => {
    try {
      // If useLocation is true, add location parameters
      if (params?.useLocation) {
        const location = await LocationService.getCurrentLocation();

        if (location) {
          const { latitude, longitude } = location.coords;

          // Add location to params
          params = {
            ...params,
            latitude,
            longitude,
            maxDistance: params.maxDistance || 10, // Default to 10km
          };
        }

        // Remove useLocation from params
        const { useLocation, ...restParams } = params;
        params = restParams;
      }

      const response = await api.get<JobsResponse>('/jobs', { params });
      return response.data;
    } catch (error) {
      console.error('Error fetching jobs:', error);
      throw error;
    }
  },

  /**
   * Get job by ID
   */
  getJobById: async (id: string): Promise<JobResponse> => {
    const response = await api.get<JobResponse>(`/jobs/${id}`);
    return response.data;
  },

  /**
   * Get recommended jobs based on user profile
   */
  getRecommendedJobs: async (params?: {
    limit?: number;
    page?: number;
    useLocation?: boolean;
    maxDistance?: number;
  }): Promise<JobsResponse> => {
    try {
      // If useLocation is true, add location parameters
      if (params?.useLocation) {
        const location = await LocationService.getCurrentLocation();

        if (location) {
          const { latitude, longitude } = location.coords;

          // Add location to params
          params = {
            ...params,
            latitude,
            longitude,
            maxDistance: params.maxDistance || 10, // Default to 10km
          };
        }

        // Remove useLocation from params
        const { useLocation, ...restParams } = params;
        params = restParams;
      }

      const response = await api.get<JobsResponse>('/jobs/recommended', { params });
      return response.data;
    } catch (error) {
      console.error('Error fetching recommended jobs:', error);
      throw error;
    }
  },

  /**
   * Get nearby jobs based on user location
   */
  getNearbyJobs: async (params?: {
    limit?: number;
    page?: number;
    maxDistance?: number; // in kilometers
  }): Promise<JobsResponse> => {
    try {
      const location = await LocationService.getCurrentLocation();

      if (!location) {
        throw new Error('Location not available');
      }

      const { latitude, longitude } = location.coords;

      const response = await api.get<JobsResponse>('/jobs/nearby', {
        params: {
          ...params,
          latitude,
          longitude,
          maxDistance: params?.maxDistance || 10, // Default to 10km
        },
      });

      return response.data;
    } catch (error) {
      console.error('Error fetching nearby jobs:', error);
      throw error;
    }
  },

  /**
   * Get job categories
   */
  getJobCategories: async (): Promise<JobCategoriesResponse> => {
    const response = await api.get<JobCategoriesResponse>('/jobs/categories');
    return response.data;
  },

  /**
   * Apply for a job
   */
  applyForJob: async (
    jobId: string,
    data: {
      coverLetter?: string;
      expectedSalary?: number;
      availability?: string;
      attachments?: File[];
    }
  ): Promise<{ success: boolean; message: string; applicationId?: string }> => {
    const formData = new FormData();

    if (data.coverLetter) {
      formData.append('coverLetter', data.coverLetter);
    }

    if (data.expectedSalary) {
      formData.append('expectedSalary', data.expectedSalary.toString());
    }

    if (data.availability) {
      formData.append('availability', data.availability);
    }

    if (data.attachments && data.attachments.length > 0) {
      data.attachments.forEach((file) => {
        formData.append('attachments', file);
      });
    }

    const response = await api.post<{ success: boolean; message: string; applicationId?: string }>(
      `/jobs/${jobId}/apply`,
      formData,
      {
        headers: {
          'Content-Type': 'multipart/form-data',
        },
      }
    );

    return response.data;
  },

  /**
   * Save job (add to favorites)
   */
  saveJob: async (jobId: string): Promise<SavedJobResponse> => {
    const response = await api.post<SavedJobResponse>(`/jobs/${jobId}/save`);
    return response.data;
  },

  /**
   * Unsave job (remove from favorites)
   */
  unsaveJob: async (jobId: string): Promise<SavedJobResponse> => {
    const response = await api.delete<SavedJobResponse>(`/jobs/${jobId}/save`);
    return response.data;
  },

  /**
   * Get saved jobs
   */
  getSavedJobs: async (params?: { limit?: number; page?: number }): Promise<JobsResponse> => {
    const response = await api.get<JobsResponse>('/jobs/saved', { params });
    return response.data;
  },

  /**
   * Check if a job is saved
   */
  isJobSaved: async (jobId: string): Promise<IsJobSavedResponse> => {
    const response = await api.get<IsJobSavedResponse>(`/jobs/${jobId}/is-saved`);
    return response.data;
  },

  /**
   * Report a job
   */
  reportJob: async (
    jobId: string,
    data: {
      reason: string;
      description: string;
    }
  ): Promise<ReportJobResponse> => {
    const response = await api.post<ReportJobResponse>(`/jobs/${jobId}/report`, data);
    return response.data;
  },
};

export default jobsAPI;
