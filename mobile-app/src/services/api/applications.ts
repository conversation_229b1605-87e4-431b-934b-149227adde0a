import api from './index';
import type { Application, ApplicationStatus } from '../../types/api';

interface ApplicationResponse {
  data: Application;
  success: boolean;
  message: string;
}

interface ApplicationsListResponse {
  data: Application[];
  total: number;
  page: number;
  limit: number;
}

interface ApplicationData {
  coverLetter?: string;
  availability?: string[];
  preferredPaymentMethod?: string;
}

const applicationsAPI = {
  /**
   * Get all applications for the current user
   */
  getMyApplications: () => {
    return api.get<ApplicationsListResponse>('/applications/my');
  },

  /**
   * Get application by ID
   */
  getApplicationById: (id: string) => {
    return api.get<ApplicationResponse>(`/applications/${id}`);
  },

  /**
   * Apply for a job
   */
  applyForJob: (jobId: string, data: ApplicationData) => {
    return api.post<ApplicationResponse>(`/applications/job/${jobId}`, data);
  },

  /**
   * Cancel an application
   */
  cancelApplication: (id: string, reason: string) => {
    return api.patch<ApplicationResponse>(`/applications/${id}/cancel`, { reason });
  },

  /**
   * Update application status (for testing)
   */
  updateStatus: (id: string, status: ApplicationStatus) => {
    return api.patch<ApplicationResponse>(`/applications/${id}/status`, { status });
  },
};

export default applicationsAPI;
