import api from './index';

/**
 * API service for trust score related operations
 */
const trustScoreAPI = {
  /**
   * Get current user's trust score details
   */
  getTrustScore: () => {
    return api.get('/users/trust-score');
  },

  /**
   * Get trust score history/logs
   */
  getTrustScoreLogs: () => {
    return api.get('/users/trust-score/logs');
  },

  /**
   * Get trust score improvement suggestions
   */
  getTrustScoreImprovementSuggestions: () => {
    return api.get('/users/trust-score/suggestions');
  },

  /**
   * Get trust score breakdown by categories
   */
  getTrustScoreBreakdown: () => {
    return api.get('/users/trust-score/breakdown');
  },
};

export default trustScoreAPI;
