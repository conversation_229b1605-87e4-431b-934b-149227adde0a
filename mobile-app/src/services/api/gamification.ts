import api from './index';

/**
 * API service for gamification related operations
 */
const gamificationAPI = {
  /**
   * Get all badges for the current user
   */
  getMyBadges: () => {
    return api.get('/gamification/badges');
  },

  /**
   * Get all available badges
   */
  getAllBadges: () => {
    return api.get('/gamification/badges/all');
  },

  /**
   * Get badge by ID
   */
  getBadgeById: (id: string) => {
    return api.get(`/gamification/badges/${id}`);
  },

  /**
   * Get user's current level
   */
  getMyLevel: () => {
    return api.get('/gamification/level');
  },

  /**
   * Get all levels
   */
  getAllLevels: () => {
    return api.get('/gamification/levels');
  },

  /**
   * Get user's achievements
   */
  getMyAchievements: () => {
    return api.get('/gamification/achievements');
  },

  /**
   * Get all available achievements
   */
  getAllAchievements: () => {
    return api.get('/gamification/achievements/all');
  },

  /**
   * Get user's progress towards next level
   */
  getLevelProgress: () => {
    return api.get('/gamification/level/progress');
  },

  /**
   * Get user's points history
   */
  getPointsHistory: (params?: {
    limit?: number;
    offset?: number;
    startDate?: string;
    endDate?: string;
  }) => {
    return api.get('/gamification/points/history', { params });
  },

  /**
   * Get user's current points
   */
  getPoints: () => {
    return api.get('/gamification/points');
  },

  /**
   * Get leaderboard
   */
  getLeaderboard: (params?: {
    limit?: number;
    offset?: number;
    timeframe?: 'week' | 'month' | 'all';
  }) => {
    return api.get('/gamification/leaderboard', { params });
  },
};

export default gamificationAPI;
