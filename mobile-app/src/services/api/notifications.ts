import api from './index';

/**
 * API service for notification related operations
 */
const notificationsAPI = {
  /**
   * Get all notifications for the current user
   */
  getNotifications: (params?: {
    limit?: number;
    offset?: number;
    read?: boolean;
    startDate?: string;
    endDate?: string;
  }) => {
    return api.get('/notifications', { params });
  },

  /**
   * Mark notification as read
   */
  markAsRead: (notificationId: string) => {
    return api.patch(`/notifications/${notificationId}/read`);
  },

  /**
   * Mark all notifications as read
   */
  markAllAsRead: () => {
    return api.patch('/notifications/read-all');
  },

  /**
   * Delete notification
   */
  deleteNotification: (notificationId: string) => {
    return api.delete(`/notifications/${notificationId}`);
  },

  /**
   * Register device for push notifications
   */
  registerDevice: (data: {
    token: string;
    platform: string;
    deviceId: string;
  }) => {
    return api.post('/notifications/devices', data);
  },

  /**
   * Unregister device from push notifications
   */
  unregisterDevice: (token: string) => {
    return api.delete(`/notifications/devices/${token}`);
  },

  /**
   * Get notification preferences
   */
  getPreferences: () => {
    return api.get('/notifications/preferences');
  },

  /**
   * Update notification preferences
   */
  updatePreferences: (preferences: {
    jobAlerts?: boolean;
    applicationUpdates?: boolean;
    paymentNotifications?: boolean;
    chatMessages?: boolean;
    marketingEmails?: boolean;
  }) => {
    return api.patch('/notifications/preferences', preferences);
  },

  /**
   * Get notification count
   */
  getUnreadCount: () => {
    return api.get('/notifications/unread-count');
  },
};

export default notificationsAPI;
