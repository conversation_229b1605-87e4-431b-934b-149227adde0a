import api from './index';

/**
 * API service for payment related operations
 */
const paymentsAPI = {
  /**
   * Get payment methods
   */
  getPaymentMethods: () => {
    return api.get('/payments/methods');
  },

  /**
   * Process a payment using Stripe
   */
  processStripePayment: (data: {
    amount: number;
    currency: string;
    description?: string;
    paymentMethodId: string;
    customerId?: string;
    metadata?: Record<string, any>;
  }) => {
    return api.post('/payments/stripe', data);
  },

  /**
   * Process a payment using UPI
   */
  processUpiPayment: (data: {
    amount: number;
    vpa: string;
    description?: string;
    reference?: string;
    metadata?: Record<string, any>;
  }) => {
    return api.post('/payments/upi', data);
  },

  /**
   * Create a Razorpay order
   */
  createRazorpayOrder: (data: {
    amount: number;
    currency: string;
    description?: string;
    customerEmail?: string;
    customerPhone?: string;
    metadata?: Record<string, any>;
  }) => {
    return api.post('/payments/razorpay/order', data);
  },

  /**
   * Process a payment using Razorpay
   */
  processRazorpayPayment: (data: {
    amount: number;
    currency: string;
    description?: string;
    customerEmail?: string;
    customerPhone?: string;
    metadata?: Record<string, any>;
    orderId?: string;
  }) => {
    return api.post('/payments/razorpay', data);
  },

  /**
   * Verify Razorpay payment signature
   */
  verifyRazorpaySignature: (data: { orderId: string; paymentId: string; signature: string }) => {
    return api.post('/payments/razorpay/verify', data);
  },

  /**
   * Get payment history
   */
  getPaymentHistory: (params?: {
    limit?: number;
    offset?: number;
    status?: string;
    startDate?: string;
    endDate?: string;
  }) => {
    return api.get('/payments/history', { params });
  },

  /**
   * Get payment details by ID
   */
  getPaymentById: (id: string) => {
    return api.get(`/payments/${id}`);
  },
};

export default paymentsAPI;
