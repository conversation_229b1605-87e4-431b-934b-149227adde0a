import api from './index';

/**
 * API service for ratings related operations
 */
const ratingsAPI = {
  /**
   * Get ratings received by the current user
   */
  getMyRatings: (params?: {
    limit?: number;
    offset?: number;
    startDate?: string;
    endDate?: string;
  }) => {
    return api.get('/ratings/received', { params });
  },

  /**
   * Get ratings given by the current user
   */
  getGivenRatings: (params?: {
    limit?: number;
    offset?: number;
    startDate?: string;
    endDate?: string;
  }) => {
    return api.get('/ratings/given', { params });
  },

  /**
   * Get rating by ID
   */
  getRatingById: (id: string) => {
    return api.get(`/ratings/${id}`);
  },

  /**
   * Rate a company
   */
  rateCompany: (data: {
    jobId: string;
    applicationId: string;
    rating: number;
    comment?: string;
  }) => {
    return api.post('/ratings/company', data);
  },

  /**
   * Get ratings for a specific job
   */
  getJobRatings: (jobId: string) => {
    return api.get(`/ratings/job/${jobId}`);
  },

  /**
   * Get ratings summary for the current user
   */
  getRatingsSummary: () => {
    return api.get('/ratings/summary');
  },

  /**
   * Update a rating
   */
  updateRating: (id: string, data: { rating: number; comment?: string }) => {
    return api.patch(`/ratings/${id}`, data);
  },

  /**
   * Delete a rating
   */
  deleteRating: (id: string) => {
    return api.delete(`/ratings/${id}`);
  },
};

export default ratingsAPI;
