import api from './index';
import type { AuthResponse } from '../../types/api';

interface OtpResponse {
  success: boolean;
  message: string;
}

interface RefreshTokenResponse {
  token: string;
  refreshToken: string;
}

interface LogoutResponse {
  success: boolean;
  message: string;
}

const authAPI = {
  /**
   * Request OTP for phone number
   */
  requestOtp: (phone: string) => {
    return api.post<OtpResponse>('/auth/otp/request', { phone });
  },

  /**
   * Verify OTP and login/register
   */
  verifyOtp: (phone: string, code: string) => {
    return api.post<AuthResponse>('/auth/otp/verify', { phone, code });
  },

  /**
   * Refresh access token using refresh token
   */
  refreshToken: (refreshToken: string) => {
    return api.post<RefreshTokenResponse>('/auth/refresh-token', { refreshToken });
  },

  /**
   * Logout user
   */
  logout: () => {
    return api.post<LogoutResponse>('/auth/logout');
  },
};

export default authAPI;
