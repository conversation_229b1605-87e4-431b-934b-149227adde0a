import { createNativeStackNavigator } from '@react-navigation/native-stack';
import ChatsListScreen from '../screens/chat/ChatsListScreen';
import ChatDetailsScreen from '../screens/chat/ChatDetailsScreen';
import { SCREENS } from '../constants/screens';
import { ChatStackParamList } from '../types/navigation';

const Stack = createNativeStackNavigator<ChatStackParamList>();

const ChatNavigator = () => {
  return (
    <Stack.Navigator
      screenOptions={{
        headerShown: false,
      }}>
      <Stack.Screen name={SCREENS.CHATS_LIST} component={ChatsListScreen} />
      <Stack.Screen name={SCREENS.CHAT_DETAILS} component={ChatDetailsScreen} />
    </Stack.Navigator>
  );
};

export default ChatNavigator;
