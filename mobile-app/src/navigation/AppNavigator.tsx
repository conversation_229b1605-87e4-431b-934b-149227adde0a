import { createNativeStackNavigator } from '@react-navigation/native-stack';
import { useAppSelector } from '../store';
import Loading from '../components/Loading';
import AuthStack from './AuthStack';
import MainTabs from './MainTabs';
import OnboardingScreen from '../screens/onboarding/OnboardingScreen';
import { SCREENS } from '../constants/screens';
import { RootStackParamList } from '../types/navigation';

const Stack = createNativeStackNavigator<RootStackParamList>();

export default function AppNavigator() {
  const { user, isLoading, hasCompletedOnboarding } = useAppSelector((state) => state.auth);

  if (isLoading) {
    return <Loading />;
  }

  return (
    <Stack.Navigator screenOptions={{ headerShown: false }}>
      {!user ? (
        <Stack.Screen name={SCREENS.AUTH} component={AuthStack} />
      ) : !hasCompletedOnboarding ? (
        <Stack.Screen name={SCREENS.ONBOARDING} component={OnboardingScreen} />
      ) : (
        <Stack.Screen name={SCREENS.MAIN} component={MainTabs} />
      )}
    </Stack.Navigator>
  );
}
