import { createNativeStackNavigator } from '@react-navigation/native-stack';
import ApplicationsScreen from '../screens/applications/ApplicationsScreen';
import ApplicationDetailsScreen from '../screens/applications/ApplicationDetailsScreen';
import { SCREENS } from '../constants/screens';

// Define the applications stack param list
export type ApplicationsStackParamList = {
  [SCREENS.APPLICATIONS_LIST]: undefined;
  [SCREENS.APPLICATION_DETAILS]: { applicationId: string };
};

const Stack = createNativeStackNavigator<ApplicationsStackParamList>();

const ApplicationsNavigator = () => {
  return (
    <Stack.Navigator
      screenOptions={{
        headerShown: false,
      }}>
      <Stack.Screen name={SCREENS.APPLICATIONS_LIST} component={ApplicationsScreen} />
      <Stack.Screen name={SCREENS.APPLICATION_DETAILS} component={ApplicationDetailsScreen} />
    </Stack.Navigator>
  );
};

export default ApplicationsNavigator;
