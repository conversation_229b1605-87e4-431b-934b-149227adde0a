import { createBottomTabNavigator } from '@react-navigation/bottom-tabs';
import { Ionicons } from '@expo/vector-icons';
import { useThemeContext } from '../providers/ThemeProvider';
import JobsNavigator from './JobsNavigator';
import ApplicationsNavigator from './ApplicationsNavigator';
import FavoritesScreen from '../screens/favorites/FavoritesScreen';
import ProfileNavigator from './ProfileNavigator';
import ChatNavigator from './ChatNavigator';
import { SCREENS } from '../constants/screens';
import { StyledTabNavigator } from '../utils/navigationInterop';
import { MainTabParamList } from '../types/navigation';

const Tab = createBottomTabNavigator<MainTabParamList>();

export default function MainTabs() {
  // Use theme context to access theme utilities
  const { theme, isDark } = useThemeContext();

  return (
    <StyledTabNavigator
      className="bg-background"
      headerClassName="bg-background border-b border-border"
      tabBarClassName="bg-card border-t border-border pt-1 pb-1 h-[60px]"
      tabBarActiveTintColorClass="text-primary"
      tabBarInactiveTintColorClass="text-gray-500"
      screenOptions={{
        headerShown: false,
      }}>
      <Tab.Screen
        name={SCREENS.JOBS_TAB}
        component={JobsNavigator}
        options={{
          tabBarLabel: 'Jobs',
          tabBarIcon: ({ color, size }) => (
            <Ionicons name="briefcase-outline" size={size} color={color} />
          ),
        }}
      />
      <Tab.Screen
        name={SCREENS.APPLICATIONS_TAB}
        component={ApplicationsNavigator}
        options={{
          tabBarLabel: 'Applications',
          tabBarIcon: ({ color, size }) => (
            <Ionicons name="document-text-outline" size={size} color={color} />
          ),
        }}
      />
      <Tab.Screen
        name={SCREENS.FAVORITES_TAB}
        component={FavoritesScreen}
        options={{
          tabBarLabel: 'Favorites',
          tabBarIcon: ({ color, size }) => (
            <Ionicons name="heart-outline" size={size} color={color} />
          ),
        }}
      />
      <Tab.Screen
        name={SCREENS.CHAT_TAB}
        component={ChatNavigator}
        options={{
          tabBarLabel: 'Messages',
          tabBarIcon: ({ color, size }) => (
            <Ionicons name="chatbubble-outline" size={size} color={color} />
          ),
        }}
      />
      <Tab.Screen
        name={SCREENS.PROFILE_TAB}
        component={ProfileNavigator}
        options={{
          tabBarLabel: 'Profile',
          tabBarIcon: ({ color, size }) => (
            <Ionicons name="person-outline" size={size} color={color} />
          ),
        }}
      />
    </StyledTabNavigator>
  );
}
