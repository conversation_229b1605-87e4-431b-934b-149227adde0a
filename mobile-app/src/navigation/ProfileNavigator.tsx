import { createNativeStackNavigator } from '@react-navigation/native-stack';
import ProfileScreen from '../screens/profile/ProfileScreen';
import EditProfileScreen from '../screens/profile/EditProfileScreen';
import SettingsScreen from '../screens/profile/SettingsScreen';
import ThemeSettingsScreen from '../screens/profile/ThemeSettingsScreen';
import TrustScoreScreen from '../screens/profile/TrustScoreScreen';
import DocumentsScreen from '../screens/profile/DocumentsScreen';
import PaymentsScreen from '../screens/payments/PaymentsScreen';
import DisputesScreen from '../screens/disputes/DisputesScreen';
import DisputeDetailsScreen from '../screens/disputes/DisputeDetailsScreen';
import CreateDisputeScreen from '../screens/disputes/CreateDisputeScreen';
import RatingsScreen from '../screens/ratings/RatingsScreen';
import RateCompanyScreen from '../screens/ratings/RateCompanyScreen';
import BadgesScreen from '../screens/gamification/BadgesScreen';
import BadgeDetailsScreen from '../screens/gamification/BadgeDetailsScreen';
import NotificationsScreen from '../screens/notifications/NotificationsScreen';
import NotificationPreferencesScreen from '../screens/notifications/NotificationPreferencesScreen';
import { SCREENS } from '../constants/screens';

// Define the profile stack param list
export type ProfileStackParamList = {
  [SCREENS.PROFILE]: undefined;
  [SCREENS.EDIT_PROFILE]: undefined;
  [SCREENS.SETTINGS]: undefined;
  [SCREENS.THEME_SETTINGS]: undefined;
  [SCREENS.TRUST_SCORE]: undefined;
  [SCREENS.PAYMENTS]: undefined;
  [SCREENS.DISPUTES]: undefined;
  [SCREENS.DISPUTE_DETAILS]: { disputeId: string };
  [SCREENS.CREATE_DISPUTE]: { jobId?: string; applicationId?: string };
  [SCREENS.RATINGS]: undefined;
  [SCREENS.RATE_COMPANY]: { jobId: string; companyId: string };
  [SCREENS.BADGES]: undefined;
  [SCREENS.BADGE_DETAILS]: { badgeId: string };
  [SCREENS.NOTIFICATIONS]: undefined;
  [SCREENS.NOTIFICATION_PREFERENCES]: undefined;
};

const Stack = createNativeStackNavigator<ProfileStackParamList>();

const ProfileNavigator = () => {
  return (
    <Stack.Navigator
      screenOptions={{
        headerShown: false,
      }}>
      <Stack.Screen name={SCREENS.PROFILE} component={ProfileScreen} />
      <Stack.Screen name={SCREENS.EDIT_PROFILE} component={EditProfileScreen} />
      <Stack.Screen name={SCREENS.SETTINGS} component={SettingsScreen} />
      <Stack.Screen name={SCREENS.THEME_SETTINGS} component={ThemeSettingsScreen} />
      <Stack.Screen name={SCREENS.TRUST_SCORE} component={TrustScoreScreen} />
      <Stack.Screen name={SCREENS.PAYMENTS} component={PaymentsScreen} />
      <Stack.Screen name={SCREENS.DISPUTES} component={DisputesScreen} />
      <Stack.Screen name={SCREENS.DISPUTE_DETAILS} component={DisputeDetailsScreen} />
      <Stack.Screen name={SCREENS.CREATE_DISPUTE} component={CreateDisputeScreen} />
      <Stack.Screen name={SCREENS.RATINGS} component={RatingsScreen} />
      <Stack.Screen name={SCREENS.RATE_COMPANY} component={RateCompanyScreen} />
      <Stack.Screen name={SCREENS.BADGES} component={BadgesScreen} />
      <Stack.Screen name={SCREENS.BADGE_DETAILS} component={BadgeDetailsScreen} />
      <Stack.Screen name={SCREENS.NOTIFICATIONS} component={NotificationsScreen} />
      <Stack.Screen
        name={SCREENS.NOTIFICATION_PREFERENCES}
        component={NotificationPreferencesScreen}
      />
    </Stack.Navigator>
  );
};

export default ProfileNavigator;
