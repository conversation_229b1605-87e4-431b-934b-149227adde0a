import { createNativeStackNavigator } from '@react-navigation/native-stack';
import JobsScreen from '../screens/jobs/JobsScreen';
import JobDetailsScreen from '../screens/jobs/JobDetailsScreen';
import NearbyJobsScreen from '../screens/jobs/NearbyJobsScreen';
import { SCREENS } from '../constants/screens';
import { RootStackParamList } from '@/types/navigation';

const Stack = createNativeStackNavigator<RootStackParamList>();

const JobsNavigator = () => {
  return (
    <Stack.Navigator
      screenOptions={{
        headerShown: false,
      }}>
      <Stack.Screen name={SCREENS.JOBS_HOME} component={JobsScreen} />
      <Stack.Screen name={SCREENS.JOB_DETAILS} component={JobDetailsScreen} />
      <Stack.Screen name={SCREENS.NEARBY_JOBS} component={NearbyJobsScreen} />
    </Stack.Navigator>
  );
};

export default JobsNavigator;
