import React, { useEffect } from 'react';
import { StyleSheet } from 'react-native';

/**
 * This component ensures NativeWind is properly initialized
 * It should be included at the root of your app
 */
export const NativeWindSetup: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  useEffect(() => {
    // Force StyleSheet registration on mount
    StyleSheet.create({});
    console.log('NativeWind setup initialized');
  }, []);

  return <>{children}</>;
};

export default NativeWindSetup;
