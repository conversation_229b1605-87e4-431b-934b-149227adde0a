/**
 * Utility functions for safely initializing the app with <PERSON><PERSON>
 */

/**
 * Safely initializes global objects needed for Hermes compatibility
 */
export function safeInitializeGlobals(): void {
  try {
    // Ensure global exists
    if (typeof global !== 'undefined') {
      // Ensure process exists
      if (!global.process) {
        global.process = {};
      }
      
      // Ensure process.env exists
      if (!global.process.env) {
        global.process.env = {};
      }
    }
  } catch (error) {
    console.warn('Error initializing globals:', error);
  }
}

/**
 * Checks if Her<PERSON> is enabled
 */
export function isHermesEnabled(): boolean {
  try {
    return typeof global.HermesInternal !== 'undefined' && global.HermesInternal !== null;
  } catch (error) {
    console.warn('Error checking Hermes status:', error);
    return false;
  }
}

/**
 * Safely executes a function with error handling
 */
export function safeExecute<T>(fn: () => T, fallback: T): T {
  try {
    return fn();
  } catch (error) {
    console.warn('Error in safe execution:', error);
    return fallback;
  }
}
