# Navigation Utilities

This document explains how to use the navigation utilities in the mobile app.

## Overview

The navigation utilities provide a type-safe way to navigate between screens in the app. They are designed to work with React Navigation and provide a consistent API for navigation.

## Navigation Types

All navigation types are defined in `src/types/navigation.ts`. This file contains:

- Type definitions for all navigation stacks
- Type definitions for all screen parameters
- Utility types for navigation props
- Utility functions for navigation

## Navigation Utilities

The navigation utilities are defined in `src/utils/navigation.ts`. This file contains:

- Functions for navigating to different screens
- Functions for resetting the navigation state
- Functions for going back
- Functions for navigating between stacks

## How to Use

### Basic Navigation

To navigate to a screen, use the `navigate` function:

```typescript
import { navigate } from '../utils/navigation';
import { SCREENS } from '../constants/screens';

// Navigate to a screen
navigate(SCREENS.HOME);

// Navigate to a screen with parameters
navigate(SCREENS.JOB_DETAILS, { jobId: '123' });
```

### Typed Navigation Hooks

To get typed navigation props, use the `useAppNavigation` hook:

```typescript
import { useAppNavigation } from '../utils/navigation';

const MyComponent = () => {
  const navigation = useAppNavigation();
  
  // Navigate to a screen
  navigation.navigate(SCREENS.HOME);
  
  // Navigate to a screen with parameters
  navigation.navigate(SCREENS.JOB_DETAILS, { jobId: '123' });
  
  return (
    // ...
  );
};
```

### Typed Route Hooks

To get typed route parameters, use the `useAppRoute` hook:

```typescript
import { useAppRoute } from '../utils/navigation';
import { SCREENS } from '../constants/screens';

const JobDetailsScreen = () => {
  const route = useAppRoute<typeof SCREENS.JOB_DETAILS>();
  const { jobId } = route.params;
  
  return (
    // ...
  );
};
```

### Stack Navigation

To navigate between stacks, use the stack-specific navigation functions:

```typescript
import { 
  navigateToAuth, 
  navigateToMain, 
  navigateToJobs, 
  navigateToApplications, 
  navigateToChat, 
  navigateToProfile 
} from '../utils/navigation';
import { SCREENS } from '../constants/screens';

// Navigate to the auth stack
navigateToAuth(SCREENS.LOGIN);

// Navigate to the main tabs
navigateToMain(SCREENS.JOBS_TAB);

// Navigate to the jobs stack
navigateToJobs(SCREENS.JOB_DETAILS, { jobId: '123' });

// Navigate to the applications stack
navigateToApplications(SCREENS.APPLICATION_DETAILS, { applicationId: '123' });

// Navigate to the chat stack
navigateToChat(SCREENS.CHAT_DETAILS, { chatId: '123', recipientId: '456' });

// Navigate to the profile stack
navigateToProfile(SCREENS.EDIT_PROFILE);
```

### Reset Navigation

To reset the navigation state, use the reset functions:

```typescript
import { resetToLogin, resetToMain, resetToOnboarding } from '../utils/navigation';

// Reset to the login screen
resetToLogin();

// Reset to the main screen
resetToMain();

// Reset to the onboarding screen
resetToOnboarding();
```

## Best Practices

1. **Always use typed navigation**: Use the navigation utilities instead of direct navigation to ensure type safety.

2. **Define all screen parameters**: Make sure all screen parameters are defined in the navigation types.

3. **Use the correct navigation function**: Use the stack-specific navigation functions when navigating between stacks.

4. **Handle navigation errors**: Use try-catch blocks when navigating to handle any errors.

5. **Document navigation parameters**: Document the parameters required for each screen to make it easier for other developers to use.

## Troubleshooting

### Navigation is not working

- Make sure the navigation container is properly set up in `App.tsx`
- Make sure the navigation reference is set using `setNavigationRef`
- Check if the screen name is correct
- Check if the parameters are correct

### Type errors

- Make sure the screen name is defined in the navigation types
- Make sure the parameters are defined in the navigation types
- Make sure you're using the correct navigation function for the stack

## Example

```typescript
import React from 'react';
import { View, Text, TouchableOpacity } from 'react-native';
import { SCREENS } from '../constants/screens';
import { navigateToJobs } from '../utils/navigation';

const HomeScreen = () => {
  return (
    <View>
      <Text>Home Screen</Text>
      <TouchableOpacity
        onPress={() => navigateToJobs(SCREENS.JOB_DETAILS, { jobId: '123' })}
      >
        <Text>View Job Details</Text>
      </TouchableOpacity>
    </View>
  );
};

export default HomeScreen;
```
