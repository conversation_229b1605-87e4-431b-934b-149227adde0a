import React from 'react';
import { cssInterop } from 'nativewind';
import { createBottomTabNavigator } from '@react-navigation/bottom-tabs';
import { createNativeStackNavigator } from '@react-navigation/native-stack';

/**
 * CSS Interop for Bottom Tab Navigator
 * Allows styling the Tab Navigator with Tailwind classes
 */
export const StyledTabNavigator = cssInterop(
  (props) => {
    const { children, headerStyle, tabBarStyle, tabBarActiveTintColor, tabBarInactiveTintColor, ...rest } = props;
    const Tab = createBottomTabNavigator();
    
    return (
      <Tab.Navigator
        screenOptions={{
          headerStyle,
          tabBarStyle,
          tabBarActiveTintColor,
          tabBarInactiveTintColor,
        }}
        {...rest}>
        {children}
      </Tab.Navigator>
    );
  },
  {
    headerClassName: 'headerStyle',
    tabBarClassName: 'tabBarStyle',
    tabBarActiveTintColorClass: 'tabBarActiveTintColor',
    tabBarInactiveTintColorClass: 'tabBarInactiveTintColor',
  }
);

/**
 * CSS Interop for Stack Navigator
 * Allows styling the Stack Navigator with Tailwind classes
 */
export const StyledStackNavigator = cssInterop(
  (props) => {
    const { children, headerStyle, contentStyle, ...rest } = props;
    const Stack = createNativeStackNavigator();
    
    return (
      <Stack.Navigator
        screenOptions={{
          headerStyle,
          contentStyle,
        }}
        {...rest}>
        {children}
      </Stack.Navigator>
    );
  },
  {
    headerClassName: 'headerStyle',
    contentClassName: 'contentStyle',
  }
);

/**
 * CSS Interop for Tab.Screen
 * Allows styling the Tab.Screen with Tailwind classes
 */
export const StyledTabScreen = cssInterop(
  (props) => {
    const { children, headerStyle, tabBarItemStyle, ...rest } = props;
    const Tab = createBottomTabNavigator();
    
    return (
      <Tab.Screen
        options={{
          headerStyle,
          tabBarItemStyle,
        }}
        {...rest}>
        {children}
      </Tab.Screen>
    );
  },
  {
    headerClassName: 'headerStyle',
    tabBarItemClassName: 'tabBarItemStyle',
  }
);

/**
 * CSS Interop for Stack.Screen
 * Allows styling the Stack.Screen with Tailwind classes
 */
export const StyledStackScreen = cssInterop(
  (props) => {
    const { children, headerStyle, contentStyle, ...rest } = props;
    const Stack = createNativeStackNavigator();
    
    return (
      <Stack.Screen
        options={{
          headerStyle,
          contentStyle,
        }}
        {...rest}>
        {children}
      </Stack.Screen>
    );
  },
  {
    headerClassName: 'headerStyle',
    contentClassName: 'contentStyle',
  }
);
