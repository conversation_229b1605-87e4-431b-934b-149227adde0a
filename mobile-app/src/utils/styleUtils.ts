import { StyleSheet } from 'react-native';

/**
 * A utility function to create styles from className strings
 * This helps ensure NativeWind styles are properly processed
 * 
 * @param classNames - The className string to process
 * @returns The processed className string
 */
export function tw(classNames: string): string {
  // Force StyleSheet registration to ensure NativeWind processing
  StyleSheet.create({});
  return classNames;
}

/**
 * Combines multiple className strings, filtering out falsy values
 * 
 * @param classNames - Array of className strings to combine
 * @returns Combined className string
 */
export function cn(...classNames: (string | boolean | undefined | null)[]): string {
  return classNames.filter(Boolean).join(' ');
}

export default {
  tw,
  cn,
};
