/**
 * Navigation utility functions
 * This file provides helper functions for type-safe navigation
 */
import { CommonActions, NavigationContainerRef } from '@react-navigation/native';
import { SCREENS } from '../constants/screens';
import {
  RootStackParamList,
  AuthStackParamList,
  MainTabParamList,
  JobsStackParamList,
  ApplicationsStackParamList,
  ChatStackParamList,
  ProfileStackParamList,
} from '../types/navigation';

// Reference to the navigation container
let navigationRef: NavigationContainerRef<RootStackParamList> | null = null;

/**
 * Set the navigation reference
 * @param ref Navigation container reference
 */
export function setNavigationRef(ref: NavigationContainerRef<RootStackParamList> | null): void {
  navigationRef = ref;
}

/**
 * Navigate to a screen
 * @param name Screen name
 * @param params Screen parameters
 */
export function navigate<RouteName extends keyof RootStackParamList>(
  name: RouteName,
  params?: RootStackParamList[RouteName]
): void {
  if (navigationRef?.isReady()) {
    // Use the CommonActions approach which is more flexible with types
    navigationRef.dispatch(
      CommonActions.navigate({
        name: name as string,
        params,
      })
    );
  } else {
    console.warn('Navigation is not ready. Cannot navigate to:', name);
  }
}

/**
 * Reset the navigation state
 * @param routes Routes to set
 * @param index Index of the active route
 */
export function reset(
  routes: { name: keyof RootStackParamList; params?: any }[],
  index: number = 0
): void {
  if (navigationRef?.isReady()) {
    navigationRef.dispatch(
      CommonActions.reset({
        index,
        routes,
      })
    );
  } else {
    console.warn('Navigation is not ready. Cannot reset navigation.');
  }
}

/**
 * Go back to the previous screen
 */
export function goBack(): void {
  if (navigationRef?.isReady() && navigationRef.canGoBack()) {
    navigationRef.goBack();
  } else {
    console.warn('Cannot go back. Navigation is not ready or no screens to go back to.');
  }
}

/**
 * Navigate to the auth stack
 * @param screen Screen name in the auth stack
 * @param params Screen parameters
 */
export function navigateToAuth<RouteName extends keyof AuthStackParamList>(
  screen: RouteName,
  params?: AuthStackParamList[RouteName]
): void {
  navigate(SCREENS.AUTH, { screen, params } as any);
}

/**
 * Navigate to the main tabs
 * @param screen Screen name in the main tabs
 * @param params Screen parameters
 */
export function navigateToMain<RouteName extends keyof MainTabParamList>(
  screen: RouteName,
  params?: MainTabParamList[RouteName]
): void {
  navigate(SCREENS.MAIN, { screen, params } as any);
}

/**
 * Navigate to the jobs stack
 * @param screen Screen name in the jobs stack
 * @param params Screen parameters
 */
export function navigateToJobs<RouteName extends keyof JobsStackParamList>(
  screen: RouteName,
  params?: JobsStackParamList[RouteName]
): void {
  navigateToMain(SCREENS.JOBS_TAB, { screen, params } as any);
}

/**
 * Navigate to the applications stack
 * @param screen Screen name in the applications stack
 * @param params Screen parameters
 */
export function navigateToApplications<RouteName extends keyof ApplicationsStackParamList>(
  screen: RouteName,
  params?: ApplicationsStackParamList[RouteName]
): void {
  navigateToMain(SCREENS.APPLICATIONS_TAB, { screen, params } as any);
}

/**
 * Navigate to the chat stack
 * @param screen Screen name in the chat stack
 * @param params Screen parameters
 */
export function navigateToChat<RouteName extends keyof ChatStackParamList>(
  screen: RouteName,
  params?: ChatStackParamList[RouteName]
): void {
  navigateToMain(SCREENS.CHAT_TAB, { screen, params } as any);
}

/**
 * Navigate to the profile stack
 * @param screen Screen name in the profile stack
 * @param params Screen parameters
 */
export function navigateToProfile<RouteName extends keyof ProfileStackParamList>(
  screen: RouteName,
  params?: ProfileStackParamList[RouteName]
): void {
  navigateToMain(SCREENS.PROFILE_TAB, { screen, params } as any);
}

/**
 * Reset navigation to the login screen
 *
 * This function resets the navigation state to the auth stack with the login screen
 * as the initial screen.
 */
export function resetToLogin(): void {
  if (navigationRef?.isReady()) {
    navigationRef.dispatch(
      CommonActions.reset({
        index: 0,
        routes: [
          {
            name: SCREENS.AUTH,
            params: { screen: SCREENS.LOGIN },
          },
        ],
      })
    );
  } else {
    console.warn('Navigation is not ready. Cannot reset to login screen.');
  }
}

/**
 * Reset navigation to the main screen
 * Properly handles nested navigation by specifying the initial tab
 *
 * This function resets the navigation state to the main screen with the Jobs tab
 * as the initial screen. It uses the proper nested navigation structure:
 * Main (Tab Navigator) > Jobs Tab > Jobs Home Screen
 */
export function resetToMain(): void {
  if (navigationRef?.isReady()) {
    navigationRef.dispatch(
      CommonActions.reset({
        index: 0,
        routes: [
          {
            name: SCREENS.MAIN,
            params: {
              screen: SCREENS.JOBS_TAB,
              params: { screen: SCREENS.JOBS_HOME },
            },
          },
        ],
      })
    );
  } else {
    console.warn('Navigation is not ready. Cannot reset to main screen.');
  }
}

/**
 * Reset navigation to the onboarding screen
 *
 * This function resets the navigation state to the onboarding screen.
 */
export function resetToOnboarding(): void {
  if (navigationRef?.isReady()) {
    navigationRef.dispatch(
      CommonActions.reset({
        index: 0,
        routes: [
          {
            name: SCREENS.ONBOARDING,
          },
        ],
      })
    );
  } else {
    console.warn('Navigation is not ready. Cannot reset to onboarding screen.');
  }
}

/**
 * Utility function to get typed navigation
 */
import { useNavigation } from '@react-navigation/native';
import { NativeStackNavigationProp } from '@react-navigation/native-stack';

export function useAppNavigation<T extends keyof RootStackParamList = never>() {
  return useNavigation<NativeStackNavigationProp<RootStackParamList, T>>();
}
