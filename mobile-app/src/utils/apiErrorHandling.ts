/**
 * API error handling utilities for the application
 * This file provides comprehensive error handling for all API calls
 */
import { Alert } from 'react-native';
import { SerializedError } from '@reduxjs/toolkit';
import { FetchBaseQueryError } from '@reduxjs/toolkit/query';
import { ApiError } from '../types/api';

/**
 * Type guard to check if an error is a FetchBaseQueryError
 */
export function isFetchBaseQueryError(
  error: unknown
): error is FetchBaseQueryError {
  return typeof error === 'object' && error != null && 'status' in error;
}

/**
 * Type guard to check if an error is a SerializedError
 */
export function isSerializedError(
  error: unknown
): error is SerializedError {
  return (
    typeof error === 'object' &&
    error != null &&
    'name' in error &&
    'message' in error
  );
}

/**
 * Type guard to check if an error is an ApiError
 */
export function isApiError(
  error: unknown
): error is ApiError {
  return (
    typeof error === 'object' &&
    error != null &&
    'statusCode' in error &&
    'message' in error &&
    'error' in error
  );
}

/**
 * Extract error message from any error type
 * @param error The error object
 * @param defaultMessage Default message to return if error message can't be extracted
 * @returns A user-friendly error message
 */
export function getErrorMessage(
  error: unknown,
  defaultMessage = 'An unexpected error occurred'
): string {
  // Handle RTK Query FetchBaseQueryError
  if (isFetchBaseQueryError(error)) {
    // Handle error response with data
    if ('data' in error && error.data) {
      if (isApiError(error.data)) {
        return error.data.message;
      }
      
      if (typeof error.data === 'string') {
        return error.data;
      }
      
      if (typeof error.data === 'object' && 'message' in error.data && typeof error.data.message === 'string') {
        return error.data.message;
      }
    }
    
    // Handle network error
    if (error.status === 'FETCH_ERROR') {
      return 'Network error. Please check your internet connection.';
    }
    
    // Handle timeout
    if (error.status === 'TIMEOUT_ERROR') {
      return 'Request timed out. Please try again.';
    }
    
    // Handle custom error
    if (error.status === 'CUSTOM_ERROR') {
      return error.error || defaultMessage;
    }
    
    // Handle HTTP status codes
    if (typeof error.status === 'number') {
      switch (error.status) {
        case 400:
          return 'Invalid request. Please check your input.';
        case 401:
          return 'Authentication required. Please log in again.';
        case 403:
          return 'You do not have permission to perform this action.';
        case 404:
          return 'The requested resource was not found.';
        case 409:
          return 'Conflict with current state of the resource.';
        case 422:
          return 'Validation error. Please check your input.';
        case 429:
          return 'Too many requests. Please try again later.';
        case 500:
          return 'Server error. Please try again later.';
        default:
          return `Error ${error.status}: Please try again later.`;
      }
    }
  }
  
  // Handle RTK SerializedError
  if (isSerializedError(error)) {
    return error.message || defaultMessage;
  }
  
  // Handle standard Error object
  if (error instanceof Error) {
    return error.message;
  }
  
  // Handle string error
  if (typeof error === 'string') {
    return error;
  }
  
  // Default case
  return defaultMessage;
}

/**
 * Show an alert with the error message
 * @param error The error object
 * @param title The alert title
 * @param defaultMessage Default message to show if error message can't be extracted
 */
export function showErrorAlert(
  error: unknown,
  title = 'Error',
  defaultMessage = 'An unexpected error occurred'
): void {
  Alert.alert(title, getErrorMessage(error, defaultMessage));
}

/**
 * Handle API error with a callback
 * @param error The error object
 * @param callback Function to call with the error message
 * @param defaultMessage Default message to use if error message can't be extracted
 */
export function handleApiError(
  error: unknown,
  callback: (message: string) => void,
  defaultMessage = 'An unexpected error occurred'
): void {
  callback(getErrorMessage(error, defaultMessage));
}

/**
 * Get error details for debugging
 * @param error The error object
 * @returns Detailed error information for debugging
 */
export function getErrorDetails(error: unknown): Record<string, unknown> {
  if (isFetchBaseQueryError(error)) {
    return {
      type: 'FetchBaseQueryError',
      status: error.status,
      data: error.data,
      error: error.error,
    };
  }
  
  if (isSerializedError(error)) {
    return {
      type: 'SerializedError',
      name: error.name,
      message: error.message,
      code: error.code,
      stack: error.stack,
    };
  }
  
  if (error instanceof Error) {
    return {
      type: 'Error',
      name: error.name,
      message: error.message,
      stack: error.stack,
    };
  }
  
  return {
    type: 'Unknown',
    error,
  };
}
