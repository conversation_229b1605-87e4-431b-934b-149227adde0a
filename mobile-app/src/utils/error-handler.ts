import { SerializedError } from '@reduxjs/toolkit';
import { FetchBaseQueryError } from '@reduxjs/toolkit/query';
import { ApiError } from '../types/api';

/**
 * Type guard to check if the error is a FetchBaseQueryError
 */
export function isFetchBaseQueryError(
  error: unknown
): error is FetchBaseQueryError {
  return typeof error === 'object' && error != null && 'status' in error;
}

/**
 * Type guard to check if the error is a SerializedError
 */
export function isSerializedError(
  error: unknown
): error is SerializedError {
  return (
    typeof error === 'object' &&
    error != null &&
    'name' in error &&
    'message' in error
  );
}

/**
 * Extract error message from RTK Query error
 * @param error The error object from RTK Query
 * @returns A user-friendly error message
 */
export function getErrorMessage(error: unknown): string {
  // Default error message
  let errorMessage = 'An unknown error occurred. Please try again.';

  if (isFetchBaseQueryError(error)) {
    // Handle FetchBaseQueryError
    if ('error' in error) {
      errorMessage = error.error || errorMessage;
    } else if ('data' in error && error.data) {
      // Try to extract message from API error response
      const data = error.data as Partial<ApiError>;
      errorMessage = data.message || errorMessage;
    }
  } else if (isSerializedError(error)) {
    // Handle SerializedError
    errorMessage = error.message || errorMessage;
  } else if (error instanceof Error) {
    // Handle standard Error object
    errorMessage = error.message;
  }

  return errorMessage;
}

/**
 * Handle API error and return a user-friendly message
 * @param error The error object from RTK Query
 * @param defaultMessage Optional default message to use if no specific message is found
 * @returns A user-friendly error message
 */
export function handleApiError(
  error: unknown,
  defaultMessage = 'An error occurred. Please try again.'
): string {
  return getErrorMessage(error) || defaultMessage;
}

/**
 * Extract validation errors from API error response
 * @param error The error object from RTK Query
 * @returns An object with field names as keys and error messages as values, or null if no validation errors
 */
export function getValidationErrors(
  error: unknown
): Record<string, string> | null {
  if (isFetchBaseQueryError(error) && 'data' in error && error.data) {
    const data = error.data as Partial<ApiError>;
    
    if (data.details && typeof data.details === 'object') {
      return data.details as Record<string, string>;
    }
  }
  
  return null;
}
