import { Animated, Easing } from 'react-native';
import { SharedTransition } from 'react-native-reanimated';
import type { MotiTransition } from 'moti';

// Pulse animation for buttons and cards
export const pulseAnimation = (scaleAnim: Animated.Value) => {
  return {
    start: () => {
      Animated.sequence([
        Animated.timing(scaleAnim, {
          toValue: 0.95,
          duration: 100,
          useNativeDriver: true,
          easing: Easing.inOut(Easing.ease),
        }),
        Animated.timing(scaleAnim, {
          toValue: 1,
          duration: 100,
          useNativeDriver: true,
          easing: Easing.inOut(Easing.ease),
        }),
      ]).start();
    },
  };
};

// Fade in animation
export const fadeIn = (fadeAnim: Animated.Value, duration = 500) => {
  return {
    start: (callback?: Animated.EndCallback) => {
      Animated.timing(fadeAnim, {
        toValue: 1,
        duration,
        useNativeDriver: true,
        easing: Easing.inOut(Easing.ease),
      }).start(callback);
    },
  };
};

// Fade out animation
export const fadeOut = (fadeAnim: Animated.Value, duration = 500) => {
  return {
    start: (callback?: Animated.EndCallback) => {
      Animated.timing(fadeAnim, {
        toValue: 0,
        duration,
        useNativeDriver: true,
        easing: Easing.inOut(Easing.ease),
      }).start(callback);
    },
  };
};

// Reanimated shared transition for list items
export const reanimatedSharedTransition = SharedTransition.custom((values) => {
  'worklet';
  return {
    easing: Easing.bezier(0.25, 0.1, 0.25, 1),
    duration: 400,
    ...values,
  };
});

// Moti-compatible shared transition
export const sharedTransition: MotiTransition = {
  type: 'timing',
  duration: 400,
  easing: Easing.bezier(0.25, 0.1, 0.25, 1),
};

// Slide in from bottom
export const slideInFromBottom = (translateY: Animated.Value, duration = 300) => {
  return {
    start: (callback?: Animated.EndCallback) => {
      Animated.timing(translateY, {
        toValue: 0,
        duration,
        useNativeDriver: true,
        easing: Easing.out(Easing.ease),
      }).start(callback);
    },
  };
};

// Slide out to bottom
export const slideOutToBottom = (translateY: Animated.Value, toValue = 100, duration = 300) => {
  return {
    start: (callback?: Animated.EndCallback) => {
      Animated.timing(translateY, {
        toValue,
        duration,
        useNativeDriver: true,
        easing: Easing.in(Easing.ease),
      }).start(callback);
    },
  };
};
