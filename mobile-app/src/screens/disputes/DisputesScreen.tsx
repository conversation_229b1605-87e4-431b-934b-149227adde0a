import React, { useState, useEffect } from 'react';
import { View, Text, ScrollView, TouchableOpacity, ActivityIndicator, RefreshControl, FlatList } from 'react-native';
import { useNavigation } from '@react-navigation/native';
import { Ionicons } from '@expo/vector-icons';
import { MotiView } from 'moti';
import { format } from 'date-fns';
import { disputesAPI } from '../../services/api';
import { sharedTransition } from '../../utils/animations';
import { SCREENS } from '../../constants/screens';
import EmptyState from '../../components/EmptyState';

const DisputesScreen = () => {
  const navigation = useNavigation();
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [disputes, setDisputes] = useState([]);
  const [filter, setFilter] = useState('all'); // 'all', 'open', 'resolved', 'cancelled'

  const fetchDisputes = async () => {
    try {
      setLoading(true);
      
      // Apply filter if not 'all'
      const params = filter !== 'all' ? { status: filter } : undefined;
      
      const response = await disputesAPI.getMyDisputes(params);
      setDisputes(response.data);
    } catch (error) {
      console.error('Error fetching disputes:', error);
    } finally {
      setLoading(false);
      setRefreshing(false);
    }
  };

  useEffect(() => {
    fetchDisputes();
  }, [filter]);

  const handleRefresh = () => {
    setRefreshing(true);
    fetchDisputes();
  };

  const formatDate = (dateString) => {
    return format(new Date(dateString), 'MMM dd, yyyy');
  };

  const getStatusColor = (status) => {
    switch (status.toLowerCase()) {
      case 'open':
        return 'bg-blue-100 text-blue-800';
      case 'in_progress':
        return 'bg-yellow-100 text-yellow-800';
      case 'resolved':
        return 'bg-green-100 text-green-800';
      case 'cancelled':
        return 'bg-gray-100 text-gray-800';
      case 'rejected':
        return 'bg-red-100 text-red-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const renderDisputeItem = ({ item }) => (
    <TouchableOpacity
      className="bg-white rounded-lg shadow-sm p-4 mb-3"
      onPress={() => navigation.navigate(SCREENS.DISPUTE_DETAILS, { disputeId: item.id })}>
      <View className="flex-row justify-between items-start">
        <View className="flex-1">
          <Text className="text-gray-900 font-medium">{item.reason}</Text>
          <Text className="text-gray-500 text-sm">Job: {item.job?.title || 'Unknown Job'}</Text>
          <Text className="text-gray-500 text-sm">Created: {formatDate(item.createdAt)}</Text>
        </View>
        <View className={`px-3 py-1 rounded-full ${getStatusColor(item.status)}`}>
          <Text className="text-xs font-medium capitalize">{item.status.replace('_', ' ')}</Text>
        </View>
      </View>
      
      {item.description && (
        <View className="mt-2 pt-2 border-t border-gray-100">
          <Text className="text-gray-700" numberOfLines={2}>
            {item.description}
          </Text>
        </View>
      )}
      
      <View className="flex-row justify-between items-center mt-3 pt-3 border-t border-gray-100">
        <View className="flex-row items-center">
          <Ionicons name="chatbubble-outline" size={16} color="#6b7280" />
          <Text className="text-gray-500 text-sm ml-1">{item.commentCount || 0} comments</Text>
        </View>
        
        {item.status === 'open' && (
          <TouchableOpacity
            className="flex-row items-center"
            onPress={() => navigation.navigate(SCREENS.DISPUTE_DETAILS, { disputeId: item.id, scrollToComments: true })}>
            <Text className="text-indigo-600 text-sm font-medium">Reply</Text>
            <Ionicons name="chevron-forward" size={16} color="#4f46e5" />
          </TouchableOpacity>
        )}
      </View>
    </TouchableOpacity>
  );

  if (loading && !refreshing) {
    return (
      <View className="flex-1 items-center justify-center bg-white">
        <ActivityIndicator size="large" color="#4f46e5" />
      </View>
    );
  }

  return (
    <View className="flex-1 bg-gray-50">
      <View className="bg-indigo-600 pt-12 pb-4 px-4">
        <TouchableOpacity
          className="flex-row items-center mb-2"
          onPress={() => navigation.goBack()}>
          <Ionicons name="arrow-back" size={24} color="white" />
          <Text className="ml-2 text-white">Back</Text>
        </TouchableOpacity>
        <Text className="text-2xl font-bold text-white">Disputes</Text>
        <Text className="text-indigo-200">Manage issues with your jobs</Text>
      </View>

      <View className="flex-row border-b border-gray-200 bg-white">
        <TouchableOpacity
          className={`flex-1 py-3 ${filter === 'all' ? 'border-b-2 border-indigo-600' : ''}`}
          onPress={() => setFilter('all')}>
          <Text
            className={`text-center font-medium ${
              filter === 'all' ? 'text-indigo-600' : 'text-gray-500'
            }`}>
            All
          </Text>
        </TouchableOpacity>
        <TouchableOpacity
          className={`flex-1 py-3 ${filter === 'open' ? 'border-b-2 border-indigo-600' : ''}`}
          onPress={() => setFilter('open')}>
          <Text
            className={`text-center font-medium ${
              filter === 'open' ? 'text-indigo-600' : 'text-gray-500'
            }`}>
            Open
          </Text>
        </TouchableOpacity>
        <TouchableOpacity
          className={`flex-1 py-3 ${filter === 'resolved' ? 'border-b-2 border-indigo-600' : ''}`}
          onPress={() => setFilter('resolved')}>
          <Text
            className={`text-center font-medium ${
              filter === 'resolved' ? 'text-indigo-600' : 'text-gray-500'
            }`}>
            Resolved
          </Text>
        </TouchableOpacity>
      </View>

      <FlatList
        className="flex-1 p-4"
        data={disputes}
        renderItem={renderDisputeItem}
        keyExtractor={(item) => item.id}
        refreshControl={
          <RefreshControl refreshing={refreshing} onRefresh={handleRefresh} />
        }
        ListEmptyComponent={
          <EmptyState
            title="No disputes found"
            message={`You don't have any ${filter !== 'all' ? filter : ''} disputes yet`}
            icon="alert-circle-outline"
            action={{
              label: 'Create a Dispute',
              onPress: () => navigation.navigate(SCREENS.CREATE_DISPUTE),
            }}
          />
        }
      />

      <TouchableOpacity
        className="absolute bottom-6 right-6 h-14 w-14 items-center justify-center rounded-full bg-indigo-600 shadow-lg"
        onPress={() => navigation.navigate(SCREENS.CREATE_DISPUTE)}>
        <Ionicons name="add" size={24} color="white" />
      </TouchableOpacity>
    </View>
  );
};

export default DisputesScreen;
