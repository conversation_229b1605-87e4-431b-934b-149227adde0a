import React, { useState, useEffect, useRef } from 'react';
import {
  View,
  Text,
  ScrollView,
  TouchableOpacity,
  ActivityIndicator,
  TextInput,
  Image,
  Alert,
  KeyboardAvoidingView,
  Platform,
} from 'react-native';
import { useNavigation, useRoute } from '@react-navigation/native';
import { Ionicons } from '@expo/vector-icons';
import { MotiView } from 'moti';
import { format } from 'date-fns';
import * as ImagePicker from 'expo-image-picker';
import { disputesAPI } from '../../services/api';
import { useAuth } from '../../contexts/AuthContext';
import { sharedTransition } from '../../utils/animations';
import { SCREENS } from '../../constants/screens';

const DisputeDetailsScreen = () => {
  const navigation = useNavigation();
  const route = useRoute();
  const { disputeId, scrollToComments } = route.params;
  const { user } = useAuth();
  const [loading, setLoading] = useState(true);
  const [dispute, setDispute] = useState(null);
  const [comments, setComments] = useState([]);
  const [newComment, setNewComment] = useState('');
  const [attachments, setAttachments] = useState([]);
  const [sendingComment, setSendingComment] = useState(false);
  const scrollViewRef = useRef(null);

  const fetchDisputeDetails = async () => {
    try {
      setLoading(true);
      const response = await disputesAPI.getDisputeById(disputeId);
      setDispute(response.data);
      setComments(response.data.comments || []);
    } catch (error) {
      console.error('Error fetching dispute details:', error);
      Alert.alert('Error', 'Failed to load dispute details');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchDisputeDetails();
  }, [disputeId]);

  useEffect(() => {
    if (scrollToComments && !loading && scrollViewRef.current) {
      setTimeout(() => {
        scrollViewRef.current.scrollToEnd({ animated: true });
      }, 500);
    }
  }, [scrollToComments, loading]);

  const formatDate = (dateString) => {
    return format(new Date(dateString), 'MMM dd, yyyy h:mm a');
  };

  const getStatusColor = (status) => {
    switch (status?.toLowerCase()) {
      case 'open':
        return 'bg-blue-100 text-blue-800';
      case 'in_progress':
        return 'bg-yellow-100 text-yellow-800';
      case 'resolved':
        return 'bg-green-100 text-green-800';
      case 'cancelled':
        return 'bg-gray-100 text-gray-800';
      case 'rejected':
        return 'bg-red-100 text-red-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const pickImage = async () => {
    const result = await ImagePicker.launchImageLibraryAsync({
      mediaTypes: ImagePicker.MediaTypeOptions.Images,
      allowsEditing: true,
      quality: 0.8,
    });

    if (!result.canceled && result.assets && result.assets.length > 0) {
      const newAttachment = result.assets[0];
      
      // Create a File object from the URI
      const response = await fetch(newAttachment.uri);
      const blob = await response.blob();
      const filename = newAttachment.uri.split('/').pop();
      const file = new File([blob], filename, { type: 'image/jpeg' });
      
      setAttachments([...attachments, file]);
    }
  };

  const removeAttachment = (index) => {
    const newAttachments = [...attachments];
    newAttachments.splice(index, 1);
    setAttachments(newAttachments);
  };

  const handleSendComment = async () => {
    if (!newComment.trim() && attachments.length === 0) return;

    try {
      setSendingComment(true);
      await disputesAPI.addComment(disputeId, newComment, attachments);
      
      // Refresh dispute details to get the new comment
      await fetchDisputeDetails();
      
      // Clear form
      setNewComment('');
      setAttachments([]);
      
      // Scroll to bottom
      if (scrollViewRef.current) {
        scrollViewRef.current.scrollToEnd({ animated: true });
      }
    } catch (error) {
      console.error('Error sending comment:', error);
      Alert.alert('Error', 'Failed to send comment');
    } finally {
      setSendingComment(false);
    }
  };

  const handleCancelDispute = async () => {
    Alert.alert(
      'Cancel Dispute',
      'Are you sure you want to cancel this dispute? This action cannot be undone.',
      [
        { text: 'No', style: 'cancel' },
        {
          text: 'Yes, Cancel',
          style: 'destructive',
          onPress: async () => {
            try {
              setLoading(true);
              await disputesAPI.cancelDispute(disputeId, 'Cancelled by user');
              await fetchDisputeDetails();
              Alert.alert('Success', 'Dispute has been cancelled');
            } catch (error) {
              console.error('Error cancelling dispute:', error);
              Alert.alert('Error', 'Failed to cancel dispute');
            } finally {
              setLoading(false);
            }
          },
        },
      ]
    );
  };

  if (loading) {
    return (
      <View className="flex-1 items-center justify-center bg-white">
        <ActivityIndicator size="large" color="#4f46e5" />
      </View>
    );
  }

  return (
    <KeyboardAvoidingView
      behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
      className="flex-1"
      keyboardVerticalOffset={Platform.OS === 'ios' ? 90 : 0}>
      <View className="flex-1 bg-gray-50">
        <View className="bg-indigo-600 pt-12 pb-4 px-4">
          <TouchableOpacity
            className="flex-row items-center mb-2"
            onPress={() => navigation.goBack()}>
            <Ionicons name="arrow-back" size={24} color="white" />
            <Text className="ml-2 text-white">Back</Text>
          </TouchableOpacity>
          <Text className="text-2xl font-bold text-white">Dispute Details</Text>
          <View className="flex-row items-center mt-1">
            <View className={`px-3 py-1 rounded-full ${getStatusColor(dispute?.status)}`}>
              <Text className="text-xs font-medium capitalize">
                {dispute?.status?.replace('_', ' ')}
              </Text>
            </View>
            <Text className="text-indigo-200 ml-2">
              Created: {formatDate(dispute?.createdAt)}
            </Text>
          </View>
        </View>

        <ScrollView className="flex-1 p-4" ref={scrollViewRef}>
          <MotiView
            from={{ opacity: 0, translateY: 20 }}
            animate={{ opacity: 1, translateY: 0 }}
            transition={sharedTransition}>
            {/* Dispute Information */}
            <View className="bg-white rounded-lg shadow-sm p-4 mb-4">
              <Text className="text-lg font-bold text-gray-900 mb-2">{dispute?.reason}</Text>
              <View className="flex-row items-center mb-3">
                <Ionicons name="briefcase-outline" size={16} color="#6b7280" />
                <Text className="text-gray-700 ml-2">
                  Job: {dispute?.job?.title || 'Unknown Job'}
                </Text>
              </View>
              <Text className="text-gray-700 mb-3">{dispute?.description}</Text>

              {dispute?.attachments && dispute.attachments.length > 0 && (
                <View className="mb-3">
                  <Text className="text-gray-700 font-medium mb-2">Attachments:</Text>
                  <ScrollView horizontal showsHorizontalScrollIndicator={false}>
                    {dispute.attachments.map((attachment, index) => (
                      <TouchableOpacity
                        key={index}
                        className="mr-3"
                        onPress={() => {
                          // Open attachment in full screen
                        }}>
                        <Image
                          source={{ uri: attachment.url }}
                          className="w-20 h-20 rounded-lg"
                          resizeMode="cover"
                        />
                      </TouchableOpacity>
                    ))}
                  </ScrollView>
                </View>
              )}

              {dispute?.status === 'open' && (
                <TouchableOpacity
                  className="bg-red-100 py-2 px-4 rounded-lg self-start mt-2"
                  onPress={handleCancelDispute}>
                  <Text className="text-red-700 font-medium">Cancel Dispute</Text>
                </TouchableOpacity>
              )}
            </View>

            {/* Comments Section */}
            <Text className="text-lg font-bold text-gray-900 mb-3">Comments</Text>
            {comments.length > 0 ? (
              comments.map((comment, index) => (
                <View
                  key={index}
                  className={`bg-white rounded-lg shadow-sm p-4 mb-3 ${
                    comment.userId === user?.id ? 'border-l-4 border-indigo-500' : ''
                  }`}>
                  <View className="flex-row justify-between items-center mb-2">
                    <Text className="font-medium text-gray-900">
                      {comment.userId === user?.id ? 'You' : comment.userName}
                    </Text>
                    <Text className="text-gray-500 text-xs">
                      {formatDate(comment.createdAt)}
                    </Text>
                  </View>
                  <Text className="text-gray-700 mb-3">{comment.text}</Text>

                  {comment.attachments && comment.attachments.length > 0 && (
                    <ScrollView horizontal showsHorizontalScrollIndicator={false}>
                      {comment.attachments.map((attachment, idx) => (
                        <TouchableOpacity
                          key={idx}
                          className="mr-3"
                          onPress={() => {
                            // Open attachment in full screen
                          }}>
                          <Image
                            source={{ uri: attachment.url }}
                            className="w-16 h-16 rounded-lg"
                            resizeMode="cover"
                          />
                        </TouchableOpacity>
                      ))}
                    </ScrollView>
                  )}
                </View>
              ))
            ) : (
              <View className="bg-white rounded-lg shadow-sm p-6 items-center mb-3">
                <Ionicons name="chatbubble-outline" size={48} color="#9ca3af" />
                <Text className="text-gray-700 font-medium mt-3">No Comments Yet</Text>
                <Text className="text-gray-500 text-center mt-1">
                  Be the first to add a comment to this dispute
                </Text>
              </View>
            )}
          </MotiView>
        </ScrollView>

        {/* Comment Input */}
        {dispute?.status === 'open' && (
          <View className="bg-white border-t border-gray-200 p-4">
            {attachments.length > 0 && (
              <ScrollView
                horizontal
                showsHorizontalScrollIndicator={false}
                className="mb-3">
                {attachments.map((attachment, index) => (
                  <View key={index} className="relative mr-3">
                    <Image
                      source={{ uri: URL.createObjectURL(attachment) }}
                      className="w-16 h-16 rounded-lg"
                      resizeMode="cover"
                    />
                    <TouchableOpacity
                      className="absolute -top-2 -right-2 bg-red-500 rounded-full p-1"
                      onPress={() => removeAttachment(index)}>
                      <Ionicons name="close" size={14} color="white" />
                    </TouchableOpacity>
                  </View>
                ))}
              </ScrollView>
            )}

            <View className="flex-row items-center">
              <TouchableOpacity
                className="p-2 mr-2"
                onPress={pickImage}>
                <Ionicons name="image-outline" size={24} color="#6b7280" />
              </TouchableOpacity>
              <TextInput
                className="flex-1 bg-gray-100 rounded-full px-4 py-2 mr-2"
                placeholder="Add a comment..."
                value={newComment}
                onChangeText={setNewComment}
                multiline
              />
              <TouchableOpacity
                className={`p-2 rounded-full ${
                  (!newComment.trim() && attachments.length === 0) || sendingComment
                    ? 'bg-gray-300'
                    : 'bg-indigo-600'
                }`}
                onPress={handleSendComment}
                disabled={(!newComment.trim() && attachments.length === 0) || sendingComment}>
                {sendingComment ? (
                  <ActivityIndicator size="small" color="white" />
                ) : (
                  <Ionicons name="send" size={20} color="white" />
                )}
              </TouchableOpacity>
            </View>
          </View>
        )}
      </View>
    </KeyboardAvoidingView>
  );
};

export default DisputeDetailsScreen;
