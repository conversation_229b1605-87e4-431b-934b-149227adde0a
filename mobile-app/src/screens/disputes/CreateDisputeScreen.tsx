import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  ScrollView,
  TouchableOpacity,
  TextInput,
  ActivityIndicator,
  Image,
  Alert,
  KeyboardAvoidingView,
  Platform,
} from 'react-native';
import { useNavigation, useRoute } from '@react-navigation/native';
import { Ionicons } from '@expo/vector-icons';
import { MotiView } from 'moti';
import * as ImagePicker from 'expo-image-picker';
import { Picker } from '@react-native-picker/picker';
import { disputesAPI, jobsAPI, applicationsAPI } from '../../services/api';
import { sharedTransition } from '../../utils/animations';
import { SCREENS } from '../../constants/screens';

const CreateDisputeScreen = () => {
  const navigation = useNavigation();
  const route = useRoute();
  const { jobId, applicationId } = route.params || {};
  
  const [loading, setLoading] = useState(false);
  const [submitting, setSubmitting] = useState(false);
  const [jobs, setJobs] = useState([]);
  const [applications, setApplications] = useState([]);
  const [disputeReasons, setDisputeReasons] = useState([]);
  
  const [selectedJobId, setSelectedJobId] = useState(jobId || '');
  const [selectedApplicationId, setSelectedApplicationId] = useState(applicationId || '');
  const [selectedReason, setSelectedReason] = useState('');
  const [description, setDescription] = useState('');
  const [attachments, setAttachments] = useState([]);

  useEffect(() => {
    fetchInitialData();
  }, []);

  useEffect(() => {
    if (selectedJobId) {
      fetchApplicationsForJob(selectedJobId);
    }
  }, [selectedJobId]);

  const fetchInitialData = async () => {
    try {
      setLoading(true);
      
      // Fetch jobs
      const jobsResponse = await jobsAPI.getJobs({ status: 'completed' });
      setJobs(jobsResponse.data);
      
      // Fetch dispute reasons
      const reasonsResponse = await disputesAPI.getDisputeReasons();
      setDisputeReasons(reasonsResponse.data);
      
      // If jobId is provided, fetch applications for that job
      if (jobId) {
        await fetchApplicationsForJob(jobId);
      }
    } catch (error) {
      console.error('Error fetching initial data:', error);
      Alert.alert('Error', 'Failed to load data');
    } finally {
      setLoading(false);
    }
  };

  const fetchApplicationsForJob = async (jobId) => {
    try {
      const response = await applicationsAPI.getApplications({ jobId, status: 'completed' });
      setApplications(response.data);
      
      // If there's only one application, select it automatically
      if (response.data.length === 1 && !selectedApplicationId) {
        setSelectedApplicationId(response.data[0].id);
      }
    } catch (error) {
      console.error('Error fetching applications:', error);
    }
  };

  const pickImage = async () => {
    const result = await ImagePicker.launchImageLibraryAsync({
      mediaTypes: ImagePicker.MediaTypeOptions.Images,
      allowsEditing: true,
      quality: 0.8,
    });

    if (!result.canceled && result.assets && result.assets.length > 0) {
      const newAttachment = result.assets[0];
      
      // Create a File object from the URI
      const response = await fetch(newAttachment.uri);
      const blob = await response.blob();
      const filename = newAttachment.uri.split('/').pop();
      const file = new File([blob], filename, { type: 'image/jpeg' });
      
      setAttachments([...attachments, file]);
    }
  };

  const removeAttachment = (index) => {
    const newAttachments = [...attachments];
    newAttachments.splice(index, 1);
    setAttachments(newAttachments);
  };

  const handleSubmit = async () => {
    // Validate form
    if (!selectedJobId) {
      Alert.alert('Error', 'Please select a job');
      return;
    }
    
    if (!selectedApplicationId) {
      Alert.alert('Error', 'Please select an application');
      return;
    }
    
    if (!selectedReason) {
      Alert.alert('Error', 'Please select a reason for the dispute');
      return;
    }
    
    if (!description.trim()) {
      Alert.alert('Error', 'Please provide a description');
      return;
    }
    
    try {
      setSubmitting(true);
      
      await disputesAPI.createDispute({
        jobId: selectedJobId,
        applicationId: selectedApplicationId,
        reason: selectedReason,
        description,
        attachments,
      });
      
      Alert.alert(
        'Success',
        'Your dispute has been submitted successfully',
        [
          {
            text: 'OK',
            onPress: () => navigation.navigate(SCREENS.DISPUTES),
          },
        ]
      );
    } catch (error) {
      console.error('Error creating dispute:', error);
      Alert.alert('Error', 'Failed to create dispute');
    } finally {
      setSubmitting(false);
    }
  };

  if (loading) {
    return (
      <View className="flex-1 items-center justify-center bg-white">
        <ActivityIndicator size="large" color="#4f46e5" />
      </View>
    );
  }

  return (
    <KeyboardAvoidingView
      behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
      className="flex-1"
      keyboardVerticalOffset={Platform.OS === 'ios' ? 90 : 0}>
      <View className="flex-1 bg-gray-50">
        <View className="bg-indigo-600 pt-12 pb-4 px-4">
          <TouchableOpacity
            className="flex-row items-center mb-2"
            onPress={() => navigation.goBack()}>
            <Ionicons name="arrow-back" size={24} color="white" />
            <Text className="ml-2 text-white">Back</Text>
          </TouchableOpacity>
          <Text className="text-2xl font-bold text-white">Create Dispute</Text>
          <Text className="text-indigo-200">Report an issue with a completed job</Text>
        </View>

        <ScrollView className="flex-1 p-4">
          <MotiView
            from={{ opacity: 0, translateY: 20 }}
            animate={{ opacity: 1, translateY: 0 }}
            transition={sharedTransition}>
            <View className="bg-white rounded-lg shadow-sm p-4 mb-4">
              <Text className="text-gray-700 font-medium mb-2">Select Job</Text>
              <View className="border border-gray-300 rounded-lg overflow-hidden mb-4">
                <Picker
                  selectedValue={selectedJobId}
                  onValueChange={(itemValue) => {
                    setSelectedJobId(itemValue);
                    setSelectedApplicationId('');
                  }}>
                  <Picker.Item label="Select a job..." value="" />
                  {jobs.map((job) => (
                    <Picker.Item key={job.id} label={job.title} value={job.id} />
                  ))}
                </Picker>
              </View>

              <Text className="text-gray-700 font-medium mb-2">Select Application</Text>
              <View className="border border-gray-300 rounded-lg overflow-hidden mb-4">
                <Picker
                  selectedValue={selectedApplicationId}
                  onValueChange={(itemValue) => setSelectedApplicationId(itemValue)}
                  enabled={selectedJobId !== ''}>
                  <Picker.Item label="Select an application..." value="" />
                  {applications.map((application) => (
                    <Picker.Item
                      key={application.id}
                      label={`Applied on ${new Date(application.createdAt).toLocaleDateString()}`}
                      value={application.id}
                    />
                  ))}
                </Picker>
              </View>

              <Text className="text-gray-700 font-medium mb-2">Reason for Dispute</Text>
              <View className="border border-gray-300 rounded-lg overflow-hidden mb-4">
                <Picker
                  selectedValue={selectedReason}
                  onValueChange={(itemValue) => setSelectedReason(itemValue)}>
                  <Picker.Item label="Select a reason..." value="" />
                  {disputeReasons.map((reason) => (
                    <Picker.Item key={reason.id} label={reason.name} value={reason.name} />
                  ))}
                </Picker>
              </View>

              <Text className="text-gray-700 font-medium mb-2">Description</Text>
              <TextInput
                className="border border-gray-300 rounded-lg p-3 min-h-[100px] mb-4 text-gray-700"
                placeholder="Describe the issue in detail..."
                value={description}
                onChangeText={setDescription}
                multiline
                textAlignVertical="top"
              />

              <Text className="text-gray-700 font-medium mb-2">Attachments (Optional)</Text>
              <View className="flex-row flex-wrap mb-4">
                {attachments.map((attachment, index) => (
                  <View key={index} className="relative mr-3 mb-3">
                    <Image
                      source={{ uri: URL.createObjectURL(attachment) }}
                      className="w-20 h-20 rounded-lg"
                      resizeMode="cover"
                    />
                    <TouchableOpacity
                      className="absolute -top-2 -right-2 bg-red-500 rounded-full p-1"
                      onPress={() => removeAttachment(index)}>
                      <Ionicons name="close" size={14} color="white" />
                    </TouchableOpacity>
                  </View>
                ))}
                <TouchableOpacity
                  className="w-20 h-20 border-2 border-dashed border-gray-300 rounded-lg items-center justify-center"
                  onPress={pickImage}>
                  <Ionicons name="add" size={24} color="#6b7280" />
                </TouchableOpacity>
              </View>

              <TouchableOpacity
                className={`py-3 rounded-lg items-center ${
                  submitting ? 'bg-indigo-400' : 'bg-indigo-600'
                }`}
                onPress={handleSubmit}
                disabled={submitting}>
                {submitting ? (
                  <ActivityIndicator color="white" />
                ) : (
                  <Text className="text-white font-medium">Submit Dispute</Text>
                )}
              </TouchableOpacity>
            </View>
          </MotiView>
        </ScrollView>
      </View>
    </KeyboardAvoidingView>
  );
};

export default CreateDisputeScreen;
