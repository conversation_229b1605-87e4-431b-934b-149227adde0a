'use client';
import { useEffect, useRef } from 'react';
import {
  View,
  Text,
  FlatList,
  Animated,
  TouchableOpacity,
  ActivityIndicator,
  ListRenderItem,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import JobCard from '@/components/JobCard';
import { staggerAnimation } from '@/utils/animations';
import { SCREENS } from '@/constants/screens';
import { useTypedNavigation } from '@/types/navigation';
import { useGetFavoritesQuery } from '@/store/api/favoritesApi';
import type { Job } from '@/types/api';

const FavoritesScreen = () => {
  const navigation = useTypedNavigation();
  const animatedValues = useRef<any>(null);

  // Use RTK Query to fetch favorites
  const { data: favoritesResponse, isLoading, error } = useGetFavoritesQuery();

  // Extract favorites from the response - map to jobs
  const favorites = favoritesResponse?.data?.favorites?.map((fav) => fav.job) || [];

  useEffect(() => {
    if (favorites.length > 0 && !animatedValues.current) {
      animatedValues.current = staggerAnimation(favorites);
      animatedValues.current.start();
    }
  }, [favorites]);

  const renderItem: ListRenderItem<Job> = ({ item, index }) => {
    if (!animatedValues.current)
      return (
        <JobCard
          job={item}
          onPress={() => navigation.navigate(SCREENS.JOB_DETAILS, { jobId: item.id })}
        />
      );

    const { opacity, translateY } =
      animatedValues.current.animations[index % animatedValues.current.animations.length];

    return (
      <Animated.View style={{ opacity, transform: [{ translateY }] }}>
        <JobCard
          job={item}
          onPress={() => navigation.navigate(SCREENS.JOB_DETAILS, { jobId: item.id })}
        />
      </Animated.View>
    );
  };

  // Show loading state
  if (isLoading) {
    return (
      <View className="flex-1 items-center justify-center bg-white">
        <ActivityIndicator size="large" color="#4f46e5" />
      </View>
    );
  }

  // Show error state
  if (error) {
    return (
      <View className="flex-1 items-center justify-center bg-white p-4">
        <Ionicons name="alert-circle-outline" size={60} color="#ef4444" />
        <Text className="mt-4 text-center text-lg font-medium text-gray-900">
          Failed to load favorites
        </Text>
        <TouchableOpacity
          className="mt-6 rounded-lg bg-indigo-600 px-6 py-3"
          onPress={() => navigation.navigate(SCREENS.JOBS_HOME)}>
          <Text className="font-medium text-white">Browse Jobs</Text>
        </TouchableOpacity>
      </View>
    );
  }

  return (
    <View className="flex-1 bg-gray-50">
      <View className="flex-row items-center justify-between border-b border-gray-200 bg-white p-4">
        <Text className="text-xl font-bold">Favorite Jobs</Text>
      </View>

      <FlatList
        data={favorites}
        keyExtractor={(item) => item.id}
        renderItem={renderItem}
        contentContainerStyle={{ padding: 16 }}
        ListEmptyComponent={
          <View className="flex-1 items-center justify-center p-10">
            <Ionicons name="heart-outline" size={60} color="gray" />
            <Text className="mt-4 text-center text-lg text-gray-500">No favorite jobs yet</Text>
            <Text className="mt-2 text-center text-gray-400">
              Tap the heart icon on any job to add it to your favorites
            </Text>
            <TouchableOpacity
              className="mt-6 rounded-lg bg-indigo-600 px-4 py-2"
              onPress={() => navigation.navigate(SCREENS.JOBS_HOME)}>
              <Text className="font-medium text-white">Browse Jobs</Text>
            </TouchableOpacity>
          </View>
        }
      />
    </View>
  );
};

export default FavoritesScreen;
