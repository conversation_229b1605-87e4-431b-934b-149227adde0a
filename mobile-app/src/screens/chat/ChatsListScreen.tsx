import React, { type ReactElement } from 'react';
import { View, Text, FlatList, TouchableOpacity, Image, ActivityIndicator } from 'react-native';
import { useNavigation } from '@react-navigation/native';
import type { NativeStackNavigationProp } from '@react-navigation/native-stack';
import { Ionicons } from '@expo/vector-icons';
import { SCREENS } from '../../constants/screens';
import { useGetChatsQuery } from '../../store/api/chatApi';
import { handleApiError } from '../../utils/error-handler';
import { formatDistanceToNow } from 'date-fns';
import type { GenericChat } from '../../types/api';

type ChatsListScreenNavigationProp = NativeStackNavigationProp<any, 'CHATS_LIST'>;

const ChatsListScreen = () => {
  const navigation = useNavigation<ChatsListScreenNavigationProp>();

  // Fetch chats using RTK Query
  const {
    data: chatsData,
    isLoading,
    isError,
    error,
    refetch,
  } = useGetChatsQuery({
    limit: 20,
    page: 1,
  });

  console.log('chatsData', chatsData, isLoading, isError, error);

  // Format the timestamp to a relative time (e.g., "2 hours ago")
  const formatMessageTime = (timestamp: string): string => {
    try {
      return formatDistanceToNow(new Date(timestamp), { addSuffix: true });
    } catch (e) {
      return 'Unknown time';
    }
  };

  // Render a chat item
  const renderItem = ({ item }: { item: GenericChat }) => {
    // Get the last message from the messages array
    const lastMessage = item.messages?.[0]?.message || 'No messages yet';
    const messageTime = item.lastMessageAt ? formatMessageTime(item.lastMessageAt) : '';

    // Get the other participant (not the current user)
    const otherParticipant = item.participants?.find((p) => p.userId !== 'current-user-id'); // TODO: Get actual current user ID
    const recipientName = otherParticipant?.user?.name || item.title || 'Unknown';
    const recipientAvatar = otherParticipant?.user?.profilePic || 'https://via.placeholder.com/150';

    // Calculate unread count for current user
    const currentUserParticipant = item.participants?.find((p) => p.userId === 'current-user-id'); // TODO: Get actual current user ID
    const unreadCount = currentUserParticipant?.unreadCount || 0;

    return (
      <TouchableOpacity
        className="flex-row border-b border-gray-100 p-4"
        onPress={() =>
          navigation.navigate(SCREENS.CHAT_DETAILS as any, {
            chatId: item.id,
            recipientId: otherParticipant?.userId || '',
            recipientName,
          })
        }>
        <Image source={{ uri: recipientAvatar }} className="h-12 w-12 rounded-full" />
        <View className="ml-3 flex-1 justify-center">
          <View className="flex-row justify-between">
            <Text className="font-semibold text-gray-900">{recipientName}</Text>
            <Text className="text-xs text-gray-500">{messageTime}</Text>
          </View>
          <Text className="mb-1 text-xs text-gray-500">
            {item.contextType === 'job' ? 'Job Chat' : 'Direct message'}
          </Text>
          <Text className="text-gray-600" numberOfLines={1}>
            {lastMessage}
          </Text>
        </View>
        {unreadCount > 0 && (
          <View className="ml-2 h-5 w-5 items-center justify-center self-center rounded-full bg-indigo-600">
            <Text className="text-xs font-bold text-white">
              {unreadCount > 99 ? '99+' : unreadCount}
            </Text>
          </View>
        )}
      </TouchableOpacity>
    );
  };

  // Show loading indicator while fetching chats
  if (isLoading) {
    return (
      <View className="flex-1 items-center justify-center bg-white">
        <ActivityIndicator size="large" color="#4f46e5" />
      </View>
    );
  }

  // Show error message if there was an error fetching chats
  if (isError) {
    return (
      <View className="flex-1 items-center justify-center bg-white p-4">
        <Text className="mb-4 text-center text-lg text-red-500">
          {handleApiError(error, 'Failed to load chats')}
        </Text>
        <TouchableOpacity onPress={() => refetch()} className="rounded-lg bg-indigo-600 px-4 py-2">
          <Text className="text-white">Try Again</Text>
        </TouchableOpacity>
      </View>
    );
  }

  // Get chats from the response
  const chats = chatsData?.data || [];

  return (
    <View className="flex-1 bg-white">
      <View className="border-b border-gray-200 px-4 pb-4 pt-12">
        <Text className="text-2xl font-bold text-gray-900">Messages</Text>
      </View>

      {chats.length > 0 ? (
        <FlatList data={chats} renderItem={renderItem} keyExtractor={(item) => item.id} />
      ) : (
        <View className="flex-1 items-center justify-center p-4">
          <Ionicons name="chatbubble-ellipses-outline" size={64} color="#d1d5db" />
          <Text className="mt-4 text-center text-lg text-gray-500">No messages yet</Text>
          <Text className="mt-2 text-center text-gray-400">
            Messages from recruiters and hiring managers will appear here
          </Text>
        </View>
      )}
    </View>
  );
};

export default ChatsListScreen;
