import React, { useState, useRef } from 'react';
import {
  View,
  Text,
  TextInput,
  TouchableOpacity,
  FlatList,
  KeyboardAvoidingView,
  Platform,
  Image,
  ActivityIndicator,
  Alert,
} from 'react-native';
import { useNavigation, useRoute } from '@react-navigation/native';
import type { RouteProp } from '@react-navigation/native';
import type { NativeStackNavigationProp } from '@react-navigation/native-stack';
import { Ionicons } from '@expo/vector-icons';
import { useAppSelector } from '../../store';
import { format } from 'date-fns';
import {
  useGetChatMessagesQuery,
  useSendMessageMutation,
  useGetChatByIdQuery,
} from '../../store/api/chatApi';
import { handleApiError } from '../../utils/error-handler';
import type { ChatMessage } from '../../types/api';

// Define the route params type
type ChatDetailsScreenRouteProp = RouteProp<any, 'CHAT_DETAILS'>;
type ChatDetailsScreenNavigationProp = NativeStackNavigationProp<any, 'CHAT_DETAILS'>;

const ChatDetailsScreen = () => {
  const navigation = useNavigation<ChatDetailsScreenNavigationProp>();
  const route = useRoute<ChatDetailsScreenRouteProp>();
  const user = useAppSelector((state) => state.auth.user);

  // Get chat ID and recipient info from route params
  const { chatId, recipientId, recipientName } = route.params;

  // State for new message input
  const [inputText, setInputText] = useState<string>('');

  // Reference for FlatList to scroll to bottom
  const flatListRef = useRef<FlatList<ChatMessage> | null>(null);

  // Fetch chat details
  const {
    data: chatData,
    isLoading: isChatLoading,
    isError: isChatError,
    error: chatError,
  } = useGetChatByIdQuery(chatId);

  // Fetch chat messages
  const {
    data: messagesData,
    isLoading: isMessagesLoading,
    isError: isMessagesError,
    error: messagesError,
    refetch,
  } = useGetChatMessagesQuery({
    chatId,
    limit: 50,
    page: 1,
  });

  // Send message mutation
  const [sendMessageMutation, { isLoading: isSending }] = useSendMessageMutation();

  // Handle sending a new message
  const sendMessage = async (): Promise<void> => {
    if (!inputText.trim() || !user) return;

    try {
      // Send the message using RTK Query
      await sendMessageMutation({
        chatId,
        data: {
          message: inputText.trim(),
        },
      }).unwrap();

      // Clear the input
      setInputText('');

      // Scroll to bottom
      setTimeout(() => {
        flatListRef.current?.scrollToEnd({ animated: true });
      }, 100);
    } catch (error) {
      Alert.alert('Error', handleApiError(error, 'Failed to send message'));
    }
  };

  // Format the timestamp
  const formatMessageTime = (timestamp: string): string => {
    try {
      return format(new Date(timestamp), 'h:mm a');
    } catch (e) {
      return 'Unknown time';
    }
  };

  // Render a message
  const renderMessage = ({ item }: { item: ChatMessage }): JSX.Element => {
    const isUser = item.senderId === user?.id;
    const messageTime = formatMessageTime(item.createdAt);

    return (
      <View
        className={`mb-3 max-w-[80%] rounded-2xl px-4 py-3 ${
          isUser ? 'self-end bg-indigo-600' : 'self-start bg-gray-200'
        }`}>
        <Text className={isUser ? 'text-white' : 'text-gray-800'}>{item.message}</Text>
        <Text className={`mt-1 text-xs ${isUser ? 'text-indigo-200' : 'text-gray-500'}`}>
          {messageTime}
        </Text>
      </View>
    );
  };

  // Show loading indicator while fetching data
  if (isMessagesLoading || isChatLoading) {
    return (
      <View className="flex-1 items-center justify-center bg-white">
        <ActivityIndicator size="large" color="#4f46e5" />
      </View>
    );
  }

  // Show error message if there was an error
  if (isMessagesError || isChatError) {
    return (
      <View className="flex-1 items-center justify-center bg-white p-4">
        <Text className="mb-4 text-center text-lg text-red-500">
          {handleApiError(messagesError || chatError, 'Failed to load chat')}
        </Text>
        <TouchableOpacity onPress={() => refetch()} className="rounded-lg bg-indigo-600 px-4 py-2">
          <Text className="text-white">Try Again</Text>
        </TouchableOpacity>
      </View>
    );
  }

  // Get messages from the response
  const messages = messagesData?.data || [];

  // Get recipient info from participants
  const otherParticipant = chatData?.participants?.find((p) => p.userId !== user?.id);
  const displayName =
    recipientName || otherParticipant?.user?.fullName || chatData?.title || 'Chat';
  const avatarUrl = otherParticipant?.user?.profilePic || 'https://via.placeholder.com/150';
  const contextInfo = chatData?.contextType === 'job' ? 'Job Chat' : '';

  return (
    <KeyboardAvoidingView
      className="flex-1"
      behavior={Platform.OS === 'ios' ? 'padding' : undefined}
      keyboardVerticalOffset={Platform.OS === 'ios' ? 90 : 0}>
      <View className="flex-1 bg-white">
        <View className="flex-row items-center border-b border-gray-200 bg-white px-4 pb-4 pt-12">
          <TouchableOpacity onPress={() => navigation.goBack()} className="mr-3">
            <Ionicons name="arrow-back" size={24} color="#4b5563" />
          </TouchableOpacity>
          <Image source={{ uri: avatarUrl }} className="mr-3 h-10 w-10 rounded-full" />
          <View>
            <Text className="font-semibold text-gray-900">{displayName}</Text>
            <Text className="text-xs text-gray-500">{contextInfo}</Text>
          </View>
        </View>

        <FlatList
          ref={flatListRef}
          data={messages}
          renderItem={renderMessage}
          keyExtractor={(item) => item.id}
          contentContainerStyle={{ padding: 16 }}
          onLayout={() => flatListRef.current?.scrollToEnd({ animated: false })}
        />

        <View className="flex-row items-center border-t border-gray-200 bg-white p-2">
          <TextInput
            className="mr-2 flex-1 rounded-full bg-gray-100 px-4 py-2"
            placeholder="Type a message..."
            value={inputText}
            onChangeText={setInputText}
            multiline
          />
          <TouchableOpacity
            className="h-10 w-10 items-center justify-center rounded-full bg-indigo-600"
            onPress={sendMessage}
            disabled={!inputText.trim() || isSending}>
            <Ionicons name="send" size={20} color="white" />
          </TouchableOpacity>
        </View>
      </View>
    </KeyboardAvoidingView>
  );
};

export default ChatDetailsScreen;
