import { useState, useRef, JSX } from 'react';
import {
  View,
  Text,
  TextInput,
  TouchableOpacity,
  FlatList,
  KeyboardAvoidingView,
  Platform,
  ActivityIndicator,
  Alert,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useRoute, useNavigation } from '@react-navigation/native';
import type { RouteProp } from '@react-navigation/native';
import type { NativeStackNavigationProp } from '@react-navigation/native-stack';
import { useAppSelector } from '../../store';
import { format } from 'date-fns';
import { MotiView } from '@/utils/animationComponents';
import {
  useGetChatMessagesQuery,
  useSendMessageMutation,
  useCreateChatMutation,
} from '../../store/api/chatApi';
import { handleApiError } from '../../utils/error-handler';
import type { ChatMessage } from '../../types/api';
import { RootStackParamList } from '@/types/navigation';

// Define the route params type
type ChatScreenRouteProp = RouteProp<RootStackParamList, 'ChatDetails'>;
type ChatScreenNavigationProp = NativeStackNavigationProp<RootStackParamList, 'ChatDetails'>;

// Define a simple transition object
const fadeTransition = {
  type: 'timing' as const,
  duration: 300,
};

const ChatScreen = () => {
  const route = useRoute<ChatScreenRouteProp>();
  const navigation = useNavigation<ChatScreenNavigationProp>();
  const user = useAppSelector((state) => state.auth.user);

  // Get chat ID and recipient ID from route params
  const { chatId, recipientId } = route.params;

  // State for new message input
  const [newMessage, setNewMessage] = useState<string>('');

  // Reference for FlatList to scroll to bottom
  const flatListRef = useRef<FlatList<ChatMessage> | null>(null);

  // Fetch chat messages using RTK Query with polling for real-time updates
  const {
    data: chatMessagesData,
    isLoading,
    isError,
    error,
    refetch,
  } = useGetChatMessagesQuery(
    {
      chatId,
      limit: 50,
      offset: 0,
    },
    {
      // Poll every 10 seconds for new messages
      pollingInterval: 10000,
    }
  );

  console.log('chatMessagesData', isLoading, chatMessagesData);

  // Send message mutation
  const [sendMessageMutation, { isLoading: isSending }] = useSendMessageMutation();

  // Set the title to recipient name
  const recipientName = route.params.recipientName || 'Chat';
  if (navigation.getParent()) {
    navigation.getParent()?.setOptions({
      title: recipientName,
    });
  }

  // Handle sending a new message
  const handleSendMessage = async (): Promise<void> => {
    if (!newMessage.trim() || !user) return;

    try {
      // Send the message using RTK Query
      await sendMessageMutation({
        chatId,
        data: {
          message: newMessage.trim(),
        },
      }).unwrap();

      // Clear the input
      setNewMessage('');

      // Scroll to bottom
      if (flatListRef.current) {
        flatListRef.current.scrollToEnd({ animated: true });
      }
    } catch (error) {
      Alert.alert('Error', handleApiError(error, 'Failed to send message'));
    }
  };

  // Render a chat message
  const renderMessage = ({ item, index }: { item: ChatMessage; index: number }): JSX.Element => {
    const isUser = item.senderId === user?.id;
    const messageTime = format(new Date(item.createdAt), 'h:mm a');

    return (
      <MotiView
        from={{ opacity: 0, translateY: 20 }}
        animate={{ opacity: 1, translateY: 0 }}
        transition={{
          ...fadeTransition,
          delay: index * 50,
        }}
        className={`mb-4 max-w-[80%] ${isUser ? 'self-end' : 'self-start'}`}>
        <View
          className={`rounded-2xl p-3 ${isUser ? 'rounded-tr-none bg-indigo-600' : 'rounded-tl-none bg-gray-200'}`}>
          <Text className={isUser ? 'text-white' : 'text-gray-800'}>{item.message}</Text>
        </View>
        <Text className={`mt-1 text-xs ${isUser ? 'text-right text-gray-500' : 'text-gray-500'}`}>
          {messageTime}
        </Text>
      </MotiView>
    );
  };

  // Show loading indicator while fetching messages
  if (isLoading) {
    return (
      <View className="flex-1 items-center justify-center">
        <ActivityIndicator size="large" color="#4f46e5" />
      </View>
    );
  }

  // Show error message if there was an error fetching messages
  if (isError) {
    // Get a more specific error message if available
    let errorMessage = 'Failed to load messages';
    if (error) {
      if ('data' in error && error.data && typeof error.data === 'object') {
        const apiError = error.data as { message?: string };
        if (apiError.message) {
          errorMessage = apiError.message;
        }
      } else if ('message' in error && typeof error.message === 'string') {
        errorMessage = error.message;
      }
    }

    return (
      <View className="flex-1 items-center justify-center p-4">
        <Ionicons name="alert-circle-outline" size={60} color="#ef4444" />
        <Text className="mb-4 mt-4 text-center text-lg text-red-500">{errorMessage}</Text>
        <TouchableOpacity onPress={() => refetch()} className="rounded-lg bg-indigo-600 px-4 py-2">
          <Text className="text-white">Try Again</Text>
        </TouchableOpacity>
      </View>
    );
  }

  // Get messages from the response
  const messages = chatMessagesData?.data?.messages || [];

  return (
    <KeyboardAvoidingView
      behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
      className="flex-1"
      keyboardVerticalOffset={Platform.OS === 'ios' ? 90 : 0}>
      <View className="flex-1 bg-white">
        <FlatList
          ref={flatListRef}
          data={messages}
          keyExtractor={(item) => item.id}
          renderItem={renderMessage}
          contentContainerStyle={{ padding: 16 }}
          onContentSizeChange={() => flatListRef.current?.scrollToEnd({ animated: true })}
          ListEmptyComponent={
            <View className="flex-1 items-center justify-center p-10">
              <Ionicons name="chatbubble-ellipses-outline" size={60} color="gray" />
              <Text className="mt-4 text-center text-lg text-gray-500">No messages yet</Text>
              <Text className="mt-2 text-center text-gray-400">
                Start the conversation by sending a message
              </Text>
            </View>
          }
        />

        <View className="border-t border-gray-200 bg-white p-2">
          <View className="flex-row items-center rounded-full bg-gray-100 px-4 py-1">
            <TextInput
              className="flex-1 py-2"
              placeholder="Type a message..."
              value={newMessage}
              onChangeText={setNewMessage}
              multiline
            />
            {isSending ? (
              <ActivityIndicator size="small" color="#4f46e5" />
            ) : (
              <TouchableOpacity
                onPress={handleSendMessage}
                disabled={!newMessage.trim() || isSending}
                className={`ml-2 ${!newMessage.trim() ? 'opacity-50' : ''}`}>
                <Ionicons name="send" size={24} color="#4f46e5" />
              </TouchableOpacity>
            )}
          </View>
        </View>
      </View>
    </KeyboardAvoidingView>
  );
};

export default ChatScreen;
