import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  ScrollView,
  TouchableOpacity,
  ActivityIndicator,
  RefreshControl,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { MotiView } from 'moti';
import { format } from 'date-fns';
import { paymentsAPI } from '../../services/api';
import { sharedTransition } from '../../utils/animations';
import { SCREENS } from '../../constants/screens';
import { useTypedNavigation } from '@/types/navigation';

const PaymentsScreen = () => {
  const navigation = useTypedNavigation();
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [paymentMethods, setPaymentMethods] = useState([]);
  const [paymentHistory, setPaymentHistory] = useState([]);
  const [activeTab, setActiveTab] = useState('methods'); // 'methods' or 'history'

  const fetchPaymentData = async () => {
    try {
      setLoading(true);

      // Fetch payment methods
      const methodsResponse = await paymentsAPI.getPaymentMethods();
      setPaymentMethods(methodsResponse.data);

      // Fetch payment history
      const historyResponse = await paymentsAPI.getPaymentHistory();
      setPaymentHistory(historyResponse.data);
    } catch (error) {
      console.error('Error fetching payment data:', error);
    } finally {
      setLoading(false);
      setRefreshing(false);
    }
  };

  useEffect(() => {
    fetchPaymentData();
  }, []);

  const handleRefresh = () => {
    setRefreshing(true);
    fetchPaymentData();
  };

  const formatDate = (dateString) => {
    return format(new Date(dateString), 'MMM dd, yyyy');
  };

  const getStatusColor = (status) => {
    switch (status.toLowerCase()) {
      case 'succeeded':
      case 'success':
        return 'bg-green-100 text-green-800';
      case 'pending':
      case 'processing':
        return 'bg-yellow-100 text-yellow-800';
      case 'failed':
      case 'error':
        return 'bg-red-100 text-red-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  if (loading && !refreshing) {
    return (
      <View className="flex-1 items-center justify-center bg-white">
        <ActivityIndicator size="large" color="#4f46e5" />
      </View>
    );
  }

  return (
    <View className="flex-1 bg-gray-50">
      <View className="bg-indigo-600 px-4 pb-4 pt-12">
        <TouchableOpacity
          className="mb-2 flex-row items-center"
          onPress={() => navigation.goBack()}>
          <Ionicons name="arrow-back" size={24} color="white" />
          <Text className="ml-2 text-white">Back</Text>
        </TouchableOpacity>
        <Text className="text-2xl font-bold text-white">Payments</Text>
        <Text className="text-indigo-200">Manage your payment methods and view history</Text>
      </View>

      <View className="flex-row border-b border-gray-200 bg-white">
        <TouchableOpacity
          className={`flex-1 py-3 ${activeTab === 'methods' ? 'border-b-2 border-indigo-600' : ''}`}
          onPress={() => setActiveTab('methods')}>
          <Text
            className={`text-center font-medium ${
              activeTab === 'methods' ? 'text-indigo-600' : 'text-gray-500'
            }`}>
            Payment Methods
          </Text>
        </TouchableOpacity>
        <TouchableOpacity
          className={`flex-1 py-3 ${activeTab === 'history' ? 'border-b-2 border-indigo-600' : ''}`}
          onPress={() => setActiveTab('history')}>
          <Text
            className={`text-center font-medium ${
              activeTab === 'history' ? 'text-indigo-600' : 'text-gray-500'
            }`}>
            Payment History
          </Text>
        </TouchableOpacity>
      </View>

      <ScrollView
        className="flex-1"
        refreshControl={<RefreshControl refreshing={refreshing} onRefresh={handleRefresh} />}>
        <MotiView
          from={{ opacity: 0, translateY: 20 }}
          animate={{ opacity: 1, translateY: 0 }}
          transition={sharedTransition}
          className="p-4">
          {activeTab === 'methods' ? (
            <>
              {/* Payment Methods */}
              <View className="mb-4">
                <TouchableOpacity
                  className="mb-3 flex-row items-center rounded-lg bg-white p-4 shadow-sm"
                  onPress={() => navigation.navigate(SCREENS.ADD_PAYMENT_METHOD, { type: 'card' })}>
                  <View className="mr-3 h-10 w-10 items-center justify-center rounded-full bg-indigo-100">
                    <Ionicons name="card-outline" size={20} color="#4f46e5" />
                  </View>
                  <View className="flex-1">
                    <Text className="font-medium text-gray-900">Add Credit/Debit Card</Text>
                    <Text className="text-sm text-gray-500">Add a new card for payments</Text>
                  </View>
                  <Ionicons name="chevron-forward" size={20} color="#9ca3af" />
                </TouchableOpacity>

                <TouchableOpacity
                  className="mb-3 flex-row items-center rounded-lg bg-white p-4 shadow-sm"
                  onPress={() => navigation.navigate(SCREENS.ADD_PAYMENT_METHOD, { type: 'upi' })}>
                  <View className="mr-3 h-10 w-10 items-center justify-center rounded-full bg-indigo-100">
                    <Ionicons name="phone-portrait-outline" size={20} color="#4f46e5" />
                  </View>
                  <View className="flex-1">
                    <Text className="font-medium text-gray-900">Add UPI ID</Text>
                    <Text className="text-sm text-gray-500">
                      Link your UPI ID for faster payments
                    </Text>
                  </View>
                  <Ionicons name="chevron-forward" size={20} color="#9ca3af" />
                </TouchableOpacity>
              </View>

              {/* Saved Payment Methods */}
              {paymentMethods.length > 0 ? (
                <>
                  <Text className="mb-2 font-medium text-gray-700">Saved Payment Methods</Text>
                  {paymentMethods.map((method, index) => (
                    <View
                      key={index}
                      className="mb-3 flex-row items-center rounded-lg bg-white p-4 shadow-sm">
                      <View className="mr-3 h-10 w-10 items-center justify-center rounded-full bg-gray-100">
                        <Ionicons
                          name={method.type === 'card' ? 'card-outline' : 'phone-portrait-outline'}
                          size={20}
                          color="#4f46e5"
                        />
                      </View>
                      <View className="flex-1">
                        <Text className="font-medium text-gray-900">
                          {method.type === 'card' ? `•••• •••• •••• ${method.last4}` : method.upiId}
                        </Text>
                        <Text className="text-sm text-gray-500">
                          {method.type === 'card'
                            ? `${method.brand} - Expires ${method.expMonth}/${method.expYear}`
                            : 'UPI ID'}
                        </Text>
                      </View>
                      <TouchableOpacity
                        className="p-2"
                        onPress={() => {
                          // Handle delete payment method
                        }}>
                        <Ionicons name="trash-outline" size={20} color="#ef4444" />
                      </TouchableOpacity>
                    </View>
                  ))}
                </>
              ) : (
                <View className="items-center rounded-lg bg-white p-6 shadow-sm">
                  <Ionicons name="wallet-outline" size={48} color="#9ca3af" />
                  <Text className="mt-3 font-medium text-gray-700">No Payment Methods</Text>
                  <Text className="mt-1 text-center text-gray-500">
                    Add a payment method to receive payments for completed jobs
                  </Text>
                </View>
              )}
            </>
          ) : (
            <>
              {/* Payment History */}
              {paymentHistory.length > 0 ? (
                paymentHistory.map((payment, index) => (
                  <TouchableOpacity
                    key={index}
                    className="mb-3 rounded-lg bg-white p-4 shadow-sm"
                    onPress={() =>
                      navigation.navigate(SCREENS.PAYMENT_DETAILS, { paymentId: payment.id })
                    }>
                    <View className="flex-row items-start justify-between">
                      <View className="flex-1">
                        <Text className="font-medium text-gray-900">{payment.description}</Text>
                        <Text className="text-sm text-gray-500">
                          {formatDate(payment.createdAt)}
                        </Text>
                      </View>
                      <Text className="font-bold">₹{payment.amount.toFixed(2)}</Text>
                    </View>
                    <View className="mt-3 flex-row items-center justify-between border-t border-gray-100 pt-3">
                      <View className="flex-row items-center">
                        <Ionicons
                          name={
                            payment.paymentMethod === 'card'
                              ? 'card-outline'
                              : 'phone-portrait-outline'
                          }
                          size={16}
                          color="#6b7280"
                        />
                        <Text className="ml-1 text-sm text-gray-500">
                          {payment.paymentMethod === 'card'
                            ? `Card •••• ${payment.last4}`
                            : `UPI: ${payment.upiId}`}
                        </Text>
                      </View>
                      <View className={`rounded-full px-2 py-1 ${getStatusColor(payment.status)}`}>
                        <Text className="text-xs font-medium capitalize">{payment.status}</Text>
                      </View>
                    </View>
                  </TouchableOpacity>
                ))
              ) : (
                <View className="items-center rounded-lg bg-white p-6 shadow-sm">
                  <Ionicons name="receipt-outline" size={48} color="#9ca3af" />
                  <Text className="mt-3 font-medium text-gray-700">No Payment History</Text>
                  <Text className="mt-1 text-center text-gray-500">
                    Your payment history will appear here
                  </Text>
                </View>
              )}
            </>
          )}
        </MotiView>
      </ScrollView>
    </View>
  );
};

export default PaymentsScreen;
