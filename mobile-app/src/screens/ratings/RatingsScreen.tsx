import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  ScrollView,
  TouchableOpacity,
  ActivityIndicator,
  RefreshControl,
  FlatList,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { MotiView } from 'moti';
import { format } from 'date-fns';
import { ratingsAPI } from '../../services/api';
import { sharedTransition } from '../../utils/animations';
import { SCREENS } from '../../constants/screens';
import EmptyState from '../../components/EmptyState';
import { useTypedNavigation } from '@/types/navigation';

const RatingsScreen = () => {
  const navigation = useTypedNavigation();
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [receivedRatings, setReceivedRatings] = useState([]);
  const [givenRatings, setGivenRatings] = useState([]);
  const [ratingSummary, setRatingSummary] = useState(null);
  const [activeTab, setActiveTab] = useState('received'); // 'received' or 'given'

  const fetchRatingsData = async () => {
    try {
      setLoading(true);

      // Fetch received ratings
      const receivedResponse = await ratingsAPI.getMyRatings();
      setReceivedRatings(receivedResponse.data);

      // Fetch given ratings
      const givenResponse = await ratingsAPI.getGivenRatings();
      setGivenRatings(givenResponse.data);

      // Fetch ratings summary
      const summaryResponse = await ratingsAPI.getRatingsSummary();
      setRatingSummary(summaryResponse.data);
    } catch (error) {
      console.error('Error fetching ratings data:', error);
    } finally {
      setLoading(false);
      setRefreshing(false);
    }
  };

  useEffect(() => {
    fetchRatingsData();
  }, []);

  const handleRefresh = () => {
    setRefreshing(true);
    fetchRatingsData();
  };

  const formatDate = (dateString) => {
    return format(new Date(dateString), 'MMM dd, yyyy');
  };

  const renderStars = (rating) => {
    return (
      <View className="flex-row">
        {[1, 2, 3, 4, 5].map((star) => (
          <Ionicons
            key={star}
            name={star <= rating ? 'star' : 'star-outline'}
            size={16}
            color={star <= rating ? '#f59e0b' : '#d1d5db'}
          />
        ))}
      </View>
    );
  };

  const renderRatingItem = ({ item }) => (
    <View className="mb-3 rounded-lg bg-white p-4 shadow-sm">
      <View className="flex-row items-start justify-between">
        <View className="flex-1">
          <Text className="font-medium text-gray-900">
            {activeTab === 'received' ? item.fromName : item.toName}
          </Text>
          <Text className="text-sm text-gray-500">Job: {item.jobTitle || 'Unknown Job'}</Text>
          <Text className="text-sm text-gray-500">{formatDate(item.createdAt)}</Text>
        </View>
        {renderStars(item.rating)}
      </View>

      {item.comment && (
        <View className="mt-2 border-t border-gray-100 pt-2">
          <Text className="text-gray-700">"{item.comment}"</Text>
        </View>
      )}
    </View>
  );

  if (loading && !refreshing) {
    return (
      <View className="flex-1 items-center justify-center bg-white">
        <ActivityIndicator size="large" color="#4f46e5" />
      </View>
    );
  }

  return (
    <View className="flex-1 bg-gray-50">
      <View className="bg-indigo-600 px-4 pb-4 pt-12">
        <TouchableOpacity
          className="mb-2 flex-row items-center"
          onPress={() => navigation.goBack()}>
          <Ionicons name="arrow-back" size={24} color="white" />
          <Text className="ml-2 text-white">Back</Text>
        </TouchableOpacity>
        <Text className="text-2xl font-bold text-white">Ratings & Reviews</Text>
        <Text className="text-indigo-200">View your ratings and reviews</Text>
      </View>

      {/* Rating Summary */}
      {ratingSummary && (
        <View className="border-b border-gray-200 bg-white p-4">
          <View className="flex-row items-center justify-between">
            <View>
              <Text className="text-sm text-gray-500">Average Rating</Text>
              <View className="flex-row items-center">
                <Text className="mr-2 text-2xl font-bold text-gray-900">
                  {ratingSummary.averageRating.toFixed(1)}
                </Text>
                {renderStars(Math.round(ratingSummary.averageRating))}
              </View>
            </View>
            <View>
              <Text className="text-sm text-gray-500">Total Reviews</Text>
              <Text className="text-2xl font-bold text-gray-900">{ratingSummary.totalRatings}</Text>
            </View>
          </View>

          {/* Rating Distribution */}
          <View className="mt-4">
            {[5, 4, 3, 2, 1].map((star) => (
              <View key={star} className="mb-1 flex-row items-center">
                <Text className="w-8 text-gray-700">{star} ★</Text>
                <View className="mx-2 h-2 flex-1 overflow-hidden rounded-full bg-gray-200">
                  <View
                    className="h-full bg-yellow-500"
                    style={{
                      width: `${
                        (ratingSummary.distribution[star] / ratingSummary.totalRatings) * 100 || 0
                      }%`,
                    }}
                  />
                </View>
                <Text className="w-8 text-right text-gray-500">
                  {ratingSummary.distribution[star] || 0}
                </Text>
              </View>
            ))}
          </View>
        </View>
      )}

      <View className="flex-row border-b border-gray-200 bg-white">
        <TouchableOpacity
          className={`flex-1 py-3 ${activeTab === 'received' ? 'border-b-2 border-indigo-600' : ''}`}
          onPress={() => setActiveTab('received')}>
          <Text
            className={`text-center font-medium ${
              activeTab === 'received' ? 'text-indigo-600' : 'text-gray-500'
            }`}>
            Received
          </Text>
        </TouchableOpacity>
        <TouchableOpacity
          className={`flex-1 py-3 ${activeTab === 'given' ? 'border-b-2 border-indigo-600' : ''}`}
          onPress={() => setActiveTab('given')}>
          <Text
            className={`text-center font-medium ${
              activeTab === 'given' ? 'text-indigo-600' : 'text-gray-500'
            }`}>
            Given
          </Text>
        </TouchableOpacity>
      </View>

      <FlatList
        className="flex-1 p-4"
        data={activeTab === 'received' ? receivedRatings : givenRatings}
        renderItem={renderRatingItem}
        keyExtractor={(item) => item.id}
        refreshControl={<RefreshControl refreshing={refreshing} onRefresh={handleRefresh} />}
        ListEmptyComponent={
          <EmptyState
            title={`No ${activeTab} ratings yet`}
            message={
              activeTab === 'received'
                ? 'Complete more jobs to receive ratings'
                : 'Rate companies after completing jobs'
            }
            icon="star-outline"
          />
        }
      />
    </View>
  );
};

export default RatingsScreen;
