import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  ScrollView,
  TouchableOpacity,
  TextInput,
  ActivityIndicator,
  Alert,
  KeyboardAvoidingView,
  Platform,
} from 'react-native';
import { useNavigation, useRoute } from '@react-navigation/native';
import { Ionicons } from '@expo/vector-icons';
import { MotiView } from 'moti';
import { ratingsAPI, jobsAPI, applicationsAPI } from '../../services/api';
import { sharedTransition } from '../../utils/animations';
import { SCREENS } from '../../constants/screens';

const RateCompanyScreen = () => {
  const navigation = useNavigation();
  const route = useRoute();
  const { jobId, applicationId } = route.params || {};
  
  const [loading, setLoading] = useState(true);
  const [submitting, setSubmitting] = useState(false);
  const [jobDetails, setJobDetails] = useState(null);
  const [applicationDetails, setApplicationDetails] = useState(null);
  const [rating, setRating] = useState(0);
  const [comment, setComment] = useState('');

  useEffect(() => {
    fetchInitialData();
  }, []);

  const fetchInitialData = async () => {
    try {
      setLoading(true);
      
      if (!jobId || !applicationId) {
        Alert.alert('Error', 'Missing job or application information');
        navigation.goBack();
        return;
      }
      
      // Fetch job details
      const jobResponse = await jobsAPI.getJobById(jobId);
      setJobDetails(jobResponse.data);
      
      // Fetch application details
      const applicationResponse = await applicationsAPI.getApplicationById(applicationId);
      setApplicationDetails(applicationResponse.data);
    } catch (error) {
      console.error('Error fetching data:', error);
      Alert.alert('Error', 'Failed to load job details');
      navigation.goBack();
    } finally {
      setLoading(false);
    }
  };

  const handleRatingPress = (value) => {
    setRating(value);
  };

  const handleSubmit = async () => {
    if (rating === 0) {
      Alert.alert('Error', 'Please select a rating');
      return;
    }
    
    try {
      setSubmitting(true);
      
      await ratingsAPI.rateCompany({
        jobId,
        applicationId,
        rating,
        comment: comment.trim() || undefined,
      });
      
      Alert.alert(
        'Success',
        'Your rating has been submitted successfully',
        [
          {
            text: 'OK',
            onPress: () => navigation.navigate(SCREENS.RATINGS),
          },
        ]
      );
    } catch (error) {
      console.error('Error submitting rating:', error);
      Alert.alert('Error', 'Failed to submit rating');
    } finally {
      setSubmitting(false);
    }
  };

  if (loading) {
    return (
      <View className="flex-1 items-center justify-center bg-white">
        <ActivityIndicator size="large" color="#4f46e5" />
      </View>
    );
  }

  return (
    <KeyboardAvoidingView
      behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
      className="flex-1"
      keyboardVerticalOffset={Platform.OS === 'ios' ? 90 : 0}>
      <View className="flex-1 bg-gray-50">
        <View className="bg-indigo-600 pt-12 pb-4 px-4">
          <TouchableOpacity
            className="flex-row items-center mb-2"
            onPress={() => navigation.goBack()}>
            <Ionicons name="arrow-back" size={24} color="white" />
            <Text className="ml-2 text-white">Back</Text>
          </TouchableOpacity>
          <Text className="text-2xl font-bold text-white">Rate Company</Text>
          <Text className="text-indigo-200">Share your experience</Text>
        </View>

        <ScrollView className="flex-1 p-4">
          <MotiView
            from={{ opacity: 0, translateY: 20 }}
            animate={{ opacity: 1, translateY: 0 }}
            transition={sharedTransition}>
            <View className="bg-white rounded-lg shadow-sm p-4 mb-4">
              <Text className="text-lg font-bold text-gray-900 mb-2">{jobDetails?.title}</Text>
              <Text className="text-gray-700 mb-1">
                Company: {jobDetails?.company?.name || 'Unknown Company'}
              </Text>
              <Text className="text-gray-700 mb-3">
                Completed on:{' '}
                {applicationDetails?.completedAt
                  ? new Date(applicationDetails.completedAt).toLocaleDateString()
                  : 'Unknown'}
              </Text>

              <View className="border-t border-gray-100 pt-4 mt-2">
                <Text className="text-gray-700 font-medium mb-4 text-center">
                  How was your experience working with this company?
                </Text>

                <View className="flex-row justify-center mb-6">
                  {[1, 2, 3, 4, 5].map((value) => (
                    <TouchableOpacity
                      key={value}
                      className="mx-2"
                      onPress={() => handleRatingPress(value)}>
                      <Ionicons
                        name={value <= rating ? 'star' : 'star-outline'}
                        size={40}
                        color={value <= rating ? '#f59e0b' : '#d1d5db'}
                      />
                    </TouchableOpacity>
                  ))}
                </View>

                <Text className="text-gray-700 font-medium mb-2">
                  Additional Comments (Optional)
                </Text>
                <TextInput
                  className="border border-gray-300 rounded-lg p-3 min-h-[100px] mb-4 text-gray-700"
                  placeholder="Share your experience working with this company..."
                  value={comment}
                  onChangeText={setComment}
                  multiline
                  textAlignVertical="top"
                />

                <TouchableOpacity
                  className={`py-3 rounded-lg items-center ${
                    submitting ? 'bg-indigo-400' : 'bg-indigo-600'
                  }`}
                  onPress={handleSubmit}
                  disabled={submitting}>
                  {submitting ? (
                    <ActivityIndicator color="white" />
                  ) : (
                    <Text className="text-white font-medium">Submit Rating</Text>
                  )}
                </TouchableOpacity>
              </View>
            </View>
          </MotiView>
        </ScrollView>
      </View>
    </KeyboardAvoidingView>
  );
};

export default RateCompanyScreen;
