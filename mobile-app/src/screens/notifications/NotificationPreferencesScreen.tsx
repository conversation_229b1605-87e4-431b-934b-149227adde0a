import React, { useState } from 'react';
import {
  View,
  Text,
  Switch,
  TouchableOpacity,
  ActivityIndicator,
  Al<PERSON>,
  Sc<PERSON>View,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { MotiView } from 'moti';
import { sharedTransition } from '../../utils/animations';
import {
  useGetNotificationPreferencesQuery,
  useUpdateNotificationPreferencesMutation,
} from '../../store/api/notificationsApi';
import { useTypedNavigation } from '@/types/navigation';

const NotificationPreferencesScreen = () => {
  const navigation = useTypedNavigation();
  const [saving, setSaving] = useState(false);

  // Use RTK Query hooks
  const { data: preferencesData, isLoading } = useGetNotificationPreferencesQuery();

  const [updatePreferences] = useUpdateNotificationPreferencesMutation();

  // Initialize preferences with default values, then update with data from API
  const [preferences, setPreferences] = useState({
    jobAlerts: true,
    applicationUpdates: true,
    paymentNotifications: true,
    chatMessages: true,
    marketingEmails: false,
  });

  // Update local state when API data is loaded
  React.useEffect(() => {
    if (preferencesData) {
      setPreferences({
        jobAlerts: preferencesData.categories?.jobs?.push || true,
        applicationUpdates: preferencesData.categories?.applications?.push || true,
        paymentNotifications: preferencesData.categories?.payments?.push || true,
        chatMessages: preferencesData.categories?.chat?.push || true,
        marketingEmails: preferencesData.categories?.marketing?.email || false,
      });
    }
  }, [preferencesData]);

  const handleToggle = (key) => {
    setPreferences({
      ...preferences,
      [key]: !preferences[key],
    });
  };

  const savePreferences = async () => {
    try {
      setSaving(true);

      // Convert preferences to the format expected by the API
      const apiPreferences = {
        push: true, // Enable push notifications globally
        email: true, // Enable email notifications globally
        sms: false, // Disable SMS notifications globally
        categories: {
          jobs: {
            push: preferences.jobAlerts,
            email: preferences.jobAlerts,
          },
          applications: {
            push: preferences.applicationUpdates,
            email: preferences.applicationUpdates,
          },
          payments: {
            push: preferences.paymentNotifications,
            email: preferences.paymentNotifications,
          },
          chat: {
            push: preferences.chatMessages,
          },
          marketing: {
            email: preferences.marketingEmails,
          },
        },
      };

      await updatePreferences(apiPreferences).unwrap();
      Alert.alert('Success', 'Notification preferences updated successfully');
    } catch (error) {
      console.error('Error saving notification preferences:', error);
      Alert.alert('Error', 'Failed to update notification preferences');
    } finally {
      setSaving(false);
    }
  };

  if (isLoading) {
    return (
      <View className="flex-1 items-center justify-center bg-white">
        <ActivityIndicator size="large" color="#4f46e5" />
      </View>
    );
  }

  return (
    <View className="flex-1 bg-gray-50">
      <View className="bg-indigo-600 px-4 pb-4 pt-12">
        <TouchableOpacity
          className="mb-2 flex-row items-center"
          onPress={() => navigation.goBack()}>
          <Ionicons name="arrow-back" size={24} color="white" />
          <Text className="ml-2 text-white">Back</Text>
        </TouchableOpacity>
        <Text className="text-2xl font-bold text-white">Notification Preferences</Text>
        <Text className="text-indigo-200">Manage how you receive notifications</Text>
      </View>

      <ScrollView className="flex-1 p-4">
        <MotiView
          from={{ opacity: 0, translateY: 20 }}
          animate={{ opacity: 1, translateY: 0 }}
          transition={sharedTransition}>
          <View className="mb-4 rounded-lg bg-white p-4 shadow-sm">
            <Text className="mb-4 text-lg font-bold text-gray-900">Push Notifications</Text>

            <View className="space-y-4">
              <View className="flex-row items-center justify-between">
                <View>
                  <Text className="font-medium text-gray-800">Job Alerts</Text>
                  <Text className="text-sm text-gray-500">New jobs matching your skills</Text>
                </View>
                <Switch
                  value={preferences.jobAlerts}
                  onValueChange={() => handleToggle('jobAlerts')}
                  trackColor={{ false: '#d1d5db', true: '#818cf8' }}
                  thumbColor={preferences.jobAlerts ? '#4f46e5' : '#f3f4f6'}
                />
              </View>

              <View className="flex-row items-center justify-between">
                <View>
                  <Text className="font-medium text-gray-800">Application Updates</Text>
                  <Text className="text-sm text-gray-500">Status changes on your applications</Text>
                </View>
                <Switch
                  value={preferences.applicationUpdates}
                  onValueChange={() => handleToggle('applicationUpdates')}
                  trackColor={{ false: '#d1d5db', true: '#818cf8' }}
                  thumbColor={preferences.applicationUpdates ? '#4f46e5' : '#f3f4f6'}
                />
              </View>

              <View className="flex-row items-center justify-between">
                <View>
                  <Text className="font-medium text-gray-800">Payment Notifications</Text>
                  <Text className="text-sm text-gray-500">Updates about your payments</Text>
                </View>
                <Switch
                  value={preferences.paymentNotifications}
                  onValueChange={() => handleToggle('paymentNotifications')}
                  trackColor={{ false: '#d1d5db', true: '#818cf8' }}
                  thumbColor={preferences.paymentNotifications ? '#4f46e5' : '#f3f4f6'}
                />
              </View>

              <View className="flex-row items-center justify-between">
                <View>
                  <Text className="font-medium text-gray-800">Chat Messages</Text>
                  <Text className="text-sm text-gray-500">New messages from employers</Text>
                </View>
                <Switch
                  value={preferences.chatMessages}
                  onValueChange={() => handleToggle('chatMessages')}
                  trackColor={{ false: '#d1d5db', true: '#818cf8' }}
                  thumbColor={preferences.chatMessages ? '#4f46e5' : '#f3f4f6'}
                />
              </View>
            </View>
          </View>

          <View className="mb-4 rounded-lg bg-white p-4 shadow-sm">
            <Text className="mb-4 text-lg font-bold text-gray-900">Email Notifications</Text>

            <View className="flex-row items-center justify-between">
              <View>
                <Text className="font-medium text-gray-800">Marketing Emails</Text>
                <Text className="text-sm text-gray-500">Promotional offers and updates</Text>
              </View>
              <Switch
                value={preferences.marketingEmails}
                onValueChange={() => handleToggle('marketingEmails')}
                trackColor={{ false: '#d1d5db', true: '#818cf8' }}
                thumbColor={preferences.marketingEmails ? '#4f46e5' : '#f3f4f6'}
              />
            </View>
          </View>

          <TouchableOpacity
            className={`items-center rounded-lg py-3 ${saving ? 'bg-indigo-400' : 'bg-indigo-600'}`}
            onPress={savePreferences}
            disabled={saving}>
            {saving ? (
              <ActivityIndicator color="white" />
            ) : (
              <Text className="font-medium text-white">Save Preferences</Text>
            )}
          </TouchableOpacity>
        </MotiView>
      </ScrollView>
    </View>
  );
};

export default NotificationPreferencesScreen;
