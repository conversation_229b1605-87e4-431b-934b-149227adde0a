import React, { useState } from 'react';
import {
  View,
  Text,
  FlatList,
  TouchableOpacity,
  ActivityIndicator,
  RefreshControl,
  Alert,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { format, isToday, isYesterday, isThisWeek } from 'date-fns';
import { SCREENS } from '../../constants/screens';
import EmptyState from '../../components/EmptyState';
import { useTypedNavigation } from '@/types/navigation';
import {
  useGetNotificationsQuery,
  useMarkAsReadMutation,
  useMarkAllAsReadMutation,
} from '../../store/api/notificationsApi';

const NotificationsScreen = () => {
  const navigation = useTypedNavigation();
  const [refreshing, setRefreshing] = useState(false);

  // Use RTK Query hooks
  const { data: notificationsData, isLoading, refetch } = useGetNotificationsQuery({});

  const [markAsRead] = useMarkAsReadMutation();
  const [markAllAsRead] = useMarkAllAsReadMutation();

  // Extract data from query results
  const notifications = notificationsData?.notifications || [];
  const unreadCount = notificationsData?.unreadCount || 0;

  const handleRefresh = async () => {
    setRefreshing(true);
    await refetch();
    setRefreshing(false);
  };

  const handleMarkAllAsRead = async () => {
    try {
      await markAllAsRead().unwrap();
    } catch (error) {
      console.error('Error marking all notifications as read:', error);
      Alert.alert('Error', 'Failed to mark notifications as read');
    }
  };

  const handleNotificationPress = async (notification) => {
    try {
      // Mark as read if not already read
      if (!notification.isRead) {
        await markAsRead(notification.id).unwrap();
      }

      // Navigate based on notification type
      navigateToNotificationTarget(notification);
    } catch (error) {
      console.error('Error handling notification press:', error);
    }
  };

  const navigateToNotificationTarget = (notification) => {
    const { type, data } = notification;

    switch (type) {
      case 'job':
        navigation.navigate(SCREENS.JOB_DETAILS, { jobId: data.jobId });
        break;
      case 'application':
        navigation.navigate(SCREENS.APPLICATION_DETAILS, { applicationId: data.applicationId });
        break;
      case 'chat':
        navigation.navigate(SCREENS.CHAT_CONVERSATION, { conversationId: data.conversationId });
        break;
      case 'payment':
        navigation.navigate(SCREENS.PAYMENT_DETAILS, { paymentId: data.paymentId });
        break;
      case 'dispute':
        navigation.navigate(SCREENS.DISPUTE_DETAILS, { disputeId: data.disputeId });
        break;
      case 'trust_score':
        navigation.navigate(SCREENS.TRUST_SCORE);
        break;
      default:
        // Do nothing for general notifications
        break;
    }
  };

  const formatNotificationDate = (dateString) => {
    const date = new Date(dateString);

    if (isToday(date)) {
      return format(date, 'h:mm a');
    } else if (isYesterday(date)) {
      return 'Yesterday';
    } else if (isThisWeek(date)) {
      return format(date, 'EEEE'); // Day name
    } else {
      return format(date, 'MMM d, yyyy');
    }
  };

  const getNotificationIcon = (type) => {
    switch (type) {
      case 'job':
        return 'briefcase-outline';
      case 'application':
        return 'document-text-outline';
      case 'chat':
        return 'chatbubble-outline';
      case 'payment':
        return 'card-outline';
      case 'dispute':
        return 'alert-circle-outline';
      case 'trust_score':
        return 'shield-checkmark-outline';
      default:
        return 'notifications-outline';
    }
  };

  const renderNotificationItem = ({ item }) => (
    <TouchableOpacity
      className={`border-b border-gray-100 p-4 ${!item.isRead ? 'bg-indigo-50' : 'bg-white'}`}
      onPress={() => handleNotificationPress(item)}>
      <View className="flex-row">
        <View
          className={`mr-3 h-10 w-10 items-center justify-center rounded-full ${!item.isRead ? 'bg-indigo-100' : 'bg-gray-100'}`}>
          <Ionicons
            name={getNotificationIcon(item.type)}
            size={20}
            color={!item.isRead ? '#4f46e5' : '#6b7280'}
          />
        </View>
        <View className="flex-1">
          <View className="flex-row items-start justify-between">
            <Text
              className={`flex-1 ${!item.isRead ? 'font-bold text-gray-900' : 'font-medium text-gray-800'}`}>
              {item.title}
            </Text>
            <Text className="ml-2 text-xs text-gray-500">
              {formatNotificationDate(item.createdAt)}
            </Text>
          </View>
          <Text className="mt-1 text-gray-600">{item.body}</Text>
        </View>
      </View>
    </TouchableOpacity>
  );

  if (isLoading && !refreshing) {
    return (
      <View className="flex-1 items-center justify-center bg-white">
        <ActivityIndicator size="large" color="#4f46e5" />
      </View>
    );
  }

  return (
    <View className="flex-1 bg-white">
      <View className="bg-indigo-600 px-4 pb-4 pt-12">
        <View className="mb-2 flex-row items-center justify-between">
          <TouchableOpacity className="flex-row items-center" onPress={() => navigation.goBack()}>
            <Ionicons name="arrow-back" size={24} color="white" />
            <Text className="ml-2 text-white">Back</Text>
          </TouchableOpacity>
          <TouchableOpacity
            className="flex-row items-center"
            onPress={() => navigation.navigate(SCREENS.NOTIFICATION_PREFERENCES)}>
            <Ionicons name="settings-outline" size={20} color="white" />
            <Text className="ml-1 text-white">Preferences</Text>
          </TouchableOpacity>
        </View>
        <Text className="text-2xl font-bold text-white">Notifications</Text>
        {unreadCount > 0 && (
          <View className="mt-2 flex-row items-center justify-between">
            <Text className="text-indigo-200">{unreadCount} unread notifications</Text>
            <TouchableOpacity
              className="rounded-full bg-indigo-500 px-3 py-1"
              onPress={handleMarkAllAsRead}>
              <Text className="text-sm text-white">Mark all as read</Text>
            </TouchableOpacity>
          </View>
        )}
      </View>

      <FlatList
        data={notifications}
        renderItem={renderNotificationItem}
        keyExtractor={(item) => item.id}
        refreshControl={<RefreshControl refreshing={refreshing} onRefresh={handleRefresh} />}
        ListEmptyComponent={
          <EmptyState
            title="No Notifications"
            message="You don't have any notifications yet"
            icon="notifications-outline"
          />
        }
      />
    </View>
  );
};

export default NotificationsScreen;
