import React, { useState } from 'react';
import { View, Text, TouchableOpacity, Switch, ScrollView, Alert } from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { SCREENS } from '../../constants/screens';
import { useTypedNavigation } from '@/types/navigation';

const SettingsScreen = () => {
  const navigation = useTypedNavigation();
  const [pushNotifications, setPushNotifications] = useState(true);
  const [emailNotifications, setEmailNotifications] = useState(true);
  const [smsNotifications, setSmsNotifications] = useState(false);
  const [darkMode, setDarkMode] = useState(false);
  const [locationServices, setLocationServices] = useState(true);

  const handleClearCache = () => {
    Alert.alert('Clear Cache', 'Are you sure you want to clear the app cache?', [
      {
        text: 'Cancel',
        style: 'cancel',
      },
      {
        text: 'Clear',
        onPress: () => {
          // Implement cache clearing logic
          Alert.alert('Success', 'Cache cleared successfully');
        },
        style: 'destructive',
      },
    ]);
  };

  const renderSettingItem = (icon, title, onPress, value, isToggle = false) => (
    <TouchableOpacity
      className="flex-row items-center justify-between border-b border-gray-100 py-4"
      onPress={isToggle ? null : onPress}>
      <View className="flex-row items-center">
        <Ionicons name={icon} size={22} color="#4f46e5" />
        <Text className="ml-3 text-gray-800">{title}</Text>
      </View>
      {isToggle ? (
        <Switch
          value={value}
          onValueChange={onPress}
          trackColor={{ false: '#d1d5db', true: '#c7d2fe' }}
          thumbColor={value ? '#4f46e5' : '#f4f4f5'}
        />
      ) : (
        <Ionicons name="chevron-forward" size={20} color="#9ca3af" />
      )}
    </TouchableOpacity>
  );

  return (
    <View className="flex-1 bg-white">
      <View className="flex-row items-center border-b border-gray-200 bg-white px-4 pb-4 pt-12">
        <TouchableOpacity onPress={() => navigation.goBack()} className="mr-3">
          <Ionicons name="arrow-back" size={24} color="#4b5563" />
        </TouchableOpacity>
        <Text className="text-xl font-bold text-gray-900">Settings</Text>
      </View>

      <ScrollView className="flex-1 px-4">
        <View className="mt-4">
          <Text className="mb-2 text-sm text-gray-500">Notifications</Text>
          {renderSettingItem(
            'notifications-outline',
            'Push Notifications',
            () => setPushNotifications(!pushNotifications),
            pushNotifications,
            true
          )}
          {renderSettingItem(
            'mail-outline',
            'Email Notifications',
            () => setEmailNotifications(!emailNotifications),
            emailNotifications,
            true
          )}
          {renderSettingItem(
            'chatbubble-outline',
            'SMS Notifications',
            () => setSmsNotifications(!smsNotifications),
            smsNotifications,
            true
          )}
          {renderSettingItem('notifications-outline', 'Notification Preferences', () =>
            navigation.navigate(SCREENS.NOTIFICATIONS_SETTINGS)
          )}
        </View>

        <View className="mt-6">
          <Text className="mb-2 text-sm text-gray-500">App Settings</Text>
          {renderSettingItem('color-palette-outline', 'Appearance', () =>
            navigation.navigate(SCREENS.THEME_SETTINGS)
          )}
          {renderSettingItem(
            'location-outline',
            'Location Services',
            () => setLocationServices(!locationServices),
            locationServices,
            true
          )}
          {renderSettingItem('language-outline', 'Language', () =>
            Alert.alert('Language', 'This feature is coming soon!')
          )}
        </View>

        <View className="mt-6">
          <Text className="mb-2 text-sm text-gray-500">Account</Text>
          {renderSettingItem('card-outline', 'Payment Methods', () =>
            navigation.navigate(SCREENS.PAYMENT_METHODS)
          )}
          {renderSettingItem('document-text-outline', 'Documents', () =>
            navigation.navigate(SCREENS.DOCUMENTS)
          )}
          {renderSettingItem('shield-checkmark-outline', 'Privacy & Security', () =>
            Alert.alert('Privacy & Security', 'This feature is coming soon!')
          )}
        </View>

        <View className="mt-6">
          <Text className="mb-2 text-sm text-gray-500">Support</Text>
          {renderSettingItem('help-circle-outline', 'Help Center', () =>
            Alert.alert('Help Center', 'This feature is coming soon!')
          )}
          {renderSettingItem('chatbox-ellipses-outline', 'Contact Support', () =>
            Alert.alert('Contact Support', 'This feature is coming soon!')
          )}
          {renderSettingItem('information-circle-outline', 'About', () =>
            Alert.alert('About', 'Job Platform v1.0.0')
          )}
        </View>

        <View className="mb-8 mt-6">
          <TouchableOpacity
            className="flex-row items-center border-b border-gray-100 py-4"
            onPress={handleClearCache}>
            <Ionicons name="trash-outline" size={22} color="#ef4444" />
            <Text className="ml-3 text-red-500">Clear Cache</Text>
          </TouchableOpacity>

          <TouchableOpacity
            className="flex-row items-center py-4"
            onPress={() =>
              Alert.alert(
                'Delete Account',
                'Are you sure you want to delete your account? This action cannot be undone.'
              )
            }>
            <Ionicons name="alert-circle-outline" size={22} color="#ef4444" />
            <Text className="ml-3 text-red-500">Delete Account</Text>
          </TouchableOpacity>
        </View>
      </ScrollView>
    </View>
  );
};

export default SettingsScreen;
