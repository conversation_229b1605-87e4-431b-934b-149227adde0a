import React from 'react';
import { View, Text, ScrollView, TouchableOpacity } from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useNavigation } from '@react-navigation/native';
import { useTheme, Theme } from '../../hooks/use-theme';
import ThemeToggle from '../../components/ThemeToggle';
import { SCREENS } from '../../constants/screens';

/**
 * Theme Settings Screen
 * Allows users to customize the app's appearance
 */
const ThemeSettingsScreen: React.FC = () => {
  const navigation = useNavigation();
  const { theme, setTheme, isDark } = useTheme();

  // Theme options for display
  const themeOptions: { value: Theme; label: string; icon: string }[] = [
    { value: 'light', label: 'Light', icon: 'sunny-outline' },
    { value: 'dark', label: 'Dark', icon: 'moon-outline' },
    { value: 'system', label: 'System', icon: 'settings-outline' },
  ];

  return (
    <View className="flex-1 bg-background">
      {/* Header */}
      <View className="flex-row items-center p-4 border-b border-border">
        <TouchableOpacity
          onPress={() => navigation.goBack()}
          className="mr-4"
          activeOpacity={0.7}>
          <Ionicons name="arrow-back" size={24} color={isDark ? 'white' : 'black'} />
        </TouchableOpacity>
        <Text className="text-xl font-bold text-foreground">Appearance</Text>
      </View>

      <ScrollView className="flex-1 p-4">
        <View className="mb-6">
          <Text className="text-lg font-bold text-foreground mb-2">Theme</Text>
          <Text className="text-muted-foreground mb-4">
            Choose how the app looks to you. Select a light or dark theme, or use your system settings.
          </Text>

          <ThemeToggle className="mb-8" />

          {/* Theme Preview Cards */}
          <View className="flex-row justify-between mt-6">
            {themeOptions.map((option) => (
              <TouchableOpacity
                key={option.value}
                className={`w-[31%] rounded-lg overflow-hidden border ${
                  theme === option.value ? 'border-primary' : 'border-border'
                }`}
                onPress={() => setTheme(option.value)}
                activeOpacity={0.7}>
                <View
                  className={`h-24 ${
                    option.value === 'dark'
                      ? 'bg-gray-900'
                      : option.value === 'light'
                      ? 'bg-white'
                      : isDark
                      ? 'bg-gray-900'
                      : 'bg-white'
                  }`}>
                  <View className="h-6 w-full px-2 flex-row items-center">
                    <View
                      className={`h-3 w-3 rounded-full mr-1 ${
                        option.value === 'dark' || (option.value === 'system' && isDark)
                          ? 'bg-gray-700'
                          : 'bg-gray-300'
                      }`}
                    />
                    <View
                      className={`h-2 w-8 rounded-full ${
                        option.value === 'dark' || (option.value === 'system' && isDark)
                          ? 'bg-gray-700'
                          : 'bg-gray-300'
                      }`}
                    />
                  </View>
                  <View className="flex-1 p-2">
                    <View
                      className={`h-4 w-12 rounded-full mb-2 ${
                        option.value === 'dark' || (option.value === 'system' && isDark)
                          ? 'bg-gray-700'
                          : 'bg-gray-300'
                      }`}
                    />
                    <View
                      className={`h-3 w-16 rounded-full ${
                        option.value === 'dark' || (option.value === 'system' && isDark)
                          ? 'bg-gray-700'
                          : 'bg-gray-300'
                      }`}
                    />
                  </View>
                </View>
                <View
                  className={`p-2 ${
                    option.value === 'dark'
                      ? 'bg-gray-800'
                      : option.value === 'light'
                      ? 'bg-gray-100'
                      : isDark
                      ? 'bg-gray-800'
                      : 'bg-gray-100'
                  }`}>
                  <Text
                    className={`text-center font-medium ${
                      option.value === 'dark' || (option.value === 'system' && isDark)
                        ? 'text-white'
                        : 'text-gray-900'
                    }`}>
                    {option.label}
                  </Text>
                </View>
              </TouchableOpacity>
            ))}
          </View>
        </View>
      </ScrollView>
    </View>
  );
};

export default ThemeSettingsScreen;
