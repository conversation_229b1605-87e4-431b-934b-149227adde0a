import React from 'react';
import {
  View,
  Text,
  ScrollView,
  TouchableOpacity,
  Image,
  ActivityIndicator,
  Alert,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { SCREENS } from '@/constants/screens';
import { useAuth } from '@/providers/AuthRTKProvider';
import {
  useGetProfileQuery,
  useGetUserStatsQuery,
  useGetUserSkillsQuery,
} from '@/store/api/userApi';
import { useGetTrustScoreQuery } from '@/store/api/trustScoreApi';
import { useGetMyBadgesQuery } from '@/store/api/gamificationApi';
import { useTypedNavigation } from '@/types/navigation';

// Define navigation helper function to handle screens that might not be in SCREENS
const navigateTo = (navigation: ReturnType<typeof useTypedNavigation>, screenName: string) => {
  // Use any to bypass TypeScript checking for screen names
  // This is a temporary solution until all screens are properly defined in the navigation types
  (navigation.navigate as any)(screenName);
};

// Define interface for profile data
interface ProfileData {
  name: string;
  email: string;
  phone: string;
  location: string;
  profilePicture?: string;
  trustScore: number;
  completedJobs: number;
  totalEarnings: string;
  skills?: string[];
  documents?: {
    [key: string]: {
      verified: boolean;
      name: string;
    };
  };
  badges?: Array<{
    id: string;
    name: string;
    icon: string; // Changed to string to accommodate any icon from the API
  }>;
}

// Mock data for fallback
const mockUserData: ProfileData = {
  name: 'John Doe',
  email: '<EMAIL>',
  phone: '+91 **********',
  location: 'Mumbai, India',
  profilePicture: 'https://randomuser.me/api/portraits/men/32.jpg',
  trustScore: 85,
  completedJobs: 12,
  totalEarnings: '₹24,500',
  skills: ['Inventory Management', 'Stock Auditing', 'Data Entry', 'Team Leadership'],
  documents: {
    idCard: { verified: true, name: 'ID Card' },
    taxId: { verified: true, name: 'Tax ID' },
    bankAccount: { verified: false, name: 'Bank Account' },
  },
  badges: [
    { id: '1', name: 'Quick Learner', icon: 'school-outline' },
    { id: '2', name: 'Punctual', icon: 'time-outline' },
    { id: '3', name: 'Team Player', icon: 'people-outline' },
  ],
};

const ProfileScreen = () => {
  const navigation = useTypedNavigation();
  const { signOut } = useAuth();

  // Use RTK Query to fetch all required data
  const {
    data: profileResponse,
    isLoading: isLoadingProfile,
    error: profileError,
  } = useGetProfileQuery();
  const { data: userStats, isLoading: isLoadingStats } = useGetUserStatsQuery();
  const { data: userSkills, isLoading: isLoadingSkills } = useGetUserSkillsQuery();
  const { data: trustScoreData, isLoading: isLoadingTrustScore } = useGetTrustScoreQuery();
  const { data: badgesData, isLoading: isLoadingBadges } = useGetMyBadgesQuery();

  // Check if any data is loading
  const isLoading =
    isLoadingProfile || isLoadingStats || isLoadingSkills || isLoadingTrustScore || isLoadingBadges;

  // Check for errors
  const error = profileError;

  // Map API responses to ProfileData or use mock data as fallback
  const userData: ProfileData = profileResponse?.data
    ? {
        name: profileResponse.data.fullName,
        email: profileResponse.data.email || '',
        phone: profileResponse.data.phone || '',
        location: `${profileResponse.data.city || ''}, ${profileResponse.data.country || ''}`,
        profilePicture: profileResponse.data.profilePic,
        trustScore: trustScoreData?.score || profileResponse.data.trustScore || 0,
        // Use actual data from API responses
        completedJobs: userStats?.completedJobs || 0,
        totalEarnings: userStats?.totalEarnings ? `₹${userStats.totalEarnings}` : '₹0',
        skills: userSkills || [],
        documents: {
          idCard: {
            verified: profileResponse.data.isKycVerified || false,
            name: 'ID Card',
          },
          taxId: {
            verified: profileResponse.data.isKycVerified || false,
            name: 'Tax ID',
          },
          bankAccount: {
            verified: profileResponse.data.isKycVerified || false,
            name: 'Bank Account',
          },
        },
        badges:
          badgesData?.badges?.map((badge) => ({
            id: badge.id,
            name: badge.name,
            icon: badge.icon || 'star-outline',
          })) || [],
      }
    : mockUserData;

  const handleSignOut = async () => {
    try {
      await signOut();
      // Navigation will be handled by the AuthContext
    } catch (error) {
      console.error('Error signing out:', error);
      Alert.alert('Error', 'Failed to sign out. Please try again.');
    }
  };

  if (isLoading) {
    return (
      <View className="flex-1 items-center justify-center bg-white">
        <ActivityIndicator size="large" color="#4f46e5" />
      </View>
    );
  }

  // Show error state if profile fetch failed
  if (error && !profileResponse) {
    return (
      <View className="flex-1 items-center justify-center bg-white p-4">
        <Ionicons name="alert-circle-outline" size={48} color="#ef4444" />
        <Text className="mt-4 text-center text-lg font-medium text-gray-900">
          Failed to load profile
        </Text>
        <Text className="mt-2 text-center text-gray-500">
          There was a problem loading your profile. Using sample data instead.
        </Text>
        <TouchableOpacity
          className="mt-6 rounded-lg bg-indigo-600 px-6 py-3"
          onPress={() => navigation.navigate(SCREENS.PROFILE)}>
          <Text className="font-medium text-white">Try Again</Text>
        </TouchableOpacity>
      </View>
    );
  }

  return (
    <ScrollView className="flex-1 bg-gray-50">
      <View className="items-center bg-indigo-600 px-4 pb-6 pt-12">
        <Image
          source={{
            uri: userData.profilePicture || 'https://randomuser.me/api/portraits/men/32.jpg',
          }}
          className="h-24 w-24 rounded-full border-4 border-white"
        />
        <Text className="mt-2 text-xl font-bold text-white">{userData.name}</Text>
        <Text className="text-indigo-200">{userData.location}</Text>

        <View className="mt-4 w-full flex-row justify-around">
          <View className="items-center">
            <Text className="text-xl font-bold text-white">{userData.completedJobs || 0}</Text>
            <Text className="text-sm text-indigo-200">Jobs</Text>
          </View>
          <TouchableOpacity
            className="items-center"
            onPress={() => navigateTo(navigation, 'TrustScore')}>
            <Text className="text-xl font-bold text-white">{userData.trustScore || 0}%</Text>
            <View className="flex-row items-center">
              <Text className="text-sm text-indigo-200">Trust Score</Text>
              <Ionicons name="chevron-forward" size={12} color="#e0e7ff" />
            </View>
          </TouchableOpacity>
          <TouchableOpacity
            className="items-center"
            onPress={() => navigateTo(navigation, 'Payments')}>
            <Text className="text-xl font-bold text-white">{userData.totalEarnings || '₹0'}</Text>
            <View className="flex-row items-center">
              <Text className="text-sm text-indigo-200">Earnings</Text>
              <Ionicons name="chevron-forward" size={12} color="#e0e7ff" />
            </View>
          </TouchableOpacity>
        </View>
      </View>

      <View className="mx-4 mt-4 rounded-lg bg-white p-4 shadow-sm">
        <Text className="mb-3 text-lg font-bold text-gray-900">Personal Information</Text>

        <View className="mb-3 flex-row items-center">
          <Ionicons name="mail-outline" size={20} color="#6b7280" className="mr-2" />
          <View className="ml-2">
            <Text className="text-sm text-gray-500">Email</Text>
            <Text className="text-gray-800">{userData.email}</Text>
          </View>
        </View>

        <View className="mb-3 flex-row items-center">
          <Ionicons name="call-outline" size={20} color="#6b7280" className="mr-2" />
          <View className="ml-2">
            <Text className="text-sm text-gray-500">Phone</Text>
            <Text className="text-gray-800">{userData.phone}</Text>
          </View>
        </View>

        <View className="flex-row items-center">
          <Ionicons name="location-outline" size={20} color="#6b7280" className="mr-2" />
          <View className="ml-2">
            <Text className="text-sm text-gray-500">Location</Text>
            <Text className="text-gray-800">{userData.location}</Text>
          </View>
        </View>
      </View>

      <View className="mx-4 mt-4 rounded-lg bg-white p-4 shadow-sm">
        <Text className="mb-3 text-lg font-bold text-gray-900">Documents</Text>

        {userData.documents &&
          Object.entries(userData.documents).map(([key, doc]) => (
            <View key={key} className="mb-3 flex-row items-center justify-between">
              <View className="flex-row items-center">
                <Ionicons name="document-text-outline" size={20} color="#6b7280" />
                <Text className="ml-2 text-gray-800">{doc.name}</Text>
              </View>
              {doc.verified ? (
                <View className="flex-row items-center rounded bg-green-100 px-2 py-1">
                  <Ionicons name="checkmark-circle" size={16} color="#10b981" />
                  <Text className="ml-1 text-xs text-green-700">Verified</Text>
                </View>
              ) : (
                <View className="flex-row items-center rounded bg-yellow-100 px-2 py-1">
                  <Ionicons name="alert-circle" size={16} color="#f59e0b" />
                  <Text className="ml-1 text-xs text-yellow-700">Pending</Text>
                </View>
              )}
            </View>
          ))}

        <TouchableOpacity
          className="mt-2 flex-row items-center"
          onPress={() => navigateTo(navigation, 'Documents')}>
          <Text className="font-medium text-indigo-600">Manage Documents</Text>
          <Ionicons name="chevron-forward" size={16} color="#4f46e5" />
        </TouchableOpacity>
      </View>

      <View className="mx-4 mt-4 rounded-lg bg-white p-4 shadow-sm">
        <Text className="mb-3 text-lg font-bold text-gray-900">Skills</Text>
        <View className="flex-row flex-wrap">
          {userData.skills && userData.skills.length > 0 ? (
            userData.skills.map((skill, index) => (
              <View key={index} className="mb-2 mr-2 rounded-full bg-indigo-100 px-3 py-1">
                <Text className="text-sm text-indigo-800">{skill}</Text>
              </View>
            ))
          ) : (
            <Text className="text-gray-500">No skills added yet</Text>
          )}
        </View>
        <TouchableOpacity
          className="mt-3 flex-row items-center"
          onPress={() => navigateTo(navigation, 'EditSkills')}>
          <Text className="font-medium text-indigo-600">Manage Skills</Text>
          <Ionicons name="chevron-forward" size={16} color="#4f46e5" />
        </TouchableOpacity>
      </View>

      <View className="mx-4 mt-4 rounded-lg bg-white p-4 shadow-sm">
        <Text className="mb-3 text-lg font-bold text-gray-900">Badges</Text>
        {userData.badges && userData.badges.length > 0 ? (
          <View className="flex-row justify-around">
            {userData.badges.map((badge) => (
              <View key={badge.id} className="items-center">
                <View className="mb-1 h-12 w-12 items-center justify-center rounded-full bg-indigo-100">
                  <Ionicons name={badge.icon as any} size={24} color="#4f46e5" />
                </View>
                <Text className="text-xs text-gray-700">{badge.name}</Text>
              </View>
            ))}
          </View>
        ) : (
          <Text className="mb-3 text-gray-500">No badges earned yet</Text>
        )}
        <TouchableOpacity
          className="mt-2 flex-row items-center"
          onPress={() => navigateTo(navigation, 'Badges')}>
          <Text className="font-medium text-indigo-600">View All Badges</Text>
          <Ionicons name="chevron-forward" size={16} color="#4f46e5" />
        </TouchableOpacity>
      </View>

      <View className="mb-8 mt-4 p-4">
        <TouchableOpacity
          className="mb-3 rounded-lg bg-indigo-600 py-3"
          onPress={() => navigateTo(navigation, 'EditProfile')}>
          <Text className="text-center font-medium text-white">Edit Profile</Text>
        </TouchableOpacity>

        <TouchableOpacity
          className="mb-3 rounded-lg bg-indigo-100 py-3"
          onPress={() => navigateTo(navigation, 'Disputes')}>
          <Text className="text-center font-medium text-indigo-700">Disputes</Text>
        </TouchableOpacity>

        <TouchableOpacity
          className="mb-3 rounded-lg bg-indigo-100 py-3"
          onPress={() => navigateTo(navigation, 'Ratings')}>
          <Text className="text-center font-medium text-indigo-700">Ratings & Reviews</Text>
        </TouchableOpacity>

        <TouchableOpacity
          className="mb-3 rounded-lg bg-indigo-100 py-3"
          onPress={() => navigateTo(navigation, 'Badges')}>
          <Text className="text-center font-medium text-indigo-700">Badges & Achievements</Text>
        </TouchableOpacity>

        <TouchableOpacity
          className="mb-3 rounded-lg bg-indigo-100 py-3"
          onPress={() => navigateTo(navigation, 'Settings')}>
          <Text className="text-center font-medium text-indigo-700">Settings</Text>
        </TouchableOpacity>

        <TouchableOpacity className="rounded-lg bg-red-100 py-3" onPress={handleSignOut}>
          <Text className="text-center font-medium text-red-700">Sign Out</Text>
        </TouchableOpacity>
      </View>
    </ScrollView>
  );
};

export default ProfileScreen;
