import React, { useState, useEffect } from "react";
import {
  View,
  Text,
  TextInput,
  TouchableOpacity,
  ScrollView,
  Image,
  ActivityIndicator,
  Alert,
} from "react-native";
import { useNavigation } from "@react-navigation/native";
import { Ionicons } from "@expo/vector-icons";
import * as ImagePicker from "expo-image-picker";
import { userAPI } from "../../services/api";

// Mock data for fallback
const mockUserData = {
  name: "<PERSON>",
  email: "<EMAIL>",
  phone: "+91 9876543210",
  location: "Mumbai, India",
  profilePicture: "https://randomuser.me/api/portraits/men/32.jpg",
  bio: "Experienced inventory specialist with 3+ years in retail and warehouse environments.",
  skills: ["Inventory Management", "Stock Auditing", "Data Entry", "Team Leadership"],
};

const EditProfileScreen = () => {
  const navigation = useNavigation();
  const [userData, setUserData] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [name, setName] = useState("");
  const [email, setEmail] = useState("");
  const [phone, setPhone] = useState("");
  const [location, setLocation] = useState("");
  const [bio, setBio] = useState("");
  const [profileImage, setProfileImage] = useState(null);
  const [isSaving, setIsSaving] = useState(false);

  useEffect(() => {
    fetchUserProfile();
  }, []);

  const fetchUserProfile = async () => {
    setLoading(true);
    setError(null);
    
    try {
      const response = await userAPI.getProfile();
      const data = response.data;
      setUserData(data);
      setName(data.name || "");
      setEmail(data.email || "");
      setPhone(data.phone || "");
      setLocation(data.location || "");
      setBio(data.bio || "");
      setProfileImage(data.profilePicture || null);
    } catch (err) {
      console.error("Error fetching user profile:", err);
      setError("Failed to load profile");
      // Fallback to mock data
      setUserData(mockUserData);
      setName(mockUserData.name);
      setEmail(mockUserData.email);
      setPhone(mockUserData.phone);
      setLocation(mockUserData.location);
      setBio(mockUserData.bio);
      setProfileImage(mockUserData.profilePicture);
      
      if (err.response?.status !== 401) { // Don't show alert for auth errors
        Alert.alert("Error", "Failed to load profile. Showing sample data.");
      }
    } finally {
      setLoading(false);
    }
  };

  const pickImage = async () => {
    const { status } = await ImagePicker.requestMediaLibraryPermissionsAsync();
    
    if (status !== "granted") {
      Alert.alert("Permission Required", "Please allow access to your photo library to change profile picture.");
      return;
    }

    const result = await ImagePicker.launchImageLibraryAsync({
      mediaTypes: ImagePicker.MediaTypeOptions.Images,
      allowsEditing: true,
      aspect: [1, 1],
      quality: 0.8,
    });

    if (!result.canceled) {
      setProfileImage(result.assets[0].uri);
    }
  };

  const handleSave = async () => {
    if (!name.trim()) {
      Alert.alert("Error", "Name is required");
      return;
    }

    setIsSaving(true);
    
    try {
      const formData = new FormData();
      formData.append("name", name);
      formData.append("location", location);
      formData.append("bio", bio);
      
      // Only append image if it's changed and is a local file (not a URL)
      if (profileImage && profileImage.startsWith('file://')) {
        const filename = profileImage.split('/').pop();
        const match = /\.(\w+)$/.exec(filename);
        const type = match ? `image/${match[1]}` : 'image/jpeg';
        
        formData.append("profilePicture", {
          uri: profileImage,
          name: filename,
          type,
        });
      }
      
      await userAPI.updateProfile(formData);
      Alert.alert("Success", "Profile updated successfully");
      navigation.goBack();
    } catch (err) {
      console.error("Error updating profile:", err);
      Alert.alert("Error", "Failed to update profile. Please try again.");
    } finally {
      setIsSaving(false);
    }
  };

  if (loading) {
    return (
      <View className="flex-1 justify-center items-center bg-white">
        <ActivityIndicator size="large" color="#4f46e5" />
      </View>
    );
  }

  return (
    <View className="flex-1 bg-white">
      <View className="bg-white pt-12 pb-4 px-4 border-b border-gray-200 flex-row items-center">
        <TouchableOpacity onPress={() => navigation.goBack()} className="mr-3">
          <Ionicons name="arrow-back" size={24} color="#4b5563" />
        </TouchableOpacity>
        <Text className="text-xl font-semibold text-gray-900">Edit Profile</Text>
      </View>

      <ScrollView className="flex-1 p-4">
        <View className="items-center mb-6">
          <TouchableOpacity onPress={pickImage}>
            <View className="relative">
              <Image
                source={{ uri: profileImage || "https://randomuser.me/api/portraits/men/32.jpg" }}
                className="w-24 h-24 rounded-full"
              />
              <View className="absolute bottom-0 right-0 bg-indigo-600 rounded-full p-2">
                <Ionicons name="camera" size={16} color="white" />
              </View>
            </View>
          </TouchableOpacity>
          <Text className="text-indigo-600 mt-2">Change Profile Picture</Text>
        </View>

        <View className="mb-4">
          <Text className="text-gray-700 mb-2 font-medium">Name</Text>
          <TextInput
            className="bg-gray-100 p-3 rounded-lg text-gray-900"
            value={name}
            onChangeText={setName}
            placeholder="Your full name"
          />
        </View>

        <View className="mb-4">
          <Text className="text-gray-700 mb-2 font-medium">Email</Text>
          <TextInput
            className="bg-gray-100 p-3 rounded-lg text-gray-900"
            value={email}
            editable={false} // Email is not editable
            placeholder="Your email address"
          />
          <Text className="text-gray-500 text-xs mt-1">Email cannot be changed</Text>
        </View>

        <View className="mb-4">
          <Text className="text-gray-700 mb-2 font-medium">Phone</Text>
          <TextInput
            className="bg-gray-100 p-3 rounded-lg text-gray-900"
            value={phone}
            editable={false} // Phone is not editable
            placeholder="Your phone number"
          />
          <Text className="text-gray-500 text-xs mt-1">Phone number cannot be changed</Text>
        </View>

        <View className="mb-4">
          <Text className="text-gray-700 mb-2 font-medium">Location</Text>
          <TextInput
            className="bg-gray-100 p-3 rounded-lg text-gray-900"
            value={location}
            onChangeText={setLocation}
            placeholder="Your location (City, State)"
          />
        </View>

        <View className="mb-6">
          <Text className="text-gray-700 mb-2 font-medium">Bio</Text>
          <TextInput
            className="bg-gray-100 p-3 rounded-lg text-gray-900"
            value={bio}
            onChangeText={setBio}
            placeholder="Tell us about yourself"
            multiline
            numberOfLines={4}
            textAlignVertical="top"
          />
        </View>

        <View className="flex-row mb-8">
          <TouchableOpacity
            className="bg-gray-200 flex-1 py-3 rounded-lg mr-2"
            onPress={() => navigation.navigate("EditSkills")}
          >
            <Text className="text-gray-700 text-center">Edit Skills</Text>
          </TouchableOpacity>
          <TouchableOpacity
            className="bg-gray-200 flex-1 py-3 rounded-lg"
            onPress={() => navigation.navigate("EditEducation")}
          >
            <Text className="text-gray-700 text-center">Edit Education</Text>
          </TouchableOpacity>
        </View>

        <TouchableOpacity
          className="bg-indigo-600 py-3 rounded-lg mt-4"
          onPress={handleSave}
          disabled={isSaving}
        >
          {isSaving ? (
            <ActivityIndicator color="white" size="small" />
          ) : (
            <Text className="text-white font-medium text-center">Save Changes</Text>
          )}
        </TouchableOpacity>
      </ScrollView>
    </View>
  );
};

export default EditProfileScreen;
