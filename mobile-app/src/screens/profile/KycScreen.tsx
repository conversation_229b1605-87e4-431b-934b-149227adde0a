'use client';

import { useState } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  ScrollView,
  Image,
  ActivityIndicator,
  Alert,
  TextInput,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import * as ImagePicker from 'expo-image-picker';
import * as DocumentPicker from 'expo-document-picker';
import { MotiView } from '@/utils/animationComponents';
import { useAuth } from '../../contexts/AuthContext';
import { api } from '../../services/api';
import { sharedTransition } from '../../utils/animations';
import { useTypedNavigation } from '@/types/navigation';

const KycScreen = () => {
  const navigation = useTypedNavigation();
  const { user, updateUser } = useAuth();
  const [loading, setLoading] = useState(false);
  const [aadharNumber, setAadharNumber] = useState(user?.aadharNumber || '');
  const [panNumber, setPanNumber] = useState(user?.panNumber || '');
  const [aadharDoc, setAadharDoc] = useState(null);
  const [panDoc, setPanDoc] = useState(null);
  const [aadharDocPreview, setAadharDocPreview] = useState(user?.aadharDoc || null);
  const [panDocPreview, setPanDocPreview] = useState(user?.panDoc || null);

  const pickAadharDocument = async () => {
    try {
      const result = await DocumentPicker.getDocumentAsync({
        type: ['image/*', 'application/pdf'],
        copyToCacheDirectory: true,
      });

      if (!result.canceled) {
        setAadharDoc(result.assets[0]);
        if (result.assets[0].mimeType.startsWith('image/')) {
          setAadharDocPreview(result.assets[0].uri);
        } else {
          setAadharDocPreview(null); // Can't preview PDF
        }
      }
    } catch (error) {
      Alert.alert('Error', 'Failed to pick document');
    }
  };

  const pickPanDocument = async () => {
    try {
      const result = await DocumentPicker.getDocumentAsync({
        type: ['image/*', 'application/pdf'],
        copyToCacheDirectory: true,
      });

      if (!result.canceled) {
        setPanDoc(result.assets[0]);
        if (result.assets[0].mimeType.startsWith('image/')) {
          setPanDocPreview(result.assets[0].uri);
        } else {
          setPanDocPreview(null); // Can't preview PDF
        }
      }
    } catch (error) {
      Alert.alert('Error', 'Failed to pick document');
    }
  };

  const takeAadharPhoto = async () => {
    const { status } = await ImagePicker.requestCameraPermissionsAsync();

    if (status !== 'granted') {
      Alert.alert('Permission Denied', 'Camera permission is required to take photos');
      return;
    }

    try {
      const result = await ImagePicker.launchCameraAsync({
        mediaTypes: ImagePicker.MediaTypeOptions.Images,
        allowsEditing: true,
        aspect: [4, 3],
        quality: 0.8,
      });

      if (!result.canceled) {
        setAadharDoc({
          uri: result.assets[0].uri,
          name: `aadhar_${Date.now()}.jpg`,
          type: 'image/jpeg',
        });
        setAadharDocPreview(result.assets[0].uri);
      }
    } catch (error) {
      Alert.alert('Error', 'Failed to take photo');
    }
  };

  const takePanPhoto = async () => {
    const { status } = await ImagePicker.requestCameraPermissionsAsync();

    if (status !== 'granted') {
      Alert.alert('Permission Denied', 'Camera permission is required to take photos');
      return;
    }

    try {
      const result = await ImagePicker.launchCameraAsync({
        mediaTypes: ImagePicker.MediaTypeOptions.Images,
        allowsEditing: true,
        aspect: [4, 3],
        quality: 0.8,
      });

      if (!result.canceled) {
        setPanDoc({
          uri: result.assets[0].uri,
          name: `pan_${Date.now()}.jpg`,
          type: 'image/jpeg',
        });
        setPanDocPreview(result.assets[0].uri);
      }
    } catch (error) {
      Alert.alert('Error', 'Failed to take photo');
    }
  };

  const handleSubmit = async () => {
    if (!aadharNumber || !panNumber) {
      Alert.alert('Error', 'Please enter both Aadhar and PAN numbers');
      return;
    }

    if (!aadharDoc && !aadharDocPreview) {
      Alert.alert('Error', 'Please upload your Aadhar document');
      return;
    }

    if (!panDoc && !panDocPreview) {
      Alert.alert('Error', 'Please upload your PAN document');
      return;
    }

    setLoading(true);

    try {
      // First update the KYC details
      await api.patch('/users/kyc', {
        aadharNumber,
        panNumber,
      });

      // Then upload documents if they exist
      if (aadharDoc) {
        const formData = new FormData();
        formData.append('file', {
          uri: aadharDoc.uri,
          name: aadharDoc.name,
          type: aadharDoc.mimeType || 'image/jpeg',
        });
        formData.append('type', 'aadhar');

        await api.post('/users/upload-document', formData, {
          headers: {
            'Content-Type': 'multipart/form-data',
          },
        });
      }

      if (panDoc) {
        const formData = new FormData();
        formData.append('file', {
          uri: panDoc.uri,
          name: panDoc.name,
          type: panDoc.mimeType || 'image/jpeg',
        });
        formData.append('type', 'pan');

        await api.post('/users/upload-document', formData, {
          headers: {
            'Content-Type': 'multipart/form-data',
          },
        });
      }

      // Update user in context
      updateUser({
        aadharNumber,
        panNumber,
        aadharDoc: aadharDocPreview,
        panDoc: panDocPreview,
      });

      Alert.alert(
        'Success',
        'Your KYC documents have been submitted for verification. We will notify you once the verification is complete.',
        [
          {
            text: 'OK',
            onPress: () => navigation.goBack(),
          },
        ]
      );
    } catch (error) {
      Alert.alert('Error', error.response?.data?.message || 'Failed to submit KYC documents');
    } finally {
      setLoading(false);
    }
  };

  return (
    <ScrollView className="flex-1 bg-white">
      <View className="p-6">
        <MotiView
          from={{ opacity: 0, translateY: 20 }}
          animate={{ opacity: 1, translateY: 0 }}
          transition={sharedTransition}
          className="space-y-6">
          <View>
            <Text className="text-2xl font-bold">KYC Verification</Text>
            <Text className="mt-2 text-gray-500">
              Please provide your identity documents for verification. This is required to work on
              our platform.
            </Text>
          </View>

          <View className="space-y-4">
            <Text className="text-lg font-semibold">Aadhar Card</Text>

            <View className="space-y-2">
              <Text className="text-gray-700">Aadhar Number</Text>
              <TextInput
                className="rounded-lg bg-gray-100 p-4"
                placeholder="Enter your 12-digit Aadhar number"
                value={aadharNumber}
                onChangeText={setAadharNumber}
                keyboardType="number-pad"
                maxLength={12}
              />
            </View>

            <View className="space-y-2">
              <Text className="text-gray-700">Upload Aadhar Card</Text>

              {aadharDocPreview ? (
                <View className="relative">
                  <Image
                    source={{ uri: aadharDocPreview }}
                    className="h-48 w-full rounded-lg"
                    resizeMode="cover"
                  />
                  <TouchableOpacity
                    className="absolute right-2 top-2 rounded-full bg-red-500 p-1"
                    onPress={() => {
                      setAadharDoc(null);
                      setAadharDocPreview(null);
                    }}>
                    <Ionicons name="close" size={16} color="white" />
                  </TouchableOpacity>
                </View>
              ) : (
                <View className="flex-row space-x-2">
                  <TouchableOpacity
                    className="flex-1 flex-row items-center justify-center space-x-2 rounded-lg bg-gray-100 p-4"
                    onPress={pickAadharDocument}>
                    <Ionicons name="document-outline" size={20} color="#4f46e5" />
                    <Text className="font-medium text-indigo-600">Choose File</Text>
                  </TouchableOpacity>

                  <TouchableOpacity
                    className="items-center justify-center rounded-lg bg-gray-100 p-4"
                    onPress={takeAadharPhoto}>
                    <Ionicons name="camera-outline" size={20} color="#4f46e5" />
                  </TouchableOpacity>
                </View>
              )}
            </View>
          </View>

          <View className="space-y-4">
            <Text className="text-lg font-semibold">PAN Card</Text>

            <View className="space-y-2">
              <Text className="text-gray-700">PAN Number</Text>
              <TextInput
                className="rounded-lg bg-gray-100 p-4"
                placeholder="Enter your 10-digit PAN number"
                value={panNumber}
                onChangeText={setPanNumber}
                autoCapitalize="characters"
                maxLength={10}
              />
            </View>

            <View className="space-y-2">
              <Text className="text-gray-700">Upload PAN Card</Text>

              {panDocPreview ? (
                <View className="relative">
                  <Image
                    source={{ uri: panDocPreview }}
                    className="h-48 w-full rounded-lg"
                    resizeMode="cover"
                  />
                  <TouchableOpacity
                    className="absolute right-2 top-2 rounded-full bg-red-500 p-1"
                    onPress={() => {
                      setPanDoc(null);
                      setPanDocPreview(null);
                    }}>
                    <Ionicons name="close" size={16} color="white" />
                  </TouchableOpacity>
                </View>
              ) : (
                <View className="flex-row space-x-2">
                  <TouchableOpacity
                    className="flex-1 flex-row items-center justify-center space-x-2 rounded-lg bg-gray-100 p-4"
                    onPress={pickPanDocument}>
                    <Ionicons name="document-outline" size={20} color="#4f46e5" />
                    <Text className="font-medium text-indigo-600">Choose File</Text>
                  </TouchableOpacity>

                  <TouchableOpacity
                    className="items-center justify-center rounded-lg bg-gray-100 p-4"
                    onPress={takePanPhoto}>
                    <Ionicons name="camera-outline" size={20} color="#4f46e5" />
                  </TouchableOpacity>
                </View>
              )}
            </View>
          </View>

          <View className="space-y-2">
            <TouchableOpacity
              onPress={handleSubmit}
              disabled={loading}
              className={`items-center rounded-lg bg-indigo-600 p-4 ${loading ? 'opacity-70' : ''}`}>
              {loading ? (
                <ActivityIndicator color="white" />
              ) : (
                <Text className="text-lg font-bold text-white">Submit Documents</Text>
              )}
            </TouchableOpacity>

            <TouchableOpacity
              onPress={() => navigation.goBack()}
              className="items-center rounded-lg p-4">
              <Text className="font-medium text-indigo-600">Cancel</Text>
            </TouchableOpacity>
          </View>
        </MotiView>
      </View>
    </ScrollView>
  );
};

export default KycScreen;
