import { useState } from 'react';
import {
  View,
  Text,
  ScrollView,
  TouchableOpacity,
  ActivityIndicator,
  RefreshControl,
} from 'react-native';
import { useNavigation } from '@react-navigation/native';
import { MotiView } from 'moti';
import { useAuth } from '../../contexts/AuthContext';
import {
  useGetTrustScoreQuery,
  useGetTrustScoreLogsQuery,
  useGetTrustScoreBreakdownQuery,
  useGetTrustScoreImprovementSuggestionsQuery,
} from '../../store/api/trustScoreApi';
import { cn } from '../../utils/styleUtils';

// Define types for the trust score data
interface TrustScoreLog {
  id: string;
  userId: string;
  action: string;
  points: number;
  reason: string;
  createdAt: string;
}

interface TrustScoreSuggestion {
  id: string;
  title: string;
  description: string;
  points: number;
  difficulty: 'easy' | 'medium' | 'hard';
}

interface CategoryScore {
  name: string;
  score: number;
  maxScore: number;
  percentage: number;
}

const TrustScoreScreen = () => {
  const navigation = useNavigation();
  const { user } = useAuth();
  const [refreshing, setRefreshing] = useState(false);

  // Use RTK Query hooks to fetch data
  const {
    data: trustScoreData,
    isLoading: isLoadingScore,
    refetch: refetchScore,
  } = useGetTrustScoreQuery();

  const {
    data: trustScoreLogsData,
    isLoading: isLoadingLogs,
    refetch: refetchLogs,
  } = useGetTrustScoreLogsQuery({});

  const {
    data: suggestionsData,
    isLoading: isLoadingSuggestions,
    refetch: refetchSuggestions,
  } = useGetTrustScoreImprovementSuggestionsQuery();

  const {
    data: breakdownData,
    isLoading: isLoadingBreakdown,
    refetch: refetchBreakdown,
  } = useGetTrustScoreBreakdownQuery();

  // Check if any data is still loading
  const isLoading = isLoadingScore || isLoadingLogs || isLoadingSuggestions || isLoadingBreakdown;

  // Extract data from query results
  const trustScoreLogs = trustScoreLogsData?.logs || [];
  const suggestions = suggestionsData?.suggestions || [];
  const breakdown = breakdownData;

  const handleRefresh = async () => {
    setRefreshing(true);
    await Promise.all([refetchScore(), refetchLogs(), refetchSuggestions(), refetchBreakdown()]);
    setRefreshing(false);
  };

  const formatDate = (dateString: string): string => {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
    });
  };

  if (isLoading && !refreshing) {
    return (
      <View className="flex-1 items-center justify-center bg-white">
        <ActivityIndicator size="large" color="#4f46e5" />
      </View>
    );
  }

  const userTrustScore = user?.trustScore || 0;

  return (
    <View className="flex-1 bg-gray-50">
      <View className="bg-indigo-600 px-4 pb-4 pt-12">
        <TouchableOpacity
          className="mb-2 flex-row items-center"
          onPress={() => navigation.goBack()}>
          <Text className="text-base text-white">← Back</Text>
        </TouchableOpacity>
        <Text className="text-2xl font-bold text-white">Trust Score</Text>
        <Text className="text-sm text-indigo-200">
          Your trust score determines your eligibility for jobs
        </Text>
      </View>

      <ScrollView
        className="flex-1"
        refreshControl={<RefreshControl refreshing={refreshing} onRefresh={handleRefresh} />}>
        <MotiView
          from={{ opacity: 0, translateY: 20 }}
          animate={{ opacity: 1, translateY: 0 }}
          className="p-4">
          {/* Current Score */}
          <View className="mb-4 items-center rounded-lg bg-white p-6 shadow-sm">
            <View
              className={cn(
                'mb-2 h-24 w-24 items-center justify-center rounded-full',
                userTrustScore >= 80
                  ? 'bg-green-500'
                  : userTrustScore >= 60
                    ? 'bg-blue-500'
                    : userTrustScore >= 40
                      ? 'bg-yellow-500'
                      : userTrustScore >= 20
                        ? 'bg-orange-500'
                        : 'bg-red-500'
              )}>
              <Text className="text-2xl font-bold text-white">{userTrustScore}</Text>
            </View>
            <Text className="mt-2 text-lg font-semibold">
              {userTrustScore >= 80
                ? 'Excellent'
                : userTrustScore >= 60
                  ? 'Good'
                  : userTrustScore >= 40
                    ? 'Average'
                    : userTrustScore >= 20
                      ? 'Below Average'
                      : 'Poor'}
            </Text>
            <Text className="mt-1 text-center text-gray-500">
              {userTrustScore >= 80
                ? 'You are eligible for all jobs including premium ones'
                : userTrustScore >= 60
                  ? 'You are eligible for most jobs'
                  : userTrustScore >= 40
                    ? 'You are eligible for standard jobs'
                    : userTrustScore >= 20
                      ? 'You are eligible for basic jobs only'
                      : 'You need to improve your score to be eligible for jobs'}
            </Text>
          </View>

          {/* Score Breakdown */}
          {breakdown && (
            <View className="mb-4 rounded-lg bg-white p-4 shadow-sm">
              <Text className="mb-3 text-lg font-semibold">Score Breakdown</Text>
              {Object.entries(breakdown.categories).map(([categoryName, categoryData]) => {
                const category = categoryData as CategoryScore;
                return (
                  <View key={categoryName} className="mb-3">
                    <View className="mb-1 flex-row justify-between">
                      <Text className="text-gray-600">{categoryName}</Text>
                      <Text className="font-medium">{category.score}/100</Text>
                    </View>
                    <View className="h-2 overflow-hidden rounded-full bg-gray-200">
                      <View
                        className={cn(
                          'h-full',
                          category.score >= 80
                            ? 'bg-green-500'
                            : category.score >= 60
                              ? 'bg-blue-500'
                              : category.score >= 40
                                ? 'bg-yellow-500'
                                : category.score >= 20
                                  ? 'bg-orange-500'
                                  : 'bg-red-500'
                        )}
                        style={{ width: `${category.score}%` }}
                      />
                    </View>
                  </View>
                );
              })}
            </View>
          )}

          {/* Improvement Suggestions */}
          {suggestions && suggestions.length > 0 && (
            <View className="mb-4 rounded-lg bg-white p-4 shadow-sm">
              <Text className="mb-3 text-lg font-semibold">Improvement Suggestions</Text>
              {suggestions.map((suggestion: TrustScoreSuggestion, index: number) => (
                <View key={index} className="mb-2 flex-row items-start">
                  <View className="mr-2 mt-0.5 h-6 w-6 items-center justify-center rounded-full bg-indigo-100">
                    <Text className="font-bold text-indigo-600">↑</Text>
                  </View>
                  <View className="flex-1">
                    <Text className="text-gray-900">{suggestion.title}</Text>
                    <Text className="text-xs text-gray-500">{suggestion.description}</Text>
                  </View>
                </View>
              ))}
            </View>
          )}

          {/* Recent Changes */}
          {trustScoreLogs && trustScoreLogs.length > 0 && (
            <View className="mb-4 rounded-lg bg-white p-4 shadow-sm">
              <Text className="mb-3 text-lg font-semibold">Recent Changes</Text>
              {trustScoreLogs.slice(0, 5).map((log: TrustScoreLog, index: number) => (
                <View
                  key={index}
                  className="mb-3 border-b border-gray-100 pb-3 last:mb-0 last:border-b-0 last:pb-0">
                  <View className="mb-1 flex-row justify-between">
                    <Text className="font-medium">{log.reason}</Text>
                    <Text
                      className={cn(
                        'font-bold',
                        log.points > 0 ? 'text-green-500' : 'text-red-500'
                      )}>
                      {log.points > 0 ? '+' : ''}
                      {log.points}
                    </Text>
                  </View>
                  <View className="flex-row justify-between">
                    <Text className="text-xs text-gray-500">{formatDate(log.createdAt)}</Text>
                  </View>
                </View>
              ))}
            </View>
          )}
        </MotiView>
      </ScrollView>
    </View>
  );
};

export default TrustScoreScreen;
