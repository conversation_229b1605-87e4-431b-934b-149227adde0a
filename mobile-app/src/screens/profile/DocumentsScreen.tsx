import React, { useState, useCallback } from 'react';
import {
  View,
  Text,
  FlatList,
  TouchableOpacity,
  ActivityIndicator,
  RefreshControl,
  Alert,
  TextInput,
  Modal,
  ListRenderItem,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import * as DocumentPicker from 'expo-document-picker';
import * as FileSystem from 'expo-file-system';
import * as Sharing from 'expo-sharing';
import { useFocusEffect } from '@react-navigation/native';
import { useTheme } from '../../hooks/use-theme';
import { useTypedNavigation } from '../../types/navigation';
import {
  useGetDocumentsQuery,
  useUploadDocumentMutation,
  useDeleteDocumentMutation,
  useLazyGetDocumentDownloadUrlQuery,
} from '../../store/api/documentsApi';
import type { Document, DocumentType, VerificationStatus } from '../../types/api';
import EmptyState from '../../components/EmptyState';

const DocumentsScreen: React.FC = () => {
  const navigation = useTypedNavigation();
  const { colors } = useTheme();
  const [searchQuery, setSearchQuery] = useState<string>('');
  const [filterModalVisible, setFilterModalVisible] = useState<boolean>(false);
  const [uploadModalVisible, setUploadModalVisible] = useState<boolean>(false);
  const [selectedDocumentType, setSelectedDocumentType] = useState<DocumentType>(
    DocumentType.OTHER
  );
  const [documentNumber, setDocumentNumber] = useState<string>('');
  const [selectedFile, setSelectedFile] = useState<any>(null);

  // RTK Query hooks
  const { data: documentsResponse, isLoading, isFetching, refetch } = useGetDocumentsQuery();

  const [uploadDocument, { isLoading: isUploading }] = useUploadDocumentMutation();
  const [deleteDocument, { isLoading: isDeleting }] = useDeleteDocumentMutation();
  const [getDownloadUrl] = useLazyGetDocumentDownloadUrlQuery();

  const documents = documentsResponse?.data?.documents || [];
  const refreshing = isFetching && !isLoading;

  // Filter documents based on search query
  const filteredDocuments = documents.filter(
    (doc) =>
      doc.documentType.toLowerCase().includes(searchQuery.toLowerCase()) ||
      (doc.documentNumber && doc.documentNumber.toLowerCase().includes(searchQuery.toLowerCase()))
  );

  // Refetch documents when screen comes into focus
  useFocusEffect(
    useCallback(() => {
      refetch();
    }, [refetch])
  );

  const handleRefresh = () => {
    refetch();
  };

  const pickDocument = async () => {
    try {
      const result = await DocumentPicker.getDocumentAsync({
        type: [
          'image/*',
          'application/pdf',
          'application/msword',
          'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
        ],
        copyToCacheDirectory: true,
      });

      if (!result.canceled) {
        setSelectedFile(result.assets[0]);
      }
    } catch (error) {
      console.error('Error picking document:', error);
      Alert.alert('Error', 'Failed to select document. Please try again.');
    }
  };

  const handleUpload = async () => {
    if (!selectedFile) {
      Alert.alert('Error', 'Please select a document first.');
      return;
    }

    try {
      await uploadDocument({
        file: {
          uri: selectedFile.uri,
          name: selectedFile.name,
          type: selectedFile.mimeType || 'application/octet-stream',
        },
        documentType: selectedDocumentType,
        documentNumber: documentNumber || undefined,
      }).unwrap();

      Alert.alert('Success', 'Document uploaded successfully!');
      setUploadModalVisible(false);
      setSelectedFile(null);
      setDocumentNumber('');
      setSelectedDocumentType(DocumentType.OTHER);
    } catch (error: any) {
      console.error('Error uploading document:', error);
      Alert.alert('Error', error.data?.message || 'Failed to upload document. Please try again.');
    }
  };

  const handleDownload = async (document: Document) => {
    try {
      const response = await getDownloadUrl(document.id).unwrap();
      const downloadUrl = response.data.downloadUrl;
      const fileName = response.data.fileName || `document_${document.id}`;

      // Download the file
      const downloadResult = await FileSystem.downloadAsync(
        downloadUrl,
        FileSystem.documentDirectory + fileName
      );

      if (downloadResult.status === 200) {
        // Share the downloaded file
        await Sharing.shareAsync(downloadResult.uri);
      } else {
        throw new Error('Download failed');
      }
    } catch (error: any) {
      console.error('Error downloading document:', error);
      Alert.alert('Error', 'Failed to download document. Please try again.');
    }
  };

  const handleDelete = (document: Document) => {
    Alert.alert(
      'Delete Document',
      `Are you sure you want to delete this ${document.documentType} document?`,
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Delete',
          style: 'destructive',
          onPress: async () => {
            try {
              await deleteDocument(document.id).unwrap();
              Alert.alert('Success', 'Document deleted successfully!');
            } catch (error: any) {
              console.error('Error deleting document:', error);
              Alert.alert('Error', 'Failed to delete document. Please try again.');
            }
          },
        },
      ]
    );
  };

  const getDocumentIcon = (documentType: DocumentType): string => {
    switch (documentType) {
      case DocumentType.ID_PROOF:
      case DocumentType.ID_CARD:
        return 'card-outline';
      case DocumentType.ADDRESS_PROOF:
        return 'location-outline';
      case DocumentType.PROFILE_PHOTO:
        return 'person-outline';
      case DocumentType.EDUCATION_CERTIFICATE:
        return 'school-outline';
      case DocumentType.WORK_CERTIFICATE:
        return 'briefcase-outline';
      case DocumentType.COMPANY_REGISTRATION:
      case DocumentType.BUSINESS_LICENSE:
        return 'business-outline';
      case DocumentType.TAX_DOCUMENT:
      case DocumentType.TAX_CERTIFICATE:
        return 'receipt-outline';
      default:
        return 'document-text-outline';
    }
  };

  const getStatusColor = (status: VerificationStatus): string => {
    switch (status) {
      case VerificationStatus.VERIFIED:
      case VerificationStatus.APPROVED:
        return '#10b981';
      case VerificationStatus.REJECTED:
        return '#ef4444';
      case VerificationStatus.PENDING:
      default:
        return '#f59e0b';
    }
  };

  const getStatusText = (status: VerificationStatus): string => {
    switch (status) {
      case VerificationStatus.VERIFIED:
      case VerificationStatus.APPROVED:
        return 'Verified';
      case VerificationStatus.REJECTED:
        return 'Rejected';
      case VerificationStatus.PENDING:
      default:
        return 'Pending';
    }
  };

  const formatDocumentType = (type: DocumentType): string => {
    return type
      .split('_')
      .map((word) => word.charAt(0).toUpperCase() + word.slice(1).toLowerCase())
      .join(' ');
  };

  const renderDocumentItem: ListRenderItem<Document> = ({ item }) => (
    <View className="mb-4 rounded-lg border border-gray-200 bg-white p-4 shadow-sm">
      <View className="flex-row items-start justify-between">
        <View className="flex-1 flex-row items-start">
          <View className="mr-3 rounded-full bg-gray-100 p-2">
            <Ionicons name={getDocumentIcon(item.documentType)} size={24} color={colors.primary} />
          </View>

          <View className="flex-1">
            <Text className="text-lg font-semibold text-gray-900">
              {formatDocumentType(item.documentType)}
            </Text>

            {item.documentNumber && (
              <Text className="text-sm text-gray-600">Number: {item.documentNumber}</Text>
            )}

            <Text className="text-xs text-gray-500">
              Uploaded: {new Date(item.createdAt).toLocaleDateString()}
            </Text>

            <View className="mt-2 flex-row items-center">
              <View
                className="rounded-full px-2 py-1"
                style={{ backgroundColor: `${getStatusColor(item.verificationStatus)}20` }}>
                <Text
                  className="text-xs font-medium"
                  style={{ color: getStatusColor(item.verificationStatus) }}>
                  {getStatusText(item.verificationStatus)}
                </Text>
              </View>
            </View>

            {item.rejectionReason && (
              <Text className="mt-1 text-xs text-red-600">Reason: {item.rejectionReason}</Text>
            )}
          </View>
        </View>

        <View className="flex-row">
          <TouchableOpacity
            onPress={() => handleDownload(item)}
            className="mr-2 rounded-full bg-blue-100 p-2">
            <Ionicons name="download-outline" size={20} color="#3b82f6" />
          </TouchableOpacity>

          <TouchableOpacity
            onPress={() => handleDelete(item)}
            className="rounded-full bg-red-100 p-2"
            disabled={isDeleting}>
            <Ionicons name="trash-outline" size={20} color="#ef4444" />
          </TouchableOpacity>
        </View>
      </View>
    </View>
  );

  return (
    <View className="flex-1 bg-gray-50">
      {/* Header */}
      <View className="flex-row items-center border-b border-gray-200 bg-white px-4 pb-4 pt-12">
        <TouchableOpacity onPress={() => navigation.goBack()} className="mr-3">
          <Ionicons name="arrow-back" size={24} color="#4b5563" />
        </TouchableOpacity>
        <Text className="flex-1 text-xl font-bold text-gray-900">My Documents</Text>
        <TouchableOpacity
          onPress={() => setUploadModalVisible(true)}
          className="rounded-full bg-indigo-600 p-2">
          <Ionicons name="add" size={24} color="white" />
        </TouchableOpacity>
      </View>

      {/* Search Bar */}
      <View className="bg-white px-4 py-3">
        <View className="flex-row items-center rounded-lg bg-gray-100 px-3 py-2">
          <Ionicons name="search-outline" size={20} color="#6b7280" />
          <TextInput
            className="ml-2 flex-1 text-gray-900"
            placeholder="Search documents..."
            value={searchQuery}
            onChangeText={setSearchQuery}
            placeholderTextColor="#9ca3af"
          />
          {searchQuery.length > 0 && (
            <TouchableOpacity onPress={() => setSearchQuery('')}>
              <Ionicons name="close-circle" size={20} color="#6b7280" />
            </TouchableOpacity>
          )}
        </View>
      </View>

      {/* Documents List */}
      {isLoading && !refreshing ? (
        <View className="flex-1 items-center justify-center">
          <ActivityIndicator size="large" color={colors.primary} />
        </View>
      ) : (
        <FlatList
          data={filteredDocuments}
          renderItem={renderDocumentItem}
          keyExtractor={(item) => item.id}
          contentContainerStyle={{ padding: 16, paddingBottom: 100 }}
          refreshControl={
            <RefreshControl
              refreshing={refreshing}
              onRefresh={handleRefresh}
              colors={[colors.primary]}
            />
          }
          ListEmptyComponent={
            <EmptyState
              icon="document-text-outline"
              title="No documents found"
              message={
                searchQuery
                  ? 'No documents match your search criteria.'
                  : 'Upload your first document to get started with verification.'
              }
            />
          }
        />
      )}

      {/* Upload Modal */}
      <Modal
        visible={uploadModalVisible}
        animationType="slide"
        presentationStyle="pageSheet"
        onRequestClose={() => setUploadModalVisible(false)}>
        <View className="flex-1 bg-white">
          <View className="flex-row items-center border-b border-gray-200 px-4 pb-4 pt-12">
            <TouchableOpacity onPress={() => setUploadModalVisible(false)} className="mr-3">
              <Ionicons name="close" size={24} color="#4b5563" />
            </TouchableOpacity>
            <Text className="flex-1 text-xl font-bold text-gray-900">Upload Document</Text>
            <TouchableOpacity
              onPress={handleUpload}
              disabled={!selectedFile || isUploading}
              className={`rounded-lg px-4 py-2 ${
                selectedFile && !isUploading ? 'bg-indigo-600' : 'bg-gray-300'
              }`}>
              {isUploading ? (
                <ActivityIndicator size="small" color="white" />
              ) : (
                <Text className={`font-medium ${selectedFile ? 'text-white' : 'text-gray-500'}`}>
                  Upload
                </Text>
              )}
            </TouchableOpacity>
          </View>

          <View className="flex-1 p-4">
            {/* Document Type Selection */}
            <View className="mb-6">
              <Text className="mb-2 text-sm font-medium text-gray-700">Document Type</Text>
              <View className="rounded-lg border border-gray-300 bg-white">
                {Object.values(DocumentType).map((type) => (
                  <TouchableOpacity
                    key={type}
                    onPress={() => setSelectedDocumentType(type)}
                    className={`flex-row items-center justify-between border-b border-gray-100 p-4 ${
                      selectedDocumentType === type ? 'bg-indigo-50' : ''
                    }`}>
                    <Text
                      className={`${selectedDocumentType === type ? 'text-indigo-600' : 'text-gray-900'}`}>
                      {formatDocumentType(type)}
                    </Text>
                    {selectedDocumentType === type && (
                      <Ionicons name="checkmark" size={20} color="#4f46e5" />
                    )}
                  </TouchableOpacity>
                ))}
              </View>
            </View>

            {/* Document Number (Optional) */}
            <View className="mb-6">
              <Text className="mb-2 text-sm font-medium text-gray-700">
                Document Number (Optional)
              </Text>
              <TextInput
                className="rounded-lg border border-gray-300 bg-white p-3 text-gray-900"
                placeholder="Enter document number if applicable"
                value={documentNumber}
                onChangeText={setDocumentNumber}
                placeholderTextColor="#9ca3af"
              />
            </View>

            {/* File Selection */}
            <View className="mb-6">
              <Text className="mb-2 text-sm font-medium text-gray-700">Select File</Text>

              {selectedFile ? (
                <View className="rounded-lg border border-gray-300 bg-white p-4">
                  <View className="flex-row items-center justify-between">
                    <View className="flex-1">
                      <Text className="font-medium text-gray-900">{selectedFile.name}</Text>
                      <Text className="text-sm text-gray-500">
                        {selectedFile.size
                          ? `${(selectedFile.size / 1024 / 1024).toFixed(2)} MB`
                          : 'Unknown size'}
                      </Text>
                    </View>
                    <TouchableOpacity
                      onPress={() => setSelectedFile(null)}
                      className="rounded-full bg-red-100 p-2">
                      <Ionicons name="trash-outline" size={20} color="#ef4444" />
                    </TouchableOpacity>
                  </View>
                </View>
              ) : (
                <TouchableOpacity
                  onPress={pickDocument}
                  className="flex-row items-center justify-center rounded-lg border-2 border-dashed border-gray-300 bg-gray-50 p-8">
                  <Ionicons name="cloud-upload-outline" size={32} color="#6b7280" />
                  <Text className="ml-2 text-gray-600">Tap to select a file</Text>
                </TouchableOpacity>
              )}
            </View>

            {/* Supported Formats */}
            <View className="rounded-lg bg-blue-50 p-4">
              <Text className="mb-2 font-medium text-blue-900">Supported Formats</Text>
              <Text className="text-sm text-blue-700">
                • Images: JPG, PNG, GIF{'\n'}• Documents: PDF, DOC, DOCX{'\n'}• Maximum file size:
                10MB
              </Text>
            </View>
          </View>
        </View>
      </Modal>
    </View>
  );
};

export default DocumentsScreen;
