// 'use client';
import { useState, useRef } from 'react';
import {
  View,
  Text,
  Image,
  TouchableOpacity,
  FlatList,
  Dimensions,
  ListRenderItem,
} from 'react-native';
import { MotiView } from '@/utils/animationComponents';
import { StatusBar } from 'expo-status-bar';
import AsyncStorage from '@react-native-async-storage/async-storage';
import STORAGE_KEYS from '../../constants/storage-keys';
import { sharedTransition } from '../../utils/animations';
import { SCREENS } from '../../constants/screens';
import { useTypedNavigation } from '@/types/navigation';

const { width } = Dimensions.get('window');

const slides = [
  {
    id: '1',
    title: 'Find Jobs Near You',
    description:
      'Discover inventory and stock audit jobs in your area that match your skills and trust score.',
    image: require('../../../assets/logo.png'),
  },
  {
    id: '2',
    title: 'Build Your Trust Score',
    description:
      'Complete jobs successfully to increase your trust score and unlock better opportunities.',
    image: require('../../../assets/logo.png'),
  },
  {
    id: '3',
    title: 'Track Your Earnings',
    description: 'Monitor your income, view payment history, and track your financial growth.',
    image: require('../../../assets/logo.png'),
  },
  {
    id: '4',
    title: 'Chat with Companies',
    description: 'Communicate directly with companies to clarify job details and requirements.',
    image: require('../../../assets/logo.png'),
  },
];

const OnboardingScreen = () => {
  const [currentIndex, setCurrentIndex] = useState(0);
  const navigation = useTypedNavigation();
  const flatListRef = useRef(null);

  const handleNext = () => {
    if (currentIndex < slides.length - 1) {
      flatListRef.current?.scrollToIndex({
        index: currentIndex + 1,
        animated: true,
      });
    } else {
      completeOnboarding();
    }
  };

  const handleSkip = () => {
    completeOnboarding();
  };

  const completeOnboarding = async () => {
    // Set both keys for compatibility
    await AsyncStorage.multiSet([
      [STORAGE_KEYS.ONBOARDING_COMPLETED, 'true'],
      [STORAGE_KEYS.ONBOARDING_COMPLETE, 'true'],
    ]);
    navigation.navigate(SCREENS.MAIN);
  };

  const renderItem: ListRenderItem<(typeof slides)[0]> = ({ item, index }) => {
    return (
      <View style={{ width }} className="items-center justify-center px-8">
        <MotiView
          from={{ opacity: 0, translateY: 50 }}
          animate={{ opacity: 1, translateY: 0 }}
          transition={{
            ...sharedTransition,
            delay: 300,
          }}
          className="items-center">
          <Image source={item.image} className="mb-8 h-64 w-64" resizeMode="contain" />
          <Text className="mb-4 text-center text-2xl font-bold">{item.title}</Text>
          <Text className="text-center text-base text-gray-600">{item.description}</Text>
        </MotiView>
      </View>
    );
  };

  const handleViewableItemsChanged = useRef(({ viewableItems }) => {
    if (viewableItems.length > 0) {
      setCurrentIndex(viewableItems[0].index);
    }
  }).current;

  return (
    <View className="flex-1 bg-white">
      <StatusBar style="dark" />

      <View className="flex-row justify-end p-4">
        <TouchableOpacity onPress={handleSkip}>
          <Text className="font-medium text-indigo-600">Skip</Text>
        </TouchableOpacity>
      </View>

      <FlatList
        ref={flatListRef}
        data={slides}
        renderItem={renderItem}
        horizontal
        pagingEnabled
        showsHorizontalScrollIndicator={false}
        keyExtractor={(item) => item.id}
        onViewableItemsChanged={handleViewableItemsChanged}
        viewabilityConfig={{ viewAreaCoveragePercentThreshold: 50 }}
      />

      <View className="p-8">
        <View className="mb-8 flex-row justify-center">
          {slides.map((_, index) => (
            <View
              key={index}
              className={`mx-1 h-2 w-2 rounded-full ${index === currentIndex ? 'w-4 bg-indigo-600' : 'bg-gray-300'}`}
            />
          ))}
        </View>

        <TouchableOpacity
          onPress={handleNext}
          className="items-center rounded-lg bg-indigo-600 py-4">
          <Text className="text-lg font-bold text-white">
            {currentIndex === slides.length - 1 ? 'Get Started' : 'Next'}
          </Text>
        </TouchableOpacity>
      </View>
    </View>
  );
};

export default OnboardingScreen;
