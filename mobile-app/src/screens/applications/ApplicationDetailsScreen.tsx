import React from 'react';
import { View, Text, ScrollView, TouchableOpacity, ActivityIndicator, Alert } from 'react-native';
import { useNavigation, useRoute, RouteProp } from '@react-navigation/native';
import { Ionicons } from '@expo/vector-icons';
import { SCREENS } from '../../constants/screens';
import {
  useGetApplicationByIdQuery,
  useWithdrawApplicationMutation,
} from '../../store/api/applicationsApi';
import { Application, ApplicationStatus } from '../../types/api';
import { getErrorMessage } from '../../utils/apiErrorHandling';
import {
  ApplicationsStackNavigationProp,
  ApplicationsStackParamList,
} from '../../types/navigation';
import { useTypedNavigation } from '@/types/navigation';

// Timeline item interface
interface TimelineItem {
  id: string;
  status: string;
  date: string;
  note: string;
}

// Mock data for fallback
const mockApplicationDetails: Application & { timeline: TimelineItem[] } = {
  id: '1',
  job: {
    id: '1',
    title: 'Frontend Developer',
    description: 'We are looking for a skilled Frontend Developer to join our team...',
    company: {
      id: '1',
      name: 'TechCorp',
      isVerified: true,
    },
    location: {
      address: '123 Tech St',
      city: 'San Francisco',
      state: 'CA',
      country: 'USA',
      zipCode: '94105',
      coordinates: {
        latitude: 37.7749,
        longitude: -122.4194,
      },
    },
    salary: {
      min: 80000,
      max: 120000,
      currency: 'USD',
    },
    category: 'Development',
    type: 'Full-time',
    status: 'active',
    requirements: [
      '3+ years of experience with React',
      'Strong JavaScript fundamentals',
      'Experience with responsive design',
      'Knowledge of modern frontend tools',
    ],
    responsibilities: [],
    benefits: [],
    startDate: '2023-11-01',
    createdAt: '2023-10-01',
    updatedAt: '2023-10-01',
    isEmergency: false,
    isFeatured: false,
    applicationsCount: 10,
  },
  user: {
    id: '1',
    fullName: 'John Doe',
    role: 'worker',
    trustScore: 85,
    isVerified: true,
    isKycVerified: false,
    isActive: true,
    isBanned: false,
    createdAt: '2023-01-01',
    updatedAt: '2023-01-01',
  },
  status: 'pending' as ApplicationStatus,
  createdAt: '2023-10-15T10:30:00Z',
  updatedAt: '2023-10-15T10:30:00Z',
  timeline: [
    { id: '1', status: 'applied', date: '2023-10-15T10:30:00Z', note: 'Application submitted' },
    {
      id: '2',
      status: 'screening',
      date: '2023-10-18T14:20:00Z',
      note: 'Resume passed initial screening',
    },
    {
      id: '3',
      status: 'interview',
      date: '2023-10-25T09:00:00Z',
      note: 'Technical interview scheduled',
    },
  ],
};

const ApplicationDetailsScreen = (): JSX.Element => {
  // Get typed navigation and route
  const navigation = useTypedNavigation();
  const route =
    useRoute<RouteProp<ApplicationsStackParamList, typeof SCREENS.APPLICATION_DETAILS>>();
  const { applicationId } = route.params;

  // Use RTK Query hook to fetch application details
  const {
    data: application,
    isLoading,
    error,
    refetch,
  } = useGetApplicationByIdQuery(applicationId, {
    // Skip the query if we don't have an applicationId
    skip: !applicationId,
  });

  // Use RTK Query mutation hook for withdrawing application
  const [withdrawApplication, { isLoading: isWithdrawing }] = useWithdrawApplicationMutation();

  // Handle application withdrawal
  const handleCancelApplication = (): void => {
    Alert.prompt(
      'Withdraw Application',
      'Please provide a reason for withdrawal:',
      [
        {
          text: 'Cancel',
          style: 'cancel',
        },
        {
          text: 'Submit',
          onPress: async (reason?: string) => {
            if (!reason) {
              Alert.alert('Error', 'Please provide a reason for withdrawal');
              return;
            }

            try {
              // Call the RTK Query mutation
              await withdrawApplication(applicationId).unwrap();
              Alert.alert('Success', 'Application withdrawn successfully');
              // Refetch data (not needed with RTK Query as it will invalidate cache automatically)
            } catch (err) {
              console.error('Error withdrawing application:', err);
              Alert.alert('Error', getErrorMessage(err, 'Failed to withdraw application'));
            }
          },
        },
      ],
      'plain-text'
    );
  };

  /**
   * Get the appropriate color class for a status
   * @param status The application status
   * @returns Tailwind CSS class string for the status
   */
  const getStatusColor = (status: string): string => {
    switch (status.toLowerCase()) {
      case 'applied':
      case 'pending':
        return 'bg-blue-100 text-blue-800';
      case 'screening':
        return 'bg-yellow-100 text-yellow-800';
      case 'interview':
      case 'shortlisted':
        return 'bg-purple-100 text-purple-800';
      case 'rejected':
        return 'bg-red-100 text-red-800';
      case 'accepted':
      case 'offer':
      case 'hired':
        return 'bg-green-100 text-green-800';
      case 'completed':
        return 'bg-teal-100 text-teal-800';
      case 'cancelled':
      case 'withdrawn':
        return 'bg-gray-100 text-gray-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  /**
   * Format a date string to a localized date
   * @param dateString ISO date string
   * @returns Formatted date string
   */
  const formatDate = (dateString: string): string => {
    const date = new Date(dateString);
    return date.toLocaleDateString();
  };

  // Show loading state
  if (isLoading || isWithdrawing) {
    return (
      <View className="flex-1 items-center justify-center bg-white">
        <ActivityIndicator size="large" color="#4f46e5" />
        {isWithdrawing && <Text className="mt-4 text-gray-600">Processing your request...</Text>}
      </View>
    );
  }

  // Show error state
  if (error) {
    return (
      <View className="flex-1 items-center justify-center bg-white p-4">
        <Ionicons name="alert-circle-outline" size={48} color="#ef4444" />
        <Text className="mt-4 text-center text-lg font-medium text-red-500">
          {getErrorMessage(error, 'Failed to load application details')}
        </Text>
        <TouchableOpacity
          className="mt-6 rounded-lg bg-indigo-600 px-6 py-3"
          onPress={() => refetch()}>
          <Text className="font-medium text-white">Try Again</Text>
        </TouchableOpacity>
        <TouchableOpacity className="mt-4" onPress={() => navigation.goBack()}>
          <Text className="text-indigo-600">Go Back</Text>
        </TouchableOpacity>
      </View>
    );
  }

  // Show empty state if no application data
  if (!application) {
    return (
      <View className="flex-1 items-center justify-center bg-white">
        <Text className="text-red-500">Application not found</Text>
        <TouchableOpacity
          className="mt-4 rounded-lg bg-indigo-600 px-4 py-2"
          onPress={() => navigation.goBack()}>
          <Text className="text-white">Go Back</Text>
        </TouchableOpacity>
      </View>
    );
  }

  return (
    <View className="flex-1 bg-white">
      <View className="bg-indigo-600 px-4 pb-4 pt-12">
        <TouchableOpacity
          className="mb-2 flex-row items-center"
          onPress={() => navigation.goBack()}>
          <Ionicons name="arrow-back" size={24} color="white" />
          <Text className="ml-2 text-white">Back</Text>
        </TouchableOpacity>
        <Text className="text-2xl font-bold text-white">{application.job?.title}</Text>
        <Text className="text-lg text-indigo-200">{application.job?.company?.name}</Text>
        <View className="mt-1 flex-row items-center">
          <Ionicons name="location-outline" size={16} color="#e0e7ff" />
          <Text className="ml-1 text-indigo-100">{application.job?.location}</Text>
        </View>
      </View>

      <ScrollView className="flex-1 p-4">
        <View className="mb-6 flex-row items-center justify-between rounded-lg bg-gray-50 p-4">
          <View>
            <Text className="text-gray-500">Status</Text>
            <View className={`mt-1 rounded-full px-3 py-1 ${getStatusColor(application.status)}`}>
              <Text className="font-medium">
                {application.status.charAt(0).toUpperCase() + application.status.slice(1)}
              </Text>
            </View>
          </View>
          <View>
            <Text className="text-gray-500">Applied on</Text>
            <Text className="mt-1 font-medium text-gray-700">
              {formatDate(application.createdAt)}
            </Text>
          </View>
        </View>

        <Text className="mb-2 text-xl font-bold text-gray-900">Application Timeline</Text>
        <View className="mb-6">
          {(application.timeline || []).map((item, index) => (
            <View key={item.id} className="mb-4 flex-row">
              <View className="mr-4 items-center">
                <View className="h-3 w-3 rounded-full bg-indigo-600" />
                {index < application.timeline.length - 1 && (
                  <View className="mt-1 h-full w-0.5 bg-gray-300" />
                )}
              </View>
              <View className="flex-1">
                <View className="flex-row justify-between">
                  <Text className="font-semibold text-gray-900">
                    {item.status.charAt(0).toUpperCase() + item.status.slice(1)}
                  </Text>
                  <Text className="text-gray-500">{formatDate(item.date)}</Text>
                </View>
                <Text className="mt-1 text-gray-600">{item.note}</Text>
              </View>
            </View>
          ))}
        </View>

        <Text className="mb-2 text-xl font-bold text-gray-900">Job Description</Text>
        <Text className="mb-4 text-gray-700">{application.job?.description}</Text>

        {application.job?.requirements && (
          <>
            <Text className="mb-2 text-xl font-bold text-gray-900">Requirements</Text>
            <View className="mb-6">
              {application.job.requirements.map((req, index) => (
                <View key={index} className="mb-2 flex-row">
                  <Text className="mr-2 text-indigo-600">•</Text>
                  <Text className="flex-1 text-gray-700">{req}</Text>
                </View>
              ))}
            </View>
          </>
        )}

        {application.status !== 'cancelled' && application.status !== 'completed' && (
          <View className="mb-8 flex-row justify-between">
            <TouchableOpacity
              className="mr-2 flex-1 rounded-lg bg-indigo-600 px-4 py-3"
              onPress={() => {
                // Navigate to chat with the company
                if (application.job?.company?.id) {
                  navigation.navigate(SCREENS.CHAT_TAB, {
                    screen: SCREENS.CHAT_DETAILS,
                    params: {
                      chatId: `job_${application.job.id}`,
                      recipientId: application.job.company.id,
                      recipientName: application.job.company.name,
                    },
                  });
                } else {
                  // Fallback if company ID is not available
                  navigation.navigate(SCREENS.CHAT_TAB);
                }
              }}>
              <Text className="text-center font-medium text-white">Contact Recruiter</Text>
            </TouchableOpacity>
            <TouchableOpacity
              className="ml-2 flex-1 rounded-lg border border-red-600 bg-white px-4 py-3"
              onPress={handleCancelApplication}>
              <Text className="text-center font-medium text-red-600">Withdraw</Text>
            </TouchableOpacity>
          </View>
        )}
      </ScrollView>
    </View>
  );
};

export default ApplicationDetailsScreen;
