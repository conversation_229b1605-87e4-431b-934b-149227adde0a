import React, { useState, useEffect } from "react";
import { View, Text, FlatList, TouchableOpacity, ActivityIndicator, Alert } from "react-native";
import { useNavigation } from "@react-navigation/native";
import { Ionicons } from "@expo/vector-icons";
import { SCREENS } from "../../constants/screens";
import { applicationsAPI } from "../../services/api";

// Mock data for fallback
const mockApplications = [
  { id: "1", jobTitle: "Frontend Developer", company: "TechCorp", status: "applied", date: "2023-10-15" },
  { id: "2", jobTitle: "UX Designer", company: "DesignHub", status: "interview", date: "2023-10-10" },
  { id: "3", jobTitle: "Product Manager", company: "ProductLabs", status: "rejected", date: "2023-09-28" },
  { id: "4", jobTitle: "Mobile Developer", company: "AppWorks", status: "accepted", date: "2023-09-20" },
];

const ApplicationsScreen = () => {
  const navigation = useNavigation();
  const [applications, setApplications] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  useEffect(() => {
    fetchApplications();
  }, []);

  const fetchApplications = async () => {
    setLoading(true);
    setError(null);
    
    try {
      const response = await applicationsAPI.getMyApplications();
      setApplications(response.data);
    } catch (err) {
      console.error("Error fetching applications:", err);
      setError("Failed to load applications");
      // Fallback to mock data
      setApplications(mockApplications);
      
      if (err.response?.status !== 401) { // Don't show alert for auth errors
        Alert.alert("Error", "Failed to load applications. Showing sample data.");
      }
    } finally {
      setLoading(false);
    }
  };

  const getStatusColor = (status) => {
    switch (status.toLowerCase()) {
      case "applied":
        return "bg-blue-100 text-blue-800";
      case "interview":
        return "bg-purple-100 text-purple-800";
      case "rejected":
        return "bg-red-100 text-red-800";
      case "accepted":
      case "offer":
        return "bg-green-100 text-green-800";
      case "completed":
        return "bg-teal-100 text-teal-800";
      case "cancelled":
        return "bg-gray-100 text-gray-800";
      default:
        return "bg-gray-100 text-gray-800";
    }
  };

  const formatDate = (dateString) => {
    const date = new Date(dateString);
    return date.toLocaleDateString();
  };

  const renderItem = ({ item }) => (
    <TouchableOpacity
      className="bg-white p-4 mb-3 rounded-lg shadow-sm border border-gray-100"
      onPress={() => navigation.navigate(SCREENS.APPLICATION_DETAILS, { applicationId: item.id })}
    >
      <Text className="text-lg font-semibold text-gray-900">{item.jobTitle || item.job?.title}</Text>
      <Text className="text-gray-600 mb-2">{item.company || item.job?.company?.name}</Text>
      <View className="flex-row justify-between items-center mt-2">
        <Text className="text-gray-500 text-sm">{formatDate(item.date || item.createdAt)}</Text>
        <View className={`px-3 py-1 rounded-full ${getStatusColor(item.status)}`}>
          <Text className="text-sm font-medium">{item.status.charAt(0).toUpperCase() + item.status.slice(1)}</Text>
        </View>
      </View>
    </TouchableOpacity>
  );

  if (loading) {
    return (
      <View className="flex-1 justify-center items-center bg-gray-50">
        <ActivityIndicator size="large" color="#4f46e5" />
      </View>
    );
  }

  return (
    <View className="flex-1 bg-gray-50 p-4">
      <View className="mb-6 mt-12">
        <Text className="text-2xl font-bold text-gray-900">My Applications</Text>
        <Text className="text-gray-600 mt-1">Track your job applications</Text>
      </View>

      {applications.length > 0 ? (
        <FlatList
          data={applications}
          renderItem={renderItem}
          keyExtractor={(item) => item.id}
          showsVerticalScrollIndicator={false}
          contentContainerStyle={{ paddingBottom: 20 }}
          refreshing={loading}
          onRefresh={fetchApplications}
        />
      ) : (
        <View className="flex-1 justify-center items-center">
          <Ionicons name="document-text-outline" size={64} color="#d1d5db" />
          <Text className="text-gray-500 mt-4 text-lg">No applications yet</Text>
          <TouchableOpacity
            className="mt-4 bg-indigo-600 px-4 py-2 rounded-lg"
            onPress={() => navigation.navigate(SCREENS.JOBS_TAB)}
          >
            <Text className="text-white font-medium">Browse Jobs</Text>
          </TouchableOpacity>
        </View>
      )}
    </View>
  );
};

export default ApplicationsScreen;
