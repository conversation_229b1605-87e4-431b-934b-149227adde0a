import React, { useState, useRef, useEffect } from 'react';
import {
  View,
  Text,
  TextInput,
  TouchableOpacity,
  Image,
  ActivityIndicator,
  Alert,
  KeyboardAvoidingView,
  Platform,
  ScrollView,
} from 'react-native';
import { MotiView } from 'moti';
import { StatusBar } from 'expo-status-bar';
import { SCREENS } from '../../constants/screens';
import { useAppNavigation } from '../../utils/navigation';
import { useAuth } from '../../hooks/use-auth';

/**
 * OTP Login Screen Component
 * Allows users to login or register using OTP verification
 */
const OtpLoginScreen: React.FC = () => {
  // Use the app navigation hook instead of useTypedNavigation
  const navigation = useAppNavigation();

  // Use the enhanced useAuth hook
  const { requestOtp, verifyOtp, isRequestOtpLoading, isVerifyOtpLoading } = useAuth();

  const [phone, setPhone] = useState<string>('');
  const [otp, setOtp] = useState<string[]>(['', '', '', '', '', '']);
  const [isOtpSent, setIsOtpSent] = useState<boolean>(false);
  const [countdown, setCountdown] = useState<number>(0);

  // Create an array of refs for the OTP inputs
  const inputRefs = useRef<(TextInput | null)[]>(Array(6).fill(null));

  useEffect(() => {
    if (countdown > 0) {
      const timer = setTimeout(() => setCountdown(countdown - 1), 1000);
      return () => clearTimeout(timer);
    }
  }, [countdown]);

  /**
   * Handle sending OTP to the provided phone number
   * Validates the phone number, sends OTP request, and handles success/error states
   */
  const handleSendOtp = async (): Promise<void> => {
    if (!phone || phone.length < 10) {
      Alert.alert('Error', 'Please enter a valid phone number');
      return;
    }

    try {
      // Call the requestOtp method from useAuth
      const result = await requestOtp(phone);
      setIsOtpSent(true);
      setCountdown(60); // 60 seconds countdown for resend
      Alert.alert('Success', 'OTP sent successfully. Please check your phone.');
    } catch (error: unknown) {
      let errorMessage = 'Failed to send OTP';
      if (error && typeof error === 'object' && 'data' in error) {
        const apiError = error.data as { message?: string };
        if (apiError && apiError.message) {
          errorMessage = apiError.message;
        }
      }
      Alert.alert('Error', errorMessage);
    }
  };

  /**
   * Handle OTP verification
   */
  const handleVerifyOtp = async (): Promise<void> => {
    const otpString = otp.join('');
    if (otpString.length !== 6) {
      Alert.alert('Error', 'Please enter a valid 6-digit OTP');
      return;
    }

    try {
      // Call the verifyOtp method from useAuth
      const response = await verifyOtp({ phone, code: otpString });

      // Check if this is a new user based on the isVerified flag
      const isNewUser = response?.user?.isVerified === false;

      if (isNewUser) {
        // Navigate to register screen with phone number
        if (navigation) {
          navigation.reset({
            index: 0,
            routes: [{ name: SCREENS.REGISTER, params: { phone } }],
          });
        }
      }
      // No need to navigate to main screen - the app navigator will handle this automatically
      // based on the updated auth state
    } catch (error: unknown) {
      let errorMessage = 'Invalid OTP';
      if (error && typeof error === 'object' && 'data' in error) {
        const apiError = error.data as { message?: string };
        if (apiError && apiError.message) {
          errorMessage = apiError.message;
        }
      }
      Alert.alert('Error', errorMessage);
    }
  };

  /**
   * Handle OTP input change
   * @param text The text entered in the input
   * @param index The index of the input
   */
  const handleOtpChange = (text: string, index: number): void => {
    const newOtp = [...otp];
    newOtp[index] = text;
    setOtp(newOtp);

    // Auto focus to next input
    if (text && index < 5 && inputRefs.current[index + 1]) {
      inputRefs.current[index + 1]?.focus();
    }
  };

  /**
   * Handle OTP input key press
   * @param e The key press event
   * @param index The index of the input
   */
  const handleOtpKeyPress = (e: { nativeEvent: { key: string } }, index: number): void => {
    // Auto focus to previous input on backspace
    if (
      e.nativeEvent.key === 'Backspace' &&
      !otp[index] &&
      index > 0 &&
      inputRefs.current[index - 1]
    ) {
      inputRefs.current[index - 1]?.focus();
    }
  };

  return (
    <KeyboardAvoidingView
      behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
      className="flex-1">
      <ScrollView contentContainerStyle={{ flexGrow: 1 }}>
        <View className="flex-1 bg-white p-6">
          <StatusBar style="dark" />

          <View className="mb-10 mt-10 items-center">
            <Image
              source={require('../../../assets/logo.png')}
              className="h-32 w-32"
              resizeMode="contain"
            />
            <Text className="mt-4 text-2xl font-bold">Welcome</Text>
            <Text className="mt-2 text-center text-gray-500">
              {isOtpSent ? 'Enter the OTP sent to your phone' : 'Sign in with your phone number'}
            </Text>
          </View>

          <MotiView
            from={{ opacity: 0, translateY: 20 }}
            animate={{ opacity: 1, translateY: 0 }}
            className="space-y-4">
            {!isOtpSent ? (
              <>
                <View>
                  <Text className="mb-2 font-medium text-gray-700">Phone Number</Text>
                  <View className="flex-row items-center">
                    <View className="rounded-l-lg bg-gray-100 p-4">
                      <Text>+91</Text>
                    </View>
                    <TextInput
                      className="flex-1 rounded-r-lg bg-gray-100 p-4"
                      placeholder="Enter your phone number"
                      value={phone}
                      onChangeText={setPhone}
                      keyboardType="phone-pad"
                      maxLength={10}
                    />
                  </View>
                </View>

                <TouchableOpacity
                  onPress={handleSendOtp}
                  disabled={isRequestOtpLoading}
                  className={`items-center rounded-lg bg-indigo-600 p-4 ${isRequestOtpLoading ? 'opacity-70' : ''}`}>
                  {isRequestOtpLoading ? (
                    <ActivityIndicator color="white" />
                  ) : (
                    <Text className="text-lg font-bold text-white">Send OTP</Text>
                  )}
                </TouchableOpacity>

                <View className="mt-6 flex-row justify-center">
                  <Text className="text-gray-600">Already have an account? </Text>
                  <TouchableOpacity
                    onPress={() => {
                      if (navigation) {
                        navigation.navigate(SCREENS.LOGIN);
                      }
                    }}>
                    <Text className="font-medium text-indigo-600">Sign In with Email</Text>
                  </TouchableOpacity>
                </View>
              </>
            ) : (
              <>
                <View>
                  <Text className="mb-2 font-medium text-gray-700">Enter OTP</Text>
                  <View className="flex-row justify-between">
                    {otp.map((digit, index) => (
                      <TextInput
                        key={index}
                        ref={(el) => {
                          // Using a callback ref pattern that doesn't return anything
                          inputRefs.current[index] = el;
                        }}
                        className="h-12 w-12 rounded-lg bg-gray-100 p-4 text-center text-lg font-bold"
                        value={digit}
                        onChangeText={(text) => handleOtpChange(text, index)}
                        onKeyPress={(e) => handleOtpKeyPress(e, index)}
                        keyboardType="number-pad"
                        maxLength={1}
                      />
                    ))}
                  </View>
                </View>

                <TouchableOpacity
                  onPress={handleVerifyOtp}
                  disabled={isVerifyOtpLoading}
                  className={`items-center rounded-lg bg-indigo-600 p-4 ${isVerifyOtpLoading ? 'opacity-70' : ''}`}>
                  {isVerifyOtpLoading ? (
                    <ActivityIndicator color="white" />
                  ) : (
                    <Text className="text-lg font-bold text-white">Verify OTP</Text>
                  )}
                </TouchableOpacity>

                <View className="mt-6 flex-row justify-center">
                  <Text className="text-gray-600">Didn't receive OTP? </Text>
                  {countdown > 0 ? (
                    <Text className="font-medium text-indigo-600">Resend in {countdown}s</Text>
                  ) : (
                    <TouchableOpacity onPress={handleSendOtp} disabled={isRequestOtpLoading}>
                      <Text className="font-medium text-indigo-600">Resend OTP</Text>
                    </TouchableOpacity>
                  )}
                </View>

                <TouchableOpacity onPress={() => setIsOtpSent(false)} className="mt-4 items-center">
                  <Text className="font-medium text-indigo-600">Change Phone Number</Text>
                </TouchableOpacity>
              </>
            )}
          </MotiView>
        </View>
      </ScrollView>
    </KeyboardAvoidingView>
  );
};

export default OtpLoginScreen;
