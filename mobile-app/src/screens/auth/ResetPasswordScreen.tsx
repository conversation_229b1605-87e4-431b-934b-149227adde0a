import { useState, useEffect } from 'react';
import {
  View,
  Text,
  TextInput,
  TouchableOpacity,
  Image,
  ActivityIndicator,
  Alert,
  KeyboardAvoidingView,
  Platform,
  ScrollView,
} from 'react-native';
import { getErrorMessage } from '../../utils/apiErrorHandling';
import { navigateToAuth, useAppRoute } from '../../utils/navigation';
import { SCREENS } from '../../constants/screens';
import { useAuth } from '../../hooks/use-auth';

const ResetPasswordScreen = () => {
  const route = useAppRoute<SCREENS.RESET_PASSWORD>();
  const token = route.params?.token || '';

  const { resetPassword, isResetPasswordLoading } = useAuth();

  const [password, setPassword] = useState<string>('');
  const [confirmPassword, setConfirmPassword] = useState<string>('');
  const [isTokenValid, setIsTokenValid] = useState<boolean | null>(null);
  const [resetComplete, setResetComplete] = useState<boolean>(false);
  const [isValidating, setIsValidating] = useState<boolean>(true);

  // Validate token when component mounts
  useEffect(() => {
    if (token) {
      validateResetToken();
    }
  }, [token]);

  const validateResetToken = async () => {
    setIsValidating(true);
    try {
      // For simplicity, we'll just assume the token is valid
      // In a real implementation, you would call an API to validate the token
      setIsTokenValid(true);
      setIsValidating(false);
    } catch (error) {
      setIsTokenValid(false);
      setIsValidating(false);
      Alert.alert('Error', getErrorMessage(error, 'Failed to validate token. Please try again.'));
    }
  };

  const handleResetPassword = async () => {
    // Validate password
    if (password.length < 8) {
      Alert.alert('Error', 'Password must be at least 8 characters long');
      return;
    }

    // Check if passwords match
    if (password !== confirmPassword) {
      Alert.alert('Error', 'Passwords do not match');
      return;
    }

    try {
      // Call the resetPassword method from useAuth
      await resetPassword(token, password);

      // Show success message
      setResetComplete(true);
    } catch (error) {
      Alert.alert('Error', getErrorMessage(error, 'Failed to reset password. Please try again.'));
    }
  };

  if (resetComplete) {
    return (
      <View className="flex-1 items-center justify-center bg-white p-6">
        <Image
          source={require('../../../assets/check-circle.png')}
          className="mb-6 h-24 w-24"
          resizeMode="contain"
        />
        <Text className="mb-4 text-center text-2xl font-bold">Password Reset Successful</Text>
        <Text className="mb-8 text-center text-gray-600">
          Your password has been reset successfully. You can now log in with your new password.
        </Text>
        <TouchableOpacity
          onPress={() => navigateToAuth(SCREENS.LOGIN)}
          className="w-full items-center rounded-lg bg-indigo-600 p-4">
          <Text className="text-lg font-bold text-white">Back to Login</Text>
        </TouchableOpacity>
      </View>
    );
  }

  if (isTokenValid === false) {
    return (
      <View className="flex-1 items-center justify-center bg-white p-6">
        <Image
          source={require('../../../assets/error.png')}
          className="mb-6 h-24 w-24"
          resizeMode="contain"
        />
        <Text className="mb-4 text-center text-2xl font-bold">Invalid or Expired Token</Text>
        <Text className="mb-8 text-center text-gray-600">
          The password reset link is invalid or has expired. Please request a new one.
        </Text>
        <TouchableOpacity
          onPress={() => navigateToAuth(SCREENS.FORGOT_PASSWORD)}
          className="w-full items-center rounded-lg bg-indigo-600 p-4">
          <Text className="text-lg font-bold text-white">Request New Link</Text>
        </TouchableOpacity>
      </View>
    );
  }

  if (isTokenValid === null || isValidating) {
    return (
      <View className="flex-1 items-center justify-center bg-white p-6">
        <ActivityIndicator size="large" color="#4f46e5" />
        <Text className="mt-4 text-gray-600">Validating reset token...</Text>
      </View>
    );
  }

  return (
    <KeyboardAvoidingView
      behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
      className="flex-1">
      <ScrollView className="flex-1 bg-white p-6">
        <View className="mb-10 mt-6 items-center">
          <Image
            source={require('../../../assets/logo.png')}
            className="h-32 w-32"
            resizeMode="contain"
          />
          <Text className="mt-4 text-2xl font-bold">Reset Password</Text>
          <Text className="mt-2 text-center text-gray-500">Enter your new password below</Text>
        </View>

        <View className="space-y-4">
          <View>
            <Text className="mb-2 font-medium text-gray-700">New Password</Text>
            <TextInput
              className="rounded-lg bg-gray-100 p-4"
              placeholder="Enter new password"
              value={password}
              onChangeText={setPassword}
              secureTextEntry
            />
          </View>

          <View>
            <Text className="mb-2 font-medium text-gray-700">Confirm Password</Text>
            <TextInput
              className="rounded-lg bg-gray-100 p-4"
              placeholder="Confirm new password"
              value={confirmPassword}
              onChangeText={setConfirmPassword}
              secureTextEntry
            />
          </View>

          <TouchableOpacity
            onPress={handleResetPassword}
            disabled={isResetPasswordLoading}
            className={`items-center rounded-lg bg-indigo-600 p-4 ${
              isResetPasswordLoading ? 'opacity-70' : ''
            }`}>
            {isResetPasswordLoading ? (
              <ActivityIndicator color="white" />
            ) : (
              <Text className="text-lg font-bold text-white">Reset Password</Text>
            )}
          </TouchableOpacity>
        </View>
      </ScrollView>
    </KeyboardAvoidingView>
  );
};

export default ResetPasswordScreen;
