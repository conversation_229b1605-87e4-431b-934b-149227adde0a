import React, { useState } from 'react';
import {
  View,
  Text,
  TextInput,
  TouchableOpacity,
  ScrollView,
  ActivityIndicator,
  Alert,
  KeyboardAvoidingView,
  Platform,
} from 'react-native';
import { useRoute } from '@react-navigation/native';
import { Ionicons } from '@expo/vector-icons';
import { SCREENS } from '../../constants/screens';
import { RegisterRequest, UserRole } from '../../types/api';
import { useTypedNavigation } from '@/types/navigation';
import { useAuth } from '../../providers/AuthRTKProvider';

interface FormData {
  fullName: string;
  email: string;
  phone: string;
  password: string;
  confirmPassword: string;
  role: UserRole;
}

interface FormErrors {
  fullName?: string;
  email?: string;
  phone?: string;
  password?: string;
  confirmPassword?: string;
  [key: string]: string | undefined;
}

// Define route params type
interface RouteParams {
  phone?: string;
}

const RegisterScreen = () => {
  const navigation = useTypedNavigation();
  const route = useRoute();
  const routeParams = route.params as RouteParams | undefined;
  const { signUp, isLoading } = useAuth();

  // Get phone from route params if coming from OTP verification
  const initialPhone = routeParams?.phone || '';

  const [formData, setFormData] = useState<FormData>({
    fullName: '',
    email: '',
    phone: initialPhone,
    password: '',
    confirmPassword: '',
    role: UserRole.WORKER, // Default role
  });

  const [errors, setErrors] = useState<FormErrors>({});
  const [showPassword, setShowPassword] = useState<boolean>(false);

  const handleChange = (field: keyof FormData, value: string): void => {
    setFormData({
      ...formData,
      [field]: value,
    });
    // Clear error when user types
    if (errors[field]) {
      setErrors({
        ...errors,
        [field]: undefined,
      });
    }
  };

  const validateForm = (): boolean => {
    const newErrors: FormErrors = {};

    if (!formData.fullName.trim()) {
      newErrors.fullName = 'Full name is required';
    }

    if (!formData.email && !formData.phone) {
      newErrors.email = 'Either email or phone is required';
      newErrors.phone = 'Either email or phone is required';
    }

    if (formData.email && !/\S+@\S+\.\S+/.test(formData.email)) {
      newErrors.email = 'Email is invalid';
    }

    if (formData.phone && formData.phone.length < 10) {
      newErrors.phone = 'Phone number is invalid';
    }

    if (!formData.password) {
      newErrors.password = 'Password is required';
    } else if (formData.password.length < 6) {
      newErrors.password = 'Password must be at least 6 characters';
    }

    if (formData.password !== formData.confirmPassword) {
      newErrors.confirmPassword = 'Passwords do not match';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleRegister = async (): Promise<void> => {
    if (!validateForm()) return;

    try {
      // Prepare data for API
      const userData: RegisterRequest = {
        fullName: formData.fullName,
        password: formData.password,
        role: formData.role,
      };

      // Add email or phone conditionally
      if (formData.email) userData.email = formData.email;
      if (formData.phone) userData.phone = formData.phone;

      // Register user using the useAuth hook
      await signUp(userData);

      // No need to navigate - the app navigator will handle this automatically
      // based on the updated auth state
    } catch (error: unknown) {
      console.log('error', error);
      // Type guard for error handling
      let errorMessage = 'Registration failed. Please try again.';

      if (error && typeof error === 'object' && 'data' in error) {
        const apiError = error.data as { message?: string };
        if (apiError && apiError.message) {
          errorMessage = apiError.message;
        }
      }

      Alert.alert('Registration Error', errorMessage);
    }
  };

  return (
    <KeyboardAvoidingView
      behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
      className="flex-1 bg-white">
      <ScrollView className="flex-1 px-6">
        <View className="mb-8 mt-16">
          <Text className="text-3xl font-bold text-gray-800">Create Account</Text>
          <Text className="mt-2 text-gray-500">
            Join our platform to find the perfect job opportunities
          </Text>
        </View>

        {/* Full Name */}
        <View className="mb-4">
          <Text className="mb-2 font-medium text-gray-700">Full Name</Text>
          <TextInput
            className={`rounded-lg border bg-gray-50 p-4 ${
              errors.fullName ? 'border-red-500' : 'border-gray-200'
            }`}
            placeholder="Enter your full name"
            value={formData.fullName}
            onChangeText={(text) => handleChange('fullName', text)}
          />
          {errors.fullName && <Text className="mt-1 text-red-500">{errors.fullName}</Text>}
        </View>

        {/* Email */}
        <View className="mb-4">
          <Text className="mb-2 font-medium text-gray-700">Email</Text>
          <TextInput
            className={`rounded-lg border bg-gray-50 p-4 ${
              errors.email ? 'border-red-500' : 'border-gray-200'
            }`}
            placeholder="Enter your email"
            keyboardType="email-address"
            autoCapitalize="none"
            value={formData.email}
            onChangeText={(text) => handleChange('email', text)}
          />
          {errors.email && <Text className="mt-1 text-red-500">{errors.email}</Text>}
        </View>

        {/* Phone */}
        <View className="mb-4">
          <Text className="mb-2 font-medium text-gray-700">Phone Number</Text>
          <TextInput
            className={`rounded-lg border bg-gray-50 p-4 ${
              errors.phone ? 'border-red-500' : 'border-gray-200'
            }`}
            placeholder="Enter your phone number"
            keyboardType="phone-pad"
            value={formData.phone}
            onChangeText={(text) => handleChange('phone', text)}
            editable={!initialPhone} // Disable editing if phone came from OTP
          />
          {errors.phone && <Text className="mt-1 text-red-500">{errors.phone}</Text>}
        </View>

        {/* Password */}
        <View className="mb-4">
          <Text className="mb-2 font-medium text-gray-700">Password</Text>
          <View className="relative">
            <TextInput
              className={`rounded-lg border bg-gray-50 p-4 ${
                errors.password ? 'border-red-500' : 'border-gray-200'
              }`}
              placeholder="Create a password"
              secureTextEntry={!showPassword}
              value={formData.password}
              onChangeText={(text) => handleChange('password', text)}
            />
            <TouchableOpacity
              className="absolute right-4 top-4"
              onPress={() => setShowPassword(!showPassword)}>
              <Ionicons
                name={showPassword ? 'eye-off-outline' : 'eye-outline'}
                size={24}
                color="gray"
              />
            </TouchableOpacity>
          </View>
          {errors.password && <Text className="mt-1 text-red-500">{errors.password}</Text>}
        </View>

        {/* Confirm Password */}
        <View className="mb-4">
          <Text className="mb-2 font-medium text-gray-700">Confirm Password</Text>
          <TextInput
            className={`rounded-lg border bg-gray-50 p-4 ${
              errors.confirmPassword ? 'border-red-500' : 'border-gray-200'
            }`}
            placeholder="Confirm your password"
            secureTextEntry={!showPassword}
            value={formData.confirmPassword}
            onChangeText={(text) => handleChange('confirmPassword', text)}
          />
          {errors.confirmPassword && (
            <Text className="mt-1 text-red-500">{errors.confirmPassword}</Text>
          )}
        </View>

        {/* Role Selection */}
        <View className="mb-6">
          <Text className="mb-2 font-medium text-gray-700">I am a:</Text>
          <View className="flex-row">
            <TouchableOpacity
              className={`mr-2 flex-1 rounded-lg p-4 ${
                formData.role === 'worker' ? 'bg-blue-500' : 'bg-gray-100'
              }`}
              onPress={() => handleChange('role', 'worker')}>
              <Text
                className={`text-center font-medium ${
                  formData.role === 'worker' ? 'text-white' : 'text-gray-700'
                }`}>
                Job Seeker
              </Text>
            </TouchableOpacity>

            <TouchableOpacity
              className={`ml-2 flex-1 rounded-lg p-4 ${
                formData.role === 'company' ? 'bg-blue-500' : 'bg-gray-100'
              }`}
              onPress={() => handleChange('role', 'company')}>
              <Text
                className={`text-center font-medium ${
                  formData.role === 'company' ? 'text-white' : 'text-gray-700'
                }`}>
                Employer
              </Text>
            </TouchableOpacity>
          </View>
        </View>

        {/* Register Button */}
        <TouchableOpacity
          className={`mb-4 items-center rounded-lg bg-blue-600 p-4 ${isLoading ? 'opacity-70' : ''}`}
          onPress={handleRegister}
          disabled={isLoading}>
          {isLoading ? (
            <ActivityIndicator color="white" />
          ) : (
            <Text className="text-lg font-bold text-white">Create Account</Text>
          )}
        </TouchableOpacity>

        {/* Login Link */}
        <View className="mb-8 flex-row justify-center">
          <Text className="text-gray-600">Already have an account? </Text>
          <TouchableOpacity onPress={() => navigation.navigate(SCREENS.LOGIN as any)}>
            <Text className="font-medium text-blue-600">Sign In</Text>
          </TouchableOpacity>
        </View>

        {/* OTP Login Link */}
        <View className="mb-8 flex-row justify-center">
          <Text className="text-gray-600">Prefer to login with OTP? </Text>
          <TouchableOpacity onPress={() => navigation.navigate(SCREENS.OTP_LOGIN as any)}>
            <Text className="font-medium text-blue-600">Use OTP</Text>
          </TouchableOpacity>
        </View>
      </ScrollView>
    </KeyboardAvoidingView>
  );
};

export default RegisterScreen;
