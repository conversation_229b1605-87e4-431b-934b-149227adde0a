import { useState } from 'react';
import {
  View,
  Text,
  TextInput,
  TouchableOpacity,
  Image,
  ActivityIndicator,
  Alert,
} from 'react-native';
import { SCREENS } from '../../constants/screens';
import type { LoginRequest } from '../../types/api';
import { navigateToAuth } from '../../utils/navigation';
import { getErrorMessage } from '../../utils/apiErrorHandling';
import { useAuth } from '../../providers/AuthRTKProvider';

const LoginScreen = () => {
  const { signIn, isLoading } = useAuth();

  const [email, setEmail] = useState<string>('');
  const [password, setPassword] = useState<string>('');

  const handleLogin = async (): Promise<void> => {
    if (!email && !password) {
      Alert.alert('Error', 'Please fill in all fields');
      return;
    }

    try {
      // Create login request object
      const loginRequest: LoginRequest = {
        password,
      };

      // Add either email or phone depending on what was provided
      if (email.includes('@')) {
        loginRequest.email = email;
      } else {
        loginRequest.phone = email;
      }

      // Call the signIn method from useAuth
      await signIn(loginRequest);

      // No need to navigate - the app navigator will handle this automatically
      // based on the updated auth state
    } catch (error) {
      // Handle error with our utility function
      Alert.alert('Login Failed', getErrorMessage(error, 'Invalid credentials'));
    }
  };

  return (
    <View className="flex-1 bg-white p-6">
      <View className="mb-10 mt-10 items-center">
        <Image
          source={require('../../../assets/logo.png')}
          className="h-32 w-32"
          resizeMode="contain"
        />
        <Text className="mt-4 text-2xl font-bold">Welcome Back</Text>
        <Text className="mt-2 text-gray-500">Sign in to continue</Text>
      </View>

      <View className="space-y-4">
        <View>
          <Text className="mb-2 font-medium text-gray-700">Email</Text>
          <TextInput
            className="rounded-lg bg-gray-100 p-4"
            placeholder="Enter your email"
            value={email}
            onChangeText={setEmail}
            keyboardType="email-address"
            autoCapitalize="none"
          />
        </View>

        <View>
          <Text className="mb-2 font-medium text-gray-700">Password</Text>
          <TextInput
            className="rounded-lg bg-gray-100 p-4"
            placeholder="Enter your password"
            value={password}
            onChangeText={setPassword}
            secureTextEntry
          />
        </View>

        <TouchableOpacity
          onPress={() => navigateToAuth(SCREENS.FORGOT_PASSWORD)}
          className="self-end">
          <Text className="font-medium text-indigo-600">Forgot Password?</Text>
        </TouchableOpacity>

        <TouchableOpacity
          onPress={handleLogin}
          disabled={isLoading}
          className={`items-center rounded-lg bg-indigo-600 p-4 ${isLoading ? 'opacity-70' : ''}`}>
          {isLoading ? (
            <ActivityIndicator color="white" />
          ) : (
            <Text className="text-lg font-bold text-white">Sign In</Text>
          )}
        </TouchableOpacity>

        <View className="mt-6 flex-row justify-center">
          <Text className="text-gray-600">Don't have an account? </Text>
          <TouchableOpacity onPress={() => navigateToAuth(SCREENS.REGISTER)}>
            <Text className="font-medium text-indigo-600">Sign Up</Text>
          </TouchableOpacity>
        </View>
      </View>
    </View>
  );
};

export default LoginScreen;
