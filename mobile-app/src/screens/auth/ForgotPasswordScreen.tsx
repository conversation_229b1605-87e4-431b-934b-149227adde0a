import { useState } from 'react';
import {
  View,
  Text,
  TextInput,
  TouchableOpacity,
  Image,
  ActivityIndicator,
  Alert,
  KeyboardAvoidingView,
  Platform,
  ScrollView,
} from 'react-native';
import { getErrorMessage } from '../../utils/apiErrorHandling';
import { goBack, navigateToAuth } from '../../utils/navigation';
import { SCREENS } from '../../constants/screens';
import { useAuth } from '../../hooks/use-auth';

const ForgotPasswordScreen = () => {
  const { forgotPassword, isForgotPasswordLoading } = useAuth();
  const [emailOrPhone, setEmailOrPhone] = useState<string>('');
  const [submitted, setSubmitted] = useState<boolean>(false);

  const handleSubmit = async () => {
    if (!emailOrPhone) {
      Alert.alert('Error', 'Please enter your email or phone number');
      return;
    }

    try {
      // Call the forgotPassword method from useAuth
      // The useAuth hook handles the email vs phone logic internally
      await forgotPassword(emailOrPhone);

      // Show success message
      setSubmitted(true);
    } catch (error) {
      Alert.alert(
        'Error',
        getErrorMessage(error, 'Failed to process your request. Please try again.')
      );
    }
  };

  if (submitted) {
    return (
      <View className="flex-1 items-center justify-center bg-white p-6">
        <Image
          source={require('../../../assets/check-circle.png')}
          className="mb-6 h-24 w-24"
          resizeMode="contain"
        />
        <Text className="mb-4 text-center text-2xl font-bold">Check Your Inbox</Text>
        <Text className="mb-8 text-center text-gray-600">
          We've sent password reset instructions to {emailOrPhone}
        </Text>
        <TouchableOpacity
          onPress={() => navigateToAuth(SCREENS.LOGIN)}
          className="mb-4 w-full items-center rounded-lg bg-indigo-600 p-4">
          <Text className="text-lg font-bold text-white">Back to Login</Text>
        </TouchableOpacity>
        <TouchableOpacity onPress={() => setSubmitted(false)}>
          <Text className="font-medium text-indigo-600">Try another email/phone</Text>
        </TouchableOpacity>
      </View>
    );
  }

  return (
    <KeyboardAvoidingView
      behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
      className="flex-1">
      <ScrollView className="flex-1 bg-white p-6">
        <TouchableOpacity onPress={goBack} className="mb-4">
          <Text className="font-medium text-indigo-600">← Back</Text>
        </TouchableOpacity>

        <View className="mb-10 mt-6 items-center">
          <Image
            source={require('../../../assets/logo.png')}
            className="h-32 w-32"
            resizeMode="contain"
          />
          <Text className="mt-4 text-2xl font-bold">Forgot Password</Text>
          <Text className="mt-2 text-center text-gray-500">
            Enter your email or phone number and we'll send you instructions to reset your password
          </Text>
        </View>

        <View className="space-y-4">
          <View>
            <Text className="mb-2 font-medium text-gray-700">Email or Phone</Text>
            <TextInput
              className="rounded-lg bg-gray-100 p-4"
              placeholder="Enter your email or phone number"
              value={emailOrPhone}
              onChangeText={setEmailOrPhone}
              keyboardType="email-address"
              autoCapitalize="none"
            />
          </View>

          <TouchableOpacity
            onPress={handleSubmit}
            disabled={isForgotPasswordLoading}
            className={`items-center rounded-lg bg-indigo-600 p-4 ${
              isForgotPasswordLoading ? 'opacity-70' : ''
            }`}>
            {isForgotPasswordLoading ? (
              <ActivityIndicator color="white" />
            ) : (
              <Text className="text-lg font-bold text-white">Send Reset Instructions</Text>
            )}
          </TouchableOpacity>

          <View className="mt-6 flex-row justify-center">
            <Text className="text-gray-600">Remember your password? </Text>
            <TouchableOpacity onPress={() => navigateToAuth(SCREENS.LOGIN)}>
              <Text className="font-medium text-indigo-600">Sign In</Text>
            </TouchableOpacity>
          </View>
        </View>
      </ScrollView>
    </KeyboardAvoidingView>
  );
};

export default ForgotPasswordScreen;
