'use client';

import { useState, useCallback } from 'react';
import { View, Text, FlatList, RefreshControl, Alert, ListRenderItem } from 'react-native';
import {
  AlertTriangle,
  ArrowRight,
  Clock,
  MapPin,
  DollarSign,
  Laptop,
  Smartphone,
} from 'lucide-react-native';
import { useAuth } from '@/providers/AuthRTKProvider';
import { TrustScoreIndicator } from '@/components/TrustScoreIndicator';
import { Button } from '@/components/ui/button';
import { Card } from '@/components/ui/card';
import { Skeleton } from '@/components/ui/skeleton';
import { Badge } from '@/components/ui/badge';
import { formatCurrency, formatDate, formatTime } from '@/utils/formatters';
import { useTypedNavigation } from '@/types/navigation';
import { useGetEmergencyJobsQuery, useApplyForJobMutation } from '@/store/api/jobsApi';
import type { Job } from '@/types/api';

export default function EmergencyJobsScreen() {
  const navigation = useTypedNavigation();
  const { user } = useAuth();
  const [refreshing, setRefreshing] = useState<boolean>(false);

  // Use RTK Query to fetch emergency jobs
  const {
    data: jobsResponse,
    isLoading,
    isFetching,
    refetch,
    error,
  } = useGetEmergencyJobsQuery({
    isEmergencyJob: true,
    status: 'open',
  });

  // Extract jobs from response or use empty array as fallback
  const jobs = jobsResponse?.data?.jobs || [];
  const loading = isLoading;

  // Use RTK Query mutation to apply for a job
  const [applyForJob, { isLoading: isApplying }] = useApplyForJobMutation();

  const handleRefresh = useCallback(() => {
    setRefreshing(true);
    refetch().finally(() => setRefreshing(false));
  }, [refetch]);

  const handleApply = useCallback(
    async (jobId: string) => {
      try {
        await applyForJob({
          jobId,
          isEmergencyJob: true,
        }).unwrap();

        Alert.alert(
          'Application Submitted',
          'Your application for this emergency job has been submitted successfully.',
          [
            {
              text: 'OK',
              onPress: () => navigation.navigate('Applications'),
            },
          ]
        );
      } catch (error) {
        console.error('Error applying for job:', error);
        Alert.alert('Error', 'Failed to submit application. Please try again.');
      }
    },
    [applyForJob, navigation]
  );

  const renderJobItem: ListRenderItem<Job> = useCallback(
    ({ item }) => {
      const userEligible = user.trustScore <= 40; // Emergency jobs are for users with trust score <= 40

      return (
        <Card className="mb-4">
          <View className="p-4">
            <View className="mb-2 flex-row items-start justify-between">
              <View className="flex-1">
                <Text className="mb-1 text-lg font-bold">{item.title}</Text>
                <View className="mb-1 flex-row items-center">
                  <MapPin size={16} className="mr-1 text-gray-500" />
                  <Text className="text-sm text-gray-500">{item.location}</Text>
                </View>
              </View>
              <Badge variant="destructive" className="flex-row items-center">
                <AlertTriangle size={12} className="mr-1" />
                <Text className="text-xs font-medium">Emergency</Text>
              </Badge>
            </View>

            <View className="my-2 border-t border-gray-200" />

            <View className="mb-3 flex-row flex-wrap justify-between">
              <View className="mb-2 mr-4 flex-row items-center">
                <Clock size={16} className="mr-1 text-gray-500" />
                <Text className="text-sm">
                  {formatDate(item.startDateTime)} • {formatTime(item.startDateTime)}
                </Text>
              </View>
              <View className="mb-2 flex-row items-center">
                <DollarSign size={16} className="mr-1 text-gray-500" />
                <Text className="text-sm font-semibold">{formatCurrency(item.paymentAmount)}</Text>
              </View>
            </View>

            <View className="mb-3 flex-row flex-wrap">
              {item.requiresLaptop && (
                <View className="mb-2 mr-2 flex-row items-center rounded-full bg-gray-100 px-3 py-1">
                  <Laptop size={14} className="mr-1" />
                  <Text className="text-xs">Laptop Required</Text>
                </View>
              )}
              {item.requiresSmartphone && (
                <View className="mb-2 mr-2 flex-row items-center rounded-full bg-gray-100 px-3 py-1">
                  <Smartphone size={14} className="mr-1" />
                  <Text className="text-xs">Smartphone Required</Text>
                </View>
              )}
            </View>

            <View className="my-2 border-t border-gray-200" />

            <View className="flex-row items-center justify-between">
              <View>
                <Text className="mb-1 text-xs text-gray-500">Trust Score Boost</Text>
                <Badge variant="outline" className="bg-green-50">
                  <Text className="text-xs text-green-700">+10 points on completion</Text>
                </Badge>
              </View>

              <Button
                onPress={() => handleApply(item.id)}
                disabled={!userEligible || isApplying}
                className={!userEligible || isApplying ? 'opacity-50' : ''}>
                <Text className="mr-1 text-white">Apply Now</Text>
                <ArrowRight size={16} color="white" />
              </Button>
            </View>

            {!userEligible && (
              <Text className="mt-2 text-center text-xs text-red-500">
                Emergency jobs are only available for workers with trust score below 40.
              </Text>
            )}
          </View>
        </Card>
      );
    },
    [handleApply, isApplying, user.trustScore]
  );

  const renderEmptyState = useCallback(() => {
    if (error) {
      return (
        <View className="flex-1 items-center justify-center p-4">
          <AlertTriangle size={48} className="mb-4 text-red-500" />
          <Text className="mb-2 text-center text-lg font-bold">Error Loading Jobs</Text>
          <Text className="mb-4 text-center text-gray-500">
            There was a problem loading emergency jobs. Please check your connection and try again.
          </Text>
          <Button variant="outline" onPress={handleRefresh}>
            Try Again
          </Button>
        </View>
      );
    }

    return (
      <View className="flex-1 items-center justify-center p-4">
        <AlertTriangle size={48} className="mb-4 text-gray-400" />
        <Text className="mb-2 text-center text-lg font-bold">No Emergency Jobs Available</Text>
        <Text className="mb-4 text-center text-gray-500">
          There are currently no emergency jobs available. Check back later for opportunities to
          rebuild your trust score.
        </Text>
        <Button variant="outline" onPress={handleRefresh}>
          Refresh
        </Button>
      </View>
    );
  }, [error, handleRefresh]);

  if (loading && !refreshing) {
    return (
      <View className="flex-1 p-4">
        <View className="mb-4">
          <Skeleton className="mb-2 h-6 w-3/4 rounded-md" />
          <Skeleton className="h-4 w-1/2 rounded-md" />
        </View>
        {[1, 2, 3].map((_, index) => (
          <View key={index} className="mb-4 rounded-lg border border-gray-200 p-4">
            <Skeleton className="mb-3 h-5 w-3/4 rounded-md" />
            <Skeleton className="mb-3 h-4 w-1/2 rounded-md" />
            <View className="mb-3 flex-row justify-between">
              <Skeleton className="h-4 w-1/3 rounded-md" />
              <Skeleton className="h-4 w-1/4 rounded-md" />
            </View>
            <Skeleton className="h-10 w-full rounded-md" />
          </View>
        ))}
      </View>
    );
  }

  return (
    <View className="flex-1 p-4">
      <View className="mb-4 rounded-lg bg-red-50 p-4">
        <View className="mb-2 flex-row items-center">
          <AlertTriangle size={20} color="#ef4444" className="mr-2" />
          <Text className="font-bold text-red-700">Emergency Jobs</Text>
        </View>
        <Text className="mb-2 text-sm text-red-700">
          These jobs are specifically available for workers with trust scores below 40 to help
          rebuild your reputation.
        </Text>
        <View className="flex-row items-center">
          <Text className="mr-2 text-sm text-red-700">Your Trust Score:</Text>
          <TrustScoreIndicator score={user.trustScore} size="sm" />
        </View>
      </View>

      <FlatList
        data={jobs}
        renderItem={renderJobItem}
        keyExtractor={(item) => item.id}
        ListEmptyComponent={renderEmptyState}
        refreshControl={<RefreshControl refreshing={refreshing} onRefresh={handleRefresh} />}
        contentContainerStyle={{ flexGrow: 1 }}
      />
    </View>
  );
}
