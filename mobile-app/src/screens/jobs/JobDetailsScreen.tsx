'use client';

import {
  View,
  Text,
  ScrollView,
  TouchableOpacity,
  ActivityIndicator,
  Alert,
  Image,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { format } from 'date-fns';
import MapView, { Marker } from 'react-native-maps';
import { useTheme } from '../../hooks/use-theme';

// Import Redux hooks
import { useAppSelector } from '../../store';
import {
  useGetJobByIdQuery,
  useSaveJobMutation,
  useUnsaveJobMutation,
  useIsJobSavedQuery,
} from '../../store/api/jobsApi';
import {
  useGetApplicationStatusQuery,
  useApplyForJobMutation,
} from '../../store/api/applicationsApi';
import { useTypedNavigation, useTypedRoute } from '@/types/navigation';
import { SCREENS } from '@/constants/screens';

export default function JobDetailsScreen() {
  const route = useTypedRoute<typeof SCREENS.JOB_DETAILS>();
  const navigation = useTypedNavigation();
  const { jobId } = route.params;
  const { colors } = useTheme();

  // Get user from Redux store
  const { user, isAuthenticated } = useAppSelector((state) => state.auth);

  // Use RTK Query hooks
  const { data: jobResponse, error, isLoading, refetch } = useGetJobByIdQuery(jobId);
  const { data: savedStatusResponse, refetch: refetchSavedStatus } = useIsJobSavedQuery(jobId, {
    skip: !isAuthenticated,
  });
  const { data: applicationStatusResponse } = useGetApplicationStatusQuery(jobId, {
    skip: !isAuthenticated,
  });
  const [saveJob] = useSaveJobMutation();
  const [unsaveJob] = useUnsaveJobMutation();
  const [applyForJob, { isLoading: applying }] = useApplyForJobMutation();

  // Extract data from response objects
  const job = jobResponse?.data;
  const hasApplied = applicationStatusResponse?.data?.hasApplied || false;
  const isFavorite = savedStatusResponse?.data?.saved || false;

  const handleApply = async () => {
    if (!isAuthenticated) {
      Alert.alert('Authentication Required', 'Please sign in to apply for jobs', [
        { text: 'Cancel', style: 'cancel' },
        { text: 'Sign In', onPress: () => navigation.navigate(SCREENS.LOGIN) },
      ]);
      return;
    }

    try {
      // Create a simple FormData for the application
      const formData = new FormData();
      formData.append('jobId', jobId);

      await applyForJob({ jobId, formData }).unwrap();
      Alert.alert('Success', 'Your application has been submitted!');
    } catch (error: any) {
      console.error('Error applying for job:', error);
      Alert.alert('Error', error?.data?.message || 'Failed to submit application');
    }
  };

  const toggleFavorite = async () => {
    if (!isAuthenticated) {
      Alert.alert('Authentication Required', 'Please sign in to save jobs', [
        { text: 'Cancel', style: 'cancel' },
        { text: 'Sign In', onPress: () => navigation.navigate(SCREENS.LOGIN) },
      ]);
      return;
    }

    try {
      if (isFavorite) {
        await unsaveJob(jobId).unwrap();
      } else {
        await saveJob(jobId).unwrap();
      }
      refetchSavedStatus();
    } catch (error: any) {
      console.error('Error toggling favorite:', error);
      Alert.alert('Error', 'Failed to update saved status');
    }
  };

  if (isLoading) {
    return (
      <View className="flex-1 items-center justify-center bg-white">
        <ActivityIndicator size="large" color={colors.primary} />
      </View>
    );
  }

  if (error || !jobResponse || !job) {
    return (
      <View className="flex-1 items-center justify-center bg-white p-4">
        <Text className="text-center text-lg">
          {error ? 'Error loading job details.' : 'Job not found or has been removed.'}
        </Text>
        <TouchableOpacity
          className="mt-4 rounded-full bg-primary px-6 py-3"
          onPress={() => (error ? refetch() : navigation.goBack())}>
          <Text className="font-medium text-white">{error ? 'Retry' : 'Go Back'}</Text>
        </TouchableOpacity>
      </View>
    );
  }

  const canApply = (user?.trustScore ?? 0) >= job.trustScoreRequired || job.isEmergencyJob;

  return (
    <View className="flex-1 bg-white">
      <ScrollView className="flex-1">
        {/* Header */}
        <View className="bg-primary p-4">
          <View className="flex-row items-center justify-between">
            <TouchableOpacity onPress={() => navigation.goBack()} className="p-2">
              <Ionicons name="arrow-back" size={24} color="white" />
            </TouchableOpacity>
            <TouchableOpacity onPress={toggleFavorite} className="p-2">
              <Ionicons name={isFavorite ? 'heart' : 'heart-outline'} size={24} color="white" />
            </TouchableOpacity>
          </View>
          <Text className="mt-2 text-2xl font-bold text-white">{job.title}</Text>
          <Text className="mt-1 text-white opacity-80">
            {job.company?.companyName || job.company?.fullName}
          </Text>

          <View className="mt-2 flex-row items-center">
            <Ionicons name="location-outline" size={16} color="white" />
            <Text className="ml-1 text-white">
              {job.locationCity}, {job.locationCountry}
            </Text>
          </View>

          <View className="mt-4 flex-row items-center justify-between rounded-lg bg-white/10 p-3">
            <View>
              <Text className="text-white opacity-80">Hourly Rate</Text>
              <Text className="text-lg font-bold text-white">${job.payRate}/hr</Text>
            </View>
            <View>
              <Text className="text-white opacity-80">Duration</Text>
              <Text className="text-lg font-bold text-white">{job.estimatedHours} hours</Text>
            </View>
            <View>
              <Text className="text-white opacity-80">Required Score</Text>
              <Text className="text-lg font-bold text-white">{job.trustScoreRequired}</Text>
            </View>
          </View>
        </View>

        {/* Job Details */}
        <View className="p-4">
          <View className="mb-4 flex-row items-center justify-between">
            <View className="flex-row items-center">
              <Ionicons name="calendar-outline" size={20} color={colors.gray[600]} />
              <Text className="ml-2 text-gray-600">
                {format(new Date(job.startDateTime), 'MMM d, yyyy')} -{' '}
                {format(new Date(job.endDateTime), 'MMM d, yyyy')}
              </Text>
            </View>
            {job.isEmergencyJob && (
              <View className="rounded-full bg-red-100 px-3 py-1">
                <Text className="font-medium text-red-600">Emergency</Text>
              </View>
            )}
          </View>

          <Text className="mb-2 text-lg font-bold">Description</Text>
          <Text className="mb-4 text-gray-700">{job.description}</Text>

          <Text className="mb-2 text-lg font-bold">Location</Text>
          <Text className="mb-2 text-gray-700">{job.locationAddress}</Text>

          {job.latitude && job.longitude && (
            <View className="mb-4 h-40 overflow-hidden rounded-lg">
              <MapView
                style={{ flex: 1 }}
                initialRegion={{
                  latitude: job.latitude,
                  longitude: job.longitude,
                  latitudeDelta: 0.01,
                  longitudeDelta: 0.01,
                }}>
                <Marker
                  coordinate={{ latitude: job.latitude, longitude: job.longitude }}
                  title={job.title}
                />
              </MapView>
            </View>
          )}

          <Text className="mb-2 text-lg font-bold">Requirements</Text>
          <View className="mb-4">
            {job.requiresLaptop && (
              <View className="mb-2 flex-row items-center">
                <Ionicons name="laptop-outline" size={20} color={colors.gray[600]} />
                <Text className="ml-2 text-gray-700">Laptop required</Text>
              </View>
            )}
            {job.requiresSmartphone && (
              <View className="mb-2 flex-row items-center">
                <Ionicons name="phone-portrait-outline" size={20} color={colors.gray[600]} />
                <Text className="ml-2 text-gray-700">Smartphone required</Text>
              </View>
            )}
            {job.skillsRequired && (
              <View className="mb-2 flex-row items-center">
                <Ionicons name="construct-outline" size={20} color={colors.gray[600]} />
                <Text className="ml-2 text-gray-700">{job.skillsRequired}</Text>
              </View>
            )}
          </View>

          <Text className="mb-2 text-lg font-bold">About the Company</Text>
          <View className="mb-4 flex-row items-center">
            {job.company?.profilePic ? (
              <Image source={{ uri: job.company.profilePic }} className="h-12 w-12 rounded-full" />
            ) : (
              <View className="h-12 w-12 items-center justify-center rounded-full bg-gray-200">
                <Ionicons name="business-outline" size={24} color={colors.gray[400]} />
              </View>
            )}
            <View className="ml-3">
              <Text className="font-bold">{job.company?.companyName || job.company?.fullName}</Text>
              {job.company?.averageRating && (
                <View className="flex-row items-center">
                  <Ionicons name="star" size={16} color="#FFD700" />
                  <Text className="ml-1 text-gray-600">
                    {job.company.averageRating?.toFixed(1)}
                  </Text>
                </View>
              )}
            </View>
          </View>
        </View>
      </ScrollView>

      {/* Apply Button */}
      <View className="border-t border-gray-200 bg-white p-4">
        {!canApply && (
          <View className="mb-3 flex-row items-center rounded-lg bg-yellow-50 p-3">
            <Ionicons name="warning-outline" size={20} color="#F59E0B" />
            <Text className="ml-2 text-yellow-700">
              Your trust score ({user?.trustScore}) is below the required level (
              {job.trustScoreRequired})
            </Text>
          </View>
        )}

        {hasApplied ? (
          <TouchableOpacity className="items-center rounded-lg bg-gray-200 py-3" disabled={true}>
            <Text className="font-bold text-gray-600">Already Applied</Text>
          </TouchableOpacity>
        ) : (
          <TouchableOpacity
            className={`items-center rounded-lg py-3 ${canApply ? 'bg-primary' : 'bg-gray-300'}`}
            onPress={handleApply}
            disabled={!canApply || applying}>
            {applying ? (
              <ActivityIndicator color="white" />
            ) : (
              <Text className={`font-bold ${canApply ? 'text-white' : 'text-gray-500'}`}>
                Apply Now
              </Text>
            )}
          </TouchableOpacity>
        )}
      </View>
    </View>
  );
}
