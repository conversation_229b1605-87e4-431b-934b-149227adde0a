import React, { useState, useEffect, useCallback } from 'react';
import {
  View,
  Text,
  FlatList,
  TouchableOpacity,
  ActivityIndicator,
  RefreshControl,
  Alert,
  ListRenderItem,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import Slider from '@react-native-community/slider';
import { SCREENS } from '@/constants/screens';
import JobCard from '@/components/JobCard';
import EmptyState from '@/components/EmptyState';
import LocationService from '@/services/LocationService';
import { useTypedNavigation } from '@/types/navigation';
import { useGetNearbyJobsQuery } from '@/store/api/jobsApi';
import type { Job } from '@/types/api';

const NearbyJobsScreen = () => {
  const navigation = useTypedNavigation();
  const [maxDistance, setMaxDistance] = useState<number>(10); // Default to 10km
  const [hasLocationPermission, setHasLocationPermission] = useState<boolean>(false);

  // Check location permission on mount
  useEffect(() => {
    checkLocationPermission();
  }, []);

  const checkLocationPermission = async () => {
    try {
      const hasPermission = await LocationService.hasLocationPermission();

      if (hasPermission) {
        setHasLocationPermission(true);
      } else {
        const granted = await LocationService.requestLocationPermission();
        setHasLocationPermission(granted);

        if (!granted) {
          Alert.alert(
            'Location Permission Required',
            'We need your location to find jobs near you. Please enable location services in your device settings.',
            [{ text: 'OK' }]
          );
        }
      }
    } catch (error) {
      console.error('Error checking location permission:', error);
      setHasLocationPermission(false);
    }
  };

  // Use RTK Query to fetch nearby jobs
  const {
    data: jobsResponse,
    isLoading,
    isFetching,
    refetch,
    error,
  } = useGetNearbyJobsQuery(
    { maxDistance },
    {
      skip: !hasLocationPermission,
    }
  );

  const jobs = jobsResponse?.data?.jobs || [];
  const loading = isLoading;
  const refreshing = isFetching && !isLoading;

  const handleRefresh = useCallback(() => {
    refetch();
  }, [refetch]);

  const handleJobPress = useCallback(
    (job: Job) => {
      navigation.navigate(SCREENS.JOB_DETAILS, { jobId: job.id });
    },
    [navigation]
  );

  const renderJobItem: ListRenderItem<Job> = useCallback(
    ({ item }) => <JobCard job={item} onPress={() => handleJobPress(item)} />,
    [handleJobPress]
  );

  if (!hasLocationPermission) {
    return (
      <View className="flex-1 bg-white">
        <View className="bg-indigo-600 px-4 pb-4 pt-12">
          <TouchableOpacity
            className="mb-2 flex-row items-center"
            onPress={() => navigation.goBack()}>
            <Ionicons name="arrow-back" size={24} color="white" />
            <Text className="ml-2 text-white">Back</Text>
          </TouchableOpacity>
          <Text className="text-2xl font-bold text-white">Nearby Jobs</Text>
          <Text className="text-indigo-200">Find jobs close to your location</Text>
        </View>

        <EmptyState
          title="Location Permission Required"
          message="We need your location to find jobs near you. Please enable location services."
          icon="location-outline"
          action={{
            label: 'Grant Permission',
            onPress: checkLocationPermission,
          }}
        />
      </View>
    );
  }

  return (
    <View className="flex-1 bg-gray-50">
      <View className="bg-indigo-600 px-4 pb-4 pt-12">
        <TouchableOpacity
          className="mb-2 flex-row items-center"
          onPress={() => navigation.goBack()}>
          <Ionicons name="arrow-back" size={24} color="white" />
          <Text className="ml-2 text-white">Back</Text>
        </TouchableOpacity>
        <Text className="text-2xl font-bold text-white">Nearby Jobs</Text>
        <Text className="text-indigo-200">Find jobs close to your location</Text>
      </View>

      <View className="border-b border-gray-200 bg-white p-4">
        <View className="mb-2 flex-row items-center justify-between">
          <Text className="font-medium text-gray-700">Distance</Text>
          <Text className="text-gray-700">{maxDistance} km</Text>
        </View>
        <Slider
          value={maxDistance}
          onValueChange={setMaxDistance}
          minimumValue={1}
          maximumValue={50}
          step={1}
          minimumTrackTintColor="#4f46e5"
          maximumTrackTintColor="#d1d5db"
          thumbTintColor="#4f46e5"
        />
        <View className="flex-row justify-between">
          <Text className="text-xs text-gray-500">1 km</Text>
          <Text className="text-xs text-gray-500">50 km</Text>
        </View>
      </View>

      {loading && !refreshing ? (
        <View className="flex-1 items-center justify-center bg-white">
          <ActivityIndicator size="large" color="#4f46e5" />
        </View>
      ) : error ? (
        <View className="flex-1 items-center justify-center p-4">
          <EmptyState
            title="Error Loading Jobs"
            message="There was a problem loading nearby jobs. Please check your connection and try again."
            icon="alert-circle-outline"
            action={{
              label: 'Try Again',
              onPress: refetch,
            }}
          />
        </View>
      ) : (
        <FlatList
          className="flex-1 p-4"
          data={jobs}
          renderItem={renderJobItem}
          keyExtractor={(item) => item.id}
          refreshControl={<RefreshControl refreshing={refreshing} onRefresh={handleRefresh} />}
          ListEmptyComponent={
            <EmptyState
              title="No Nearby Jobs"
              message={`No jobs found within ${maxDistance} km of your location`}
              icon="location-outline"
            />
          }
        />
      )}
    </View>
  );
};

export default NearbyJobsScreen;
