'use client';

import { useState, useCallback } from 'react';
import {
  View,
  Text,
  FlatList,
  RefreshControl,
  ActivityIndicator,
  TouchableOpacity,
  ListRenderItem,
} from 'react-native';
import { useFocusEffect } from '@react-navigation/native';
import { Ionicons } from '@expo/vector-icons';
import { useGetJobsQuery } from '../../store/api/jobsApi';
import { useAppSelector } from '../../store';
import JobCard from '../../components/JobCard';
import SearchBar from '../../components/SearchBar';
import FilterModal from '../../components/FilterModal';
import EmptyState from '../../components/EmptyState';
import { useTheme } from '../../hooks/use-theme';
import { SCREENS } from '../../constants/screens';
import { useTypedNavigation } from '../../types/navigation';
import type { Job, JobsQueryParams } from '../../types/api';

interface JobFilters extends JobsQueryParams {
  trustScoreMin: number;
  trustScoreMax: number;
  isEmergency: boolean;
  city: string;
}

export default function JobsScreen() {
  const navigation = useTypedNavigation();
  const { user } = useAppSelector((state) => state.auth);
  const { colors } = useTheme();
  const [searchQuery, setSearchQuery] = useState<string>('');
  const [filterModalVisible, setFilterModalVisible] = useState<boolean>(false);
  const [filters, setFilters] = useState<JobFilters>({
    trustScoreMin: 0,
    trustScoreMax: 100,
    isEmergency: false,
    city: '',
  });

  // Convert filters to API query params
  const queryParams: JobsQueryParams = {
    search: searchQuery || undefined,
    // Add trustScore as a custom parameter
    // ...(user?.trustScore ? { trustScoreRequired: user.trustScore } : {}),
    isEmergency: filters.isEmergency || undefined,
    city: filters.city || undefined,
    // These might need to be adjusted based on actual API parameters
    ...(filters.trustScoreMin > 0 ? { minTrustScore: filters.trustScoreMin } : {}),
    ...(filters.trustScoreMax < 100
      ? { maxTrustScore: filters.trustScoreMax ?? user?.trustScore }
      : {}),
    ...{ maxTrustScore: filters.trustScoreMax ?? user?.trustScore },
  };

  // Use RTK Query hook to fetch jobs
  const {
    data: { data: jobsResponse = [], meta } = {},
    isLoading: loading,
    isFetching,
    refetch,
  } = useGetJobsQuery(queryParams);

  const jobs = jobsResponse || [];
  const refreshing = isFetching && !loading;

  // Refetch jobs when screen comes into focus
  useFocusEffect(
    useCallback(() => {
      refetch();
    }, [refetch])
  );

  const handleRefresh = () => {
    refetch();
  };

  const handleSearch = (query: string) => {
    setSearchQuery(query);
  };

  const handleFilterApply = (newFilters: JobFilters) => {
    setFilters(newFilters);
    setFilterModalVisible(false);
  };

  const renderItem: ListRenderItem<Job> = ({ item }) => (
    <JobCard
      job={item}
      onPress={() => navigation.navigate(SCREENS.JOB_DETAILS, { jobId: item.id })}
    />
  );

  return (
    <View className="flex-1 bg-white">
      <View className="flex-row items-center justify-between bg-white px-4 pb-2 pt-12">
        <Text className="text-2xl font-bold">Available Jobs</Text>
        <View className="flex-row">
          <TouchableOpacity
            onPress={() => navigation.navigate(SCREENS.NEARBY_JOBS)}
            className="mr-2 p-2">
            <Ionicons name="location-outline" size={24} color={colors.primary} />
          </TouchableOpacity>
          <TouchableOpacity onPress={() => setFilterModalVisible(true)} className="p-2">
            <Ionicons name="options-outline" size={24} color={colors.primary} />
          </TouchableOpacity>
        </View>
      </View>

      <SearchBar
        placeholder="Search jobs..."
        value={searchQuery}
        onChangeText={handleSearch}
        onSubmit={() => refetch()}
      />

      {loading && !refreshing ? (
        <View className="flex-1 items-center justify-center">
          <ActivityIndicator size="large" color={colors.primary} />
        </View>
      ) : (
        <FlatList
          data={jobs}
          renderItem={renderItem}
          keyExtractor={(item) => item.id}
          contentContainerStyle={{ padding: 16, paddingBottom: 100 }}
          refreshControl={
            <RefreshControl
              refreshing={refreshing}
              onRefresh={handleRefresh}
              colors={[colors.primary]}
            />
          }
          ListEmptyComponent={
            <EmptyState
              icon="briefcase-outline"
              title="No jobs found"
              message="Try adjusting your filters or check back later for new opportunities."
            />
          }
        />
      )}

      <FilterModal
        visible={filterModalVisible}
        onClose={() => setFilterModalVisible(false)}
        filters={filters}
        onApply={handleFilterApply}
        trustScore={user?.trustScore}
      />
    </View>
  );
}
