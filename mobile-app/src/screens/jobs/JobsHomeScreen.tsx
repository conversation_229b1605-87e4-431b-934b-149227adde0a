import React, { useState, useCallback } from 'react';
import {
  View,
  Text,
  FlatList,
  TouchableOpacity,
  RefreshControl,
  ActivityIndicator,
  ListRenderItem,
} from 'react-native';
import { useFocusEffect } from '@react-navigation/native';
import { Ionicons } from '@expo/vector-icons';
import { SCREENS } from '@/constants/screens';
import JobCard from '@/components/JobCard';
import SearchBar from '@/components/SearchBar';
import FilterModal from '@/components/FilterModal';
import EmptyState from '@/components/EmptyState';
import { useTypedNavigation } from '@/types/navigation';
import { useGetJobsQuery } from '@/store/api/jobsApi';
import type { Job, JobsQueryParams } from '@/types/api';

// Mock data for fallback
const mockJobs = [
  {
    id: 'job1',
    title: 'Warehouse Associate',
    company: 'Amazon Logistics',
    location: 'Mumbai, Maharashtra',
    salary: '₹18,000 - ₹22,000/month',
    type: 'Full-time',
    postedAt: '2 days ago',
    logo: 'https://logo.clearbit.com/amazon.com',
    urgent: true,
  },
  {
    id: 'job2',
    title: 'Retail Sales Associate',
    company: 'Reliance Retail',
    location: 'Delhi, Delhi',
    salary: '₹15,000 - ₹18,000/month',
    type: 'Full-time',
    postedAt: '1 day ago',
    logo: 'https://logo.clearbit.com/ril.com',
  },
  {
    id: 'job3',
    title: 'Delivery Executive',
    company: 'Zomato',
    location: 'Bangalore, Karnataka',
    salary: '₹20,000 - ₹25,000/month',
    type: 'Full-time',
    postedAt: '3 days ago',
    logo: 'https://logo.clearbit.com/zomato.com',
  },
  {
    id: 'job4',
    title: 'Inventory Specialist',
    company: 'Flipkart',
    location: 'Hyderabad, Telangana',
    salary: '₹22,000 - ₹26,000/month',
    type: 'Full-time',
    postedAt: '5 days ago',
    logo: 'https://logo.clearbit.com/flipkart.com',
    urgent: true,
  },
  {
    id: 'job5',
    title: 'Stock Auditor',
    company: 'Big Bazaar',
    location: 'Chennai, Tamil Nadu',
    salary: '₹16,000 - ₹20,000/month',
    type: 'Part-time',
    postedAt: '1 week ago',
    logo: 'https://logo.clearbit.com/futuregroup.in',
  },
];

const JobsHomeScreen = () => {
  const navigation = useTypedNavigation();
  const [searchQuery, setSearchQuery] = useState<string>('');
  const [filterModalVisible, setFilterModalVisible] = useState<boolean>(false);
  const [filters, setFilters] = useState<Record<string, any>>({});
  const [page, setPage] = useState<number>(1);
  const [limit] = useState<number>(10);

  // Prepare query parameters for RTK Query
  const queryParams: JobsQueryParams = {
    page,
    limit,
    ...(searchQuery ? { search: searchQuery } : {}),
    ...filters,
  };

  // Use RTK Query to fetch jobs
  const {
    data: jobsResponse,
    isLoading,
    isFetching,
    refetch,
    error,
  } = useGetJobsQuery(queryParams);

  // Extract jobs from response or use mock data as fallback
  const jobs = jobsResponse?.data?.jobs || (error ? mockJobs : []);
  const hasMore = jobsResponse?.data?.hasMore || false;
  const refreshing = isFetching && !isLoading;
  const loadingMore = isFetching && !isLoading && page > 1;

  // Refetch jobs when screen comes into focus
  useFocusEffect(
    useCallback(() => {
      refetch();
    }, [refetch])
  );

  const handleRefresh = useCallback(() => {
    setPage(1);
    refetch();
  }, [refetch]);

  const handleLoadMore = useCallback(() => {
    if (hasMore && !loadingMore) {
      setPage((prevPage) => prevPage + 1);
    }
  }, [hasMore, loadingMore]);

  const handleSearch = useCallback(() => {
    setPage(1);
    refetch();
  }, [refetch]);

  const handleFilterApply = useCallback(
    (newFilters: Record<string, any>) => {
      setFilters(newFilters);
      setPage(1);
      refetch();
    },
    [refetch]
  );

  const renderFooter = () => {
    if (!loadingMore) return null;

    return (
      <View className="py-4">
        <ActivityIndicator size="small" color="#4f46e5" />
      </View>
    );
  };

  // Define the render item function with proper typing
  const renderItem: ListRenderItem<Job> = useCallback(
    ({ item }) => (
      <JobCard
        job={item}
        onPress={() => navigation.navigate(SCREENS.JOB_DETAILS, { jobId: item.id })}
      />
    ),
    [navigation]
  );

  return (
    <View className="flex-1 bg-gray-50">
      <View className="border-b border-gray-200 bg-white px-4 pb-4 pt-12">
        <View className="mb-4 flex-row items-center justify-between">
          <Text className="text-2xl font-bold text-gray-900">Find Jobs</Text>
          <TouchableOpacity
            onPress={() => navigation.navigate(SCREENS.NOTIFICATIONS)}
            className="h-10 w-10 items-center justify-center rounded-full bg-gray-100">
            <Ionicons name="notifications-outline" size={22} color="#4b5563" />
          </TouchableOpacity>
        </View>

        <SearchBar
          value={searchQuery}
          onChangeText={setSearchQuery}
          onSubmit={handleSearch}
          onFilterPress={() => setFilterModalVisible(true)}
        />
      </View>

      <FlatList
        data={jobs}
        renderItem={renderItem}
        keyExtractor={(item) => item.id}
        contentContainerStyle={{ padding: 16 }}
        refreshControl={
          <RefreshControl refreshing={refreshing} onRefresh={handleRefresh} colors={['#4f46e5']} />
        }
        ListEmptyComponent={
          isLoading ? null : (
            <EmptyState
              title="No Jobs Found"
              message="We couldn't find any jobs matching your criteria. Try adjusting your filters or search query."
              icon="search"
              action={{
                label: 'Clear Filters',
                onPress: () => {
                  setSearchQuery('');
                  setFilters({});
                  setPage(1);
                  refetch();
                },
              }}
            />
          )
        }
        ListFooterComponent={renderFooter}
        onEndReached={handleLoadMore}
        onEndReachedThreshold={0.3}
      />

      {isLoading && !refreshing && (
        <View className="absolute inset-0 items-center justify-center bg-white/70">
          <ActivityIndicator size="large" color="#4f46e5" />
        </View>
      )}

      <FilterModal
        visible={filterModalVisible}
        onClose={() => setFilterModalVisible(false)}
        onApply={handleFilterApply}
        initialFilters={filters}
      />

      <TouchableOpacity
        className="absolute bottom-6 right-6 h-14 w-14 items-center justify-center rounded-full bg-indigo-600 shadow-lg"
        onPress={() => navigation.navigate(SCREENS.EMERGENCY_JOBS)}>
        <Ionicons name="flash" size={24} color="#ffffff" />
      </TouchableOpacity>
    </View>
  );
};

export default JobsHomeScreen;
