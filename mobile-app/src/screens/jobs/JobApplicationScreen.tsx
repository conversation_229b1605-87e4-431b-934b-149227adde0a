import React, { useState } from 'react';
import {
  View,
  Text,
  ScrollView,
  TextInput,
  TouchableOpacity,
  ActivityIndicator,
  Alert,
  Switch,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import * as DocumentPicker from 'expo-document-picker';
import { ScreenName, ScreenNameType, SCREENS } from '@/constants/screens';
import { useTypedNavigation, useTypedRoute } from '@/types/navigation';
import { useApplyForJobMutation } from '@/store/api/applicationsApi';
import type { Job } from '@/types/api';

// Define route params type
interface RouteParams {
  jobId: string;
  job: Job;
}

const JobApplicationScreen = () => {
  const navigation = useTypedNavigation();
  const route = useTypedRoute<ScreenNameType['JOB_APPLICATION']>();
  const { jobId, job } = route.params;

  const [coverLetter, setCoverLetter] = useState<string>('');
  const [expectedSalary, setExpectedSalary] = useState<string>('');
  const [availableDate, setAvailableDate] = useState<string>('');
  const [resume, setResume] = useState<DocumentPicker.DocumentPickerAsset | null>(null);
  const [canWorkNights, setCanWorkNights] = useState<boolean>(false);
  const [canWorkWeekends, setCanWorkWeekends] = useState<boolean>(false);
  const [hasTransport, setHasTransport] = useState<boolean>(false);

  // Use RTK Query mutation to apply for a job
  const [applyForJob, { isLoading }] = useApplyForJobMutation();

  const pickResume = async () => {
    try {
      const result = await DocumentPicker.getDocumentAsync({
        type: [
          'application/pdf',
          'application/msword',
          'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
        ],
        copyToCacheDirectory: true,
      });

      if (!result.canceled) {
        setResume(result.assets[0]);
      }
    } catch (err) {
      console.error('Error picking document:', err);
      Alert.alert('Error', 'Failed to select document. Please try again.');
    }
  };

  const validateForm = () => {
    if (!coverLetter.trim()) {
      Alert.alert('Error', 'Please provide a cover letter');
      return false;
    }
    if (!expectedSalary.trim()) {
      Alert.alert('Error', 'Please provide your expected salary');
      return false;
    }
    if (!availableDate.trim()) {
      Alert.alert('Error', 'Please provide your availability date');
      return false;
    }
    if (!resume) {
      Alert.alert('Error', 'Please upload your resume');
      return false;
    }
    return true;
  };

  const handleSubmit = async () => {
    if (!validateForm()) return;

    try {
      const formData = new FormData();
      formData.append('coverLetter', coverLetter);
      formData.append('expectedSalary', expectedSalary);
      formData.append('availableDate', availableDate);
      formData.append('canWorkNights', canWorkNights.toString());
      formData.append('canWorkWeekends', canWorkWeekends.toString());
      formData.append('hasTransport', hasTransport.toString());

      if (resume) {
        const fileUri = resume.uri;
        const filename = resume.name;
        const mimeType = resume.mimeType;

        // @ts-ignore - FormData typing issue with React Native
        formData.append('resume', {
          uri: fileUri,
          name: filename,
          type: mimeType,
        });
      }

      // Use RTK Query mutation to apply for job
      await applyForJob({
        jobId,
        formData,
      }).unwrap();

      Alert.alert('Application Submitted', 'Your application has been submitted successfully!', [
        {
          text: 'OK',
          onPress: () => navigation.navigate(SCREENS.APPLICATIONS),
        },
      ]);
    } catch (err) {
      console.error('Error submitting application:', err);
      Alert.alert('Error', 'Failed to submit application. Please try again.');
    }
  };

  return (
    <View className="flex-1 bg-white">
      <View className="flex-row items-center border-b border-gray-200 bg-white px-4 pb-4 pt-12">
        <TouchableOpacity onPress={() => navigation.goBack()} className="mr-3">
          <Ionicons name="arrow-back" size={24} color="#4b5563" />
        </TouchableOpacity>
        <Text className="text-xl font-semibold text-gray-900">Apply for Job</Text>
      </View>

      <ScrollView className="flex-1 p-4">
        <View className="mb-6 rounded-lg bg-gray-50 p-4">
          <Text className="text-lg font-bold text-gray-900">{job.title}</Text>
          <Text className="text-gray-700">
            {typeof job.company === 'object' ? job.company.name : job.company}
          </Text>
          <Text className="text-gray-600">
            {typeof job.location === 'object' ? job.location.city : job.location}
          </Text>
        </View>

        <View className="mb-6">
          <Text className="mb-2 font-medium text-gray-700">Cover Letter</Text>
          <TextInput
            className="rounded-lg bg-gray-100 p-3 text-gray-900"
            value={coverLetter}
            onChangeText={setCoverLetter}
            placeholder="Introduce yourself and explain why you're a good fit for this position"
            multiline
            numberOfLines={6}
            textAlignVertical="top"
          />
        </View>

        <View className="mb-6">
          <Text className="mb-2 font-medium text-gray-700">Expected Salary</Text>
          <TextInput
            className="rounded-lg bg-gray-100 p-3 text-gray-900"
            value={expectedSalary}
            onChangeText={setExpectedSalary}
            placeholder="e.g. ₹20,000/month"
            keyboardType="number-pad"
          />
        </View>

        <View className="mb-6">
          <Text className="mb-2 font-medium text-gray-700">Available From</Text>
          <TextInput
            className="rounded-lg bg-gray-100 p-3 text-gray-900"
            value={availableDate}
            onChangeText={setAvailableDate}
            placeholder="e.g. Immediately, 15th June 2023"
          />
        </View>

        <View className="mb-6">
          <Text className="mb-2 font-medium text-gray-700">Resume/CV</Text>
          {resume ? (
            <View className="flex-row items-center justify-between rounded-lg bg-gray-100 p-3">
              <View className="flex-1 flex-row items-center">
                <Ionicons name="document-text" size={24} color="#4f46e5" />
                <Text className="ml-2 flex-1 text-gray-900" numberOfLines={1}>
                  {resume.name}
                </Text>
              </View>
              <TouchableOpacity onPress={() => setResume(null)}>
                <Ionicons name="close-circle" size={24} color="#6b7280" />
              </TouchableOpacity>
            </View>
          ) : (
            <TouchableOpacity
              className="flex-row items-center justify-center rounded-lg bg-gray-100 p-3"
              onPress={pickResume}>
              <Ionicons name="cloud-upload-outline" size={24} color="#4f46e5" />
              <Text className="ml-2 font-medium text-indigo-600">Upload Resume</Text>
            </TouchableOpacity>
          )}
          <Text className="mt-1 text-xs text-gray-500">
            Supported formats: PDF, DOC, DOCX (Max 5MB)
          </Text>
        </View>

        <View className="mb-6">
          <Text className="mb-4 font-medium text-gray-700">Additional Information</Text>

          <View className="mb-2 rounded-lg bg-gray-50 p-3">
            <View className="flex-row items-center justify-between">
              <Text className="text-gray-800">Can work night shifts</Text>
              <Switch
                value={canWorkNights}
                onValueChange={setCanWorkNights}
                trackColor={{ false: '#d1d5db', true: '#c7d2fe' }}
                thumbColor={canWorkNights ? '#4f46e5' : '#f3f4f6'}
              />
            </View>
          </View>

          <View className="mb-2 rounded-lg bg-gray-50 p-3">
            <View className="flex-row items-center justify-between">
              <Text className="text-gray-800">Can work weekends</Text>
              <Switch
                value={canWorkWeekends}
                onValueChange={setCanWorkWeekends}
                trackColor={{ false: '#d1d5db', true: '#c7d2fe' }}
                thumbColor={canWorkWeekends ? '#4f46e5' : '#f3f4f6'}
              />
            </View>
          </View>

          <View className="rounded-lg bg-gray-50 p-3">
            <View className="flex-row items-center justify-between">
              <Text className="text-gray-800">Have own transportation</Text>
              <Switch
                value={hasTransport}
                onValueChange={setHasTransport}
                trackColor={{ false: '#d1d5db', true: '#c7d2fe' }}
                thumbColor={hasTransport ? '#4f46e5' : '#f3f4f6'}
              />
            </View>
          </View>
        </View>
      </ScrollView>

      <View className="border-t border-gray-200 bg-white p-4">
        <TouchableOpacity
          className="rounded-lg bg-indigo-600 py-3"
          onPress={handleSubmit}
          disabled={isLoading}>
          {isLoading ? (
            <ActivityIndicator color="#ffffff" />
          ) : (
            <Text className="text-center font-bold text-white">Submit Application</Text>
          )}
        </TouchableOpacity>
      </View>
    </View>
  );
};

export default JobApplicationScreen;
