'use client';

import { useState } from 'react';
import { View, Text, ScrollView, TouchableOpacity, ActivityIndicator } from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { format } from 'date-fns';
import { sharedTransition } from '@/utils/animations';
import { useGetEarningsQuery } from '@/store/api/paymentsApi';
import type { TimeRange } from '@/types/api';
import { MotiView } from '@/utils/animationComponents';

const EarningsScreen = () => {
  const [timeRange, setTimeRange] = useState<TimeRange>('week');
  const [showDetails, setShowDetails] = useState<boolean>(false);

  // Use RTK Query hook to fetch earnings data
  const { data: earnings, isLoading } = useGetEarningsQuery(timeRange);

  const getTimeRangeLabel = () => {
    switch (timeRange) {
      case 'week':
        return 'This Week';
      case 'month':
        return 'This Month';
      case 'year':
        return 'This Year';
      default:
        return 'This Week';
    }
  };

  if (isLoading) {
    return (
      <View className="flex-1 items-center justify-center bg-white">
        <ActivityIndicator size="large" color="#4f46e5" />
      </View>
    );
  }

  return (
    <ScrollView className="flex-1 bg-white">
      <View className="p-6">
        <MotiView
          from={{ opacity: 0, translateY: 20 }}
          animate={{ opacity: 1, translateY: 0 }}
          transition={sharedTransition}
          className="space-y-6">
          <View className="flex-row items-center justify-between">
            <Text className="text-2xl font-bold">Earnings</Text>
            <View className="flex-row overflow-hidden rounded-lg bg-gray-100">
              <TouchableOpacity
                className={`px-3 py-2 ${timeRange === 'week' ? 'bg-indigo-600' : ''}`}
                onPress={() => setTimeRange('week')}>
                <Text className={timeRange === 'week' ? 'text-white' : 'text-gray-700'}>Week</Text>
              </TouchableOpacity>
              <TouchableOpacity
                className={`px-3 py-2 ${timeRange === 'month' ? 'bg-indigo-600' : ''}`}
                onPress={() => setTimeRange('month')}>
                <Text className={timeRange === 'month' ? 'text-white' : 'text-gray-700'}>
                  Month
                </Text>
              </TouchableOpacity>
              <TouchableOpacity
                className={`px-3 py-2 ${timeRange === 'year' ? 'bg-indigo-600' : ''}`}
                onPress={() => setTimeRange('year')}>
                <Text className={timeRange === 'year' ? 'text-white' : 'text-gray-700'}>Year</Text>
              </TouchableOpacity>
            </View>
          </View>

          <View className="rounded-lg bg-indigo-600 p-6">
            <Text className="text-sm text-white">{getTimeRangeLabel()}</Text>
            <Text className="mt-2 text-3xl font-bold text-white">
              ₹{earnings?.summary?.totalEarnings || 0}
            </Text>

            <View className="mt-6 flex-row justify-between">
              <View>
                <Text className="text-xs text-indigo-200">Jobs Completed</Text>
                <Text className="font-bold text-white">
                  {earnings?.summary?.jobsCompleted || 0}
                </Text>
              </View>
              <View>
                <Text className="text-xs text-indigo-200">Platform Fee</Text>
                <Text className="font-bold text-white">₹{earnings?.summary?.platformFee || 0}</Text>
              </View>
              <View>
                <Text className="text-xs text-indigo-200">Net Earnings</Text>
                <Text className="font-bold text-white">₹{earnings?.summary?.netEarnings || 0}</Text>
              </View>
            </View>
          </View>

          <TouchableOpacity
            className="flex-row items-center justify-between"
            onPress={() => setShowDetails(!showDetails)}>
            <Text className="text-lg font-semibold">Earnings Breakdown</Text>
            <Ionicons name={showDetails ? 'chevron-up' : 'chevron-down'} size={20} color="gray" />
          </TouchableOpacity>

          {showDetails && (
            <View className="space-y-4">
              <View className="flex-row justify-between rounded-lg bg-gray-50 p-4">
                <Text className="text-gray-700">Gross Earnings</Text>
                <Text className="font-semibold">₹{earnings?.summary?.totalEarnings || 0}</Text>
              </View>
              <View className="flex-row justify-between rounded-lg bg-gray-50 p-4">
                <Text className="text-gray-700">Platform Commission (10%)</Text>
                <Text className="font-semibold text-red-500">
                  -₹{earnings?.summary?.platformCommission || 0}
                </Text>
              </View>
              <View className="flex-row justify-between rounded-lg bg-gray-50 p-4">
                <Text className="text-gray-700">Fixed Fee</Text>
                <Text className="font-semibold text-red-500">
                  -₹{earnings?.summary?.fixedFee || 0}
                </Text>
              </View>
              <View className="flex-row justify-between rounded-lg bg-gray-50 p-4">
                <Text className="font-semibold text-gray-700">Net Earnings</Text>
                <Text className="font-bold">₹{earnings?.summary?.netEarnings || 0}</Text>
              </View>
            </View>
          )}

          <Text className="text-lg font-semibold">Recent Payouts</Text>

          {earnings?.payouts && earnings.payouts.length > 0 ? (
            <View className="space-y-4">
              {earnings.payouts.map((payout: any) => (
                <MotiView
                  key={payout.id}
                  from={{ opacity: 0, translateY: 20 }}
                  animate={{ opacity: 1, translateY: 0 }}
                  transition={sharedTransition}
                  className="rounded-lg border border-gray-200 bg-white p-4 shadow-sm">
                  <View className="flex-row items-start justify-between">
                    <View>
                      <Text className="font-semibold">{payout.job.title}</Text>
                      <Text className="text-sm text-gray-500">
                        {format(new Date(payout.createdAt), 'MMM dd, yyyy')}
                      </Text>
                    </View>
                    <View>
                      <Text className="text-right font-bold">₹{payout.netPay}</Text>
                      <Text className="text-right text-xs text-gray-500">
                        (₹{payout.grossPay} - ₹{payout.commission})
                      </Text>
                    </View>
                  </View>

                  <View className="mt-4 flex-row justify-between">
                    <View className="flex-row items-center">
                      <Ionicons
                        name="checkmark-circle"
                        size={16}
                        color={payout.status === 'completed' ? 'green' : 'orange'}
                      />
                      <Text className="ml-1 text-sm capitalize">{payout.status}</Text>
                    </View>
                    {payout.status === 'completed' && payout.transactionId && (
                      <Text className="text-xs text-gray-500">Txn: {payout.transactionId}</Text>
                    )}
                  </View>
                </MotiView>
              ))}
            </View>
          ) : (
            <View className="items-center rounded-lg bg-gray-50 p-6">
              <Ionicons name="cash-outline" size={48} color="gray" />
              <Text className="mt-2 text-center text-gray-500">
                No payouts found for this period
              </Text>
            </View>
          )}
        </MotiView>
      </View>
    </ScrollView>
  );
};

export default EarningsScreen;
