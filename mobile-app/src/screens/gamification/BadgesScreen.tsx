import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  ScrollView,
  TouchableOpacity,
  ActivityIndicator,
  RefreshControl,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { MotiView } from 'moti';
import { gamificationAPI } from '../../services/api';
import { sharedTransition } from '../../utils/animations';
import { SCREENS } from '../../constants/screens';
import BadgeSystem from '../../components/BadgeSystem';
import { useTypedNavigation } from '@/types/navigation';

const BadgesScreen = () => {
  const navigation = useTypedNavigation();
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [badges, setBadges] = useState([]);
  const [allBadges, setAllBadges] = useState([]);
  const [level, setLevel] = useState(null);
  const [levelProgress, setLevelProgress] = useState(null);
  const [activeTab, setActiveTab] = useState('earned'); // 'earned' or 'available'

  const fetchBadgesData = async () => {
    try {
      setLoading(true);

      // Fetch user's badges
      const badgesResponse = await gamificationAPI.getMyBadges();
      setBadges(badgesResponse.data);

      // Fetch all available badges
      const allBadgesResponse = await gamificationAPI.getAllBadges();
      setAllBadges(allBadgesResponse.data);

      // Fetch user's level
      const levelResponse = await gamificationAPI.getMyLevel();
      setLevel(levelResponse.data);

      // Fetch level progress
      const progressResponse = await gamificationAPI.getLevelProgress();
      setLevelProgress(progressResponse.data);
    } catch (error) {
      console.error('Error fetching badges data:', error);
    } finally {
      setLoading(false);
      setRefreshing(false);
    }
  };

  useEffect(() => {
    fetchBadgesData();
  }, []);

  const handleRefresh = () => {
    setRefreshing(true);
    fetchBadgesData();
  };

  const handleBadgePress = (badge) => {
    navigation.navigate(SCREENS.BADGE_DETAILS, { badgeId: badge.id });
  };

  // Transform badges data for BadgeSystem component
  const transformBadges = (badgesList, includeUnearnedBadges = false) => {
    const earnedBadgeIds = badges.map((badge) => badge.id);

    return badgesList.map((badge) => ({
      id: badge.id,
      name: badge.name,
      description: badge.description,
      icon: badge.iconUrl,
      awardedAt: badge.awardedAt,
      isLocked: includeUnearnedBadges ? !earnedBadgeIds.includes(badge.id) : false,
    }));
  };

  if (loading && !refreshing) {
    return (
      <View className="flex-1 items-center justify-center bg-white">
        <ActivityIndicator size="large" color="#4f46e5" />
      </View>
    );
  }

  return (
    <View className="flex-1 bg-gray-50">
      <View className="bg-indigo-600 px-4 pb-4 pt-12">
        <TouchableOpacity
          className="mb-2 flex-row items-center"
          onPress={() => navigation.goBack()}>
          <Ionicons name="arrow-back" size={24} color="white" />
          <Text className="ml-2 text-white">Back</Text>
        </TouchableOpacity>
        <Text className="text-2xl font-bold text-white">Badges & Achievements</Text>
        <Text className="text-indigo-200">Collect badges by completing jobs and tasks</Text>
      </View>

      {/* Level Information */}
      {level && levelProgress && (
        <View className="border-b border-gray-200 bg-white p-4">
          <View className="mb-2 flex-row items-center justify-between">
            <View>
              <Text className="text-sm text-gray-500">Current Level</Text>
              <View className="flex-row items-center">
                <Text className="mr-2 text-2xl font-bold text-gray-900">{level.level}</Text>
                <Text className="text-gray-700">{level.title}</Text>
              </View>
            </View>
            <View className="h-16 w-16 items-center justify-center rounded-full bg-indigo-100">
              <Ionicons name="trophy" size={32} color="#4f46e5" />
            </View>
          </View>

          <View className="mb-1">
            <View className="mb-1 flex-row justify-between">
              <Text className="text-sm text-gray-700">Progress to Level {level.level + 1}</Text>
              <Text className="text-sm text-gray-700">
                {levelProgress.currentPoints}/{levelProgress.requiredPoints} XP
              </Text>
            </View>
            <View className="h-2 overflow-hidden rounded-full bg-gray-200">
              <View
                className="h-full bg-indigo-600"
                style={{
                  width: `${(levelProgress.currentPoints / levelProgress.requiredPoints) * 100}%`,
                }}
              />
            </View>
          </View>
          <Text className="text-sm text-gray-500">
            {levelProgress.requiredPoints - levelProgress.currentPoints} XP needed for next level
          </Text>
        </View>
      )}

      <View className="flex-row border-b border-gray-200 bg-white">
        <TouchableOpacity
          className={`flex-1 py-3 ${activeTab === 'earned' ? 'border-b-2 border-indigo-600' : ''}`}
          onPress={() => setActiveTab('earned')}>
          <Text
            className={`text-center font-medium ${
              activeTab === 'earned' ? 'text-indigo-600' : 'text-gray-500'
            }`}>
            Earned ({badges.length})
          </Text>
        </TouchableOpacity>
        <TouchableOpacity
          className={`flex-1 py-3 ${activeTab === 'available' ? 'border-b-2 border-indigo-600' : ''}`}
          onPress={() => setActiveTab('available')}>
          <Text
            className={`text-center font-medium ${
              activeTab === 'available' ? 'text-indigo-600' : 'text-gray-500'
            }`}>
            Available ({allBadges.length})
          </Text>
        </TouchableOpacity>
      </View>

      <ScrollView
        className="flex-1 p-4"
        refreshControl={<RefreshControl refreshing={refreshing} onRefresh={handleRefresh} />}>
        <MotiView
          from={{ opacity: 0, translateY: 20 }}
          animate={{ opacity: 1, translateY: 0 }}
          transition={sharedTransition}
          className="flex-1">
          <View className="flex-1">
            {activeTab === 'earned' ? (
              badges.length > 0 ? (
                <BadgeSystem badges={transformBadges(badges)} onBadgePress={handleBadgePress} />
              ) : (
                <View className="items-center rounded-lg bg-white p-6 shadow-sm">
                  <Ionicons name="ribbon-outline" size={48} color="#9ca3af" />
                  <Text className="mt-3 font-medium text-gray-700">No Badges Yet</Text>
                  <Text className="mt-1 text-center text-gray-500">
                    Complete jobs and tasks to earn badges
                  </Text>
                </View>
              )
            ) : (
              <BadgeSystem
                badges={transformBadges(allBadges, true)}
                onBadgePress={handleBadgePress}
              />
            )}
          </View>
        </MotiView>
      </ScrollView>
    </View>
  );
};

export default BadgesScreen;
