import React, { useState, useEffect } from 'react';
import { View, Text, ScrollView, TouchableOpacity, ActivityIndicator, Image } from 'react-native';
import { useNavigation, useRoute } from '@react-navigation/native';
import { Ionicons } from '@expo/vector-icons';
import { MotiView } from 'moti';
import { gamificationAPI } from '../../services/api';
import { sharedTransition } from '../../utils/animations';
import { SCREENS } from '../../constants/screens';

const BadgeDetailsScreen = () => {
  const navigation = useNavigation();
  const route = useRoute();
  const { badgeId } = route.params;
  
  const [loading, setLoading] = useState(true);
  const [badge, setBadge] = useState(null);
  const [userHasBadge, setUserHasBadge] = useState(false);

  useEffect(() => {
    fetchBadgeDetails();
  }, [badgeId]);

  const fetchBadgeDetails = async () => {
    try {
      setLoading(true);
      
      // Fetch badge details
      const response = await gamificationAPI.getBadgeById(badgeId);
      setBadge(response.data);
      
      // Check if user has this badge
      const userBadgesResponse = await gamificationAPI.getMyBadges();
      const userBadges = userBadgesResponse.data;
      setUserHasBadge(userBadges.some(b => b.id === badgeId));
    } catch (error) {
      console.error('Error fetching badge details:', error);
    } finally {
      setLoading(false);
    }
  };

  const formatDate = (dateString) => {
    if (!dateString) return 'Not earned yet';
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
    });
  };

  if (loading) {
    return (
      <View className="flex-1 items-center justify-center bg-white">
        <ActivityIndicator size="large" color="#4f46e5" />
      </View>
    );
  }

  return (
    <View className="flex-1 bg-gray-50">
      <View className="bg-indigo-600 pt-12 pb-4 px-4">
        <TouchableOpacity
          className="flex-row items-center mb-2"
          onPress={() => navigation.goBack()}>
          <Ionicons name="arrow-back" size={24} color="white" />
          <Text className="ml-2 text-white">Back</Text>
        </TouchableOpacity>
        <Text className="text-2xl font-bold text-white">Badge Details</Text>
      </View>

      <ScrollView className="flex-1 p-4">
        <MotiView
          from={{ opacity: 0, translateY: 20 }}
          animate={{ opacity: 1, translateY: 0 }}
          transition={sharedTransition}>
          <View className="bg-white rounded-lg shadow-sm p-6 mb-4 items-center">
            <View className={`p-6 rounded-full mb-4 ${userHasBadge ? 'bg-indigo-100' : 'bg-gray-100'}`}>
              {badge?.iconUrl ? (
                <Image
                  source={{ uri: badge.iconUrl }}
                  className="w-24 h-24"
                  resizeMode="contain"
                />
              ) : (
                <Ionicons
                  name="ribbon"
                  size={64}
                  color={userHasBadge ? '#4f46e5' : '#9ca3af'}
                />
              )}
            </View>
            
            <Text className="text-2xl font-bold text-gray-900 mb-1">{badge?.name}</Text>
            
            <View className={`px-3 py-1 rounded-full mb-4 ${userHasBadge ? 'bg-green-100' : 'bg-gray-100'}`}>
              <Text className={`text-sm font-medium ${userHasBadge ? 'text-green-800' : 'text-gray-600'}`}>
                {userHasBadge ? 'Earned' : 'Not Earned'}
              </Text>
            </View>
            
            <Text className="text-gray-700 text-center mb-6">{badge?.description}</Text>
            
            {userHasBadge && badge?.awardedAt && (
              <View className="flex-row items-center">
                <Ionicons name="calendar-outline" size={16} color="#6b7280" />
                <Text className="text-gray-600 ml-2">
                  Earned on {formatDate(badge.awardedAt)}
                </Text>
              </View>
            )}
          </View>

          <View className="bg-white rounded-lg shadow-sm p-4 mb-4">
            <Text className="text-lg font-bold text-gray-900 mb-3">How to Earn</Text>
            <View className="space-y-3">
              {badge?.requirements?.map((requirement, index) => (
                <View key={index} className="flex-row">
                  <View className="h-6 w-6 rounded-full bg-indigo-100 items-center justify-center mr-3 mt-0.5">
                    <Text className="text-indigo-600 font-bold text-xs">{index + 1}</Text>
                  </View>
                  <View className="flex-1">
                    <Text className="text-gray-800">{requirement.description}</Text>
                    {requirement.progress && (
                      <View className="mt-1">
                        <View className="flex-row justify-between mb-1">
                          <Text className="text-gray-500 text-xs">
                            Progress: {requirement.progress.current}/{requirement.progress.required}
                          </Text>
                          <Text className="text-gray-500 text-xs">
                            {Math.round((requirement.progress.current / requirement.progress.required) * 100)}%
                          </Text>
                        </View>
                        <View className="h-1.5 bg-gray-200 rounded-full overflow-hidden">
                          <View
                            className="h-full bg-indigo-600"
                            style={{
                              width: `${(requirement.progress.current / requirement.progress.required) * 100}%`,
                            }}
                          />
                        </View>
                      </View>
                    )}
                  </View>
                </View>
              ))}

              {(!badge?.requirements || badge.requirements.length === 0) && (
                <Text className="text-gray-600">
                  Complete specific tasks or achievements to earn this badge.
                </Text>
              )}
            </View>
          </View>

          <View className="bg-white rounded-lg shadow-sm p-4 mb-4">
            <Text className="text-lg font-bold text-gray-900 mb-3">Rewards</Text>
            {badge?.rewards?.map((reward, index) => (
              <View key={index} className="flex-row items-center mb-2">
                <Ionicons
                  name={
                    reward.type === 'points'
                      ? 'star'
                      : reward.type === 'level'
                      ? 'trending-up'
                      : 'gift'
                  }
                  size={20}
                  color="#4f46e5"
                  className="mr-2"
                />
                <Text className="text-gray-700 ml-2">{reward.description}</Text>
              </View>
            ))}

            {(!badge?.rewards || badge.rewards.length === 0) && (
              <Text className="text-gray-600">
                Earn this badge to unlock special rewards and recognition.
              </Text>
            )}
          </View>
        </MotiView>
      </ScrollView>
    </View>
  );
};

export default BadgeDetailsScreen;
