// API configuration
import Constants from 'expo-constants';

// Get environment variables from app.json extra or use defaults
const { expoConfig } = Constants;
const env = expoConfig?.extra || {};

// API configuration with fallbacks
export const API_URL = env.API_URL || 'http://localhost:3040/api/v1';

// App configuration
export const APP_NAME = env.APP_NAME || 'JobMatch';
export const APP_VERSION = env.APP_VERSION || '1.0.0';

// Feature flags
export const FEATURES = {
  CHAT_ENABLED: env.CHAT_ENABLED !== false,
  NOTIFICATIONS_ENABLED: env.NOTIFICATIONS_ENABLED !== false,
  EMERGENCY_JOBS_ENABLED: env.EMERGENCY_JOBS_ENABLED !== false,
  GAMIFICATION_ENABLED: env.GAMIFICATION_ENABLED !== false,
};

// Default timeout for API requests (in milliseconds)
export const API_TIMEOUT = env.API_TIMEOUT || 15000;
