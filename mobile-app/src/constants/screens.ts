export const SCREENS = {
  // Auth Screens
  WELCOME: 'Welcome',
  LOGIN: 'Login',
  REGISTER: 'Register',
  OTP_VERIFICATION: 'OTPVerification',
  OTP_LOGIN: 'OTPLogin',
  FORGOT_PASSWORD: 'ForgotPassword',
  RESET_PASSWORD: 'ResetPassword',
  AUTH: 'Auth',
  ONBOARDING: 'Onboarding',
  MAIN: 'Main',

  // Main Tabs
  HOME_TAB: 'HomeTab',
  JOBS_TAB: 'JobsTab',
  APPLICATIONS_TAB: 'ApplicationsTab',
  FAVORITES_TAB: 'FavoritesTab',
  CHAT_TAB: 'ChatTab',
  PROFILE_TAB: 'ProfileTab',

  // Jobs Screens
  JOBS_HOME: 'JobsHome',
  JOB_DETAILS: 'JobDetails',
  JOB_APPLICATION: 'JobApplication',
  EMERGENCY_JOBS: 'EmergencyJobs',
  SAVED_JOBS: 'SavedJobs',
  NEARBY_JOBS: 'NearbyJobs',

  // Applications Screens
  APPLICATIONS: 'Applications',
  APPLICATIONS_LIST: 'ApplicationsList',
  APPLICATION_DETAILS: 'ApplicationDetails',

  // Profile Screens
  PROFILE: 'Profile',
  EDIT_PROFILE: 'EditProfile',
  SETTINGS: 'Settings',
  THEME_SETTINGS: 'ThemeSettings',
  HELP_SUPPORT: 'HelpSupport',
  TRUST_SCORE: 'TrustScore',
  DOCUMENTS: 'Documents',

  // Payment Screens
  PAYMENTS: 'Payments',
  PAYMENT_DETAILS: 'PaymentDetails',
  ADD_PAYMENT_METHOD: 'AddPaymentMethod',
  PAYMENT_METHODS: 'PaymentMethods',

  // Notification Screens
  NOTIFICATIONS_SETTINGS: 'NotificationsSettings',

  // Dispute Screens
  DISPUTES: 'Disputes',
  DISPUTE_DETAILS: 'DisputeDetails',
  CREATE_DISPUTE: 'CreateDispute',

  // Rating Screens
  RATINGS: 'Ratings',
  RATE_COMPANY: 'RateCompany',

  // Gamification Screens
  BADGES: 'Badges',
  BADGE_DETAILS: 'BadgeDetails',
  LEADERBOARD: 'Leaderboard',

  // Notification Screens
  NOTIFICATIONS: 'Notifications',
  NOTIFICATION_PREFERENCES: 'NotificationPreferences',

  // Chat Screens
  CHAT: 'Chat',
  CHATS_LIST: 'ChatsList',
  CHAT_DETAILS: 'ChatDetails',
  CHAT_CONVERSATION: 'ChatConversation',
} as const;

// Type for type-safe screen names
export type ScreenNameType = typeof SCREENS;
export type ScreenName = (typeof SCREENS)[keyof typeof SCREENS];
