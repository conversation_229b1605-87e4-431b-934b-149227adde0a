/**
 * Constants for AsyncStorage keys used throughout the app
 *
 * This file centralizes all AsyncStorage key definitions to:
 * - Prevent typos when accessing AsyncStorage
 * - Make it easier to track what data we're storing
 * - Provide proper TypeScript typing
 */

/**
 * Type definition for storage keys
 */

/**
 * Storage keys used throughout the app
 */
const STORAGE_KEYS = {
  // Authentication related keys
  AUTH_TOKEN: '@JobApp:token',
  AUTH_REFRESH_TOKEN: '@JobApp:refreshToken',
  USER_DATA: '@JobApp:user',
  ONBOARDING_COMPLETED: '@JobApp:onboardingCompleted',
  ONBOARDING_COMPLETE: '@JobApp:onboardingComplete', // Used in OnboardingScreen

  // Legacy auth keys (used in api.ts)
  LEGACY_AUTH_TOKEN: 'auth_token',
  LEGACY_REFRESH_TOKEN: 'refresh_token',

  // Notification related keys
  PUSH_TOKEN: 'pushToken',

  // Location related keys
  LOCATION_CACHE: '@location_cache',
  LOCATION_TIMESTAMP: '@location_timestamp',
  LOCATION_PERMISSION: '@location_permission',

  // Network related keys
  NETWORK_REQUEST_QUEUE: '@network_request_queue',

  // Theme related keys
  THEME_PREFERENCE: '@JobApp:theme',
  COLOR_SCHEME: '@JobApp:colorScheme',
  DARK_MODE_ENABLED: '@JobApp:darkModeEnabled',

  // Cache related
  CACHE_PREFIX: '@app_cache:',
} as const;

export default STORAGE_KEYS;

export type StorageKeys = typeof STORAGE_KEYS;
