import React from 'react';
import { View, Text, TouchableOpacity } from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useThemeContext } from '../providers/ThemeProvider';
import { Theme } from '../hooks/use-theme';
import { cn } from '../utils/styleUtils';

interface ThemeToggleProps {
  className?: string;
}

/**
 * Theme toggle component
 * Allows users to switch between light, dark, and system themes
 */
export const ThemeToggle: React.FC<ThemeToggleProps> = ({ className = '' }) => {
  const { theme, setTheme } = useThemeContext();

  // Define theme options
  const themeOptions: { value: Theme; label: string; icon: string }[] = [
    { value: 'light', label: 'Light', icon: 'sunny-outline' },
    { value: 'dark', label: 'Dark', icon: 'moon-outline' },
    { value: 'system', label: 'System', icon: 'settings-outline' },
  ];

  return (
    <View className={cn('flex-row justify-around rounded-lg bg-card p-1', className)}>
      {themeOptions.map((option) => (
        <TouchableOpacity
          key={option.value}
          className={cn(
            'flex-1 flex-row items-center justify-center rounded-md px-3 py-2',
            theme === option.value ? 'bg-primary' : ''
          )}
          onPress={() => setTheme(option.value)}
          activeOpacity={0.7}>
          <Ionicons
            name={option.icon as any}
            size={16}
            className={cn('mr-2', theme === option.value ? 'text-white' : 'text-gray-500')}
          />
          <Text
            className={cn(
              'text-sm font-medium',
              theme === option.value ? 'text-white' : 'text-gray-600'
            )}>
            {option.label}
          </Text>
        </TouchableOpacity>
      ))}
    </View>
  );
};

export default ThemeToggle;
