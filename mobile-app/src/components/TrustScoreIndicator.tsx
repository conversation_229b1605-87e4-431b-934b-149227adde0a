import type React from 'react';
import { View, Text, TouchableOpacity } from 'react-native';
import { Feather } from '@expo/vector-icons';

interface TrustScoreIndicatorProps {
  score: number;
  size?: 'small' | 'medium' | 'large';
  showLabel?: boolean;
  className?: string;
  onPress?: () => void;
}

export const TrustScoreIndicator: React.FC<TrustScoreIndicatorProps> = ({
  score,
  size = 'medium',
  showLabel = true,
  className = '',
  onPress,
}) => {
  // Determine color based on score
  const getColorClass = () => {
    if (score >= 80) return 'bg-green-500';
    if (score >= 60) return 'bg-lime-500';
    if (score >= 40) return 'bg-yellow-500';
    if (score >= 20) return 'bg-orange-500';
    return 'bg-red-500';
  };

  // Determine icon based on score
  const getIcon = () => {
    if (score >= 80) return 'award';
    if (score >= 60) return 'thumbs-up';
    if (score >= 40) return 'user-check';
    if (score >= 20) return 'alert-circle';
    return 'alert-triangle';
  };

  // Determine size dimensions
  const getDimensions = () => {
    switch (size) {
      case 'small':
        return {
          container: 'h-6 w-6',
          icon: 12,
          text: 'text-xs',
          label: 'text-xs',
        };
      case 'large':
        return {
          container: 'h-12 w-12',
          icon: 24,
          text: 'text-lg font-bold',
          label: 'text-sm',
        };
      case 'medium':
      default:
        return {
          container: 'h-8 w-8',
          icon: 16,
          text: 'text-sm font-semibold',
          label: 'text-xs',
        };
    }
  };

  const dimensions = getDimensions();
  const colorClass = getColorClass();
  const icon = getIcon();

  const Container = onPress ? TouchableOpacity : View;

  return (
    <View className={`flex flex-row items-center ${className}`}>
      <Container
        className={`${dimensions.container} ${colorClass} mr-2 items-center justify-center rounded-full`}
        onPress={onPress}
        activeOpacity={onPress ? 0.7 : 1}>
        <Feather name={icon} size={dimensions.icon} color="white" />
      </Container>
      <View>
        <Text className={`${dimensions.text} text-gray-900`}>{score}</Text>
        {showLabel && <Text className={`${dimensions.label} text-gray-500`}>Trust Score</Text>}
      </View>
    </View>
  );
};

export default TrustScoreIndicator;
