import React, { useState } from 'react';
import { View, Text, Modal, TouchableOpacity, ScrollView, Switch } from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import Slider from '@react-native-community/slider';

interface FilterModalProps {
  visible: boolean;
  onClose: () => void;
  onApply: (filters: any) => void;
  initialFilters?: any;
  filters?: any;
  trustScore?: number;
}

const FilterModal: React.FC<FilterModalProps> = ({
  visible,
  onClose,
  onApply,
  initialFilters = {},
}) => {
  const [jobType, setJobType] = useState(initialFilters.jobType || []);
  const [salary, setSalary] = useState(initialFilters.salary || [10000, 100000]);
  const [experience, setExperience] = useState(initialFilters.experience || '');
  const [urgentOnly, setUrgentOnly] = useState(initialFilters.urgentOnly || false);
  const [remoteOnly, setRemoteOnly] = useState(initialFilters.remoteOnly || false);

  const jobTypes = ['Full-time', 'Part-time', 'Contract', 'Temporary', 'Internship'];
  const experienceLevels = ['Entry Level', 'Mid Level', 'Senior Level', 'Manager', 'Director'];

  const toggleJobType = (type: string) => {
    if (jobType.includes(type)) {
      setJobType(jobType.filter((t) => t !== type));
    } else {
      setJobType([...jobType, type]);
    }
  };

  const handleApply = () => {
    onApply({
      jobType,
      salary,
      experience,
      urgentOnly,
      remoteOnly,
    });
    onClose();
  };

  const resetFilters = () => {
    setJobType([]);
    setSalary([10000, 100000]);
    setExperience('');
    setUrgentOnly(false);
    setRemoteOnly(false);
  };

  return (
    <Modal visible={visible} animationType="slide" transparent>
      <View className="flex-1 bg-black/50">
        <View className="mt-20 flex-1 rounded-t-3xl bg-white">
          <View className="flex-row items-center justify-between border-b border-gray-200 p-4">
            <TouchableOpacity onPress={onClose}>
              <Ionicons name="close" size={24} color="#4b5563" />
            </TouchableOpacity>
            <Text className="text-xl font-bold text-gray-900">Filters</Text>
            <TouchableOpacity onPress={resetFilters}>
              <Text className="font-medium text-indigo-600">Reset</Text>
            </TouchableOpacity>
          </View>

          <ScrollView className="flex-1 p-4">
            <View className="mb-6">
              <Text className="mb-3 text-lg font-bold text-gray-900">Job Type</Text>
              <View className="flex-row flex-wrap">
                {jobTypes.map((type) => (
                  <TouchableOpacity
                    key={type}
                    className={`mb-2 mr-2 rounded-full border px-4 py-2 ${
                      jobType.includes(type)
                        ? 'border-indigo-600 bg-indigo-600'
                        : 'border-gray-300 bg-white'
                    }`}
                    onPress={() => toggleJobType(type)}>
                    <Text className={`${jobType.includes(type) ? 'text-white' : 'text-gray-700'}`}>
                      {type}
                    </Text>
                  </TouchableOpacity>
                ))}
              </View>
            </View>

            <View className="mb-6">
              <Text className="mb-3 text-lg font-bold text-gray-900">Salary Range</Text>
              <View className="px-2">
                <Slider
                  minimumValue={10000}
                  maximumValue={100000}
                  step={5000}
                  value={salary[1]}
                  onValueChange={(value) => setSalary([salary[0], value])}
                  minimumTrackTintColor="#4f46e5"
                  maximumTrackTintColor="#d1d5db"
                  thumbTintColor="#4f46e5"
                />
                <View className="mt-2 flex-row justify-between">
                  <Text className="text-gray-600">₹{salary[0].toLocaleString()}</Text>
                  <Text className="text-gray-600">₹{salary[1].toLocaleString()}</Text>
                </View>
              </View>
            </View>

            <View className="mb-6">
              <Text className="mb-3 text-lg font-bold text-gray-900">Experience Level</Text>
              <View className="flex-row flex-wrap">
                {experienceLevels.map((level) => (
                  <TouchableOpacity
                    key={level}
                    className={`mb-2 mr-2 rounded-full border px-4 py-2 ${
                      experience === level
                        ? 'border-indigo-600 bg-indigo-600'
                        : 'border-gray-300 bg-white'
                    }`}
                    onPress={() => setExperience(level)}>
                    <Text className={`${experience === level ? 'text-white' : 'text-gray-700'}`}>
                      {level}
                    </Text>
                  </TouchableOpacity>
                ))}
              </View>
            </View>

            <View className="mb-6">
              <Text className="mb-3 text-lg font-bold text-gray-900">Other Filters</Text>
              <View className="mb-2 rounded-lg bg-gray-50 p-3">
                <View className="flex-row items-center justify-between">
                  <View className="flex-row items-center">
                    <Ionicons name="alert-circle-outline" size={20} color="#4f46e5" />
                    <Text className="ml-2 text-gray-800">Urgent Jobs Only</Text>
                  </View>
                  <Switch
                    value={urgentOnly}
                    onValueChange={setUrgentOnly}
                    trackColor={{ false: '#d1d5db', true: '#c7d2fe' }}
                    thumbColor={urgentOnly ? '#4f46e5' : '#f3f4f6'}
                  />
                </View>
              </View>
              <View className="rounded-lg bg-gray-50 p-3">
                <View className="flex-row items-center justify-between">
                  <View className="flex-row items-center">
                    <Ionicons name="home-outline" size={20} color="#4f46e5" />
                    <Text className="ml-2 text-gray-800">Remote Jobs Only</Text>
                  </View>
                  <Switch
                    value={remoteOnly}
                    onValueChange={setRemoteOnly}
                    trackColor={{ false: '#d1d5db', true: '#c7d2fe' }}
                    thumbColor={remoteOnly ? '#4f46e5' : '#f3f4f6'}
                  />
                </View>
              </View>
            </View>
          </ScrollView>

          <View className="border-t border-gray-200 p-4">
            <TouchableOpacity className="rounded-lg bg-indigo-600 py-3" onPress={handleApply}>
              <Text className="text-center font-bold text-white">Apply Filters</Text>
            </TouchableOpacity>
          </View>
        </View>
      </View>
    </Modal>
  );
};

export default FilterModal;
