import React from 'react';
import { View, Text, Image, TouchableOpacity } from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { cn } from '../utils/styleUtils';

interface EmptyStateProps {
  title: string;
  message: string;
  icon?: keyof typeof Ionicons.glyphMap;
  action?: {
    label: string;
    onPress: () => void;
  };
  image?: any;
}

const EmptyState: React.FC<EmptyStateProps> = ({ title, message, icon, action, image }) => {
  return (
    <View className="flex-1 items-center justify-center p-6">
      {image ? (
        <Image source={image} className="mb-4 h-48 w-48" resizeMode="contain" />
      ) : icon ? (
        <View className="mb-4 h-24 w-24 items-center justify-center rounded-full bg-indigo-100">
          <Ionicons name={icon} size={48} className="text-indigo-600" />
        </View>
      ) : null}

      <Text className="mb-2 text-center text-xl font-bold text-gray-900">{title}</Text>
      <Text className="mb-6 text-center text-gray-600">{message}</Text>

      {action && (
        <TouchableOpacity className="rounded-lg bg-indigo-600 px-6 py-3" onPress={action.onPress}>
          <Text className="font-medium text-white">{action.label}</Text>
        </TouchableOpacity>
      )}
    </View>
  );
};

export default EmptyState;
