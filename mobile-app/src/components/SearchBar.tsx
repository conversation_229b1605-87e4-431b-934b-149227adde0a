import React from 'react';
import { View, TextInput, TouchableOpacity } from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { cn } from '../utils/styleUtils';

interface SearchBarProps {
  placeholder?: string;
  value?: string;
  onChangeText?: (text: string) => void;
  onSubmit?: () => void;
  onFilterPress?: () => void;
  showFilter?: boolean;
  autoFocus?: boolean;
}

const SearchBar: React.FC<SearchBarProps> = ({
  placeholder = 'Search jobs, skills, companies...',
  value = '',
  onChangeText,
  onSubmit,
  onFilterPress,
  showFilter = true,
  autoFocus = false,
}) => {
  const handleFilterPress = () => {
    if (onFilterPress) {
      onFilterPress();
    } else {
      // If no custom filter handler is provided, do nothing
      // or navigate to a default screen if needed
      console.log('No filter handler provided');
    }
  };

  return (
    <View className="flex-row items-center rounded-lg bg-gray-100 px-3 py-2">
      <Ionicons name="search-outline" size={20} className="text-gray-500" />
      <TextInput
        className="ml-2 flex-1 text-gray-800"
        placeholder={placeholder}
        placeholderTextColor="#9ca3af"
        value={value}
        onChangeText={onChangeText}
        onSubmitEditing={onSubmit}
        returnKeyType="search"
        autoFocus={autoFocus}
      />
      {value.length > 0 && (
        <TouchableOpacity onPress={() => onChangeText && onChangeText('')}>
          <Ionicons name="close-circle" size={20} className="text-gray-500" />
        </TouchableOpacity>
      )}
      {showFilter && (
        <TouchableOpacity className="ml-2" onPress={handleFilterPress}>
          <Ionicons name="options-outline" size={20} className="text-gray-500" />
        </TouchableOpacity>
      )}
    </View>
  );
};

export default SearchBar;
