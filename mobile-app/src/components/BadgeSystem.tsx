import type React from 'react';
import { View, Text, Image, FlatList, TouchableOpacity } from 'react-native';
import { MaterialIcons } from '@expo/vector-icons';
import { cn } from '../utils/styleUtils';

export interface Badge {
  id: string;
  name: string;
  description: string;
  icon: string;
  awardedAt?: string;
  isLocked?: boolean;
}

interface BadgeSystemProps {
  badges: Badge[];
  onBadgePress?: (badge: Badge) => void;
}

const BadgeSystem: React.FC<BadgeSystemProps> = ({ badges, onBadgePress }) => {
  const renderBadge = ({ item }: { item: Badge }) => {
    return (
      <TouchableOpacity
        className={cn(
          'w-[48%] items-center rounded-xl bg-white p-4 shadow-sm',
          item.isLocked ? 'bg-gray-100 opacity-80' : ''
        )}
        onPress={() => onBadgePress && onBadgePress(item)}
        activeOpacity={0.7}>
        <View className="mb-2 h-16 w-16 items-center justify-center">
          {item.isLocked ? (
            <MaterialIcons name="lock" size={32} className="text-gray-400" />
          ) : (
            <Image source={{ uri: item.icon }} className="h-16 w-16" resizeMode="contain" />
          )}
        </View>
        <Text
          className={cn(
            'mb-1 text-center text-base font-bold',
            item.isLocked ? 'text-gray-400' : 'text-gray-900'
          )}
          numberOfLines={1}>
          {item.name}
        </Text>
        <Text
          className={cn(
            'mb-2 text-center text-xs',
            item.isLocked ? 'text-gray-400' : 'text-gray-600'
          )}
          numberOfLines={2}>
          {item.description}
        </Text>
        {item.awardedAt && !item.isLocked && (
          <Text className="text-center text-xs text-green-600">
            Awarded: {new Date(item.awardedAt).toLocaleDateString()}
          </Text>
        )}
      </TouchableOpacity>
    );
  };

  return (
    <View className="flex-1">
      <FlatList
        data={badges}
        renderItem={renderBadge}
        keyExtractor={(item) => item.id}
        numColumns={2}
        columnWrapperStyle={{ justifyContent: 'space-between', marginBottom: 16 }}
        showsVerticalScrollIndicator={false}
        ListEmptyComponent={
          <View className="items-center justify-center p-8">
            <MaterialIcons name="emoji-events" size={48} className="text-gray-300" />
            <Text className="mt-4 text-lg font-bold text-gray-500">No badges yet</Text>
            <Text className="mt-2 text-center text-sm text-gray-400">
              Complete jobs to earn badges!
            </Text>
          </View>
        }
      />
    </View>
  );
};

export default BadgeSystem;
