import React, { useState } from 'react';
import { View, Text, TouchableOpacity, Alert, ActivityIndicator } from 'react-native';
import RazorpayCheckout from 'react-native-razorpay';
import {
  useCreateRazorpayOrderMutation,
  useVerifyRazorpaySignatureMutation,
} from '../../store/api/paymentsApi';

interface RazorpayPaymentProps {
  amount: number;
  currency?: string;
  description: string;
  customerEmail?: string;
  customerPhone?: string;
  metadata?: Record<string, unknown>;
  onSuccess: (paymentData: Record<string, unknown>) => void;
  onError: (error: Error) => void;
  disabled?: boolean;
  buttonText?: string;
  buttonClassName?: string;
}

export const RazorpayPayment: React.FC<RazorpayPaymentProps> = ({
  amount,
  currency = 'INR',
  description,
  customerEmail,
  customerPhone,
  metadata,
  onSuccess,
  onError,
  disabled = false,
  buttonText = 'Pay with Razorpay',
  buttonClassName = '',
}) => {
  const [isProcessing, setIsProcessing] = useState(false);
  const [createOrder] = useCreateRazorpayOrderMutation();
  const [verifySignature] = useVerifyRazorpaySignatureMutation();

  const handlePayment = async () => {
    try {
      setIsProcessing(true);

      // Step 1: Create Razorpay order
      const orderResponse = await createOrder({
        amount,
        currency,
        description,
        customerEmail,
        customerPhone,
        metadata,
      }).unwrap();

      if (!orderResponse.success) {
        throw new Error('Failed to create payment order');
      }

      const order = orderResponse.gatewayResponse;

      // Step 2: Open Razorpay checkout
      const options = {
        description,
        image: 'https://your-app-logo-url.com/logo.png', // Replace with your app logo
        currency,
        key: 'rzp_test_your_key_id', // This should come from your app config
        amount: amount.toString(),
        order_id: order.id,
        name: 'JobMatch',
        prefill: {
          email: customerEmail || '',
          contact: customerPhone || '',
          name: 'Customer',
        },
        theme: { color: '#3B82F6' },
      };

      interface RazorpayCheckoutResult {
        razorpay_payment_id: string;
        razorpay_order_id: string;
        razorpay_signature: string;
      }

      const paymentResult = await new Promise<RazorpayCheckoutResult>((resolve, reject) => {
        RazorpayCheckout.open(options)
          .then((data: RazorpayCheckoutResult) => {
            resolve(data);
          })
          .catch((error: Error) => {
            reject(error);
          });
      });

      // Step 3: Verify payment signature
      const verificationResponse = await verifySignature({
        orderId: order.id,
        paymentId: paymentResult.razorpay_payment_id,
        signature: paymentResult.razorpay_signature,
      }).unwrap();

      if (verificationResponse.verified) {
        onSuccess({
          orderId: order.id,
          paymentId: paymentResult.razorpay_payment_id,
          signature: paymentResult.razorpay_signature,
          amount,
          currency,
          description,
          metadata,
        });
      } else {
        throw new Error('Payment verification failed');
      }
    } catch (error: unknown) {
      console.error('Razorpay payment error:', error);

      let errorMessage = 'Payment failed. Please try again.';
      let errorToPass: Error;

      if (error instanceof Error) {
        errorToPass = error;
        errorMessage = error.message;
      } else if (typeof error === 'object' && error !== null) {
        const errorObj = error as { code?: string; message?: string };
        if (errorObj.code === 'payment_cancelled') {
          errorMessage = 'Payment was cancelled by user.';
        } else if (errorObj.code === 'payment_failed') {
          errorMessage = 'Payment failed. Please check your payment details.';
        } else if (errorObj.message) {
          errorMessage = errorObj.message;
        }
        errorToPass = new Error(errorMessage);
      } else {
        errorToPass = new Error(errorMessage);
      }

      Alert.alert('Payment Error', errorMessage);
      onError(errorToPass);
    } finally {
      setIsProcessing(false);
    }
  };

  return (
    <TouchableOpacity
      onPress={handlePayment}
      disabled={disabled || isProcessing}
      className={`
        flex-row items-center justify-center rounded-lg bg-blue-600 px-6 py-3
        ${disabled || isProcessing ? 'opacity-50' : 'active:bg-blue-700'}
        ${buttonClassName}
      `}>
      {isProcessing ? (
        <>
          <ActivityIndicator size="small" color="white" className="mr-2" />
          <Text className="font-semibold text-white">Processing...</Text>
        </>
      ) : (
        <Text className="font-semibold text-white">{buttonText}</Text>
      )}
    </TouchableOpacity>
  );
};

// Hook for easier usage
export const useRazorpayPayment = () => {
  const [createOrder] = useCreateRazorpayOrderMutation();
  const [verifySignature] = useVerifyRazorpaySignatureMutation();

  const processPayment = async (paymentData: {
    amount: number;
    currency?: string;
    description: string;
    customerEmail?: string;
    customerPhone?: string;
    metadata?: Record<string, unknown>;
  }) => {
    try {
      // Create order
      const orderResponse = await createOrder(paymentData).unwrap();

      if (!orderResponse.success) {
        throw new Error('Failed to create payment order');
      }

      const order = orderResponse.gatewayResponse;

      // Open Razorpay checkout
      const options = {
        description: paymentData.description,
        image: 'https://your-app-logo-url.com/logo.png',
        currency: paymentData.currency || 'INR',
        key: 'rzp_test_your_key_id',
        amount: paymentData.amount.toString(),
        order_id: order.id,
        name: 'JobMatch',
        prefill: {
          email: paymentData.customerEmail || '',
          contact: paymentData.customerPhone || '',
          name: 'Customer',
        },
        theme: { color: '#3B82F6' },
      };

      interface RazorpayCheckoutResult {
        razorpay_payment_id: string;
        razorpay_order_id: string;
        razorpay_signature: string;
      }

      const paymentResult = await new Promise<RazorpayCheckoutResult>((resolve, reject) => {
        RazorpayCheckout.open(options)
          .then((data: RazorpayCheckoutResult) => resolve(data))
          .catch((error: Error) => reject(error));
      });

      // Verify signature
      const verificationResponse = await verifySignature({
        orderId: order.id,
        paymentId: paymentResult.razorpay_payment_id,
        signature: paymentResult.razorpay_signature,
      }).unwrap();

      if (!verificationResponse.verified) {
        throw new Error('Payment verification failed');
      }

      return {
        success: true,
        orderId: order.id,
        paymentId: paymentResult.razorpay_payment_id,
        signature: paymentResult.razorpay_signature,
        amount: paymentData.amount,
        currency: paymentData.currency || 'INR',
        description: paymentData.description,
        metadata: paymentData.metadata,
      };
    } catch (error) {
      return {
        success: false,
        error,
      };
    }
  };

  return { processPayment };
};

export default RazorpayPayment;
