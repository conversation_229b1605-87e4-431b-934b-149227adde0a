import React, { useState } from 'react';
import { View, Text, TouchableOpacity, ScrollView } from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { RazorpayPayment } from './RazorpayPayment';

export type PaymentMethodType = 'stripe' | 'razorpay' | 'upi' | 'bank_transfer';

interface PaymentMethod {
  id: PaymentMethodType;
  name: string;
  description: string;
  icon: keyof typeof Ionicons.glyphMap;
  enabled: boolean;
}

interface PaymentMethodSelectorProps {
  amount: number;
  currency?: string;
  description: string;
  customerEmail?: string;
  customerPhone?: string;
  metadata?: Record<string, unknown>;
  onSuccess: (paymentData: { paymentMethod: PaymentMethodType; [key: string]: unknown }) => void;
  onError: (error: Error) => void;
  availableMethods?: PaymentMethodType[];
}

// Payment methods ordered with Razorpay first (default for Indian users)
const paymentMethods: PaymentMethod[] = [
  {
    id: 'razorpay',
    name: '<PERSON><PERSON><PERSON><PERSON>',
    description: 'Pay securely with cards, UPI, wallets & more',
    icon: 'card-outline',
    enabled: true,
  },
  {
    id: 'upi',
    name: 'UPI',
    description: 'Pay using UPI ID or QR code',
    icon: 'phone-portrait-outline',
    enabled: true,
  },
  {
    id: 'stripe',
    name: 'Credit/Debit Card',
    description: 'Pay with your credit or debit card',
    icon: 'card-outline',
    enabled: true,
  },
  {
    id: 'bank_transfer',
    name: 'Bank Transfer',
    description: 'Direct bank account transfer',
    icon: 'business-outline',
    enabled: true,
  },
];

export const PaymentMethodSelector: React.FC<PaymentMethodSelectorProps> = ({
  amount,
  currency = 'INR',
  description,
  customerEmail,
  customerPhone,
  metadata,
  onSuccess,
  onError,
  availableMethods = ['razorpay', 'stripe', 'upi'],
}) => {
  const [selectedMethod, setSelectedMethod] = useState<PaymentMethodType | null>('razorpay');

  const filteredMethods = paymentMethods.filter(
    (method) => availableMethods.includes(method.id) && method.enabled
  );

  const handleMethodSelect = (methodId: PaymentMethodType) => {
    setSelectedMethod(methodId);
  };

  const handlePaymentSuccess = (paymentData: Record<string, unknown>) => {
    onSuccess({
      ...paymentData,
      paymentMethod: selectedMethod,
    });
  };

  const renderPaymentButton = () => {
    if (!selectedMethod) return null;

    switch (selectedMethod) {
      case 'razorpay':
        return (
          <RazorpayPayment
            amount={amount}
            currency={currency}
            description={description}
            customerEmail={customerEmail}
            customerPhone={customerPhone}
            metadata={metadata}
            onSuccess={handlePaymentSuccess}
            onError={onError}
            buttonText={`Pay ₹${(amount / 100).toFixed(2)} with Razorpay`}
            buttonClassName="mt-4"
          />
        );

      case 'stripe':
        return (
          <TouchableOpacity
            className="mt-4 rounded-lg bg-purple-600 px-6 py-3"
            onPress={() => {
              // Implement Stripe payment
              onError(new Error('Stripe payment not implemented yet'));
            }}>
            <Text className="text-center font-semibold text-white">
              Pay ₹{(amount / 100).toFixed(2)} with Card
            </Text>
          </TouchableOpacity>
        );

      case 'upi':
        return (
          <TouchableOpacity
            className="mt-4 rounded-lg bg-green-600 px-6 py-3"
            onPress={() => {
              // Implement UPI payment
              onError(new Error('UPI payment not implemented yet'));
            }}>
            <Text className="text-center font-semibold text-white">
              Pay ₹{(amount / 100).toFixed(2)} with UPI
            </Text>
          </TouchableOpacity>
        );

      case 'bank_transfer':
        return (
          <TouchableOpacity
            className="mt-4 rounded-lg bg-gray-600 px-6 py-3"
            onPress={() => {
              // Implement bank transfer
              onError(new Error('Bank transfer not implemented yet'));
            }}>
            <Text className="text-center font-semibold text-white">
              Pay ₹{(amount / 100).toFixed(2)} via Bank Transfer
            </Text>
          </TouchableOpacity>
        );

      default:
        return null;
    }
  };

  return (
    <View className="rounded-lg bg-white p-4 shadow-sm">
      <Text className="mb-4 text-lg font-semibold text-gray-900">Choose Payment Method</Text>

      <Text className="mb-6 text-2xl font-bold text-gray-900">₹{(amount / 100).toFixed(2)}</Text>

      <ScrollView className="max-h-64">
        {filteredMethods.map((method) => (
          <TouchableOpacity
            key={method.id}
            onPress={() => handleMethodSelect(method.id)}
            className={`
              mb-3 flex-row items-center rounded-lg border-2 p-4
              ${
                selectedMethod === method.id
                  ? 'border-blue-500 bg-blue-50'
                  : 'border-gray-200 bg-white'
              }
            `}>
            <View
              className={`
                mr-3 h-6 w-6 items-center justify-center rounded-full border-2
                ${selectedMethod === method.id ? 'border-blue-500 bg-blue-500' : 'border-gray-300'}
              `}>
              {selectedMethod === method.id && <View className="h-2 w-2 rounded-full bg-white" />}
            </View>

            <Ionicons
              name={method.icon}
              size={24}
              color={selectedMethod === method.id ? '#3B82F6' : '#6B7280'}
              className="mr-3"
            />

            <View className="flex-1">
              <Text
                className={`
                  font-semibold
                  ${selectedMethod === method.id ? 'text-blue-900' : 'text-gray-900'}
                `}>
                {method.name}
              </Text>
              <Text
                className={`
                  text-sm
                  ${selectedMethod === method.id ? 'text-blue-700' : 'text-gray-600'}
                `}>
                {method.description}
              </Text>
            </View>
          </TouchableOpacity>
        ))}
      </ScrollView>

      {renderPaymentButton()}
    </View>
  );
};

export default PaymentMethodSelector;
