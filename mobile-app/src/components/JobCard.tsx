'use client';

import type React from 'react';
import { View, Text, TouchableOpacity, Alert } from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { format } from 'date-fns';
import { JobStatus } from '../shared-types';
import { useTheme } from '../hooks/use-theme';
import { cn } from '../utils/styleUtils';
import {
  useAddFavoriteMutation,
  useRemoveFavoriteMutation,
  useIsFavoriteQuery,
} from '../store/api/favoritesApi';
import type { Job } from '../types/api';

interface JobCardProps {
  job: Job;
  onPress: () => void;
  showFavoriteButton?: boolean;
}

const JobCard: React.FC<JobCardProps> = ({ job, onPress, showFavoriteButton = true }) => {
  const { colors } = useTheme();

  // RTK Query hooks for favorites
  const { data: favoriteData } = useIsFavoriteQuery(job.id);
  const [addFavorite, { isLoading: isAddingFavorite }] = useAddFavoriteMutation();
  const [removeFavorite, { isLoading: isRemovingFavorite }] = useRemoveFavoriteMutation();

  const isFavorite = favoriteData?.data?.isFavorite ?? false;
  const isLoadingFavorite = isAddingFavorite || isRemovingFavorite;

  const handleFavoriteToggle = async (e: React.SyntheticEvent) => {
    e.stopPropagation();

    if (isLoadingFavorite) return;

    try {
      if (isFavorite) {
        await removeFavorite(job.id).unwrap();
      } else {
        await addFavorite(job.id).unwrap();
      }
    } catch (error) {
      console.error('Error toggling favorite:', error);
      Alert.alert('Error', 'Failed to update favorites. Please try again.', [{ text: 'OK' }]);
    }
  };

  // Format date to readable format
  const formattedStartDate = format(new Date(job.startDateTime), 'MMM d, yyyy');

  // Calculate total pay - use paymentAmount if available, otherwise calculate from hourly rate
  const totalPay =
    job.payRate && job.estimatedHours ? Number.parseFloat(job.payRate) * job.estimatedHours : 0;

  // Helper function to convert to Pascal Case
  const toPascalCase = (str: string): string => {
    return str
      .split(' ')
      .map((word) => word.charAt(0).toUpperCase() + word.slice(1).toLowerCase())
      .join(' ');
  };

  return (
    <TouchableOpacity
      className="mb-4 overflow-hidden rounded-lg border border-gray-100 bg-white shadow-sm"
      onPress={onPress}
      activeOpacity={0.7}>
      {job.isEmergencyJob && (
        <View className="bg-red-500 px-3 py-1">
          <Text className="text-center text-xs font-medium text-white">EMERGENCY JOB</Text>
        </View>
      )}

      <View className="p-4">
        <View className="flex-row justify-between">
          <View className="mr-2 flex-1">
            <Text className="text-lg font-bold text-gray-800" numberOfLines={1}>
              {job.title}
            </Text>
            <Text className="text-sm text-gray-600" numberOfLines={1}>
              {job.company?.companyName || job.company?.fullName}
            </Text>
          </View>

          {showFavoriteButton && (
            <TouchableOpacity
              onPress={handleFavoriteToggle}
              className="p-2"
              disabled={isLoadingFavorite}>
              <Ionicons
                name={isFavorite ? 'heart' : 'heart-outline'}
                size={24}
                color={isFavorite ? '#ef4444' : '#9ca3af'}
              />
            </TouchableOpacity>
          )}
        </View>

        <View className="mt-2 flex-row items-center">
          <Ionicons name="location-outline" size={16} className="text-gray-500" />
          <Text className="ml-1 text-sm text-gray-500" numberOfLines={1}>
            {job.city || job.locationCity || 'Location not specified'},{' '}
            {job.country || job.locationCountry || ''}
          </Text>
        </View>

        <View className="mt-3 flex-row justify-between">
          <View className="flex-row items-center">
            <Ionicons name="calendar-outline" size={16} className="text-gray-500" />
            <Text className="ml-1 text-sm text-gray-500">{formattedStartDate}</Text>
          </View>

          <View className="flex-row items-center">
            <Ionicons name="time-outline" size={16} className="text-gray-500" />
            <Text className="ml-1 text-sm text-gray-500">
              {job.estimatedHours || job.duration || 'N/A'} hrs
            </Text>
          </View>

          <View className="flex-row items-center">
            <Ionicons name="cash-outline" size={16} className="text-gray-500" />
            <Text className="ml-1 text-sm text-gray-500">
              {job.payRate ? `$${job.payRate}/hr` : 'Rate TBD'}
            </Text>
          </View>
        </View>

        <View className="mt-3 flex-row items-center justify-between border-t border-gray-100 pt-3">
          <View className="flex-row items-center">
            <View
              className={cn(
                'mr-2 h-3 w-3 rounded-full',
                job.status === JobStatus.OPEN
                  ? 'bg-green-500'
                  : job.status === JobStatus.IN_PROGRESS
                    ? 'bg-blue-500'
                    : job.status === JobStatus.COMPLETED
                      ? 'bg-gray-500'
                      : job.status === JobStatus.CANCELLED
                        ? 'bg-red-500'
                        : 'bg-gray-500'
              )}
            />
            <Text className="text-sm text-gray-700">
              {toPascalCase(job.status.replace(/_/g, ' '))}
            </Text>
          </View>

          <View className="flex-row items-center">
            <Ionicons name="shield-checkmark-outline" size={16} className="text-gray-500" />
            <Text className="ml-1 text-sm text-gray-700">
              {toPascalCase('trust score')}: {job.trustScoreRequired}+
            </Text>
          </View>

          <Text className="font-bold text-primary">
            ${totalPay > 0 ? totalPay.toFixed(2) : 'TBD'}
          </Text>
        </View>
      </View>
    </TouchableOpacity>
  );
};

export default JobCard;
