import React, { useState } from 'react';
import { View, Text, FlatList, ActivityIndicator, RefreshControl } from 'react-native';
import { useGetJobsQuery } from '../store/api/jobsApi';
import JobCard from './JobCard';
import EmptyState from './EmptyState';
import type { Job } from '../types/api';

interface JobsListProps {
  category?: string;
  searchQuery?: string;
  onJobPress?: (job: Job) => void;
}

/**
 * JobsList component that uses RTK Query to fetch and display jobs
 */
const JobsList: React.FC<JobsListProps> = ({ category, searchQuery, onJobPress }) => {
  const [page, setPage] = useState(1);
  const limit = 10;

  // Use RTK Query hook to fetch jobs
  const { data, error, isLoading, isFetching, refetch } = useGetJobsQuery({
    limit,
    offset: (page - 1) * limit,
    category,
    search: searchQuery,
    sortBy: 'createdAt',
    sortOrder: 'desc',
  });

  // Handle pagination
  const handleLoadMore = () => {
    if (data?.data && data.data.jobs.length >= limit && !isFetching) {
      setPage(page + 1);
    }
  };

  // Render job item
  const renderItem = ({ item }: { item: Job }) => (
    <JobCard job={item} onPress={() => onJobPress?.(item)} />
  );

  // Render empty state
  const renderEmptyComponent = () => {
    if (isLoading) {
      return (
        <View className="items-center justify-center py-10">
          <ActivityIndicator size="large" color="#0000ff" />
        </View>
      );
    }

    if (error) {
      return (
        <EmptyState
          title="Error loading jobs"
          message="There was an error loading jobs. Please try again."
          actionLabel="Retry"
          onAction={refetch}
        />
      );
    }

    return (
      <EmptyState
        title="No jobs found"
        message={
          searchQuery
            ? `No jobs found matching "${searchQuery}"`
            : category
              ? `No jobs found in ${category}`
              : 'No jobs available at the moment'
        }
      />
    );
  };

  // Render footer (loading indicator for pagination)
  const renderFooter = () => {
    if (!isFetching || isLoading) return null;

    return (
      <View className="items-center justify-center py-4">
        <ActivityIndicator size="small" color="#0000ff" />
      </View>
    );
  };

  return (
    <FlatList
      data={data?.data?.jobs || []}
      keyExtractor={(item) => item.id}
      renderItem={renderItem}
      ListEmptyComponent={renderEmptyComponent}
      ListFooterComponent={renderFooter}
      onEndReached={handleLoadMore}
      onEndReachedThreshold={0.5}
      refreshControl={<RefreshControl refreshing={isLoading} onRefresh={refetch} />}
      contentContainerStyle={{
        flexGrow: 1,
        paddingHorizontal: 16,
        paddingBottom: 16,
      }}
      className="bg-gray-50"
    />
  );
};

export default JobsList;
