import React, { ReactNode, useEffect, useRef } from 'react';
import { View, ViewProps, Animated } from 'react-native';

interface SafeMotiViewProps extends ViewProps {
  children: ReactNode;
  from?: {
    opacity?: number;
    translateY?: number;
    translateX?: number;
    scale?: number;
    [key: string]: any;
  };
  animate?: {
    opacity?: number;
    translateY?: number;
    translateX?: number;
    scale?: number;
    [key: string]: any;
  };
  transition?: {
    type?: string;
    duration?: number;
    delay?: number;
    [key: string]: any;
  };
  exit?: any;
  state?: any;
  delay?: number;
}

/**
 * A simplified animation view that mimics MotiView functionality
 * but uses React Native's Animated API directly to avoid hook issues.
 */
const SafeMotiView: React.FC<SafeMotiViewProps> = ({
  children,
  from,
  animate,
  transition,
  className,
  style,
  ...props
}) => {
  // Create animated values for opacity and transform
  const opacity = useRef(new Animated.Value(from?.opacity ?? 0)).current;
  const translateY = useRef(new Animated.Value(from?.translateY ?? 0)).current;
  const translateX = useRef(new Animated.Value(from?.translateX ?? 0)).current;
  const scale = useRef(new Animated.Value(from?.scale ?? 1)).current;

  useEffect(() => {
    // Create an animation for each property
    const animations = [];

    // Opacity animation
    if (animate?.opacity !== undefined) {
      animations.push(
        Animated.timing(opacity, {
          toValue: animate.opacity,
          duration: transition?.duration ?? 300,
          delay: transition?.delay ?? 0,
          useNativeDriver: true,
        })
      );
    }

    // TranslateY animation
    if (animate?.translateY !== undefined) {
      animations.push(
        Animated.timing(translateY, {
          toValue: animate.translateY,
          duration: transition?.duration ?? 300,
          delay: transition?.delay ?? 0,
          useNativeDriver: true,
        })
      );
    }

    // TranslateX animation
    if (animate?.translateX !== undefined) {
      animations.push(
        Animated.timing(translateX, {
          toValue: animate.translateX,
          duration: transition?.duration ?? 300,
          delay: transition?.delay ?? 0,
          useNativeDriver: true,
        })
      );
    }

    // Scale animation
    if (animate?.scale !== undefined) {
      animations.push(
        Animated.timing(scale, {
          toValue: animate.scale,
          duration: transition?.duration ?? 300,
          delay: transition?.delay ?? 0,
          useNativeDriver: true,
        })
      );
    }

    // Start all animations in parallel
    if (animations.length > 0) {
      Animated.parallel(animations).start();
    }
  }, [animate, transition, opacity, translateY, translateX, scale]);

  return (
    <Animated.View
      className={className}
      style={[
        style,
        {
          opacity,
          transform: [{ translateY }, { translateX }, { scale }],
        },
      ]}
      {...props}>
      {children}
    </Animated.View>
  );
};

export default SafeMotiView;
