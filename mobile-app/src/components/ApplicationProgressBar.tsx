import type React from 'react';
import { View, Text } from 'react-native';
import { MaterialIcons } from '@expo/vector-icons';
import { cn } from '../utils/styleUtils';

type ApplicationStatus =
  | 'pending'
  | 'accepted'
  | 'rejected'
  | 'cancelled'
  | 'completed'
  | 'no_show';

interface ApplicationProgressBarProps {
  status: ApplicationStatus;
}

const ApplicationProgressBar: React.FC<ApplicationProgressBarProps> = ({ status }) => {
  const getStatusIndex = (status: ApplicationStatus): number => {
    switch (status) {
      case 'pending':
        return 0;
      case 'accepted':
        return 1;
      case 'completed':
        return 2;
      case 'rejected':
        return -1;
      case 'cancelled':
        return -2;
      case 'no_show':
        return -3;
      default:
        return 0;
    }
  };

  const statusIndex = getStatusIndex(status);
  const isRejected = statusIndex < 0;

  const getStatusIcon = (status: ApplicationStatus): string => {
    switch (status) {
      case 'pending':
        return 'hourglass-empty';
      case 'accepted':
        return 'check-circle';
      case 'completed':
        return 'verified';
      case 'rejected':
        return 'cancel';
      case 'cancelled':
        return 'remove-circle';
      case 'no_show':
        return 'person-off';
      default:
        return 'hourglass-empty';
    }
  };

  const getStatusText = (status: ApplicationStatus): string => {
    switch (status) {
      case 'pending':
        return 'Application Pending';
      case 'accepted':
        return 'Application Accepted';
      case 'completed':
        return 'Job Completed';
      case 'rejected':
        return 'Application Rejected';
      case 'cancelled':
        return 'Application Cancelled';
      case 'no_show':
        return 'Marked as No-Show';
      default:
        return 'Application Pending';
    }
  };

  if (isRejected) {
    // For rejected, cancelled, or no-show statuses
    const statusColorClass =
      status === 'rejected' || status === 'no_show'
        ? 'text-red-500'
        : status === 'cancelled'
          ? 'text-gray-500'
          : 'text-orange-500';

    return (
      <View className="p-4">
        <View className="flex-row items-center justify-center rounded-lg bg-red-50 p-3">
          <MaterialIcons name={getStatusIcon(status)} size={24} className={statusColorClass} />
          <Text className={cn('ml-2 text-base font-bold', statusColorClass)}>
            {getStatusText(status)}
          </Text>
        </View>
      </View>
    );
  }

  // For normal progress: pending -> accepted -> completed
  return (
    <View className="p-4">
      <View className="flex-row items-center justify-between">
        {/* Step 1: Applied */}
        <View className="h-10 w-10 items-center justify-center rounded-full bg-green-50">
          <MaterialIcons name="check-circle" size={24} className="text-green-500" />
        </View>

        {/* Line 1 */}
        <View
          className={`mx-2 h-[3px] flex-1 ${statusIndex >= 1 ? 'bg-green-500' : 'bg-gray-200'}`}
        />

        {/* Step 2: Accepted */}
        <View
          className={`h-10 w-10 items-center justify-center rounded-full ${
            statusIndex >= 1 ? 'bg-green-50' : 'bg-gray-100'
          }`}>
          <MaterialIcons
            name={statusIndex >= 1 ? 'check-circle' : 'radio-button-unchecked'}
            size={24}
            className={statusIndex >= 1 ? 'text-green-500' : 'text-gray-400'}
          />
        </View>

        {/* Line 2 */}
        <View
          className={`mx-2 h-[3px] flex-1 ${statusIndex >= 2 ? 'bg-green-500' : 'bg-gray-200'}`}
        />

        {/* Step 3: Completed */}
        <View
          className={`h-10 w-10 items-center justify-center rounded-full ${
            statusIndex >= 2 ? 'bg-blue-50' : 'bg-gray-100'
          }`}>
          <MaterialIcons
            name={statusIndex >= 2 ? 'verified' : 'radio-button-unchecked'}
            size={24}
            className={statusIndex >= 2 ? 'text-blue-500' : 'text-gray-400'}
          />
        </View>
      </View>

      <View className="mt-2 flex-row justify-between">
        <Text className="w-[70px] text-center text-xs">Applied</Text>
        <Text
          className={`w-[70px] text-center text-xs ${statusIndex >= 1 ? 'text-black' : 'text-gray-400'}`}>
          Accepted
        </Text>
        <Text
          className={`w-[70px] text-center text-xs ${statusIndex >= 2 ? 'text-black' : 'text-gray-400'}`}>
          Completed
        </Text>
      </View>

      <Text
        className={cn(
          'mt-4 text-center text-base font-bold',
          status === 'pending'
            ? 'text-orange-500'
            : status === 'accepted'
              ? 'text-green-500'
              : status === 'completed'
                ? 'text-blue-500'
                : status === 'rejected' || status === 'no_show'
                  ? 'text-red-500'
                  : status === 'cancelled'
                    ? 'text-gray-500'
                    : 'text-orange-500'
        )}>
        {getStatusText(status)}
      </Text>
    </View>
  );
};

export default ApplicationProgressBar;
