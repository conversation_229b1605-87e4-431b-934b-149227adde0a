@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    /* Base colors */
    --background: 0 0% 100%;
    --foreground: 222.2 84% 4.9%;

    /* Card colors */
    --card: 0 0% 100%;
    --card-foreground: 222.2 84% 4.9%;

    /* Popover colors */
    --popover: 0 0% 100%;
    --popover-foreground: 222.2 84% 4.9%;

    /* Primary colors - Using centralized design tokens */
    --primary: 199 89% 48%; /* #0ea5e9 converted to HSL */
    --primary-foreground: 210 40% 98%;

    /* Secondary colors */
    --secondary: 210 40% 96.1%;
    --secondary-foreground: 222.2 47.4% 11.2%;

    /* Muted colors */
    --muted: 210 40% 96.1%;
    --muted-foreground: 215.4 16.3% 46.9%;

    /* Accent colors */
    --accent: 210 40% 96.1%;
    --accent-foreground: 222.2 47.4% 11.2%;

    /* Destructive colors */
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;

    /* Success colors */
    --success: 142 71% 45%;
    --success-foreground: 210 40% 98%;

    /* Warning colors */
    --warning: 38 92% 50%;
    --warning-foreground: 210 40% 98%;

    /* Info colors */
    --info: 202 100% 51%;
    --info-foreground: 210 40% 98%;

    /* Border, input, and ring colors */
    --border: 214.3 31.8% 91.4%;
    --input: 214.3 31.8% 91.4%;
    --ring: 222 84% 4.9%;

    /* Radius */
    --radius: 0.5rem;
  }

  .dark {
    /* Base colors */
    --background: 222 84% 4.9%;
    --foreground: 210 40% 98%;

    /* Card colors */
    --card: 222 84% 4.9%;
    --card-foreground: 210 40% 98%;

    /* Popover colors */
    --popover: 222 84% 4.9%;
    --popover-foreground: 210 40% 98%;

    /* Primary colors */
    --primary: 202 100% 51%;
    --primary-foreground: 210 40% 98%;

    /* Secondary colors */
    --secondary: 217.2 32.6% 17.5%;
    --secondary-foreground: 210 40% 98%;

    /* Muted colors */
    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 215 20.2% 65.1%;

    /* Accent colors */
    --accent: 217.2 32.6% 17.5%;
    --accent-foreground: 210 40% 98%;

    /* Destructive colors */
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;

    /* Success colors */
    --success: 142 71% 35%;
    --success-foreground: 210 40% 98%;

    /* Warning colors */
    --warning: 38 92% 50%; /* #f59e0b converted to HSL */
    --warning-foreground: 210 40% 98%;

    /* Info colors */
    --info: 199 89% 48%; /* Same as primary for consistency */
    --info-foreground: 210 40% 98%;

    /* Border, input, and ring colors */
    --border: 214.3 31.8% 91.4%;
    --input: 214.3 31.8% 91.4%;
    --ring: 199 89% 48%;
  }
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
  }
}
