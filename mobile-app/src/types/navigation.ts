/**
 * Navigation types for the application
 * This file contains all the TypeScript types for navigation
 */
import { useNavigation, useRoute, RouteProp } from '@react-navigation/native';
import { NativeStackNavigationProp } from '@react-navigation/native-stack';
import { SCREENS, ScreenName } from '../constants/screens';
import { ApplicationStatus, Job } from './api';

/**
 * Job filter parameters
 */
export interface JobFilters {
  categories?: string[];
  minSalary?: number;
  maxSalary?: number;
  jobTypes?: string[];
  distance?: number;
  startDate?: Date;
  endDate?: Date;
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
}

type OverriddenScreenParams = {
  // Auth Screens
  [SCREENS.REGISTER]: { phone?: string };
  [SCREENS.OTP_VERIFICATION]: { phone: string; purpose: 'login' | 'register' };

  // Jobs Screens
  [SCREENS.JOB_DETAILS]: { jobId: string };
  [SCREENS.JOB_APPLICATION]: { jobId: string; job: Job };

  // Applications Screens
  [SCREENS.APPLICATIONS_LIST]: { status?: ApplicationStatus };
  [SCREENS.APPLICATION_DETAILS]: { applicationId: string };

  // Chat Screens
  [SCREENS.CHAT_DETAILS]: { chatId: string; recipientId: string; recipientName?: string };

  // Payment Screens
  [SCREENS.PAYMENT_DETAILS]: { paymentId: string };
  [SCREENS.ADD_PAYMENT_METHOD]: { type: 'bank' | 'upi' | 'card' };

  // Dispute Screens
  [SCREENS.DISPUTE_DETAILS]: { disputeId: string };
  [SCREENS.CREATE_DISPUTE]: { jobId?: string; applicationId?: string };

  // Rating Screens
  [SCREENS.RATE_COMPANY]: { jobId: string; companyId: string };

  // Gamification Screens
  [SCREENS.BADGE_DETAILS]: { badgeId: string };
};

// Define a combined navigation param list using intersection types
// Base type sets all screens to undefined by default
export type RootStackParamList = Record<
  Exclude<ScreenName, keyof OverriddenScreenParams>,
  undefined
> &
  OverriddenScreenParams;

/**
 * A type-safe navigation hook that provides proper typing for all screens and their parameters
 *
 * @returns A navigation object with proper typing for all screens and their parameters
 *
 * @example
 * const navigation = useTypedNavigation();
 *
 * // Navigate to a screen without parameters
 * navigation.navigate(SCREENS.LOGIN);
 *
 * // Navigate to a screen with parameters
 * navigation.navigate(SCREENS.JOB_DETAILS, { jobId: '123' });
 */
export function useTypedNavigation() {
  return useNavigation<NativeStackNavigationProp<RootStackParamList>>();
}

/**
 * A type-safe route hook that provides proper typing for route parameters
 *
 * @template T - The screen name to get route parameters for
 * @returns A route object with properly typed parameters
 *
 * @example
 * // For a screen with parameters
 * const route = useTypedRoute<typeof SCREENS.JOB_DETAILS>();
 * const { jobId } = route.params; // jobId is properly typed as string
 *
 * // For a screen without parameters
 * const route = useTypedRoute<typeof SCREENS.LOGIN>();
 * // route.params will be undefined
 */
export function useTypedRoute<T extends keyof RootStackParamList>() {
  return useRoute<RouteProp<RootStackParamList, T>>();
}

declare global {
  namespace ReactNavigation {
    interface RootParamList extends RootStackParamList {}
  }
}
