/**
 * API response types for the application
 * This file contains all the TypeScript interfaces and types for API requests and responses
 */
import {
  ApiResponse as SharedApiResponse,
  PaginatedResponse as SharedPaginatedResponse,
  PaginationMeta as SharedPaginationMeta,
  ApiErrorResponse as SharedApiErrorResponse,
  PaginationParams as SharedPaginationParams,
} from '../shared-types';

import { Job as SharedJob, JobStatus as SharedJobStatus } from '../shared-types';

import { User as SharedUser, UserRole as SharedUserRole } from '../shared-types';

import {
  Application as SharedApplication,
  ApplicationStatus as SharedApplicationStatus,
} from '../shared-types';

/**
 * Re-export shared API response types
 */
export type ApiResponse<T> = SharedApiResponse<T>;
export type PaginationMeta = SharedPaginationMeta;
export type PaginatedResponse<T> = SharedPaginatedResponse<T>;
export type ApiErrorResponse = SharedApiErrorResponse;
export type PaginationParams = SharedPaginationParams;

// User related types
export interface User extends SharedUser {
  // Additional fields specific to mobile app
  trustScore: number; // Ensure trustScore is always defined
}

/**
 * User role type - using shared enum
 */
export const UserRole = SharedUserRole;
export type UserRole = SharedUserRole;

/**
 * Type guard to check if a user is a worker
 */
export function isWorker(user: User): boolean {
  return user.role === UserRole.WORKER;
}

/**
 * Type guard to check if a user is a company
 */
export function isCompany(user: User): boolean {
  return user.role === UserRole.COMPANY;
}

/**
 * Type guard to check if a user is an admin
 */
export function isAdmin(user: User): boolean {
  return user.role === UserRole.ADMIN;
}

// Auth related types
export interface AuthResponseData {
  user: User;
  token: string;
  refreshToken: string;
  access_token?: string; // For backward compatibility with backend
  isNewUser?: boolean; // For OTP verification response
}

export type AuthResponse = ApiResponse<AuthResponseData>;

export interface LoginRequest {
  email?: string;
  phone?: string;
  password: string;
}

export interface OtpLoginRequest {
  phone: string;
  code: string; // Changed from 'otp' to 'code' to match backend
}

export interface RegisterRequest {
  email?: string;
  phone?: string;
  password: string;
  fullName: string;
  role: UserRole;
}

// Job related types
export interface Job {
  id: string;
  title: string;
  description: string;
  companyId: string;
  company: Company;
  userId?: string;
  location: string;
  city?: string;
  state?: string;
  country?: string;
  postalCode?: string;
  latitude?: number;
  longitude?: number;
  startDateTime: string;
  endDateTime: string;
  payRate: string;
  duration: number;
  trustScoreRequired: number;
  requiredWorkers: number;
  hiredWorkers: number;
  requiresLaptop: boolean;
  requiresSmartphone: boolean;
  skillsRequired?: string;
  requirements?: string[];
  responsibilities?: string[];
  benefits?: string[];
  isEmergencyJob: boolean;
  isFeatured?: boolean;
  status: JobStatus;
  createdAt: string;
  updatedAt: string;
  distance?: number;
  applicationsCount?: number;
  salary?: {
    min: number;
    max: number;
    currency: string;
  };
  category?: string;
  type?: string;

  // Additional properties for backward compatibility with existing components
  locationCity?: string;
  locationCountry?: string;
  locationAddress?: string;
  estimatedHours?: number;

  // For backward compatibility
  get startDate(): string;

  get endDate(): string | undefined;
}

/**
 * Job status type - using shared enum
 */
export const JobStatus = SharedJobStatus;
export type JobStatus = SharedJobStatus;

export interface Company {
  id: string;
  name: string;
  logo?: string;
  description?: string;
  website?: string;
  industry?: string;
  size?: string;
  registrationNumber?: string;
  taxId?: string;
  address?: string;
  city?: string;
  state?: string;
  country?: string;
  postalCode?: string;
  rating?: number;
  reviewsCount?: number;
  isKycVerified: boolean;
  isActive: boolean;
  isBanned: boolean;
  userId: string;
  user?: User;
  createdAt: string;
  updatedAt: string;

  // Additional properties for backward compatibility
  companyName?: string;
  fullName?: string;
  profilePic?: string;
  averageRating?: number;
}

export interface CompaniesResponseData {
  companies: Company[];
  total: number;
  limit: number;
  offset: number;
}

export type CompaniesResponse = ApiResponse<CompaniesResponseData>;

export interface CompaniesQueryParams {
  limit?: number;
  offset?: number;
  search?: string;
  industry?: string;
  isKycVerified?: boolean;
  isActive?: boolean;
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
}

export interface Location {
  address: string;
  city: string;
  state: string;
  country: string;
  postalCode: string;
  zipCode?: string; // For backward compatibility
  coordinates: {
    latitude: number;
    longitude: number;
  };
}

// Jobs response type
export type JobsResponse = PaginatedResponse<Job>;

export interface JobsQueryParams extends PaginationParams {
  search?: string;
  category?: string;
  status?: string;
  minSalary?: number;
  maxSalary?: number;
  startDate?: string;
  endDate?: string;
  latitude?: number;
  longitude?: number;
  maxDistance?: number;
}

// Application related types
export interface Application {
  id: string;
  jobId: string;
  job?: Job;
  workerId: string;
  worker?: User;
  status: ApplicationStatus;
  coverLetter?: string;
  expectedSalary?: number;
  availability?: string;
  attachments?: string[];
  rejectionReason?: string;
  cancellationReason?: string;
  isEmergencyJob?: boolean;
  createdAt: string;
  updatedAt: string;
}

/**
 * Application status type - using shared enum
 */
export const ApplicationStatus = SharedApplicationStatus;
export type ApplicationStatus = SharedApplicationStatus;

export interface ApplicationsResponseData {
  applications: Application[];
  total: number;
  limit: number;
  offset: number;
}

export type ApplicationsResponse = ApiResponse<ApplicationsResponseData>;

/**
 * Notification types
 */
export enum NotificationType {
  JOB_APPLICATION = 'job_application',
  APPLICATION_STATUS = 'application_status',
  JOB_OFFER = 'job_offer',
  PAYMENT = 'payment',
  CHAT_MESSAGE = 'chat_message',
  SYSTEM = 'system',
  DISPUTE = 'dispute',
  RATING = 'rating',
  BADGE = 'badge',
  LEVEL_UP = 'level_up',
}

/**
 * Notification data interface
 */
export interface NotificationData {
  jobId?: string;
  applicationId?: string;
  chatId?: string;
  userId?: string;
  disputeId?: string;
  ratingId?: string;
  badgeId?: string;
  levelId?: string;
  paymentId?: string;
  [key: string]: string | undefined;
}

/**
 * Notification interface
 */
export interface Notification {
  id: string;
  type: NotificationType;
  title: string;
  message: string;
  isRead: boolean;
  data?: NotificationData;
  createdAt: string;
}

/**
 * Notifications response interface
 */
export interface NotificationsResponseData {
  notifications: Notification[];
  total: number;
  unreadCount: number;
}

export type NotificationsResponse = ApiResponse<NotificationsResponseData>;

// Import shared chat types
import {
  ChatType,
  ParticipantRole,
  MessageType,
  MessageStatus,
  type GenericChat,
  type ChatParticipant,
} from '../shared-types';

// Re-export for convenience
export {
  ChatType,
  ParticipantRole,
  MessageType,
  MessageStatus,
  type GenericChat,
  type ChatParticipant,
};

// Enhanced chat message interface
export interface ChatMessage {
  id: string;
  chatId: string;
  genericChatId?: string;
  messageType: MessageType;
  message: string;
  fileUrl?: string;
  fileName?: string;
  fileSize?: number;
  fileMimeType?: string;
  senderId: string;
  sender?: User;
  senderType?: 'worker' | 'company'; // For backward compatibility
  status: MessageStatus;
  isRead: boolean;
  readAt?: string;
  metadata?: Record<string, any>;
  replyToId?: string;
  replyTo?: ChatMessage;
  isEdited: boolean;
  editedAt?: string;
  createdAt: string;
  updatedAt: string;
}

// Legacy chat interface (for backward compatibility)
export interface Chat {
  id: string;
  workerId: string;
  worker?: User;
  companyId: string;
  company?: Company;
  companyUserId?: string;
  jobId?: string;
  job?: Job;
  lastMessage?: ChatMessage;
  unreadCount: number;
  createdAt: string;
  updatedAt: string;
}

export interface ChatsResponseData {
  chats: Chat[];
  total: number;
  limit: number;
  offset: number;
}

export type ChatsResponse = ApiResponse<ChatsResponseData>;

export interface ChatMessagesResponseData {
  messages: ChatMessage[];
  total: number;
  limit: number;
  offset: number;
}

export type ChatMessagesResponse = ApiResponse<ChatMessagesResponseData>;

// Payment related types
export type PaymentStatus = 'pending' | 'processing' | 'completed' | 'failed';
export type PaymentMethod = 'bank_transfer' | 'upi' | 'wallet' | 'card';

export interface Payment {
  id: string;
  jobId: string;
  job?: Job;
  workerId: string;
  worker?: User;
  companyId: string;
  company?: User;
  amount: number;
  commission: number;
  netAmount: number;
  status: PaymentStatus;
  paymentMethod?: PaymentMethod;
  transactionId?: string;
  paymentDate?: string;
  createdAt: string;
  updatedAt: string;
}

export interface PaymentsResponseData {
  payments: Payment[];
  total: number;
  limit: number;
  offset: number;
}

export type PaymentsResponse = ApiResponse<PaymentsResponseData>;

export interface PaymentMethodDetails {
  id: string;
  type: PaymentMethod;
  isDefault: boolean;
  details: {
    accountNumber?: string;
    bankName?: string;
    ifscCode?: string;
    upiId?: string;
    cardNumber?: string;
    cardExpiry?: string;
    walletId?: string;
  };
  createdAt: string;
}

// Dispute related types
export type DisputeStatus = 'open' | 'in_progress' | 'resolved' | 'cancelled';
export type DisputeType = 'payment' | 'job' | 'application' | 'other';

export interface Dispute {
  id: string;
  title: string;
  description: string;
  status: DisputeStatus;
  type: DisputeType;
  createdBy: User;
  assignedTo?: User;
  relatedJob?: Job;
  relatedApplication?: Application;
  attachments?: string[];
  resolution?: string;
  createdAt: string;
  updatedAt: string;
  closedAt?: string;
}

export interface DisputesResponseData {
  disputes: Dispute[];
  total: number;
  limit: number;
  offset: number;
}

export type DisputesResponse = ApiResponse<DisputesResponseData>;

// Rating related types
export interface Rating {
  id: string;
  rating: number;
  comment: string;
  fromUser: User;
  toUser: User;
  job: Job;
  createdAt: string;
}

export interface RatingsResponseData {
  ratings: Rating[];
  total: number;
  limit: number;
  offset: number;
}

export type RatingsResponse = ApiResponse<RatingsResponseData>;

export interface RatingSummary {
  averageRating: number;
  totalRatings: number;
  ratingCounts: {
    1: number;
    2: number;
    3: number;
    4: number;
    5: number;
  };
  categories?: Record<
    string,
    {
      averageRating: number;
      totalRatings: number;
    }
  >;
}

// Badge and gamification related types
export interface Badge {
  id: string;
  name: string;
  description: string;
  icon: string;
  criteria: string;
  isEarned: boolean;
  earnedAt?: string;
  progress?: number;
}

export interface Level {
  id: string;
  name: string;
  requiredPoints: number;
  icon: string;
  benefits: string[];
}

export interface LevelProgress {
  currentLevel: Level;
  nextLevel: Level;
  currentPoints: number;
  pointsToNextLevel: number;
  percentage: number;
}

export interface Achievement {
  id: string;
  name: string;
  description: string;
  icon: string;
  earnedAt: string;
  points: number;
}

// Trust score related types
export interface TrustScore {
  score: number;
  level: string;
  nextLevel: string;
  pointsToNextLevel: number;
}

// Document related types
export enum DocumentType {
  ID_PROOF = 'id_proof',
  ADDRESS_PROOF = 'address_proof',
  PROFILE_PHOTO = 'profile_photo',
  EDUCATION_CERTIFICATE = 'education_certificate',
  WORK_CERTIFICATE = 'work_certificate',
  COMPANY_REGISTRATION = 'company_registration',
  TAX_DOCUMENT = 'tax_document',
  REGISTRATION_CERTIFICATE = 'registration_certificate',
  TAX_CERTIFICATE = 'tax_certificate',
  BUSINESS_LICENSE = 'business_license',
  ID_CARD = 'id_card',
  OTHER = 'other',
}

export enum VerificationStatus {
  PENDING = 'pending',
  VERIFIED = 'verified',
  REJECTED = 'rejected',
  APPROVED = 'approved',
}

export interface Document {
  id: string;
  userId?: string;
  companyId?: string;
  documentType: DocumentType;
  documentUrl: string;
  documentNumber?: string;
  verificationStatus: VerificationStatus;
  verifiedBy?: string;
  verifiedAt?: string;
  rejectionReason?: string;
  createdAt: string;
  updatedAt: string;

  // Computed properties for display
  fileName?: string;
  fileSize?: number;
  mimeType?: string;
}

export interface DocumentsResponseData {
  documents: Document[];
  total: number;
  limit: number;
  offset: number;
}

export type DocumentsResponse = ApiResponse<DocumentsResponseData>;

export interface UploadDocumentRequest {
  documentType: DocumentType;
  documentNumber?: string;
  file: {
    uri: string;
    name: string;
    type: string;
  };
}

export interface TrustScoreLog {
  id: string;
  userId: string;
  action: string;
  points: number;
  reason: string;
  createdAt: string;
}

export interface TrustScoreLogsResponseData {
  logs: TrustScoreLog[];
  total: number;
}

export interface TrustScoreLogsResponse extends ApiResponse<TrustScoreLogsResponseData> {
  logs: TrustScoreLog[];
}

export interface TrustScoreCategory {
  name: string;
  score: number;
  maxScore: number;
  percentage: number;
}

export interface TrustScoreBreakdown {
  categories: TrustScoreCategory[];
}

export interface TrustScoreSuggestion {
  id: string;
  title: string;
  description: string;
  points: number;
  difficulty: 'easy' | 'medium' | 'hard';
}

export interface TrustScoreSuggestions {
  suggestions: TrustScoreSuggestion[];
}

// Error response type
export interface ApiError {
  success: boolean;
  statusCode: number;
  message: string;
  error: string;
  details?: Record<string, unknown>;
}
