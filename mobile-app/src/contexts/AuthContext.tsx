'use client';

import type React from 'react';
import { createContext, useState, useContext, useEffect } from 'react';
import AsyncStorage from '@react-native-async-storage/async-storage';
import api from '../services/api';
import { PushNotificationService } from '../services/push-notification.service';
import STORAGE_KEYS from '../constants/storage-keys';
import type { User, RegisterRequest, LoginRequest, AuthResponse } from '../types/api';

interface AuthContextData {
  user: User | null;
  isLoading: boolean;
  hasCompletedOnboarding: boolean;
  signIn: (credentials: LoginRequest) => Promise<void>;
  signInWithOtp: (phone: string, otp: string) => Promise<void>;
  signUp: (userData: RegisterRequest) => Promise<void>;
  signOut: () => Promise<void>;
  updateUser: (data: Partial<User>) => Promise<void>;
  completeOnboarding: () => Promise<void>;
}

const AuthContext = createContext<AuthContextData>({} as AuthContextData);

export const AuthProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [user, setUser] = useState<User | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [hasCompletedOnboarding, setHasCompletedOnboarding] = useState(false);

  useEffect(() => {
    loadStoredData();
  }, []);

  // Initialize push notifications when user is authenticated
  useEffect(() => {
    if (user) {
      initializePushNotifications();
    }
  }, [user]);

  /**
   * Load stored authentication data from AsyncStorage
   */
  async function loadStoredData(): Promise<void> {
    setIsLoading(true);
    try {
      const [userString, token, _refreshToken, onboardingCompleted] = await AsyncStorage.multiGet([
        STORAGE_KEYS.USER_DATA,
        STORAGE_KEYS.AUTH_TOKEN,
        STORAGE_KEYS.AUTH_REFRESH_TOKEN,
        STORAGE_KEYS.ONBOARDING_COMPLETED,
      ]);

      if (token[1] && userString[1]) {
        api.defaults.headers.common.Authorization = `Bearer ${token[1]}`;
        setUser(JSON.parse(userString[1]) as User);
        setHasCompletedOnboarding(onboardingCompleted[1] === 'true');
      }
    } catch (error) {
      console.error('Error loading stored auth data:', error);
    } finally {
      setIsLoading(false);
    }
  }

  async function signIn(credentials: LoginRequest): Promise<void> {
    try {
      const response = await api.post<AuthResponse>('/auth/login', credentials);
      const { user, token, refreshToken } = response.data;

      api.defaults.headers.common.Authorization = `Bearer ${token}`;

      await AsyncStorage.multiSet([
        [STORAGE_KEYS.USER_DATA, JSON.stringify(user)],
        [STORAGE_KEYS.AUTH_TOKEN, token],
        [STORAGE_KEYS.AUTH_REFRESH_TOKEN, refreshToken],
      ]);

      setUser(user);

      // Check if onboarding is completed
      const onboardingCompleted = await AsyncStorage.getItem(STORAGE_KEYS.ONBOARDING_COMPLETED);
      setHasCompletedOnboarding(onboardingCompleted === 'true');
    } catch (error) {
      console.error('Sign in error:', error);
      throw error;
    }
  }

  async function signInWithOtp(phone: string, otp: string): Promise<void> {
    try {
      const response = await api.post<AuthResponse>('/auth/verify-otp', { phone, otp });
      const { user, token, refreshToken } = response.data;

      api.defaults.headers.common.Authorization = `Bearer ${token}`;

      await AsyncStorage.multiSet([
        [STORAGE_KEYS.USER_DATA, JSON.stringify(user)],
        [STORAGE_KEYS.AUTH_TOKEN, token],
        [STORAGE_KEYS.AUTH_REFRESH_TOKEN, refreshToken],
      ]);

      setUser(user);

      // Check if onboarding is completed
      const onboardingCompleted = await AsyncStorage.getItem(STORAGE_KEYS.ONBOARDING_COMPLETED);
      setHasCompletedOnboarding(onboardingCompleted === 'true');
    } catch (error) {
      console.error('OTP sign in error:', error);
      throw error;
    }
  }

  async function signUp(userData: RegisterRequest): Promise<void> {
    try {
      const response = await api.post<AuthResponse>('/auth/register', userData);
      const { user, token, refreshToken } = response.data;

      api.defaults.headers.common.Authorization = `Bearer ${token}`;

      await AsyncStorage.multiSet([
        [STORAGE_KEYS.USER_DATA, JSON.stringify(user)],
        [STORAGE_KEYS.AUTH_TOKEN, token],
        [STORAGE_KEYS.AUTH_REFRESH_TOKEN, refreshToken],
      ]);

      setUser(user);
      setHasCompletedOnboarding(false);
    } catch (error) {
      console.error('Sign up error:', error);
      throw error;
    }
  }

  async function signOut(): Promise<void> {
    try {
      // Unregister device token before signing out
      await unregisterPushNotifications();

      await AsyncStorage.multiRemove([
        STORAGE_KEYS.USER_DATA,
        STORAGE_KEYS.AUTH_TOKEN,
        STORAGE_KEYS.AUTH_REFRESH_TOKEN,
      ]);
      api.defaults.headers.common.Authorization = '';
      setUser(null);
    } catch (error) {
      console.error('Sign out error:', error);
      throw error;
    }
  }

  /**
   * Initialize push notifications
   */
  async function initializePushNotifications(): Promise<void> {
    try {
      const pushNotificationService = PushNotificationService.getInstance();
      await pushNotificationService.initialize();
    } catch (error) {
      console.error('Failed to initialize push notifications:', error);
    }
  }

  /**
   * Unregister push notifications
   */
  async function unregisterPushNotifications(): Promise<void> {
    try {
      const pushNotificationService = PushNotificationService.getInstance();
      await pushNotificationService.unregisterDeviceToken();
      pushNotificationService.cleanup();
    } catch (error) {
      console.error('Failed to unregister push notifications:', error);
    }
  }

  async function updateUser(data: Partial<User>): Promise<void> {
    try {
      const response = await api.put<{ data: User }>('/users/profile', data);
      const updatedUser = response.data.data;

      await AsyncStorage.setItem(STORAGE_KEYS.USER_DATA, JSON.stringify(updatedUser));
      setUser(updatedUser);
    } catch (error) {
      console.error('Update user error:', error);
      throw error;
    }
  }

  async function completeOnboarding(): Promise<void> {
    await AsyncStorage.setItem(STORAGE_KEYS.ONBOARDING_COMPLETED, 'true');
    setHasCompletedOnboarding(true);
  }

  return (
    <AuthContext.Provider
      value={{
        user,
        isLoading,
        hasCompletedOnboarding,
        signIn,
        signInWithOtp,
        signUp,
        signOut,
        updateUser,
        completeOnboarding,
      }}>
      {children}
    </AuthContext.Provider>
  );
};

export function useAuth() {
  const context = useContext(AuthContext);
  if (!context) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
}
