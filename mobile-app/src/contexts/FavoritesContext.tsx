"use client"

import type React from "react"
import { create<PERSON>ontext, useContext, useState, useEffect, type ReactNode } from "react"
import { favoritesAPI } from "../services/api"
import { useAuth } from "./AuthContext"

interface Job {
  id: string
  title: string
  company: {
    id: string
    name: string
  }
  // Add other job properties as needed
}

interface FavoritesContextType {
  favorites: Job[]
  isLoading: boolean
  error: string | null
  addFavorite: (jobId: string) => Promise<void>
  removeFavorite: (jobId: string) => Promise<void>
  isFavorite: (jobId: string) => boolean
  refreshFavorites: () => Promise<void>
}

const FavoritesContext = createContext<FavoritesContextType | undefined>(undefined)

export const FavoritesProvider: React.FC<{ children: ReactNode }> = ({ children }) => {
  const [favorites, setFavorites] = useState<Job[]>([])
  const [isLoading, setIsLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const { isAuthenticated } = useAuth()

  const fetchFavorites = async () => {
    if (!isAuthenticated) return

    setIsLoading(true)
    setError(null)

    try {
      const response = await favoritesAPI.getFavorites()
      // The API returns favorites with job data included
      const favoritesWithJobs = response.data.map((fav: any) => fav.job)
      setFavorites(favoritesWithJobs)
    } catch (err: any) {
      setError(err.response?.data?.message || "Failed to fetch favorites")
      console.error("Error fetching favorites:", err)
    } finally {
      setIsLoading(false)
    }
  }

  useEffect(() => {
    if (isAuthenticated) {
      fetchFavorites()
    } else {
      setFavorites([])
    }
  }, [isAuthenticated])

  const addFavorite = async (jobId: string) => {
    setIsLoading(true)
    setError(null)

    try {
      await favoritesAPI.addFavorite(jobId)
      // Refresh favorites after adding
      await fetchFavorites()
    } catch (err: any) {
      setError(err.response?.data?.message || "Failed to add favorite")
      console.error("Error adding favorite:", err)
    } finally {
      setIsLoading(false)
    }
  }

  const removeFavorite = async (jobId: string) => {
    setIsLoading(true)
    setError(null)

    try {
      await favoritesAPI.removeFavorite(jobId)
      // Update local state immediately for better UX
      setFavorites(favorites.filter((job) => job.id !== jobId))
    } catch (err: any) {
      setError(err.response?.data?.message || "Failed to remove favorite")
      console.error("Error removing favorite:", err)
      // Refresh favorites to ensure consistency
      await fetchFavorites()
    } finally {
      setIsLoading(false)
    }
  }

  const isFavorite = (jobId: string) => {
    return favorites.some((job) => job.id === jobId)
  }

  const refreshFavorites = fetchFavorites

  return (
    <FavoritesContext.Provider
      value={{
        favorites,
        isLoading,
        error,
        addFavorite,
        removeFavorite,
        isFavorite,
        refreshFavorites,
      }}
    >
      {children}
    </FavoritesContext.Provider>
  )
}

export const useFavorites = () => {
  const context = useContext(FavoritesContext)
  if (context === undefined) {
    throw new Error("useFavorites must be used within a FavoritesProvider")
  }
  return context
}
