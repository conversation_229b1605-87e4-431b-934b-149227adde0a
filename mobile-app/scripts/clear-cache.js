#!/usr/bin/env node

/**
 * Script to clear all caches for React Native/Expo project
 * This helps resolve cache-related issues after configuration changes
 */

const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');

console.log('🧹 Clearing all caches...');

const commands = [
  // Clear npm cache
  'npm cache clean --force',
  
  // Clear Expo cache
  'npx expo install --fix',
  
  // Clear Metro cache
  'npx expo start --clear',
  
  // Clear React Native cache
  'npx react-native start --reset-cache',
];

// Directories to remove
const dirsToRemove = [
  'node_modules/.cache',
  '.expo',
  'android/.gradle',
  'ios/build',
  'android/app/build',
];

// Remove cache directories
dirsToRemove.forEach(dir => {
  const fullPath = path.resolve(__dirname, '..', dir);
  if (fs.existsSync(fullPath)) {
    console.log(`🗑️  Removing ${dir}...`);
    try {
      fs.rmSync(fullPath, { recursive: true, force: true });
      console.log(`✅ Removed ${dir}`);
    } catch (error) {
      console.log(`⚠️  Could not remove ${dir}: ${error.message}`);
    }
  }
});

console.log('✅ Cache clearing complete!');
console.log('💡 Now run: npx expo start --clear');
