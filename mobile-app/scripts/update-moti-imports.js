/**
 * <PERSON><PERSON><PERSON> to update MotiView imports across the app
 * 
 * This script finds all files that import MotiView directly from 'moti'
 * and updates them to import from our custom animationComponents utility.
 * 
 * Usage: node scripts/update-moti-imports.js
 */

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

// Configuration
const srcDir = path.resolve(__dirname, '../src');
const fileExtensions = ['.js', '.jsx', '.ts', '.tsx'];

// Find all files that import MotiView from 'moti'
console.log('Finding files with MotiView imports...');
const grepCommand = `grep -r "import.*MotiView.*from 'moti'" ${srcDir} --include="*.{js,jsx,ts,tsx}"`;

try {
  const grepOutput = execSync(grepCommand).toString();
  const filesToUpdate = grepOutput
    .split('\n')
    .filter(Boolean)
    .map(line => line.split(':')[0]);

  console.log(`Found ${filesToUpdate.length} files to update.`);

  // Update each file
  filesToUpdate.forEach(filePath => {
    console.log(`Updating ${filePath}...`);
    
    let content = fs.readFileSync(filePath, 'utf8');
    
    // Replace the import statement
    content = content.replace(
      /import\s+\{\s*MotiView\s*\}\s*from\s+['"]moti['"]/g,
      "import { MotiView } from '@/utils/animationComponents'"
    );
    
    // Also handle cases where MotiView is imported with other components
    content = content.replace(
      /import\s+\{([^}]*),\s*MotiView\s*,([^}]*)\}\s*from\s+['"]moti['"]/g,
      "import {$1,$2} from 'moti';\nimport { MotiView } from '@/utils/animationComponents'"
    );
    
    fs.writeFileSync(filePath, content, 'utf8');
  });

  console.log('All imports updated successfully!');
} catch (error) {
  console.error('Error updating imports:', error.message);
  process.exit(1);
}
