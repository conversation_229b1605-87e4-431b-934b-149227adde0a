/**
 * <PERSON><PERSON><PERSON> to set up <PERSON><PERSON> properly in the project
 * 
 * This script performs the following tasks:
 * 1. Checks if the Reanimated plugin is properly configured in babel.config.js
 * 2. Ensures proper import order in App.tsx
 * 3. Verifies that MotiProvider is properly set up
 * 
 * Usage: node scripts/setup-moti.js
 */

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

// Configuration
const rootDir = path.resolve(__dirname, '..');
const babelConfigPath = path.join(rootDir, 'babel.config.js');
const appPath = path.join(rootDir, 'App.tsx');

// Check babel.config.js
console.log('Checking babel.config.js...');
let babelConfig = fs.readFileSync(babelConfigPath, 'utf8');

if (!babelConfig.includes('react-native-reanimated/plugin')) {
  console.error('Error: Reanimated plugin not found in babel.config.js');
  console.log('Please add the following to your babel.config.js:');
  console.log('plugins: [\'react-native-reanimated/plugin\']');
  process.exit(1);
}

// Check App.tsx
console.log('Checking App.tsx...');
let appContent = fs.readFileSync(appPath, 'utf8');

if (!appContent.includes("import 'react-native-reanimated';")) {
  console.error('Error: Reanimated import not found at the top of App.tsx');
  console.log('Please add the following at the top of your App.tsx:');
  console.log('import \'react-native-reanimated\';');
  process.exit(1);
}

if (!appContent.includes('<MotiProvider>')) {
  console.error('Error: MotiProvider not found in App.tsx');
  console.log('Please wrap your app with MotiProvider');
  process.exit(1);
}

// All checks passed
console.log('✅ Moti setup looks good!');
console.log('');
console.log('Next steps:');
console.log('1. Run "yarn start --reset-cache" to restart your app with a clean cache');
console.log('2. If you still encounter issues, try "yarn clean && yarn start --reset-cache"');
console.log('');
console.log('Note: If you\'re using Hermes, make sure to run with:');
console.log('yarn start:fresh');
