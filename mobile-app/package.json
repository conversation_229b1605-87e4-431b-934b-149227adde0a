{"name": "inventory-audit-worker-app", "version": "1.0.0", "main": "index.js", "scripts": {"start": "expo start", "android": "expo run:android", "ios": "expo run:ios", "web": "expo start --web", "format": "prettier --write \"**/*.{js,jsx,ts,tsx,json,md}\"", "clean:android": "cd android && ./gradlew clean && cd ..", "clean:ios": "cd ios && xcodebuild clean && cd ..", "clean": "yarn clean:android && yarn clean:ios", "start:fresh": "yarn clean && yarn start --reset-cache", "start:no-hermes": "EXPO_USE_HERMES=0 expo start --clear", "fix:moti": "node scripts/update-moti-imports.js && node scripts/setup-moti.js", "clear-cache": "node scripts/clear-cache.js", "start:clear": "npx expo start --clear", "reset": "rm -rf node_modules && npm install && npx expo start --clear"}, "dependencies": {"@expo/config-plugins": "^10.0.2", "@expo/vector-icons": "^14.1.0", "@react-native-async-storage/async-storage": "2.1.2", "@react-native-community/netinfo": "^11.4.1", "@react-native-community/slider": "^4.5.6", "@react-native-picker/picker": "^2.11.0", "@react-navigation/bottom-tabs": "^6.5.8", "@react-navigation/native": "^6.1.7", "@react-navigation/native-stack": "^6.9.13", "@reduxjs/toolkit": "^2.8.2", "date-fns": "^2.30.0", "expo": "^53.0.9", "expo-device": "^7.1.4", "expo-document-picker": "^13.1.5", "expo-image-picker": "~16.1.4", "expo-location": "~18.1.4", "expo-notifications": "~0.31.1", "expo-status-bar": "~2.2.3", "moti": "^0.30.0", "nativewind": "^4.1.23", "react": "19.0.0", "react-native": "0.79.2", "react-native-gesture-handler": "~2.24.0", "react-native-maps": "1.20.1", "react-native-reanimated": "~3.17.4", "react-native-razorpay": "^2.3.0", "react-native-safe-area-context": "^5.4.0", "react-native-screens": "~4.10.0", "react-native-svg": "15.11.2", "react-redux": "^9.2.0"}, "devDependencies": {"@babel/core": "^7.20.0", "@types/react": "~19.0.10", "babel-plugin-module-resolver": "^5.0.0", "metro": "^0.82.0", "metro-resolver": "^0.82.3", "metro-runtime": "^0.82.3", "prettier": "^3.0.0", "prettier-plugin-tailwindcss": "^0.4.1", "tailwindcss": "^3.4.17", "typescript": "~5.8.3"}, "private": true}