// Import Reanimated first - this is critical for <PERSON><PERSON> to work properly
import 'react-native-reanimated';
import 'react-native-gesture-handler';

// Initialize globals for Hermes compatibility first
import { safeInitializeGlobals, isHermesEnabled } from './src/utils/safeInitialize';
safeInitializeGlobals();
console.log('Hermes is enabled:', isHermesEnabled());

// Import global CSS for NativeWind
import './src/global.css';

// Then import React and other dependencies
import React, { useEffect, useState } from 'react';
import { LogBox, Text, View } from 'react-native';
import { SafeAreaProvider } from 'react-native-safe-area-context';
import { NavigationContainer } from '@react-navigation/native';

// Import services
import NetworkService from './src/services/NetworkService';
import CacheService from './src/services/CacheService';
import LocationService from './src/services/LocationService';

// Use mock notification service
// In Expo Go with SDK 53+, expo-notifications is not supported
// This avoids the "expo-notifications functionality was removed from Expo Go" error

// Import navigation
import MainNavigator from './src/navigation/AppNavigator';
import NativeWindSetup from './src/utils/NativeWindSetup';
import { setNavigationRef } from './src/utils/navigation';

// Import Redux Provider
import ReduxProvider from './src/store/ReduxProvider';

// Import Providers
import ThemeProvider from './src/providers/ThemeProvider';
import MotiProvider from './src/providers/MotiProvider';
import NotificationService from '@/services/NotificationService';

// Ignore specific warnings
LogBox.ignoreLogs(['Require cycle:', 'Remote debugger']);

const App = () => {
  const [isOffline, setIsOffline] = useState(false);

  useEffect(() => {
    // Initialize services with proper error handling
    try {
      // Initialize notification service
      NotificationService.initialize().catch((error: Error) => {
        console.error('Failed to initialize notification service:', error);
      });

      // Initialize network service
      NetworkService.initialize();

      // Initialize location service
      LocationService.initialize().catch((error) => {
        console.error('Failed to initialize location service:', error);
      });

      // Add network connection listener
      NetworkService.addConnectionListener((isConnected) => {
        setIsOffline(!isConnected);

        // Clear expired cache when coming back online
        if (isConnected) {
          CacheService.clearExpiredCache();
        }
      });
    } catch (error) {
      console.error('Error initializing services:', error);
    }

    // Clean up resources when the app is unmounted
    return () => {
      try {
        NotificationService.cleanup();
        NetworkService.cleanup();
        LocationService.cleanup();
      } catch (error) {
        console.error('Error cleaning up services:', error);
      }
    };
  }, []);

  return (
    <NativeWindSetup>
      <ReduxProvider>
        <ThemeProvider>
          <MotiProvider>
            <SafeAreaProvider>
              {isOffline && (
                <View className="items-center justify-center bg-destructive p-1">
                  <Text className="text-white">
                    You are offline. Some features may be unavailable.
                  </Text>
                </View>
              )}

              <NavigationContainer ref={(navigatorRef) => setNavigationRef(navigatorRef)}>
                <MainNavigator />
              </NavigationContainer>
            </SafeAreaProvider>
          </MotiProvider>
        </ThemeProvider>
      </ReduxProvider>
    </NativeWindSetup>
  );
};

export default App;
