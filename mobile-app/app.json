{"expo": {"name": "Job Platform", "slug": "inventory-audit-job", "version": "1.0.0", "orientation": "portrait", "icon": "./assets/icon.png", "userInterfaceStyle": "light", "splash": {"image": "./assets/splash.png", "resizeMode": "contain", "backgroundColor": "#ffffff"}, "assetBundlePatterns": ["**/*"], "ios": {"supportsTablet": true, "bundleIdentifier": "com.yourcompany.jobplatform"}, "android": {"adaptiveIcon": {"foregroundImage": "./assets/adaptive-icon.png", "backgroundColor": "#ffffff"}, "package": "com.yourcompany.jobplatform", "permissions": ["android.permission.ACCESS_COARSE_LOCATION", "android.permission.ACCESS_FINE_LOCATION"]}, "web": {"favicon": "./assets/favicon.png", "bundler": "metro"}, "plugins": [["expo-location", {"locationAlwaysAndWhenInUsePermission": "Allow Job Platform to use your location."}], ["expo-notifications", {"icon": "./assets/notification-icon.png", "color": "#ffffff"}]], "extra": {"eas": {"projectId": "ec182f75-81cc-4340-a237-4d04616670ee"}, "API_URL": "http://localhost:3000/api/v1", "APP_NAME": "JobMatch", "APP_VERSION": "1.0.0", "CHAT_ENABLED": true, "NOTIFICATIONS_ENABLED": true, "EMERGENCY_JOBS_ENABLED": true, "GAMIFICATION_ENABLED": true, "API_TIMEOUT": 15000}, "owner": "nitinjha121"}}