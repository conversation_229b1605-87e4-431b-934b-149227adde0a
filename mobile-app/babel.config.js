module.exports = function (api) {
  // Use a more defensive approach for cache
  try {
    if (api && api.cache && typeof api.cache === 'function') {
      api.cache(true);
    }
  } catch (error) {
    // Silently continue without cache if there's an issue
    console.warn('Babel cache not available, continuing without cache');
  }

  const config = {
    presets: [['babel-preset-expo', { jsxImportSource: 'nativewind' }], 'nativewind/babel'],
    plugins: [
      [
        'module-resolver',
        {
          root: ['./src'],
          alias: {
            '@': './src',
            '@shared': '../shared/src',
            '@shared-types': '../shared/src/types',
          },
          extensions: ['.js', '.jsx', '.ts', '.tsx', '.json'],
        },
      ],
      // Keep reanimated plugin last
      'react-native-reanimated/plugin',
    ],
  };

  return config;
};
