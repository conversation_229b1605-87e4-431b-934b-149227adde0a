# Job Platform Mobile App

A React Native mobile application for the Job Platform, allowing workers to find and apply for jobs, manage applications, and communicate with companies.

## Features

- **Authentication**: Login, registration, and OTP verification
- **Job Management**: Browse, search, filter, and apply for jobs
- **Application Tracking**: View and manage job applications
- **Messaging**: Chat with companies and recruiters
- **Profile Management**: Update profile, upload documents, and manage KYC verification
- **Trust Score System**: View and improve trust score
- **Notifications**: Receive and manage notifications
- **Offline Support**: Use the app even when offline with data caching and request queueing

## Technology Stack

- **React Native**: Mobile app framework
- **Expo**: Development platform
- **TypeScript**: Type-safe JavaScript
- **Redux Toolkit**: State management
- **RTK Query**: API data fetching and caching
- **NativeWind**: Tailwind CSS for React Native
- **React Navigation**: Navigation library

## Project Structure

```
mobile-app/
├── assets/                # Static assets like images and fonts
├── src/
│   ├── components/        # Reusable UI components
│   ├── constants/         # App constants
│   ├── hooks/             # Custom React hooks
│   ├── navigation/        # Navigation configuration
│   ├── screens/           # Screen components
│   ├── services/          # Service modules
│   ├── store/             # Redux store configuration
│   │   ├── api/           # RTK Query API endpoints
│   │   └── slices/        # Redux slices
│   ├── types/             # TypeScript type definitions
│   └── utils/             # Utility functions
├── App.tsx                # Main app component
└── index.js               # Entry point
```

## TypeScript Implementation

The application uses TypeScript throughout to ensure type safety:

- **API Types**: Comprehensive interfaces for all API requests and responses
- **Navigation Types**: Type-safe navigation parameters and routes
- **Redux Types**: Typed state and actions
- **Component Props**: Properly typed component props

## RTK Query Implementation

All API calls use RTK Query for consistent data fetching, caching, and state management:

- **Base API Configuration**: Central configuration with error handling and offline support
- **API Slices**: Separate slices for different API domains (auth, jobs, applications, etc.)
- **Optimistic Updates**: Immediate UI updates with rollback on error
- **Cache Invalidation**: Proper cache invalidation strategies
- **Error Handling**: Consistent error handling across all API calls

## Getting Started

### Prerequisites

- Node.js (v16+)
- Yarn or npm
- Expo CLI
- Android Studio or Xcode (for native builds)

### Installation

1. Clone the repository
2. Install dependencies:
   ```bash
   cd mobile-app
   yarn install
   ```

3. Start the development server:
   ```bash
   yarn start
   ```

4. Run on a device or emulator:
   ```bash
   # For Android
   yarn android
   
   # For iOS
   yarn ios
   ```

## Development Guidelines

### Code Style

- Use functional components with hooks
- Follow TypeScript best practices
- Use NativeWind classes for styling (no inline styles or StyleSheet objects)
- Implement proper error handling
- Write meaningful comments

### State Management

- Use Redux Toolkit for global state
- Use RTK Query for API calls
- Use local state for component-specific state
- Use context for theme, localization, etc.

### Navigation

- Use typed navigation parameters
- Implement proper navigation patterns
- Handle deep linking

### Error Handling

- Use the error handling utilities in `src/utils/apiErrorHandling.ts`
- Show user-friendly error messages
- Log errors for debugging

## Troubleshooting

### Hermes Engine Issues

If you encounter issues with the Hermes engine:

1. Try running without Hermes:
   ```bash
   yarn start:no-hermes
   ```

2. Clean the project:
   ```bash
   yarn clean
   yarn start:fresh
   ```

### Build Issues

If you encounter build issues:

1. Clear Metro bundler cache:
   ```bash
   yarn start --reset-cache
   ```

2. Clean native builds:
   ```bash
   yarn clean
   ```

## License

This project is proprietary and confidential.
