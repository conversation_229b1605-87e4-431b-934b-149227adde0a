# API Configuration
API_URL=http://localhost:3000/api/v1

# App Configuration
APP_NAME=JobMatch
APP_VERSION=1.0.0

# Feature Flags
CHAT_ENABLED=true
NOTIFICATIONS_ENABLED=true
EMERGENCY_JOBS_ENABLED=true
GAMIFICATION_ENABLED=true

# API Timeout (in milliseconds)
API_TIMEOUT=15000

# Firebase Configuration (for push notifications)
FIREBASE_API_KEY=your_firebase_api_key
FIREBASE_AUTH_DOMAIN=your_firebase_auth_domain
FIREBASE_PROJECT_ID=your_firebase_project_id
FIREBASE_STORAGE_BUCKET=your_firebase_storage_bucket
FIREBASE_MESSAGING_SENDER_ID=your_firebase_messaging_sender_id
FIREBASE_APP_ID=your_firebase_app_id
FIREBASE_MEASUREMENT_ID=your_firebase_measurement_id

# Maps Configuration
GOOGLE_MAPS_API_KEY=your_google_maps_api_key

# Payment Gateway Configuration
STRIPE_PUBLISHABLE_KEY=your_stripe_publishable_key
RAZORPAY_KEY_ID=your_razorpay_key_id
