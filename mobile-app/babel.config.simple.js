// Simple babel configuration as object export
// Use this if the function-based config causes cache issues

module.exports = {
  presets: [
    ['babel-preset-expo', { jsxImportSource: 'nativewind' }], 
    'nativewind/babel'
  ],
  plugins: [
    [
      'module-resolver',
      {
        root: ['./src'],
        alias: {
          '@': './src',
          '@shared-types': './src/shared-types',
        },
        extensions: ['.js', '.jsx', '.ts', '.tsx', '.json'],
      },
    ],
    // Keep reanimated plugin last
    'react-native-reanimated/plugin',
  ],
};
