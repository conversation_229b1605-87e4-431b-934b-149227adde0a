# Multi-Platform Job Matching Application

A comprehensive job-matching platform connecting companies with temporary workers for inventory and stock audit jobs. The system includes a mobile app for workers, a web portal for companies, and an admin portal to manage the ecosystem.

## Features

### Mobile App (React Native + Expo)
- OTP-based phone number login
- KYC document upload (Aad<PERSON>ar, PAN)
- User profile with trust score
- Job feed with filters
- Job application flow
- Emergency job section
- Rating & feedback
- Earnings page
- Favorites feature
- Chat feature
- Gamification with badges

### Company Portal (Web App)
- Authentication & business KYC
- Job management
- Worker rating system
- Payment dashboard
- Real-time chat
- Job templates
- Performance analytics
- Feedback section

### Admin Portal (Web App)
- Dashboard with stats
- User management
- Job management
- Dispute resolution
- Payout management
- Trust score policy settings
- User activity log
- Reporting feature
- Notification system

## Tech Stack

- **Mobile App**: React Native with Expo, NativeWind
- **Web Portals**: Next.js, TypeScript, Tailwind CSS
- **Backend**: NestJS, TypeORM
- **Database**: PostgreSQL
- **Authentication**: JWT, OTP
- **DevOps**: <PERSON><PERSON>, <PERSON><PERSON> Compose

## Getting Started

### Prerequisites

- Node.js (v18+)
- Yarn
- Docker and Docker Compose
- PostgreSQL (or use the Docker container)

### Installation

1. Clone the repository:
   \`\`\`bash
   git clone https://github.com/yourusername/job-platform.git
   cd job-platform
   \`\`\`

2. Install dependencies:
   \`\`\`bash
   yarn install
   \`\`\`

3. Set up environment variables:
   Create a `.env` file in the root directory with the following variables:
   \`\`\`
   DATABASE_URL=********************************************/job_platform
   JWT_SECRET=your_jwt_secret
   \`\`\`

4. Start the development environment:
   \`\`\`bash
   docker-compose up
   \`\`\`

5. Access the applications:
   - Mobile App: Use Expo Go app with `yarn workspace mobile-app start`
   - Company Portal: http://localhost:3000
   - Admin Portal: http://localhost:3002
   - Backend API: http://localhost:3001

## Project Structure

\`\`\`
job-platform/
├── backend/             # NestJS backend
├── mobile-app/          # React Native mobile app
├── company-portal/      # Next.js company web portal
├── admin-portal/        # Next.js admin web portal
├── shared/              # Shared types and utilities
├── docker-compose.yml   # Docker Compose configuration
└── package.json         # Root package.json for workspaces
\`\`\`

## License

This project is licensed under the MIT License - see the LICENSE file for details.
\`\`\`

This completes the monorepo structure for the multi-platform job matching application. The codebase is now properly organized with all the required components and features. The application is ready for deployment and can be run locally using Docker Compose.
